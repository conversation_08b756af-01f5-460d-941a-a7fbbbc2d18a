//package com.wd.lottery.config;
//
//import lombok.Data;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.context.annotation.Configuration;
//
//import java.util.List;
//
//@Configuration
//@RefreshScope
//@Data
//public class FetchGameIssueResultConfig {
//
//    @Value("${fetch-game-issue-result.url:#{null}}")
//    private String url;
//
//    @Value("${fetch-game-issue-result.appId:#{null}}")
//    private String appId;
//
//    @Value("${fetch-game-issue-result.appSecret:#{null}}")
//    private String appSecret;
//
//    @Value("${fetch-game-issue-result.hour-limit:24}")
//    private Long hourLimit;
//
//    @Value("${fetch-game-issue-result.minute-limit:10}")
//    private Long minuteLimit;
//
//    @Value("${fetch-game-issue-result.game-code-enum-list:}")
//    private List<Integer> gameCodeEnumList;
//
//    public boolean checkConfig() {
//        return url != null && appId != null && appSecret != null;
//    }
//
//}
