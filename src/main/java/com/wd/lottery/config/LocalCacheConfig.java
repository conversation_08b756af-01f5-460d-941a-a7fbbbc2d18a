package com.wd.lottery.config;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.service.MerchantIpRegularService;
import com.wd.lottery.module.member.service.MemberTokenService;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.merchant.service.MerchantService;
import com.wd.lottery.module.merchant.service.MerchantTokenService;
import com.wd.lottery.module.third.repo.ThirdPlatformRepo;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Configuration
@RefreshScope
@Slf4j
public class LocalCacheConfig {

    @Autowired
    private MemberTokenService memberTokenService;
    @Autowired
    private MerchantTokenService merchantTokenService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private MerchantConfigService merchantConfigService;

    @Autowired
    private ThirdPlatformRepo thirdPlatformRepo;
    @Autowired
    private MerchantIpRegularService ipRegularService;

    @Value("${local-cache.tiny.expire-time-seconds:10}")
    private long localCacheTinyExpireTimeSeconds;
    @Value("${local-cache.tiny.maxsize:10000}")
    private long localCacheTinyMaxsize;

    @Value("${local-cache.channel.expire-time-seconds:5}")
    private long localCacheChannelExpireTimeSeconds;
    @Value("${local-cache.channel.maxsize:10000}")
    private long localCacheChannelMaxsize;

    @Value("${local-cache.short.expire-time:5}")
    private long localCacheShortExpireTime;
    @Value("${local-cache.short.maxsize:100000}")
    private long localCacheShortMaxsize;

    @Value("${local-cache.long.expire-time:10}")
    private long localCacheLongExpireTime;
    @Value("${local-cache.long.maxsize:10000}")
    private long localCacheLongMaxsize;

    @Value("${local-cache.member-token.expire-time:2}")
    private long localCacheMemberTokenExpireTime;
    @Value("${local-cache.member-token.max-size: 10000}")
    private long localCacheMemberTokenMaxSize;

    @Value("${local-cache.merchant-token.expire-time:2}")
    private long localCacheMerchantTokenExpireTime;
    @Value("${local-cache.merchant-token.max-size: 10000}")
    private long localCacheMerchantTokenMaxSize;

    @Value("${local-cache.now-game-issue.expire-time:5}")
    //单位秒
    private long localCacheNowGameIssueExpireTime;
    @Value("${local-cache.now-game-issue.max-size: 10000}")
    private long localCacheNowGameIssueMaxSize;

    @Value("${local-cache.merchant-key.expire-time:5}")
    private long localCacheMerchantKeyExpireTime;
    @Value("${local-cache.merchant-key.max-size: 10000}")
    private long localCacheMerchantKeyMaxSize;

    @Value("${local-cache.merchant-key.expire-time:5}")
    private long localCacheMerchantConfigExpireTime;
    @Value("${local-cache.merchant-key.max-size: 10000}")
    private long localCacheMerchantConfigMaxSize;

    @Value("${local-cache.merchant-key.expire-time:5}")
    private long localCacheMerchantConfigAuditResetZeroExpireTime;
    @Value("${local-cache.merchant-key.max-size: 10000}")
    private long localCacheMerchantConfigAuditResetZeroMaxSize;

    @Value("${local-cache.game-result-list.expire-time-seconds:10}")
    private long localCacheGameResultListExpireTimeSeconds;
    @Value("${local-cache.game-issue.maxsize:10000}")
    private long localCacheGameResultListMaxsize;

    @Value("${local-cache.game-statistic-list.expire-time-seconds:60}")
    private long localCacheGameStatisticExpireTimeSeconds;
    @Value("${local-cache.game-issue.maxsize:10000}")
    private long localCacheGameStatisticMaxsize;

    @Value("${local-cache.activity-safe-revenue.expire-time-seconds:30}")
    private long localCacheActivitySafeRevenueExpireTimeSeconds;
    @Value("${local-cache.activity-safe-revenue.maxsize:10000}")
    private long localCacheActivitySafeRevenueMaxsize;

    @Value("${local-cache.third-platform-all-map.expire-time: 2}")
    private long localCacheThirdPlatformAllMapExpireTime;
    @Value("${local-cache.third-platform-all-map.maxsize:1}")
    private long localCacheThirdPlatformAllMapMaxsize;

    @Value("${local-cache.merchant-ip-regular.expire-time:3}")
    private long localCacheMerchantIpRegularExpireTime;
    @Value("${local-cache.merchant-ip-regular.max-size: 10000}")
    private long localCacheMerchantIpRegularMaxSize;

    @Value("${local-cache.merchant-ip-rule-switch.expire-time:3}")
    private long localCacheMerchantIpRuleSwitchExpireTime;
    @Value("${local-cache.merchant-ip-rule-switch.max-size: 10000}")
    private long localCacheMerchantIpRuleSwitchMaxSize;

//    @Value("${local-cache.third-platform-player-exist.expire-time:7}")
//    private long localCacheThirdPlatformPlayerExistExpireTime;
//    @Value("${local-cache.third-platform-player-exist.max-size: 10000}")
//    private long localCacheThirdPlatformPlayerExistMaxSize;
    @Value("${local-cache.merchant-maintain-info.expire-time:10}")
    private long localCacheMerchantMaintainInfoExpireTime;
    @Value("${local-cache.merchant-maintain-info.max-size: 10000}")
    private long localCacheMerchantMaintainInfoMaxSize;

    @Value("${local-cache.third-register-agent.expire-time:7}")
    private long localCacheThirdRegisterAgentExpireTime;
    @Value("${local-cache.third-register-agent.max-size: 10000}")
    private long localCacheThirdRegisterAgentMaxSize;

    @Value("${local-cache.merchant-deposit-withdraw-order-count.expire-time:5}")
    private long localCacheMerchantDepositWithdrawOrderCountExpireTime;
    @Value("${local-cache.merchant-deposit-withdraw-order-count.max-size: 1000}")
    private long localCacheMerchantDepositWithdrawOrderCountMaxSize;


    @Value("${local-cache.activity-single-invite-settled.expire-time:1}")
    private long localCacheActivitySingleInviteSettledExpireTime;
    @Value("${local-cache.activity-single-invite-settled.maxsize:10000}")
    private long localCacheActivitySingleInviteSettledMaxSize;

    @Value("${local-cache.member-aa-team-proxy.expire-time:10}")
    private long localCacheMemberAATeamProxyExpireTime;
    @Value("${local-cache.member-aa-team-proxy.maxsize:10000}")
    private long localCacheMemberAATeamProxyMaxSize;

    @Bean("caffeineCacheManager")
    @Primary
    public CacheManager caffeineCacheManager() {
        ArrayList<CaffeineCache> caches = new ArrayList<>();
        caches.add(tinyCache());
        caches.add(paymentChannelCache());
        caches.add(shortCache());
        caches.add(longCache());
        caches.add(memberTokenCache());
        caches.add(merchantTokenCache());
        caches.add(merchantKeyCache());
        caches.add(merchantConfigKeyCache());
        caches.add(merchantConfigAuditResetZeroKeyCache());
        caches.add(activitySafeRevenueCache());
        caches.add(thirdPlatformAllMapCache());
        caches.add(merchantIpRegularCache());
        caches.add(merchantIpRuleSwitchCache());
        caches.add(merchantMaintainInfoCache());
        caches.add(thirdRegisterAgentCache());
        caches.add(merchantDepositWithdrawOrderCount());
        caches.add(activitySingleInviteSettledCache());
        caches.add(memberAATeamProxyCache());
        SimpleCacheManager cacheManager = new SimpleCacheManager();
        cacheManager.setCaches(caches);
        return cacheManager;
    }

    @NotNull
    private CaffeineCache tinyCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_TINY_NAME,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheTinyExpireTimeSeconds, TimeUnit.SECONDS)
                        .maximumSize(localCacheTinyMaxsize)
                        .build());
    }

    @NotNull
    private CaffeineCache shortCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_SHORT_NAME,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheShortExpireTime, TimeUnit.MINUTES)
                        .maximumSize(localCacheShortMaxsize)
                        .build());
    }

    @NotNull
    private CaffeineCache longCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_LONG_NAME,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheLongExpireTime, TimeUnit.MINUTES)
                        .maximumSize(localCacheLongMaxsize)
                        .build());
    }

    @Bean(Constants.KEY_GENERATOR)
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(target.getClass().getSimpleName());
            stringBuilder.append(StringPool.COLON);
            stringBuilder.append(method.getName());
            stringBuilder.append(StringPool.LEFT_BRACKET);
            for (Object param : params) {
                stringBuilder.append(param);
                stringBuilder.append(StringPool.COMMA);
            }
            stringBuilder.append(StringPool.RIGHT_BRACKET);
            log.debug("key:{}", stringBuilder);
            return stringBuilder.toString();
        };
    }

    private CaffeineCache memberTokenCache() {

        return new CaffeineCache(Constants.LOCAL_CACHE_MEMBER_CACHE_NAME,
                Caffeine.newBuilder()
                        .refreshAfterWrite(localCacheMemberTokenExpireTime, TimeUnit.MINUTES)
                        .maximumSize(localCacheMemberTokenMaxSize)
                        .build(key -> memberTokenService.loadMemberToken(key.toString())));
    }

    private CaffeineCache merchantTokenCache() {

        return new CaffeineCache(Constants.LOCAL_CACHE_MERCHANT_TOKEN_CACHE_NAME,
                Caffeine.newBuilder()
                        .refreshAfterWrite(localCacheMerchantTokenExpireTime, TimeUnit.MINUTES)
                        .maximumSize(localCacheMerchantTokenMaxSize)
                        .build(key -> merchantTokenService.loadMerchantAdminTokenInfoDTO(key.toString())));
    }


    private CaffeineCache merchantKeyCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_MERCHANT_KEY,
                Caffeine.newBuilder()
                        .refreshAfterWrite(localCacheMerchantKeyExpireTime, TimeUnit.MINUTES)
                        .maximumSize(localCacheMerchantKeyMaxSize)
                        .build(operatorToken -> merchantService.loadVerifyInfo(operatorToken.toString())));
    }

    private CaffeineCache merchantConfigKeyCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_MERCHANT_CONFIG,
                Caffeine.newBuilder()
                        .refreshAfterWrite(localCacheMerchantConfigExpireTime, TimeUnit.MINUTES)
                        .maximumSize(localCacheMerchantConfigMaxSize)
                        .build(key -> {
                            String[] split = key.toString().split(":");
                            return merchantConfigService.loadByMerchantIdAndDictKey(Long.parseLong(split[0]), split[1], CurrencyEnum.valueOf(split[2]));
                        }));
    }

    private CaffeineCache merchantConfigAuditResetZeroKeyCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_MERCHANT_CONFIG_AUDIT_RESET_ZERO,
                Caffeine.newBuilder()
                        .refreshAfterWrite(localCacheMerchantConfigAuditResetZeroExpireTime, TimeUnit.MINUTES)
                        .maximumSize(localCacheMerchantConfigAuditResetZeroMaxSize)
                        .build(key -> {
                            String cleanedString = key.toString().replace(StringPool.LEFT_SQ_BRACKET, StringPool.EMPTY).replace(StringPool.RIGHT_SQ_BRACKET, StringPool.EMPTY).trim();
                            List<Long> merchantIds = Stream.of(cleanedString.split(StringPool.COMMA))
                                    .map(String::trim)
                                    .map(Long::parseLong)
                                    .collect(Collectors.toList());
                            return merchantConfigService.loadAuditResetZero(merchantIds);
                        }));
    }

    private CaffeineCache activitySafeRevenueCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_ACTIVITY_SAFE_REVENUE,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheActivitySafeRevenueExpireTimeSeconds, TimeUnit.SECONDS)
                        .maximumSize(localCacheActivitySafeRevenueMaxsize)
                        .build());
    }

    private CaffeineCache thirdPlatformAllMapCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_THIRD_PLATFORM_ALL_MAP,
                Caffeine.newBuilder()
                        .refreshAfterWrite(localCacheThirdPlatformAllMapExpireTime, TimeUnit.SECONDS)
                        .maximumSize(localCacheThirdPlatformAllMapMaxsize)
                        .build(key -> thirdPlatformRepo.getAllMap()));
    }

    /**
     * 商户ip规则配置缓存
     *
     * @return {@link CaffeineCache}
     */
    private CaffeineCache merchantIpRegularCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_MERCHANT_IP_REGULAR,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheMerchantIpRegularExpireTime, TimeUnit.MINUTES)
                        .maximumSize(localCacheMerchantIpRegularMaxSize)
                        .build(merchantId -> this.ipRegularService.loadMerchantIpRegular(Convert.toLong(merchantId))));
    }

    /**
     * 商户ip规则后台开关是否开启缓存
     * @return {@link CaffeineCache}
     */
    private CaffeineCache merchantIpRuleSwitchCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_MERCHANT_IP_RULE_SWITCH,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheMerchantIpRuleSwitchExpireTime, TimeUnit.MINUTES)
                        .maximumSize(localCacheMerchantIpRuleSwitchMaxSize)
                        .build(merchantId -> this.ipRegularService.analyzeMerchantIpRegular(Convert.toLong(merchantId))));
    }

    /**
     *
     * 商户的维护缓存
     * <AUTHOR>
     */
    private CaffeineCache merchantMaintainInfoCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_MERCHANT_MAINTAIN_INFO,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheMerchantMaintainInfoExpireTime, TimeUnit.SECONDS)
                        .maximumSize(localCacheMerchantMaintainInfoMaxSize)
                        .build());
    }

    /**
     * 缓存三方平台注册的代理
     *
     * @return {@link CaffeineCache}
     */
    private CaffeineCache thirdRegisterAgentCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_THIRD_REGISTER_AGENT,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheThirdRegisterAgentExpireTime, TimeUnit.DAYS)
                        .maximumSize(localCacheThirdRegisterAgentMaxSize)
                        .build());
    }


    /**
     * 缓存商户当前未处理订单数量
     * @return {@link CaffeineCache}
     */
    private CaffeineCache merchantDepositWithdrawOrderCount() {
        return new CaffeineCache(Constants.LOCAL_CACHE_MERCHANT_DEPOSIT_WITHDRAW_ORDER_COUNT,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheMerchantDepositWithdrawOrderCountExpireTime, TimeUnit.SECONDS)
                        .maximumSize(localCacheMerchantDepositWithdrawOrderCountMaxSize)
                        .build());
    }

    /**
     * 缓存单次邀请已领取奖励的商户会员
     *
     * @return {@link CaffeineCache}
     */
    private CaffeineCache activitySingleInviteSettledCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_ACTIVITY_SINGLE_INVITE_SETTLED,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheActivitySingleInviteSettledExpireTime, TimeUnit.HOURS)
                        .maximumSize(localCacheActivitySingleInviteSettledMaxSize)
                        .build());
    }

    /**
     * 缓存AA团队代理会员查询相关信息
     * @return {@link CaffeineCache}
     */
    @NotNull
    private CaffeineCache memberAATeamProxyCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_MEMBER_AA_TEAM_PROXY_NAME,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheMemberAATeamProxyExpireTime, TimeUnit.MINUTES)
                        .maximumSize(localCacheMemberAATeamProxyMaxSize)
                        .build());
    }

    @NotNull
    private CaffeineCache paymentChannelCache() {
        return new CaffeineCache(Constants.LOCAL_CACHE_PAYMENT_CHANNEL_NAME,
                Caffeine.newBuilder()
                        .expireAfterWrite(localCacheChannelExpireTimeSeconds, TimeUnit.SECONDS)
                        .maximumSize(localCacheChannelMaxsize)
                        .build());
    }

}
