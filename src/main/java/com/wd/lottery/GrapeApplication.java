package com.wd.lottery;

import com.mzt.logapi.starter.annotation.EnableLogRecord;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@MapperScan("com.wd.lottery.module.*.mapper")
@EnableCaching
@EnableAsync
@EnableRetry
@ServletComponentScan
@EnableLogRecord(tenant = "com.mzt.grape")
public class GrapeApplication {
	private static ConfigurableApplicationContext configurableApplicationContext;
	public static void main(String[] args) {
		configurableApplicationContext = SpringApplication.run(GrapeApplication.class, args);
	}

	public static ConfigurableApplicationContext getConfigurableApplicationContext(){
		return configurableApplicationContext;
	}

	public static <T> T getBean(Class<T> tClass) {
		return configurableApplicationContext.getBean(tClass);
	}

}
