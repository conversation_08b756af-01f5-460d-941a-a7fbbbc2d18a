package com.wd.lottery.common.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * Description: json serialize/deserialize using jackson library
 *
 * <p> Created on 2024/7/5.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
public class JacksonUtil {
    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");

    private static final ObjectMapper om = new ObjectMapper();
    private static final XmlMapper xmlMapper = new XmlMapper();

    static {
        configureObjectMapper(om);
        configureObjectMapper(xmlMapper);
    }

    private static void configureObjectMapper(ObjectMapper xmlMapper) {
        xmlMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        xmlMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, false);
        xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        xmlMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        xmlMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        xmlMapper.configure(SerializationFeature.WRITE_DURATIONS_AS_TIMESTAMPS, false);
        xmlMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(timeFormatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(timeFormatter));
        xmlMapper.registerModule(javaTimeModule);
        xmlMapper.setTimeZone(TimeZone.getDefault());
    }

    @SneakyThrows
    public static String toJSONString(Object value) {
        return om.writeValueAsString(value);
    }


    @SuppressWarnings("unused")
    @SneakyThrows
    public static String toPrettyString(Object value) {
        return om.writerWithDefaultPrettyPrinter().writeValueAsString(value);
    }

    @SneakyThrows
    public static <T> T toJavaObject(String content, Class<T> clazz) {
        return om.readValue(content, clazz);
    }

    @SneakyThrows
    public static <T> T toJavaObject(String content, TypeReference<T> typeReference) {
        return om.readValue(content, typeReference);
    }

    @SneakyThrows
    public static <T> T toJavaObject(InputStream inputStream, TypeReference<T> typeReference) {
        return om.readValue(inputStream, typeReference);
    }

    /**
     * Judge given string is json, number or true/false is json
     *
     * @param content given string
     * @return is json string
     */
    public static boolean isJsonString(String content) {
        if(StringUtils.isBlank(content)) {
            return false;
        }
        ObjectMapper objectMapper = new ObjectMapper()
                .enable(DeserializationFeature.FAIL_ON_TRAILING_TOKENS);
        try {
            objectMapper.readTree(content);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * Judge given string is standard json object.
     * Given string must contain '{}' token
     *
     * @param content given string
     * @return true is standard json
     */
    public static boolean isJsonObjectString(String content) {
        try {
            JacksonUtil.toJavaObject(content, new TypeReference<Map<String, Object>>() {
            });
            return true;
        } catch (Exception e) {
            // ignore
        }
        return false;
    }

    /**
     * Judge given string is standard json array.
     * Given string must contain '[]' token
     *
     * @param content given string
     * @return true is standard json
     */
    @SuppressWarnings("unused")
    public static boolean isJsonArrayString(String content) {
        try {
            JacksonUtil.toJavaObject(content, new TypeReference<List<Object>>() {
            });
            return true;
        } catch (Exception e) {
            // ignore
        }
        return false;
    }

    @SneakyThrows
    public static <T> T xmlToObject(String xmlContent, TypeReference<T> typeReference){
        log.debug("parse xml to object with typeReference, content: {}", xmlContent);
        return xmlMapper.readValue(xmlContent, typeReference);
    }
    @SneakyThrows
    public static <T> T xmlToObject(String xmlContent, Class<T> tClass){
        log.debug("parse xml to object with class, content: {}", xmlContent);
        return xmlMapper.readValue(xmlContent, tClass);
    }


    private JacksonUtil(){}
}
