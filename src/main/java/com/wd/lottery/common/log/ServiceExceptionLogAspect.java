package com.wd.lottery.common.log;

import com.wd.lottery.common.exception.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class ServiceExceptionLogAspect {
    @Pointcut("execution(public * com.wd.lottery.module.*.service.*.*.*(..))")
    public void serviceExceptionLog() {
    }

    @Around("serviceExceptionLog()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        try {
            return pjp.proceed();
        } catch (ApiException apiException) {
            log.info("Service ApiException, class: [{}], method: [{}], param: [{}]", pjp.getTarget().getClass().getName(), pjp.getSignature().getName(), pjp.getArgs());
            throw apiException;
        } catch (Exception e) {
            log.warn("Service exception, class: [{}], method: [{}], param: [{}]", pjp.getTarget().getClass().getName(), pjp.getSignature().getName(), pjp.getArgs());
            throw e;
        }
    }
}
