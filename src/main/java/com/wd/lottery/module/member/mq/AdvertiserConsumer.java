package com.wd.lottery.module.member.mq;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.RabbitMQConstants;
import com.wd.lottery.module.cash.constatns.SubTradeTypeEnum;
import com.wd.lottery.module.cash.dto.CashBetOrderDeliverDTO;
import com.wd.lottery.module.member.constants.AdvertiserEnum;
import com.wd.lottery.module.member.dto.AdvertiserPushDTO;
import com.wd.lottery.module.member.dto.MemberInviteRegisterDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.entity.MemberRefer;
import com.wd.lottery.module.member.service.AdvertiserPushService;
import com.wd.lottery.module.member.service.AdvertiserService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Component
@Slf4j
public class AdvertiserConsumer {

    private static final String NAME = "AdvertiserConsumer";

    @Resource
    private AdvertiserService advertiserService;

    private final Map<Integer, AdvertiserPushService> advertiserPushServiceMap;

    public AdvertiserConsumer(List<AdvertiserPushService> advertiserPushServices) {
        this.advertiserPushServiceMap = advertiserPushServices.stream()
                .collect(Collectors.toMap(
                        service -> service.getAdvertiserEnum().getCode(),
                        service -> service
                ));
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = RabbitMQConstants.MEMBER_ADVERTISER_REGISTER_QUEUE),
            exchange = @Exchange(value = RabbitMQConstants.MEMBER_INVITE_REGISTER_EXCHANGE, delayed = Exchange.TRUE,
                    arguments = @Argument(name = RabbitMQConstants.X_DELAYED_TYPE, value = ExchangeTypes.DIRECT)),
            key = RabbitMQConstants.MEMBER_INVITE_ROULETTE_REGISTER_ROUTE_KEY
    ))
    public void consumerRegisterMessage(String msg, Channel channel, Message message) {
        try {
            log.debug("AdvertiserConsumer..receive register  msg: {}", msg);
            MemberInviteRegisterDTO memberInviteRegisterDTO = JSONUtil.toBean(msg, MemberInviteRegisterDTO.class, true);
            if (Objects.isNull(memberInviteRegisterDTO)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            AdvertiserPushDTO advertiserPushDTO = advertiserService.getAdvertiserPushDTO(memberInviteRegisterDTO.getId(), memberInviteRegisterDTO.getMerchantId());
            if (Objects.isNull(advertiserPushDTO)) {
                log.info("[{}] - 不需要推送入款事件 memberInviteRegisterDTO:{}", NAME, memberInviteRegisterDTO);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            Integer advertiserType = getAdvertiserType(advertiserPushDTO);
            AdvertiserPushService advertiserPushService = advertiserPushServiceMap.get(advertiserType);
            if (Objects.isNull(advertiserPushService)) {
                log.info("[{}] - 未找到广告商推送服务 advertiserType:{}", NAME, advertiserType);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            advertiserPushService.pushRegisterEvent(memberInviteRegisterDTO, advertiserPushDTO);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("{}- consumer msg fail, msg:{}", message.getMessageProperties().getConsumerQueue(), msg, e);
            try {
                TimeUnit.SECONDS.sleep(Constants.DEFAULT_RABBIT_CONSUMER_FAIL_SLEEP_SECONDS);
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
            } catch (Exception ex) {
                log.error("{}-Not ack exception", message.getMessageProperties().getConsumerQueue(), e);
            }
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = RabbitMQConstants.MEMBER_ADVERTISER_DEPOSIT_QUEUE),
            exchange = @Exchange(value = RabbitMQConstants.CASH_MEMBER_WALLET_DELIVER_EXCHANGE, delayed = Exchange.TRUE,
                    arguments = @Argument(name = RabbitMQConstants.X_DELAYED_TYPE, value = ExchangeTypes.DIRECT)),
            key = RabbitMQConstants.CASH_MEMBER_WALLET_DELIVER_ROUTE_KEY
    ))
    @Transactional
    public void consumerDepositMessage(String msg, Channel channel, Message message) {
        log.debug("AdvertiserConsumer..receive deposit msg: {}", msg);
        try {
            CashBetOrderDeliverDTO cashBetOrderDeliverDTO = null;
            try {
                cashBetOrderDeliverDTO = JSONUtil.toBean(msg, CashBetOrderDeliverDTO.class);
            } catch (Exception e) {
                log.error("[{}] - 入款消息序列化异常...", NAME, e);
                try {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                    return;
                } catch (IOException ex) {
                    log.info(String.format("[{}] 入款消息序列化手动ack异常:%s", msg), NAME, ex);
                }
            }

            if (Objects.isNull(cashBetOrderDeliverDTO)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            checkCashMemberWalletLogHis(cashBetOrderDeliverDTO);
            if (ObjectUtil.notEqual(cashBetOrderDeliverDTO.getSubTradeTypeEnum(), SubTradeTypeEnum.MEMBER_DEPOSIT)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            AdvertiserPushDTO advertiserPushDTO = advertiserService.getAdvertiserPushDTO(cashBetOrderDeliverDTO.getMemberId(), cashBetOrderDeliverDTO.getMerchantId());
            if (Objects.isNull(advertiserPushDTO)) {
                log.info("[{}] - 不需要推送入款事件 cashBetOrderDeliverDTO:{}", NAME, cashBetOrderDeliverDTO);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            Integer advertiserType = getAdvertiserType(advertiserPushDTO);
            AdvertiserPushService advertiserPushService = advertiserPushServiceMap.get(advertiserType);
            if (Objects.isNull(advertiserPushService)) {
                log.info("[{}] - 未找到广告商推送服务 advertiserType:{}", NAME, advertiserType);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            advertiserPushService.pushRechargeEvent(cashBetOrderDeliverDTO, advertiserPushDTO);
            if (isFirstRechargedOnRegistrationDay(cashBetOrderDeliverDTO, advertiserPushDTO.getMember())) {
                advertiserPushService.pushFistRechargeEvent(cashBetOrderDeliverDTO, advertiserPushDTO);
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("{}- consumer msg fail, msg:{}", message.getMessageProperties().getConsumerQueue(), message, e);
            try {
                TimeUnit.SECONDS.sleep(Constants.DEFAULT_RABBIT_CONSUMER_FAIL_SLEEP_SECONDS);
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
            } catch (Exception ex) {
                log.error("{}-Not ack exception", message.getMessageProperties().getConsumerQueue(), e);
            }
        }
    }

    private Integer getAdvertiserType(AdvertiserPushDTO advertiserPushDTO) {
        return Optional.ofNullable(advertiserPushDTO.getMemberRefer())
                .map(MemberRefer::getAdvertiserEnum)
                .map(AdvertiserEnum::getCode)
                .orElse(AdvertiserEnum.NONE.getCode());
    }

    private static void checkCashMemberWalletLogHis(CashBetOrderDeliverDTO cashBetOrderDeliverDTO) {
        Assert.notNull(cashBetOrderDeliverDTO.getId());
        Assert.notNull(cashBetOrderDeliverDTO.getMemberId());
        Assert.notNull(cashBetOrderDeliverDTO.getMerchantId());
        Assert.notNull(cashBetOrderDeliverDTO.getCurrencyEnum());
        Assert.notNull(cashBetOrderDeliverDTO.getTradeTypeEnum());
        Assert.notNull(cashBetOrderDeliverDTO.getSubTradeTypeEnum());
        Assert.notNull(cashBetOrderDeliverDTO.getCreateTime());
        Assert.notNull(cashBetOrderDeliverDTO.getAmount());
    }

    private static boolean isFirstRechargedOnRegistrationDay(CashBetOrderDeliverDTO cashBetOrderDeliverDTO, Member member) {
        return cashBetOrderDeliverDTO != null
                && BooleanEnum.TRUE == cashBetOrderDeliverDTO.getIsFirstCharge()
                && member.getCreateTime().toLocalDate().equals(cashBetOrderDeliverDTO.getCreateTime().toLocalDate());
    }

}
