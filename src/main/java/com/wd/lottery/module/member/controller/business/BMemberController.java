package com.wd.lottery.module.member.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.common.util.RequestUtil;
import com.wd.lottery.common.vo.ChartVO;
import com.wd.lottery.module.common.constants.LogSubTypeConstants;
import com.wd.lottery.module.common.constants.LogTypeConstants;
import com.wd.lottery.module.member.dto.*;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.dto.*;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.service.business.BMemberService;
import com.wd.lottery.module.member.vo.MemberAssetsVO;
import com.wd.lottery.module.member.vo.MemberSummaryVO;
import com.wd.lottery.module.member.vo.MemberThirdUserVo;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.List;

/**
 * <p>
 * 用户 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
@RestController
@RequestMapping("${business-path}/${module-path.member}/member")
@Tag(name = "会员模块")
public class BMemberController {
    @Resource
    private BMemberService bMemberService;

    @Operation(summary = "初始化提现密码")
    @Parameters({@Parameter(name = "memberId", description = "会员ID", example = "1001")})
    @PostMapping("resetWithdrawPasswd")
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_RESET_WITHDRAW_PASSWD, subType = LogSubTypeConstants.OTHER,
            success = "商户:{{#merchant}},UID:{{#memberId}}, 币种:{{#currencyEnum}}")
    public ApiResult resetWithdrawPasswd(@RequestBody @Schema(description = "会员ID", example = "0") Long memberId) {
        bMemberService.resetWithdrawPasswd(memberId,AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        LogRecordContext.putVariable("merchant", AdminTokenInfoUtil.getAdminName());
        LogRecordContext.putVariable("memberId", memberId);
        LogRecordContext.putVariable("currencyEnum", AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        return ApiResult.success();
    }

    /**
     * 禁止/启用会员提现功能
     * @param param
     * @return
     */
    @Operation(summary = "禁止/启用会员提现功能")
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_WITHDRAW_STATUS, subType = LogSubTypeConstants.UPDATE,
            success = "UID:{{#memberId}}, 币种:{{#currencyEnum}}, 状态:{{#withdrawStatusEnum}}")
    @PostMapping("updateWithdrawStatus")
    public ApiResult<?> updateWithdrawStatus(@RequestBody BMemberWithdrawStatusParam param) {
        bMemberService.updateWithdrawStatus(param);
        LogRecordContext.putVariable("memberId", param.getMemberId());
        LogRecordContext.putVariable("currencyEnum", AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        LogRecordContext.putVariable("withdrawStatusEnum", param.getWithdrawStatusEnum() == BooleanEnum.TRUE ? "允許" : "禁止");
        return ApiResult.success();
    }

    @Operation(summary = "查询会员三方游戏帐号列表")
    @Parameters({@Parameter(name = "memberId", description = "会员ID", example = "1001")})
    @PostMapping("getMemberThirdUser")
    public ApiResult<Page<MemberThirdUserVo>> getMemberThirdUser(@RequestBody MemberPageQueryParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        return ApiResult.success(bMemberService.getMemberThirdUser(param));
    }

    @GetMapping("page")
    @Operation(summary = "分页查询会员信息")
    public ApiResult<Page<MemberDTO>> page(@Valid MemberPageQueryParam param) {
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        param.setMerchantId(merchantId);
        param.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        Page<MemberDTO> page = bMemberService.getMemberPage(param);
        return ApiResult.success(page);
    }

    @GetMapping("/exportMember")
    @Operation(summary = "导出会员")
    public ApiResult<?> exportMember(HttpServletRequest request, @ParameterObject MemberPageQueryParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        param.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        param.setLanguage(request.getHeader("Accept-Language"));
        param.setCreateBy(AdminTokenInfoUtil.getAdminName());
        param.setAdminId(AdminTokenInfoUtil.getMerchantAdminId());
        param.setRoleId(AdminTokenInfoUtil.getRoleId());
        return bMemberService.exportMember(param);
    }


    @PostMapping("enable")
    @Operation(summary = "启用会员")
    public ApiResult<Boolean> enableMember(@RequestBody @Schema(description = "会员ID", example = "0") Long memberId){
        bMemberService.enableMember(memberId, AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        return ApiResult.success();
    }

    @PostMapping("disable")
    @Operation(summary = "禁用会员")
    public ApiResult<Boolean> disableMember(@RequestBody @Schema(description = "会员ID", example = "0") Long memberId){
        bMemberService.disableMember(memberId, AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        return ApiResult.success();
    }

    @Operation(summary = "校验会员ID")
    @GetMapping("checkMemberId")
    public ApiResult<Boolean> checkMemberId(@RequestParam Long memberId){
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        return ApiResult.success(bMemberService.checkMemberId(memberId, merchantId));
    }

    @Operation(summary = "批量校验会员ID")
    @PostMapping("checkMemberIdList")
    public ApiResult<?> checkMemberIdList(@RequestBody List<Long> memberIdList) {
        bMemberService.checkMemberIdList(memberIdList, AdminTokenInfoUtil.getRequestMerchantIdNotNull(), AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        return ApiResult.success();
    }

    @Operation(summary = "查询会员资产", description = "总资产 = 保险箱余额 + 钱包余额 + 各三方平台余额")
    @Parameters({@Parameter(name = "memberId", description = "会员ID", example = "1001")})
    @GetMapping("getTotalAssets")
    public ApiResult<MemberAssetsVO> getTotalAssets(@RequestParam Long memberId) {

        return ApiResult.success(bMemberService.getTotalAssets(memberId));
    }


    @Operation(summary = "批量禁用会员")
    @PostMapping("disableBatch")
    @Parameters({@Parameter(name = "memberIdList", description = "会员ID数组")})
    public ApiResult<Boolean> disableBatch(@RequestBody List<Long> memberIdList){
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        bMemberService.disableMemberBatch(memberIdList, merchantId);
        return ApiResult.success();
    }

    @Operation(summary = "批量启用会员")
    @PostMapping("enableBatch")
    @Parameters({@Parameter(name = "memberIdList", description = "会员ID数组")})
    public ApiResult<Boolean> enableBatch(@RequestBody List<Long> memberIdList){
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        bMemberService.enableMemberBatch(memberIdList, merchantId);
        return ApiResult.success();
    }

    @Operation(summary = "修改会员备注")
    @PostMapping("updateRemark")
    public ApiResult<Boolean> updateMemberRemark(@RequestBody @Valid MemberUpdateRemarkParam param){
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        param.setMerchantId(merchantId);

        bMemberService.updateMemberRemark(param);
        return ApiResult.success(true);
    }

    @Operation(summary = "會員概要")
    @GetMapping("/memberSummary")
    public ApiResult<MemberSummaryVO> memberSummary(@ParameterObject MemberSummaryDTO memberSummaryDto) {
        memberSummaryDto.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        CurrencyEnum currencyEnum = AdminTokenInfoUtil.getRequestCurrencyEnumNotNull();
        return ApiResult.success(bMemberService.statistics(memberSummaryDto, currencyEnum));
    }

    @Operation(summary = "首頁報表-會員新增人數")
    @GetMapping("/memberCreateCount")
    public ApiResult<Long> memberCreateCount(@ParameterObject MemberCreateCountDTO memberCreateCountDto) {
        memberCreateCountDto.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        CurrencyEnum currencyEnum = AdminTokenInfoUtil.getRequestCurrencyEnumNotNull();
        return ApiResult.success(bMemberService.memberCreateCount(memberCreateCountDto, currencyEnum));
    }

    @Operation(summary = "首頁報表-會員新增折線圖")
    @GetMapping("/chart")
    public ApiResult<List<ChartVO>> chart(@ParameterObject MemberCreateCountChartDTO memberCreateCountChartDto) {
        memberCreateCountChartDto.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        memberCreateCountChartDto.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        return ApiResult.success(bMemberService.getAllByCreateTimeToChart(memberCreateCountChartDto));
    }

    @Operation(summary = "分页查询代理下级会员")
    @GetMapping("proxyMemberPage")
    public ApiResult<Page<ProxyMemberDTO>> proxyMemberPage(@Valid MemberProxyPageQueryParam param){
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        param.setMerchantId(merchantId);
        param.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        Page<ProxyMemberDTO> page = bMemberService.proxyMemberPage(param);
        return ApiResult.success(page);
    }

    @Operation(summary = "导出下级会员")
    @GetMapping("exportSubordinateMember")
    public ApiResult<?> exportSubordinateMember(HttpServletRequest request, @ParameterObject MemberProxyPageQueryParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        param.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        param.setLanguage(request.getHeader("Accept-Language"));
        param.setCreateBy(AdminTokenInfoUtil.getAdminName());
        return bMemberService.exportSubordinateMember(param);
    }


    @Operation(summary = "B端创建会员(手机号)")
    @PostMapping("createMemberByMobile")
    public ApiResult<?> createMemberByMobile(@RequestBody @Valid MemberMobileRegisterParam param){
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        CurrencyEnum currencyEnum = AdminTokenInfoUtil.getRequestCurrencyEnumNotNull();
        param.setCurrencyEnum(currencyEnum);
        param.setRegisterIp(RequestUtil.getBigIntegerIpFromRequest());
        param.setRegisterDomain(RequestUtil.getHostname());
        return bMemberService.registerWithMobile(param);
    }

    @Operation(summary = "B端创建会员(邮箱)")
    @PostMapping("createMemberByEmail")
    public ApiResult<?> createMemberByEmail(@RequestBody @Valid MemberEmailRegisterParam param){
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        CurrencyEnum currencyEnum = AdminTokenInfoUtil.getRequestCurrencyEnumNotNull();
        param.setCurrencyEnum(currencyEnum);
        param.setRegisterIp(RequestUtil.getBigIntegerIpFromRequest());
        param.setRegisterDomain(RequestUtil.getHostname());
        return bMemberService.registerWithEmail(param);
    }

    @Operation(summary = "修改会员密码")
    @PostMapping("changeMemberPasswd")
    public ApiResult<Boolean> changeMemberPasswd(@RequestBody @Valid BMemberChangePasswdParam param){
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        bMemberService.changeMemberPasswd(param);

        return ApiResult.success(true);
    }

    @Operation(summary = "修改真實名稱")
    @PostMapping("updateRealName")
    public ApiResult<Long> updateRealName(@RequestBody @Valid MemberUpdateRealNameParam param){
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        return ApiResult.success(bMemberService.updateMemberRealName(param));
    }

    @Operation(summary = "修改会员分组")
    @PostMapping("updateGroup")
    public ApiResult<Boolean> updateGroup(@RequestBody @Valid MemberUpdateGroupParam param){
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        bMemberService.changeMemberGroup(param);
        return ApiResult.success(true);
    }
}
