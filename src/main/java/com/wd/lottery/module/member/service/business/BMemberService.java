package com.wd.lottery.module.member.service.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.vo.ChartVO;
import com.wd.lottery.module.member.dto.*;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.dto.*;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.vo.MemberAssetsVO;
import com.wd.lottery.module.member.vo.MemberSummaryVO;
import com.wd.lottery.module.member.vo.MemberThirdUserVo;

import java.util.List;

/**
 * Description:
 *
 * <p> Created on 2024/7/1.
 *
 * <AUTHOR>
 * @version 0.1
 */
public interface BMemberService {
    Page<MemberDTO> getMemberPage(MemberPageQueryParam param);

    void enableMember(Long memberId, Long merchantId);

    void disableMember(Long memberId, Long merchantId);

    Boolean checkMemberId(Long memberId, Long merchantId);

    void checkMemberIdList(List<Long> memberIdList, Long merchantId, CurrencyEnum currencyEnum);


    /**
     * This method is used to get the total assets of a member.
     *
     * @param memberId memberId
     * @return The total assets of the member.
     */
    MemberAssetsVO getTotalAssets(Long memberId);

    void disableMemberBatch(List<Long> memberIds, Long merchantId);

    void enableMemberBatch(List<Long> memberIds, Long merchantId);

    void updateMemberRemark(MemberUpdateRemarkParam param);

    Long updateMemberRealName(MemberUpdateRealNameParam param);

    MemberSummaryVO statistics(MemberSummaryDTO memberSummaryDto, CurrencyEnum currencyEnum);

    Long memberCreateCount(MemberCreateCountDTO memberCreateCountDto, CurrencyEnum currencyEnum);

    /**
     * 查询下级代理分页
     *
     * @param param 分页查询参数
     * @return 下级会员分页
     */
    Page<ProxyMemberDTO> proxyMemberPage(MemberProxyPageQueryParam param);

    /**
     * 手机号创建会员
     *
     * @param param 创建会员参数
     * @return 创建结果
     */
    ApiResult<?> registerWithMobile(MemberMobileRegisterParam param);

    /**
     * 邮箱号创建会员
     *
     * @param param 创建会员参数
     * @return 创建结果
     */
    ApiResult<?> registerWithEmail(MemberEmailRegisterParam param);

    /**
     * 后台修改会员密码
     *
     * @param param 修改密码参数
     */
    void changeMemberPasswd(BMemberChangePasswdParam param);

    List<ChartVO> getAllByCreateTimeToChart(MemberCreateCountChartDTO memberCreateCountChartDto);

    Page<MemberThirdUserVo> getMemberThirdUser(MemberPageQueryParam param);

    ApiResult<?> exportMember(MemberPageQueryParam param);

    ApiResult<?> exportSubordinateMember(MemberProxyPageQueryParam param);

    void updateWithdrawStatus(BMemberWithdrawStatusParam param);

    void changeMemberGroup(MemberUpdateGroupParam param);
    void resetWithdrawPasswd(Long memberId,Long merchantId);
}
