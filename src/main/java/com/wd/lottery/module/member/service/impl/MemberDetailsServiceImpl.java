package com.wd.lottery.module.member.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.common.util.IPUtils;
import com.wd.lottery.module.cash.constatns.SubTradeTypeEnum;
import com.wd.lottery.module.member.dto.MemberDetailsDTO;
import com.wd.lottery.module.member.dto.MemberIpDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.service.MemberDetailsService;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.member.util.MemberPhoneUtil;
import com.wd.lottery.module.member.vo.*;
import com.wd.lottery.module.member.vo.*;
import com.wd.lottery.module.merchant.service.BMerchantOverviewPermissionService;
import com.wd.lottery.module.report.constans.DepositTypeEnum;
import com.wd.lottery.module.report.entity.ReportFirstDepositHis;
import com.wd.lottery.module.report.entity.ReportMemberActivityTotal;
import com.wd.lottery.module.report.entity.ReportMemberTotal;
import com.wd.lottery.module.report.service.ReportFirstDepositHisService;
import com.wd.lottery.module.report.service.ReportMemberActivityTotalService;
import com.wd.lottery.module.report.service.ReportMemberTotalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MemberDetailsServiceImpl implements MemberDetailsService {

    private final ReportMemberTotalService reportMemberTotalService;

    private final ReportMemberActivityTotalService reportMemberActivityTotalService;

    private final ReportFirstDepositHisService reportFirstDepositHisService;

    private final MemberService memberService;

    private final BMerchantOverviewPermissionService merchantOverviewPermissionService;

    /**
     * 會員詳情
     *
     * @param memberDetailsDto dto
     * @return vo
     */
    @Override
    public MemberDetailsVO searchDetails(MemberDetailsDTO memberDetailsDto) {
        Long memberId = memberDetailsDto.getMemberId();
        Long merchantId = memberDetailsDto.getMerchantId();
        MemberDetailsVO memberDetailsVo = new MemberDetailsVO();

        // 會員報表
        ReportMemberTotal reportMemberTotal = reportMemberTotalService.getByMemberIdAndMerchantId(memberId, merchantId);
        memberDetailsVo.setMemberReport(processReportMemberTotal(reportMemberTotal));

        // 首充表
        List<ReportFirstDepositHis> reportFirstDepositHisList = reportFirstDepositHisService.findByMemberIdAndMerchantId(memberId, merchantId, memberDetailsDto.getCurrencyEnum());
        memberDetailsVo.setReportFirstDepositHisList(processReportFirstDepositHis(reportFirstDepositHisList));

        // 會員表
        Member member = memberService.getMemberByIdNotCache(memberId, merchantId);
        Map<Long, String> channelNameMap = merchantOverviewPermissionService.getChannelNameMap(memberDetailsDto.getMerchantId(), memberDetailsDto.getAdminId(), memberDetailsDto.getCurrencyEnum(), memberDetailsDto.getRoleId());
        memberDetailsVo.setMember(processMember(member,channelNameMap));

        // 會員活動報表
        List<ReportMemberActivityTotal> reportMemberActivityTotalList = reportMemberActivityTotalService.getByMemberIdAndMerchantId(memberId, merchantId);
        memberDetailsVo.setReportMemberActivityTotalList(processReportMemberActivityTotalList(reportMemberActivityTotalList));

        return memberDetailsVo;
    }

    /**
     * 返回member表 註冊ip相同數量
     *
     * @param ip           ip
     * @param merchantId   merchant uid
     * @param currencyEnum currency enum
     * @return count
     */
    @Override
    public Long registerIpSameCount(String ip, Long merchantId, CurrencyEnum currencyEnum) {
        BigInteger ipBigInteger = IPUtils.changeIpv4OrIpv6ToBigInteger(ip);
        if (ipBigInteger == null) {
            throw new ApiException(CommonCode.FAILED);
        }
        return memberService.countByRegisterIp(ipBigInteger, merchantId, currencyEnum);
    }

    /**
     * 返回member表 註冊ip相同資料
     *
     * @param memberIpDto dto
     * @return list vo
     */
    @Override
    public Page<MemberDetailsMemberSameVO> registerIpSame(MemberIpDTO memberIpDto) {
        Page<MemberDetailsMemberSameVO> page = new Page<>(memberIpDto.getCurrent(), memberIpDto.getSize());

        BigInteger ipBigInteger = IPUtils.changeIpv4OrIpv6ToBigInteger(memberIpDto.getIp());
        if (ipBigInteger == null) {
            throw new ApiException(CommonCode.FAILED);
        }

        Page<Member> memberPage = memberService.getByRegisterIp(ipBigInteger, memberIpDto.getMerchantId(), memberIpDto.getCurrencyEnum(), memberIpDto.getSize(), memberIpDto.getCurrent());
        List<Member> memberList = memberPage.getRecords();
        if (CollectionUtil.isNotEmpty(memberList)) {
            page = new Page<>(memberPage.getPages(), memberPage.getSize());
            page.setRecords(BeanUtil.copyToList(memberList, MemberDetailsMemberSameVO.class));
            page.setCurrent(memberPage.getCurrent());
            page.setPages(memberPage.getPages());
            page.setTotal(memberPage.getTotal());
        }

        return page;
    }

    /**
     * 返回member表 登錄ip相同數量
     *
     * @param ip           ip
     * @param merchantId   merchant uid
     * @param currencyEnum currency enum
     * @return count
     */
    @Override
    public Long loginIpSameCount(String ip, Long merchantId, CurrencyEnum currencyEnum) {
        BigInteger ipBigInteger = IPUtils.changeIpv4OrIpv6ToBigInteger(ip);
        if (ipBigInteger == null) {
            throw new ApiException(CommonCode.FAILED);
        }
        return memberService.countByLoginIp(ipBigInteger, merchantId, currencyEnum);
    }

    /**
     * 返回member表 登錄ip相同資料
     *
     * @param memberIpDto dto
     * @return page vo
     */
    @Override
    public Page<MemberDetailsMemberSameVO> loginIpSame(MemberIpDTO memberIpDto) {
        Page<MemberDetailsMemberSameVO> page = new Page<>(memberIpDto.getCurrent(), memberIpDto.getSize());

        BigInteger ipBigInteger = IPUtils.changeIpv4OrIpv6ToBigInteger(memberIpDto.getIp());
        if (ipBigInteger == null) {
            throw new ApiException(CommonCode.FAILED);
        }

        Page<Member> memberPage = memberService.getByLoginIp(ipBigInteger, memberIpDto.getMerchantId(), memberIpDto.getCurrencyEnum(), memberIpDto.getSize(), memberIpDto.getCurrent());
        List<Member> memberList = memberPage.getRecords();
        if (CollectionUtil.isNotEmpty(memberList)) {
            page = new Page<>(memberPage.getPages(), memberPage.getSize());
            page.setRecords(BeanUtil.copyToList(memberList, MemberDetailsMemberSameVO.class));
            page.setCurrent(memberPage.getCurrent());
            page.setPages(memberPage.getPages());
            page.setTotal(memberPage.getTotal());
        }

        return page;
    }

    private static MemberDetailsReportVO processReportMemberTotal(ReportMemberTotal reportMemberTotal) {
        if (reportMemberTotal != null) {
            MemberDetailsReportVO memberDetailsReportVo = BeanUtil.copyProperties(reportMemberTotal, MemberDetailsReportVO.class);
            memberDetailsReportVo.setActualProfit(Optional.ofNullable(memberDetailsReportVo.getWinLossAmount()).orElse(Constants.ZERO_LONG) + Optional.ofNullable(memberDetailsReportVo.getActivityAmount()).orElse(Constants.ZERO_LONG));
            return memberDetailsReportVo;
        } else {
            return new MemberDetailsReportVO();
        }
    }

    private static List<MemberDetailsReportFirstDepositHisVO> processReportFirstDepositHis(List<ReportFirstDepositHis> reportFirstDepositHisList) {
        List<DepositTypeEnum> depositTypeEnumList = Arrays.stream(DepositTypeEnum.values()).collect(Collectors.toList());
        List<MemberDetailsReportFirstDepositHisVO> memberDetailsReportFirstDepositHisList = BeanUtil.copyToList(reportFirstDepositHisList, MemberDetailsReportFirstDepositHisVO.class);

        Map<DepositTypeEnum, MemberDetailsReportFirstDepositHisVO> memberDetailsReportFirstDepositHisVoMap = memberDetailsReportFirstDepositHisList.stream().collect(Collectors.toMap(MemberDetailsReportFirstDepositHisVO::getDepositTypeEnum, Function.identity()));

        depositTypeEnumList.forEach(depositTypeEnum -> {
            if (memberDetailsReportFirstDepositHisVoMap.get(depositTypeEnum) == null) {
                MemberDetailsReportFirstDepositHisVO memberDetailsReportFirstDepositHisVo = new MemberDetailsReportFirstDepositHisVO();
                memberDetailsReportFirstDepositHisVo.setDepositTypeEnum(depositTypeEnum);
                memberDetailsReportFirstDepositHisList.add(memberDetailsReportFirstDepositHisVo);
            }
        });
        return memberDetailsReportFirstDepositHisList;
    }

    private MemberDetailsMemberVO processMember(Member member, Map<Long, String> channelNameMap) {
        MemberDetailsMemberVO memberDetailsMemberVo = BeanUtil.copyProperties(member, MemberDetailsMemberVO.class, "mobile");
        if (Objects.nonNull(member.getMobile())) {
            memberDetailsMemberVo.setMobile(member.getMobile().toString());
        }
        memberDetailsMemberVo.setRegisterIp(IPUtils.changeBigIntegerToIpv4OrIpv6(member.getRegisterIp()));
        memberDetailsMemberVo.setRegisterArea(IPUtils.getCountry(member.getRegisterIp()));
        if (member.getLoginIp() != null) {
            memberDetailsMemberVo.setLastLoginIp(IPUtils.changeBigIntegerToIpv4OrIpv6(member.getLoginIp()));
            memberDetailsMemberVo.setLoginArea(IPUtils.getCountry(member.getLoginIp()));
        }
        if (StringUtils.isBlank(member.getWithdrawPasswd())) {
            memberDetailsMemberVo.setIsSetWithdrawPassword(BooleanEnum.FALSE);
        } else {
            memberDetailsMemberVo.setIsSetWithdrawPassword(BooleanEnum.TRUE);
        }

        // 账号、手机号
        if (!AdminTokenInfoUtil.isAdmin() && !MemberPhoneUtil.isShowMemberPhone()) {
            String memberName = memberDetailsMemberVo.getMemberName();
            if (StrUtil.isNotEmpty(memberName) && MemberPhoneUtil.isMemberNamePhone(memberName)) {
                memberDetailsMemberVo.setMemberName("***".concat(memberName.substring(memberName.length() - 4)));
            }
            String mobile = memberDetailsMemberVo.getMobile();
            if (StrUtil.isNotEmpty(mobile)) {
                memberDetailsMemberVo.setMobile("***".concat(mobile.substring(mobile.length() - 4)));
            }
        }

        // 注册域名
        memberDetailsMemberVo.setRegisterDomain(member.getRegisterDomain());
        // 登录域名
        memberDetailsMemberVo.setLoginUrl(member.getLoginUrl());

        memberDetailsMemberVo.setChannelName(channelNameMap.get(member.getChannelId()));
        return memberDetailsMemberVo;
    }

    private static List<MemberDetailsReportMemberActivityTotalVO> processReportMemberActivityTotalList(List<ReportMemberActivityTotal> reportMemberActivityTotalList) {
        Set<SubTradeTypeEnum> enableStatisticReportSubTradeTypeEnumSet = SubTradeTypeEnum.getEnableStatisticReportSubTradeTypeEnumSet();
        List<MemberDetailsReportMemberActivityTotalVO> memberDetailsReportMemberActivityTotalVoList = BeanUtil.copyToList(reportMemberActivityTotalList, MemberDetailsReportMemberActivityTotalVO.class);

        Map<SubTradeTypeEnum, MemberDetailsReportMemberActivityTotalVO> memberDetailsReportMemberActivityTotalVoMap = memberDetailsReportMemberActivityTotalVoList.stream().collect(Collectors.toMap(MemberDetailsReportMemberActivityTotalVO::getSubTradeTypeEnum, Function.identity()));

        enableStatisticReportSubTradeTypeEnumSet.forEach(subTradeTypeEnum -> {
            if (memberDetailsReportMemberActivityTotalVoMap.get(subTradeTypeEnum) == null) {
                MemberDetailsReportMemberActivityTotalVO memberDetailsReportMemberActivityTotalVo = new MemberDetailsReportMemberActivityTotalVO();
                memberDetailsReportMemberActivityTotalVo.setSubTradeTypeEnum(subTradeTypeEnum);
                memberDetailsReportMemberActivityTotalVo.setActivityAmount(Constants.ZERO_LONG);
                memberDetailsReportMemberActivityTotalVo.setActivityCount(Constants.ZERO_LONG);
                memberDetailsReportMemberActivityTotalVoList.add(memberDetailsReportMemberActivityTotalVo);
            }
        });

        return memberDetailsReportMemberActivityTotalVoList;
    }

}
