package com.wd.lottery.module.member.service.impl.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.common.util.IPUtils;
import com.wd.lottery.common.vo.ChartVO;
import com.wd.lottery.module.activity.dto.ActivitySafeMemberWalletDTO;
import com.wd.lottery.module.activity.service.ActivitySafeMemberWalletService;
import com.wd.lottery.module.cash.dto.CashMemberWalletDTO;
import com.wd.lottery.module.cash.service.CashMemberWalletService;
import com.wd.lottery.module.common.config.ExportExcelConfig;
import com.wd.lottery.module.common.constants.ExcelExportParamEnum;
import com.wd.lottery.module.common.constants.ExportTypeEnum;
import com.wd.lottery.module.common.constants.LogSubTypeConstants;
import com.wd.lottery.module.common.constants.LogTypeConstants;
import com.wd.lottery.module.common.export.MemberExportExcelStrategy;
import com.wd.lottery.module.common.export.SubordinateMemberExportExcelStrategy;
import com.wd.lottery.module.common.param.AsyncExportParam;
import com.wd.lottery.module.common.service.ExportExcelService;
import com.wd.lottery.module.member.constants.MemberRedisConstants;
import com.wd.lottery.module.member.constants.MemberStateEnum;
import com.wd.lottery.module.member.constants.MemberTypeEnum;
import com.wd.lottery.module.member.dto.*;
import com.wd.lottery.module.member.dto.*;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.entity.MemberGroup;
import com.wd.lottery.module.member.entity.MemberProxy;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.service.*;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.service.*;
import com.wd.lottery.module.member.service.business.BMemberService;
import com.wd.lottery.module.member.util.MemberPhoneUtil;
import com.wd.lottery.module.member.vo.MemberAssetsVO;
import com.wd.lottery.module.member.vo.MemberSummaryVO;
import com.wd.lottery.module.member.vo.MemberThirdUserVo;
import com.wd.lottery.module.third.dto.ThirdUserBalanceDTO;
import com.wd.lottery.module.third.dto.ThirdUserDTO;
import com.wd.lottery.module.third.entity.ThirdUserEntity;
import com.wd.lottery.module.third.mapper.ThirdUserMapper;
import com.wd.lottery.module.third.service.ThirdSiteUserInnerService;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <p> Created on 2024/7/1.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BMemberServiceImpl implements BMemberService {

    private final MemberService memberService;
    private final ActivitySafeMemberWalletService activitySafeMemberWalletService;
    private final CashMemberWalletService cashMemberWalletService;
    private final ThirdSiteUserInnerService thirdSiteUserInnerService;
    private final MemberProxyService memberProxyService;
    private final MemberInnerService memberInnerService;
    private final ThirdUserMapper thirdUserMapper;
    private final ExportExcelConfig exportExcelConfig;
    private final ExportExcelService exportExcelService;
    private final MemberGroupService memberGroupService;
    private final RedisService redisService;
    private final MemberReferService memberReferService;

    @Override
    public Page<MemberDTO> getMemberPage(MemberPageQueryParam param) {
        Assert.notNull(param.getSortEnum());
        Assert.notNull(param.getQuerySortTypeEnum());
        Page<Member> page = Page.of(param.getCurrent(), param.getSize());
        LambdaQueryWrapper<Member> queryWrapper = buildMemberPageQueryWrapper(param);
        queryWrapper.in(CollectionUtil.isNotEmpty(param.getChannelIdSet()),Member::getChannelId, param.getChannelIdSet());
        queryWrapper.eq(CollectionUtil.isNotEmpty(param.getChannelIdSet()),Member::getIsDirect, BooleanEnum.TRUE);
        switch (param.getSortEnum()) {
            case Asc:
                queryWrapper.orderByAsc(param.getQuerySortTypeEnum().getColumn());
                break;
            case Desc:
                queryWrapper.orderByDesc(param.getQuerySortTypeEnum().getColumn());
                break;
        }

        Page<Member> entityPage = memberService.page(page, queryWrapper);

        Page<MemberDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(entityPage, dtoPage, "records");
        List<MemberDTO> dtoRecords = entityPage.getRecords()
                .stream()
                .map(this::convert2Dto)
                .collect(Collectors.toList());

        dtoPage.setRecords(dtoRecords);

        return dtoPage;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_DETAILS, subType = LogSubTypeConstants.UPDATE,
            success = "uid:{{#memberId}}, 用户启用")
    public void enableMember(Long memberId, Long merchantId) {
        memberService.enableMember(memberId, merchantId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_DETAILS, subType = LogSubTypeConstants.UPDATE,
            success = "uid:{{#memberId}}, 用户停用")
    public void disableMember(Long memberId, Long merchantId) {
        memberService.disableMember(memberId, merchantId);
    }

    @Override
    public Boolean checkMemberId(Long memberId, Long merchantId) {
        Member member = memberService.getMemberById(memberId, merchantId);
        return !Objects.isNull(member) && MemberStateEnum.DISABLE != member.getMemberStateEnum();
    }

    @Override
    public void checkMemberIdList(List<Long> memberIdList, Long merchantId, CurrencyEnum currencyEnum) {
        memberService.checkMemberIdList(memberIdList, merchantId, currencyEnum);
    }

    @Override
    public MemberAssetsVO getTotalAssets(Long memberId) {

        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();

        // 会员信息
        Member member = memberService.getMemberById(memberId, merchantId);
        // 钱包余额
        CashMemberWalletDTO walletDTO = cashMemberWalletService.getByMemberId(merchantId, memberId);
        // 活动保险箱余额
        ActivitySafeMemberWalletDTO safeWallet = activitySafeMemberWalletService.getByMemberId(merchantId, memberId);

        MemberAssetsVO assetsVO = buildMemberAsserts(member, walletDTO, safeWallet);

        List<ThirdUserDTO> thirdUsers = thirdSiteUserInnerService.listActiveThirdUser(memberId, merchantId);
        List<ThirdUserBalanceDTO> thirdUserBalanceDTOS = thirdSiteUserInnerService.listThirdUserBalance(thirdUsers);

        assetsVO.setThirdBalances(thirdUserBalanceDTOS);

        assetsVO.calculateTotal();

        return assetsVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_LIST, subType = LogSubTypeConstants.UPDATE,
            success = "UID:{{#memberIds}}, 用户停用")
    public void disableMemberBatch(List<Long> memberIds, Long merchantId) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return;
        }
        memberService.lambdaUpdate()
                .set(Member::getMemberStateEnum, MemberStateEnum.DISABLE)
                .set(Member::getUpdateTime, LocalDateTime.now())
                .eq(Member::getMerchantId, merchantId)
                .in(Member::getId, memberIds)
                .update();

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_LIST, subType = LogSubTypeConstants.UPDATE,
            success = "UID:{{#memberIds}}, 用户启用")
    public void enableMemberBatch(List<Long> memberIds, Long merchantId) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return;
        }
        memberService.lambdaUpdate()
                .set(Member::getMemberStateEnum, MemberStateEnum.ENABLE)
                .set(Member::getUpdateTime, LocalDateTime.now())
                .eq(Member::getMerchantId, merchantId)
                .in(Member::getId, memberIds)
                .update();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMemberRemark(MemberUpdateRemarkParam param) {
        memberService.lambdaUpdate()
                .set(Member::getRemark, param.getRemark())
                .set(Member::getUpdateTime, LocalDateTime.now())
                .eq(Member::getMerchantId, param.getMerchantId())
                .eq(Member::getId, param.getMemberId())
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_DETAILS, subType = LogSubTypeConstants.UPDATE,
            success = "UID:{{#param.memberId}}, 添加/修改真实姓名:{{#param.realName}}")
    public Long updateMemberRealName(MemberUpdateRealNameParam param) {
        memberService.lambdaUpdate()
                .set(Member::getRealName, param.getRealName())
                .set(Member::getUpdateTime, LocalDateTime.now())
                .eq(Member::getMerchantId, param.getMerchantId())
                .eq(Member::getId, param.getMemberId())
                .update();
        return param.getMemberId();
    }

    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public MemberSummaryVO statistics(MemberSummaryDTO memberSummaryDto, CurrencyEnum currencyEnum) {
        MemberSummaryVO memberSummaryVO = new MemberSummaryVO();
        List<Long> memberIds = new ArrayList<>();
        if (Objects.nonNull(memberSummaryDto.getHighMemberId())) {
            // 測試會員和他的下級不列入報表
            Member queryMember = memberService.getMemberById(memberSummaryDto.getHighMemberId(), memberSummaryDto.getMerchantId());
            if (Objects.isNull(queryMember) || queryMember.getMemberTypeEnum() == MemberTypeEnum.TEST_MEMBER) {
                return memberSummaryVO;
            }

            memberIds.add(memberSummaryDto.getHighMemberId());
            List<Long> memberIdList = memberProxyService.getMemberIdByHighMemberIdAndLevel(memberSummaryDto.getHighMemberId(), memberSummaryDto.getMerchantId(), Constants.ONE_LONG);
            if (CollectionUtil.isNotEmpty(memberIdList)) {
                memberIds.addAll(memberIdList);
            }
            memberSummaryDto.setMemberIdList(memberIds);
        }

        LambdaQueryChainWrapper<Member> totalCountQueryWrapper = memberService.lambdaQuery();
        totalCountQueryWrapper.eq(Member::getMerchantId, memberSummaryDto.getMerchantId());
        totalCountQueryWrapper.eq(Member::getCurrencyEnum, currencyEnum);
        totalCountQueryWrapper.eq(Member::getMemberTypeEnum, MemberTypeEnum.FORMAL_MEMBER);

        if (CollectionUtils.isNotEmpty(memberSummaryDto.getMemberIdList())) {
            totalCountQueryWrapper.in(Member::getId, memberSummaryDto.getMemberIdList());
        }
        totalCountQueryWrapper.in(CollectionUtil.isNotEmpty(memberSummaryDto.getChannelIdSet()),Member::getChannelId, memberSummaryDto.getChannelIdSet());
        if (CollectionUtil.isNotEmpty(memberSummaryDto.getChannelIdSet())) {
            totalCountQueryWrapper.eq(Member::getIsDirect, BooleanEnum.TRUE);
        }
        memberSummaryVO.setTotalCount(totalCountQueryWrapper.count());

        LambdaQueryChainWrapper<Member> createCountQueryWrapper = memberService.lambdaQuery();
        createCountQueryWrapper.eq(Member::getMerchantId, memberSummaryDto.getMerchantId());
        createCountQueryWrapper.eq(Member::getCurrencyEnum, currencyEnum);
        createCountQueryWrapper.eq(Member::getMemberTypeEnum, MemberTypeEnum.FORMAL_MEMBER);
        createCountQueryWrapper.in(CollectionUtil.isNotEmpty(memberSummaryDto.getChannelIdSet()),Member::getChannelId, memberSummaryDto.getChannelIdSet());
        createCountQueryWrapper.eq(CollectionUtil.isNotEmpty(memberSummaryDto.getChannelIdSet()),Member::getIsDirect, BooleanEnum.TRUE);
        createCountQueryWrapper.between(Member::getCreateTime, memberSummaryDto.getStartTime(), memberSummaryDto.getEndTime());
        memberSummaryVO.setCreateCount(createCountQueryWrapper.count());

        return memberSummaryVO;
    }

    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_LONG_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public Long memberCreateCount(MemberCreateCountDTO memberCreateCountDto, CurrencyEnum currencyEnum) {
        LambdaQueryChainWrapper<Member> queryWrapper = memberService.lambdaQuery();
        queryWrapper.eq(Member::getMerchantId, memberCreateCountDto.getMerchantId());
        queryWrapper.eq(Member::getCurrencyEnum, currencyEnum);
        queryWrapper.eq(Member::getMemberTypeEnum, MemberTypeEnum.FORMAL_MEMBER);
        queryWrapper.between(Member::getCreateTime, memberCreateCountDto.getStartTime(), memberCreateCountDto.getEndTime());
        return queryWrapper.count();
    }

    @Override
    public Page<ProxyMemberDTO> proxyMemberPage(MemberProxyPageQueryParam param) {
        Page<ProxyMemberDTO> dtoPage = new Page<>(param.getCurrent(), param.getSize());
        // 查询所有下级代理
        Map<Long, MemberProxy> proxyMap = memberProxyService.getAllUnderMapByHighMemberIdAndMemberId(param.getMerchantId(), param.getProxyUid(), param.getUid());
        if (MapUtils.isEmpty(proxyMap)) {
            return dtoPage;
        }
        Collection<MemberProxy> availableSubProxies = proxyMap.values();
        if (Objects.nonNull(param.getMaxProxyLevel())) {
            availableSubProxies = availableSubProxies.stream().filter(i -> i.getLev() <= param.getMaxProxyLevel()).collect(Collectors.toSet());
        }
        if (Objects.nonNull(param.getMinProxyLevel())) {
            availableSubProxies = availableSubProxies.stream().filter(i -> i.getLev() >= param.getMinProxyLevel()).collect(Collectors.toSet());
        }
        if (CollectionUtils.isEmpty(availableSubProxies)) {
            return dtoPage;
        }
        Set<Long> memberIdSet = availableSubProxies.stream().map(MemberProxy::getMemberId).collect(Collectors.toSet());
        LambdaQueryWrapper<Member> queryWrapper = buildMemberPageQueryWrapper(param);
        queryWrapper.in(Member::getId, memberIdSet);

        switch (param.getSortEnum()) {
            case Asc:
                queryWrapper.orderByAsc(param.getQuerySortTypeEnum().getColumn());
                break;
            case Desc:
                queryWrapper.orderByDesc(param.getQuerySortTypeEnum().getColumn());
                break;
        }
        Page<Member> entityPage = memberService.page(new Page<>(param.getCurrent(), param.getSize()), queryWrapper);

        BeanUtils.copyProperties(entityPage, dtoPage, "records");
        List<ProxyMemberDTO> dtoRecords = entityPage.getRecords()
                .stream()
                .map(i -> {
                    ProxyMemberDTO dto = new ProxyMemberDTO();
                    BeanUtils.copyProperties(i, dto);
                    dto.setUid(i.getId());
                    dto.setUpperUid(i.getDirectHighMemberId());
                    dto.setProxyLevel(proxyMap.get(i.getId()).getLev());
                    dto.setIsOnline(i.getLastLoginTime());
                    dto.setRegisterTime(i.getCreateTime());
                    // convert ip
                    dto.setRegisterIp(IPUtils.changeBigIntegerToIpv4OrIpv6(i.getRegisterIp()));
                    dto.setArea(IPUtils.getCountry(dto.getRegisterIp()));
                    return dto;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dtoRecords)) {
            fillSubProxyMemberCount(dtoRecords, param.getMerchantId());
        }

        dtoPage.setRecords(dtoRecords);

        return dtoPage;
    }

    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_LIST, subType = LogSubTypeConstants.CREATE,
            success = "区号:{{#param.areaCode}} 手机号码:{{#param.mobile}} 真实姓名:{{#param.realName}} 邀请码:{{#param.inviteCode}}")
    public ApiResult<?> registerWithMobile(MemberMobileRegisterParam param) {
        String remark = param.getRemark();
        if (StringUtils.isBlank(remark)) {
            param.setRemark("created by merchant");
        }
        return memberInnerService.registerWithMobile(param);
    }

    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_LIST, subType = LogSubTypeConstants.CREATE,
            success = "邮箱:{{#param.email}} 真实姓名:{{#param.realName}} 邀请码:{{#param.inviteCode}}")
    public ApiResult<?> registerWithEmail(MemberEmailRegisterParam param) {
        String remark = param.getRemark();
        if (StringUtils.isBlank(remark)) {
            param.setRemark("created by merchant");
        }
        return memberInnerService.registerWithEmail(param);
    }

    @Override
    public void changeMemberPasswd(BMemberChangePasswdParam param) {
        memberInnerService.changeMemberPasswd(param);
    }

    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_LONG_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<ChartVO> getAllByCreateTimeToChart(MemberCreateCountChartDTO memberCreateCountChartDto) {
        List<ChartVO> chartVoList = new ArrayList<>();
        // 前端要求沒有的資料也要有日期跟0 value
        LocalDate startDate = memberCreateCountChartDto.getStartDate();
        LocalDate endDate = memberCreateCountChartDto.getEndDate();
        while (!startDate.isAfter(endDate)) {
            ChartVO chartVO = new ChartVO();
            chartVO.setCreateDate(startDate);
            chartVO.setValue(0L);

            chartVoList.add(chartVO);
            startDate = startDate.plusDays(1);
        }
        Map<LocalDate, Long> chartVoMap = chartVoList.stream()
                .collect(Collectors.toMap(ChartVO::getCreateDate, ChartVO::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        List<Member> memberList = memberService.getAllByCreateTimeToChart(
                memberCreateCountChartDto.getStartDate().atStartOfDay(), memberCreateCountChartDto.getEndDate().atTime(LocalTime.MAX),
                memberCreateCountChartDto.getMerchantId(), memberCreateCountChartDto.getCurrencyEnum());

        Map<LocalDate, Long> memberMap = memberList.stream().collect(Collectors.toMap(member -> member.getCreateTime().toLocalDate(), Member::getTotalCount));
        memberMap.keySet().forEach(localDate -> {
            if (chartVoMap.get(localDate) != null) {
                chartVoMap.put(localDate, memberMap.get(localDate));
            }
        });
        return chartVoMap.entrySet().stream()
                .map(entry -> {
                    ChartVO chartVO = new ChartVO();
                    chartVO.setCreateDate(entry.getKey());
                    chartVO.setValue(entry.getValue());
                    return chartVO;
                })
                .collect(Collectors.toList());
    }

    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_GROUP, subType = LogSubTypeConstants.UPDATE,
            success = "修改会员分组成功， UID: {{#param.uid}}, 修改前:{{#param.srcGname}}， 修改后:{{#param.destGname}}",
            fail = "修改会员分组失败， UID: {{#param.uid}}, 修改前:{{#param.srcGname}}， 参数:{{#param.destGname}}")
    @Override
    public void changeMemberGroup(MemberUpdateGroupParam param) {
        Member member = memberService.getMemberById(param.getUid(), param.getMerchantId());
        if (Objects.isNull(member)) {
            throw new ApiException(CommonCode.MEMBER_NOT_EXIST);
        }
        MemberGroup group = memberGroupService.getGroupById(param.getMerchantId(), param.getDestGid());
        if (Objects.isNull(group)) {
            throw new ApiException(CommonCode.MEMBER_GROUP_NOT_EXIST);
        }
        if (group.getCurrencyEnum() != member.getCurrencyEnum()) {
            log.error("update member group failed, param group currency not matched");
            throw new ApiException(CommonCode.PARAM_INVALID);
        }
        memberService.changeMemberGroup(param);
    }

    @Override
    public Page<MemberThirdUserVo> getMemberThirdUser(MemberPageQueryParam param) {
        Page<ThirdUserEntity> page = Page.of(param.getCurrent(), param.getSize());
        Page<ThirdUserEntity> thirdUserEntityPage = thirdUserMapper.selectPage(page, new LambdaQueryWrapper<ThirdUserEntity>()
                .select(ThirdUserEntity::getMemberId, ThirdUserEntity::getPlatformCode, ThirdUserEntity::getThirdUserName,
                        ThirdUserEntity::getThirdUserId).eq(ThirdUserEntity::getMerchantId,param.getMerchantId()).eq(ThirdUserEntity::getMemberId, param.getUid()));
        if (Objects.isNull(thirdUserEntityPage) || CollectionUtils.isEmpty(thirdUserEntityPage.getRecords())) {
            return new Page<>(param.getCurrent(), param.getSize());
        }

        return new Page<MemberThirdUserVo>(param.getCurrent(), param.getSize()).setRecords(
                thirdUserEntityPage.getRecords().stream().map(thirdUserEntity -> {
            MemberThirdUserVo memberThirdUserVo = new MemberThirdUserVo();
            BeanUtils.copyProperties(thirdUserEntity, memberThirdUserVo);
            return memberThirdUserVo;
        }).collect(Collectors.toList())).setTotal(thirdUserEntityPage.getTotal());
    }



    private void fillSubProxyMemberCount(List<ProxyMemberDTO> dtoRecords, Long merchantId) {
        Set<Long> collect = dtoRecords.stream().map(ProxyMemberDTO::getUid).collect(Collectors.toSet());
        Map<Long, Long> subCountMap = memberProxyService.getHighMemberAllUnderPopulationMapByHighMemberIdList(collect, merchantId);
        dtoRecords.forEach(i -> {
            Long count = subCountMap.get(i.getUid());
            if (Objects.isNull(count)) {
                count = 0L;
            }
            i.setSubMemberCount(count - 1);
        });
    }

    private MemberAssetsVO buildMemberAsserts(Member memberInfo, CashMemberWalletDTO walletDTO, ActivitySafeMemberWalletDTO safeWallet) {

        MemberAssetsVO vo = new MemberAssetsVO();
        vo.setUid(memberInfo.getId());
        vo.setMemberName(memberInfo.getMemberName());

        vo.setWallet(walletDTO.getCashBalance());
        vo.setSafeBox(safeWallet.getCashBalance());
        return vo;
    }


    private MemberDTO convert2Dto(Member member) {
        MemberDTO dto = new MemberDTO();
        BeanUtils.copyProperties(member, dto);
        dto.setIsOnline(member.getLastLoginTime());
        dto.setRegisterIp(IPUtils.changeBigIntegerToIpv4OrIpv6(member.getRegisterIp()));
        dto.setArea(IPUtils.getCountry(dto.getRegisterIp()));
        String loginUrl = member.getLoginUrl();
        if (StringUtils.isNotBlank(loginUrl)) {
            dto.setLastLoginUrl(loginUrl);
        }
        BigInteger loginIp = member.getLoginIp();
        if (Objects.nonNull(loginIp)) {
            String loginIpStr = IPUtils.changeBigIntegerToIpv4OrIpv6(loginIp);
            String loginIpArea = IPUtils.getCountry(loginIpStr);
            dto.setLastLoginIp(loginIpStr + "(" + loginIpArea + ")");
        }

        // 账号、手机号
        if (!AdminTokenInfoUtil.isAdmin() && !MemberPhoneUtil.isShowMemberPhone()) {
            String memberName = dto.getMemberName();
            if (StrUtil.isNotEmpty(memberName) && MemberPhoneUtil.isMemberNamePhone(memberName)) {
                dto.setMemberName("***".concat(memberName.substring(memberName.length() - 4)));
            }
            String mobile = dto.getMobile();
            if (StrUtil.isNotEmpty(mobile)) {
                dto.setMobile("***".concat(mobile.substring(mobile.length() - 4)));
            }
        }
        return dto;
    }

    private LambdaQueryWrapper<Member> buildMemberPageQueryWrapper(MemberPageQueryParam param) {
        LambdaQueryWrapper<Member> queryWrapper = new LambdaQueryWrapper<>();
        // merchant id
        queryWrapper.eq(Member::getMerchantId, param.getMerchantId());
        // uid
        queryWrapper.eq(Objects.nonNull(param.getUid()), Member::getId, param.getUid());
        // member name
        queryWrapper.like(StringUtils.isNotBlank(param.getMemberName()), Member::getMemberName, param.getMemberName());
        // real name
        queryWrapper.like(StringUtils.isNotBlank(param.getRealName()), Member::getRealName, param.getRealName());
        // mobile
        if (StringUtils.isNotBlank(param.getMobile())) {
            queryWrapper.eq(Member::getMobile, toLongMobile(param.getMobile()));
        }
        // currency
        queryWrapper.eq(Objects.nonNull(param.getCurrencyEnum()), Member::getCurrencyEnum, param.getCurrencyEnum());

        // email
        queryWrapper.eq(StringUtils.isNotBlank(param.getEmail()), Member::getEmail, param.getEmail());
        // registerIp
        if (StringUtils.isNotBlank(param.getRegisterIp())) {
            queryWrapper.eq(Member::getRegisterIp, IPUtils.convertIpToBigInteger(param.getRegisterIp()));
        }
        // lastLoginIp
        if (StringUtils.isNotBlank(param.getLastLoginIp())) {
            queryWrapper.eq(Member::getLoginIp, IPUtils.convertIpToBigInteger(param.getLastLoginIp()));
        }

        // vip level
        queryWrapper.eq(Objects.nonNull(param.getVipLevel()), Member::getVipLevel, param.getVipLevel());
        // member state
        queryWrapper.eq(Objects.nonNull(param.getMemberStateEnum()), Member::getMemberStateEnum, param.getMemberStateEnum());
        // register device
        queryWrapper.eq(Objects.nonNull(param.getRegisterDeviceEnum()), Member::getRegisterDeviceEnum, param.getRegisterDeviceEnum());
        // group id
        queryWrapper.eq(Objects.nonNull(param.getGroupId()), Member::getGroupId, param.getGroupId());
        // register time
        queryWrapper.ge(Objects.nonNull(param.getRegisterTimeBegin()), Member::getCreateTime, param.getRegisterTimeBegin());
        queryWrapper.le(Objects.nonNull(param.getRegisterTimeEnd()), Member::getCreateTime, param.getRegisterTimeEnd());

        // lastLogin time
        queryWrapper.ge(Objects.nonNull(param.getLastLoginTimeBegin()), Member::getLastLoginTime, param.getLastLoginTimeBegin());
        queryWrapper.le(Objects.nonNull(param.getLastLoginTimeEnd()), Member::getLastLoginTime, param.getLastLoginTimeEnd());
        // online status
        Boolean isOnline = param.getIsOnline();
        if (Objects.nonNull(isOnline)) {
            LocalDateTime now = LocalDateTime.now();
            if (Boolean.TRUE.equals(isOnline)) {
                queryWrapper.ge(Member::getLastLoginTime, now.plusHours(-1));
            } else {
                queryWrapper.and(w -> w.isNull(Member::getLastLoginTime).or(w1 -> w1.lt(Member::getLastLoginTime, now.plusHours(-1))));
            }
        }
        queryWrapper.eq(Objects.nonNull(param.getMemberTypeEnum()), Member::getMemberTypeEnum, param.getMemberTypeEnum());
        return queryWrapper;
    }

    private Long toLongMobile(String mobile) {
        if (StringUtils.startsWith(mobile, "+")) {
            mobile = mobile.substring(1);
        }
        return Long.parseLong(mobile);
    }

    @Override
    public ApiResult<?> exportMember(MemberPageQueryParam param) {
        MemberExportParam exportParam = new MemberExportParam();
        buildExportParam(exportParam,param);
        AsyncExportParam asyncExportParam = new AsyncExportParam();
        asyncExportParam.setMerchantId(param.getMerchantId());
        asyncExportParam.setLanguage(param.getLanguage());
        asyncExportParam.setCreateBy(param.getCreateBy());
        asyncExportParam.setExportType(ExportTypeEnum.MEMBER_LIST_DATA);
        asyncExportParam.setFileName(ExportTypeEnum.MEMBER_LIST_DATA.getDesc() + "_" + DateUtil.today() + ".xlsx");

        log.info("exportParam..{},AsyncExportParam...{},map...{}", param, asyncExportParam, JSONUtil.toJsonStr(exportParam));

        MemberExportExcelStrategy strategy =
                new MemberExportExcelStrategy(ExcelExportParamEnum.MEMBER_LIST.getMapperMethod(),
                        ExcelExportParamEnum.MEMBER_LIST.getNextSearchField(), exportParam, exportExcelConfig.getLimit(), param.getLanguage());
        exportExcelService.asyncExportExcel(asyncExportParam, strategy, param.getIsAll());
        return ApiResult.success();
    }

    private void buildExportParam(MemberExportParam exportParam, MemberPageQueryParam param) {
        exportParam.setMerchantId(param.getMerchantId());
        exportParam.setCurrencyEnum(param.getCurrencyEnum());
        if (Objects.nonNull(param.getVipLevel())) {
            exportParam.setVipLevel(param.getVipLevel());
        }
        if (Objects.nonNull(param.getRegisterDeviceEnum())) {
            exportParam.setRegisterDeviceEnum(param.getRegisterDeviceEnum());
        }
        if (Objects.nonNull(param.getMemberStateEnum())) {
            exportParam.setMemberStateEnum(param.getMemberStateEnum());
        }
        if (Objects.nonNull(param.getUid())) {
            exportParam.setUid(param.getUid());
        }
        Boolean isOnline = param.getIsOnline();
        if (Objects.nonNull(isOnline)) {
            LocalDateTime now = LocalDateTime.now();
            exportParam.setLastLoginTime(now.plusHours(-1));
            if (Boolean.TRUE.equals(isOnline)) {
                exportParam.setOnline(Constants.ONLINE);
            } else {
                exportParam.setOffline(Constants.OFFLINE);
            }
        }
        if (Objects.nonNull(param.getRegisterTimeBegin()) && Objects.nonNull(param.getRegisterTimeEnd())){
            exportParam.setRegisterTimeBegin(param.getRegisterTimeBegin());
            exportParam.setRegisterTimeEnd(param.getRegisterTimeEnd());
        }
        if (Objects.nonNull(param.getLastLoginTimeBegin()) && Objects.nonNull(param.getLastLoginTimeEnd())){
            exportParam.setLastLoginTimeBegin(param.getLastLoginTimeBegin());
            exportParam.setLastLoginTimeEnd(param.getLastLoginTimeEnd());
        }
        if (CollUtil.isNotEmpty(param.getChannelIdSet())) {
            exportParam.setChannelIdSet(param.getChannelIdSet());
            exportParam.setIsDirect(BooleanEnum.TRUE.getCode());
        }
        exportParam.setAdminId(param.getAdminId());
        exportParam.setRoleId(param.getRoleId());
        if (param.getIsAll()) {
            //导出全部
            exportParam.setLimit(Long.valueOf(exportExcelConfig.getLimit()));
            exportParam.setId(0);
        } else {
            //导出当前页
            exportParam.setLimit(Objects.nonNull(param.getSize()) ? param.getSize() : exportExcelConfig.getLimit());
        }
    }

    @Override
    public ApiResult<?> exportSubordinateMember(MemberProxyPageQueryParam param) {
        SubordinateMemberExportParam exportParam = new SubordinateMemberExportParam();
        buildExportSubordinateMemberParam(exportParam,param);
        AsyncExportParam asyncExportParam = new AsyncExportParam();
        asyncExportParam.setMerchantId(param.getMerchantId());
        asyncExportParam.setLanguage(param.getLanguage());
        asyncExportParam.setCreateBy(param.getCreateBy());
        asyncExportParam.setExportType(ExportTypeEnum.SUBORDINATE_MEMBER_LIST_DATA);
        asyncExportParam.setFileName(ExportTypeEnum.SUBORDINATE_MEMBER_LIST_DATA.getDesc() + "_" + DateUtil.today() + ".xlsx");
        log.info("exportParam..{},AsyncExportParam...{},map...{}", param, asyncExportParam, JSONUtil.toJsonStr(exportParam));
        SubordinateMemberExportExcelStrategy strategy =
                new SubordinateMemberExportExcelStrategy(ExcelExportParamEnum.SUBORDINATE_MEMBER_LIST.getMapperMethod(),
                        ExcelExportParamEnum.SUBORDINATE_MEMBER_LIST.getNextSearchField(), exportParam, exportExcelConfig.getLimit(), param.getLanguage());
        exportExcelService.asyncExportExcel(asyncExportParam, strategy, param.getIsAll());
        return ApiResult.success();
    }

    @Override
    public void updateWithdrawStatus(BMemberWithdrawStatusParam param) {
        Member member = new Member();
        member.setId(param.getMemberId());
        member.setWithdrawStatusEnum(param.getWithdrawStatusEnum());
        memberService.updateById(member);
    }


    @Override
    public void resetWithdrawPasswd(Long memberId,Long merchantId) {
        memberService.resetWithdrawPasswd(memberId,merchantId);
        String key = String.format(MemberRedisConstants.MEMBER_WITHDRAW_PASSWD_ERROR_NUM, LocalDate.now(),memberId);
        redisService.del(key);
    }

    private void buildExportSubordinateMemberParam(SubordinateMemberExportParam exportParam, MemberProxyPageQueryParam param) {
        // 查询所有下级代理
        Map<Long, MemberProxy> proxyMap = memberProxyService.getAllUnderMapByHighMemberIdAndMemberId(param.getMerchantId(), param.getProxyUid(), param.getUid());
        if (MapUtils.isEmpty(proxyMap)) {
            return;
        }
        Collection<MemberProxy> availableSubProxies = proxyMap.values();
        if (Objects.nonNull(param.getMaxProxyLevel())) {
            availableSubProxies = availableSubProxies.stream().filter(i -> i.getLev() <= param.getMaxProxyLevel()).collect(Collectors.toSet());
        }
        if (Objects.nonNull(param.getMinProxyLevel())) {
            availableSubProxies = availableSubProxies.stream().filter(i -> i.getLev() >= param.getMinProxyLevel()).collect(Collectors.toSet());
        }
        if (CollectionUtils.isEmpty(availableSubProxies)) {
            return;
        }
        Set<Long> memberIdSet = availableSubProxies.stream().map(MemberProxy::getMemberId).collect(Collectors.toSet());
        exportParam.setMemberIdSet(memberIdSet);
        exportParam.setProxyMap(proxyMap);

        // merchant id
        exportParam.setMerchantId(param.getMerchantId());
        // currency
        exportParam.setCurrencyEnum(param.getCurrencyEnum());
        // uid
        if (Objects.nonNull(param.getUid())) {
            exportParam.setUid(param.getUid());
        }
        // proxyUid
        if (Objects.nonNull(param.getProxyUid())) {
            exportParam.setProxyUid(param.getProxyUid());
        }
        // member name
        if (StringUtils.isNotBlank(param.getMemberName())) {
            exportParam.setMemberName(param.getMemberName());
        }
        // real name
        if (StringUtils.isNotBlank(param.getRealName())) {
            exportParam.setRealName(param.getRealName());
        }
        // mobile
        if (StringUtils.isNotBlank(param.getMobile())) {
            exportParam.setMobile(param.getMobile());
        }
        // email
        if (StringUtils.isNotBlank(param.getEmail())) {
            exportParam.setEmail(param.getEmail());
        }
        // registerIp
        if (StringUtils.isNotBlank(param.getRegisterIp())) {
            exportParam.setRegisterIp(IPUtils.convertIpToBigInteger(param.getRegisterIp()));
        }
        // lastLoginIp
        if (StringUtils.isNotBlank(param.getLastLoginIp())) {
            exportParam.setLastLoginIp(IPUtils.convertIpToBigInteger(param.getLastLoginIp()));
        }
        // vip level
        if (Objects.nonNull(param.getVipLevel())) {
            exportParam.setVipLevel(param.getVipLevel());
        }
        // member state
        if (Objects.nonNull(param.getMemberStateEnum())) {
            exportParam.setMemberStateEnum(param.getMemberStateEnum());
        }
        // register device
        if (Objects.nonNull(param.getRegisterDeviceEnum())) {
            exportParam.setRegisterDeviceEnum(param.getRegisterDeviceEnum());
        }
        // group id
        if (Objects.nonNull(param.getGroupId())) {
            exportParam.setGroupId(param.getGroupId());
        }
        // register time
        if (Objects.nonNull(param.getRegisterTimeBegin())&&Objects.nonNull(param.getRegisterTimeEnd())){
            exportParam.setRegisterTimeBegin(param.getRegisterTimeBegin());
            exportParam.setRegisterTimeEnd(param.getRegisterTimeEnd());
        }
        // lastLogin time
        if (Objects.nonNull(param.getLastLoginTimeBegin())&&Objects.nonNull(param.getLastLoginTimeEnd())){
            exportParam.setLastLoginTimeBegin(param.getLastLoginTimeBegin());
            exportParam.setLastLoginTimeEnd(param.getLastLoginTimeEnd());
        }

        Boolean isOnline = param.getIsOnline();
        if (Objects.nonNull(isOnline)) {
            LocalDateTime now = LocalDateTime.now();
            exportParam.setLastLoginTime(now.plusHours(-1));
            if (Boolean.TRUE.equals(isOnline)) {
                exportParam.setOnline(Constants.ONLINE);
            } else {
                exportParam.setOffline(Constants.OFFLINE);
            }
        }
        if (param.getIsAll()) {
            //导出全部
            exportParam.setLimit(Long.valueOf(exportExcelConfig.getLimit()));
            exportParam.setId(0);
        } else {
            //导出当前页
            exportParam.setLimit(Objects.nonNull(param.getSize()) ? param.getSize() : exportExcelConfig.getLimit());
        }
    }

}

