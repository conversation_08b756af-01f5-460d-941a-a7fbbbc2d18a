package com.wd.lottery.module.member.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.entity.CreateAndUpdateEntity;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.common.util.MemberTokenInfoUtil;
import com.wd.lottery.module.cash.constatns.PlatformEnum;
import com.wd.lottery.module.common.constants.CommonConstants;
import com.wd.lottery.module.common.constants.LogSubTypeConstants;
import com.wd.lottery.module.common.constants.LogTypeConstants;
import com.wd.lottery.module.common.constants.MemberLoginLogTypeConstants;
import com.wd.lottery.module.common.service.CommonLoginLogService;
import com.wd.lottery.module.member.dto.*;
import com.wd.lottery.module.member.dto.*;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.entity.MemberWithdrawAccount;
import com.wd.lottery.module.member.mapper.MemberWithdrawAccountMapper;
import com.wd.lottery.module.member.param.MemberInfoUpdateParam;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.member.service.MemberWithdrawAccountService;
import com.wd.lottery.module.member.util.MemberAccountUtil;
import com.wd.lottery.module.member.vo.MemberDetailsMemberWithdrawAccountVO;
import com.wd.lottery.module.merchant.dto.MerchantConfigWithdrawBankAccountBindDTO;
import com.wd.lottery.module.merchant.dto.MerchantConfigWithdrawPlatformDTO;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.payment.entity.PaymentBank;
import com.wd.lottery.module.payment.service.PaymentBankService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MemberWithdrawAccountServiceImpl extends ServiceImpl<MemberWithdrawAccountMapper, MemberWithdrawAccount> implements MemberWithdrawAccountService {

    private static final String logText = "提現銀行卡綁定";

    private final MemberService memberService;

    private final MemberWithdrawAccountMapper memberWithdrawAccountMapper;

    private final MerchantConfigService merchantConfigService;

    private final PaymentBankService paymentBankService;
    private final CommonLoginLogService commonLoginLogService;



    @Override
    public List<MemberWithdrawAccount> findByMemberId(Long memberId, Long merchantId) {
        LambdaQueryWrapper<MemberWithdrawAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberWithdrawAccount::getMemberId, memberId);
        queryWrapper.eq(MemberWithdrawAccount::getMerchantId, merchantId);
        return super.list(queryWrapper);
    }

    @Override
    public MemberWithdrawAccount findByIdAndMemberIdAndMerchantIdNotNull(Long id, Long memberId, Long merchantId) {
        MemberWithdrawAccount memberWithdrawAccount = super.lambdaQuery()
                .eq(MemberWithdrawAccount::getId, id)
                .eq(MemberWithdrawAccount::getMemberId, memberId)
                .eq(MemberWithdrawAccount::getEnableEnum, EnableEnum.TRUE)
                .eq(MemberWithdrawAccount::getMerchantId, merchantId).one();
        Assert.notNull(memberWithdrawAccount, "memberWithdrawAccount is null, id:{}, memberId:{}, merchantId:{}", id, memberId, merchantId);
        return memberWithdrawAccount;
    }

    /**
     * 會員詳情, 銀行卡資訊
     *
     * @param memberDetailsDto dto
     * @param merchantId       merchant uid
     * @return list dto
     */
    @Override
    public List<MemberDetailsMemberWithdrawAccountVO> findByMemberDetails(MemberDetailsDTO memberDetailsDto, Long merchantId) {
        List<MemberDetailsMemberWithdrawAccountVO> memberDetailsMemberWithdrawAccountVoBaseList = new ArrayList<>();
        Map<Long, String> memberIdAndRealNameMap = new HashMap<>();
        LambdaQueryWrapper<MemberWithdrawAccount> queryWrapper = new LambdaQueryWrapper<>();
        if (memberDetailsDto.getMemberId() != null) {
            queryWrapper.eq(MemberWithdrawAccount::getMemberId, memberDetailsDto.getMemberId());
        }

        if (StringUtils.isNotBlank(memberDetailsDto.getRealName())) {
            List<Member> memberList = memberService.getMemberByRealName(memberDetailsDto.getRealName(), merchantId);
            if (CollectionUtil.isNotEmpty(memberList)) {
                memberIdAndRealNameMap = memberList.stream().collect(Collectors.toMap(Member::getId, Member::getRealName));
                queryWrapper.in(MemberWithdrawAccount::getMemberId, memberList.stream().map(Member::getId).collect(Collectors.toList()));
            } else {
                return memberDetailsMemberWithdrawAccountVoBaseList;
            }
        }

        if (StringUtils.isNotBlank(memberDetailsDto.getAccount())) {
            queryWrapper.eq(MemberWithdrawAccount::getAccount, memberDetailsDto.getAccount());
        }

        if (memberDetailsDto.getPlatformEnum() != null) {
            queryWrapper.eq(MemberWithdrawAccount::getPlatformEnum, memberDetailsDto.getPlatformEnum());
        }

        if (memberDetailsDto.getPaymentBankId() != null) {
            queryWrapper.eq(MemberWithdrawAccount::getPaymentBankId, memberDetailsDto.getPaymentBankId());
        }

        if (memberDetailsDto.getEnableEnum() != null) {
            queryWrapper.eq(MemberWithdrawAccount::getEnableEnum, memberDetailsDto.getEnableEnum());
        }

        queryWrapper.eq(MemberWithdrawAccount::getMerchantId, merchantId);
        List<MemberWithdrawAccount> memberWithdrawAccountList = super.list(queryWrapper);
        if (CollectionUtil.isEmpty(memberWithdrawAccountList)) {
            return memberDetailsMemberWithdrawAccountVoBaseList;
        }

        if (CollectionUtil.isEmpty(memberIdAndRealNameMap)) {
            List<Long> memberIds = memberWithdrawAccountList.stream().map(MemberWithdrawAccount::getMemberId).collect(Collectors.toList());
            List<Member> memberList = memberService.getMemberByIds(memberIds, merchantId);
            memberIdAndRealNameMap = memberList.stream().collect(Collectors.toMap(Member::getId, member -> Optional.ofNullable(member.getRealName()).orElse("")
                    ));
        }

        List<Long> paymentBankIdList = memberWithdrawAccountList.stream().map(MemberWithdrawAccount::getPaymentBankId).collect(Collectors.toList());
        List<PaymentBank> paymentBankList = CollectionUtils.isEmpty(paymentBankIdList) ? new ArrayList<>() : paymentBankService.findByIds(paymentBankIdList);

        List<MemberDetailsMemberWithdrawAccountVO> memberDetailsMemberWithdrawAccountVoList = BeanUtil.copyToList(memberWithdrawAccountList, MemberDetailsMemberWithdrawAccountVO.class);
        if (CollectionUtil.isNotEmpty(paymentBankList)) {
            Map<Long, PaymentBank> paymentBankMap = paymentBankList.stream().collect(Collectors.toMap(PaymentBank::getId, Function.identity()));

            Map<Long, String> finalMemberIdAndRealNameMap = memberIdAndRealNameMap;
            memberDetailsMemberWithdrawAccountVoList.forEach(memberDetailsMemberWithdrawAccountVo -> {
                if (paymentBankMap.get(memberDetailsMemberWithdrawAccountVo.getPaymentBankId()) != null && memberDetailsDto.getCurrencyEnum() == paymentBankMap.get(memberDetailsMemberWithdrawAccountVo.getPaymentBankId()).getCurrencyEnum()) {
                    String name = paymentBankMap.get(memberDetailsMemberWithdrawAccountVo.getPaymentBankId()).getName();
                    String channel = paymentBankMap.get(memberDetailsMemberWithdrawAccountVo.getPaymentBankId()).getChannel();
                    memberDetailsMemberWithdrawAccountVo.setBankName(name.equals(channel) ? name : name + "-" + channel);

                    if (finalMemberIdAndRealNameMap.get(memberDetailsMemberWithdrawAccountVo.getMemberId()) != null) {
                        memberDetailsMemberWithdrawAccountVo.setRealName(finalMemberIdAndRealNameMap.get(memberDetailsMemberWithdrawAccountVo.getMemberId()));
                    }
                    // 卡号
                    log.debug("isShowMemberAccount:{} isAdmin :{}",MemberAccountUtil.isShowMemberAccount(), AdminTokenInfoUtil.isAdmin());
                    if (!AdminTokenInfoUtil.isAdmin() && !MemberAccountUtil.isShowMemberAccount()) {
                        String account = memberDetailsMemberWithdrawAccountVo.getAccount();
                        if (StrUtil.isNotEmpty(account) ) {
                            memberDetailsMemberWithdrawAccountVo.setAccount("***".concat(account.substring(account.length() - 4)));
                        }
                    }
                    memberDetailsMemberWithdrawAccountVoBaseList.add(memberDetailsMemberWithdrawAccountVo);
                }
            });
        }
        return memberDetailsMemberWithdrawAccountVoBaseList;
    }

    /**
     * c端新增銀行卡
     *
     * @param memberWithdrawAccountDto dto
     * @return uid
     */
    @Override
    @Transactional
    public Long saveDataByC(MemberWithdrawAccountDTO memberWithdrawAccountDto) {
        Long merchantId = memberWithdrawAccountDto.getMerchantId();
        CurrencyEnum currencyEnum = memberWithdrawAccountDto.getCurrencyEnum();

        MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonConstants.COMMON_DICT_KEY_WITHDRAW_BANK_ACCOUNT_BIND, currencyEnum);
        if (merchantConfig == null) {
            log.error("[{}] 當前商戶無配置, merchantId:{}, currencyEnum:{}", logText, merchantId, currencyEnum.name());
            throw new ApiException(CommonCode.FAILED);
        }

        PaymentBank paymentBank = paymentBankService.findById(memberWithdrawAccountDto.getPaymentBankId());
        if (paymentBank == null) {
            log.error("[{}] 銀行卡綁定 bank id異常, paymentBankId:{}", logText, memberWithdrawAccountDto.getPaymentBankId());
            throw new ApiException(CommonCode.FAILED);
        }

        if (currencyEnum != paymentBank.getCurrencyEnum()) {
            log.error("[{}] 銀行卡綁定 幣種異常, param currency:{}, bank currency:{}", logText, currencyEnum, paymentBank.getCurrencyEnum());
            throw new ApiException(CommonCode.FAILED);
        }

        this.validateIFSCRequirement(memberWithdrawAccountDto, paymentBank);
        this.validateAccountRequiredField(memberWithdrawAccountDto, paymentBank);

        MerchantConfigWithdrawBankAccountBindDTO merchantConfigWithdrawBankAccountBindDto = merchantConfig.merchantConfigListGetFirstNotNull(MerchantConfigWithdrawBankAccountBindDTO.class);
        Integer bindingLimitCount = getBindingLimitCount(merchantConfigWithdrawBankAccountBindDto, memberWithdrawAccountDto.getPlatformEnum());

        LambdaQueryWrapper<MemberWithdrawAccount> queryWrapper = new LambdaQueryWrapper<>();
        // 同一家銀行類型的帳號有無資料 >> 有資料等於當前帳號重複
        queryWrapper.and(wrapper -> wrapper.eq(MemberWithdrawAccount::getPaymentBankId, memberWithdrawAccountDto.getPaymentBankId())
                .eq(MemberWithdrawAccount::getAccount, memberWithdrawAccountDto.getAccount())
                .eq(MemberWithdrawAccount::getMerchantId, merchantId));

        // 同一會員資料, 同一類型 >> 後續判斷是否有預設使用
        queryWrapper.or(wrapper -> wrapper.eq(MemberWithdrawAccount::getMemberId, memberWithdrawAccountDto.getMemberId())
                .eq(MemberWithdrawAccount::getPlatformEnum, memberWithdrawAccountDto.getPlatformEnum())
                .eq(MemberWithdrawAccount::getMerchantId, merchantId));
        List<MemberWithdrawAccount> memberWithdrawAccountList = super.list(queryWrapper);

        // 判斷目前資料是否已達到商戶配置上限
        long bindingCount = memberWithdrawAccountList.stream()
                .filter(memberWithdrawAccount -> Objects.equals(memberWithdrawAccount.getMemberId(), memberWithdrawAccountDto.getMemberId()))
                .filter(memberWithdrawAccount -> Objects.equals(memberWithdrawAccount.getEnableEnum(), EnableEnum.TRUE))
                .count();
        if (bindingCount >= bindingLimitCount) {
            throw new ApiException(CommonCode.BIND_LIMIT);
        }

        // 使用者同一銀行類型只能綁定一個帳號
        List<MemberWithdrawAccount> bankIdAndAccountRepoeatList = memberWithdrawAccountList.stream()
                .filter(memberWithdrawAccount ->
                        (Objects.equals(memberWithdrawAccount.getPaymentBankId(), memberWithdrawAccountDto.getPaymentBankId()) &&
                                Objects.equals(memberWithdrawAccount.getAccount(), memberWithdrawAccountDto.getAccount())))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(bankIdAndAccountRepoeatList)) {
            if (memberWithdrawAccountDto.getPlatformEnum() == PlatformEnum.BANK) {
                throw new ApiException(CommonCode.BANK_BIND_ERROR);
            }
            if (memberWithdrawAccountDto.getPlatformEnum() == PlatformEnum.VIRTUAL) {
                throw new ApiException(CommonCode.VIRTUAL_BIND_ERROR);
            }
            if (memberWithdrawAccountDto.getPlatformEnum() == PlatformEnum.WALLET) {
                throw new ApiException(CommonCode.VIRTUAL_BIND_ERROR);
            }
        }

        if (hasDuplicateWithdrawAccountTypeBinding(memberWithdrawAccountDto.getMerchantId(), memberWithdrawAccountDto.getMemberId(), memberWithdrawAccountDto.getPaymentBankId())) {
            throw new ApiException(CommonCode.BANK_BIND_ERROR);
        }

        // 如果沒有綁定過,當前新增的帳號要預設使用
        EnableEnum defaultUseEnum = EnableEnum.FALSE;
        if (memberWithdrawAccountList.isEmpty()) {
            defaultUseEnum = EnableEnum.TRUE;
        }

        MemberWithdrawAccount memberWithdrawAccount = new MemberWithdrawAccount();
        memberWithdrawAccount.setId(IdWorker.getId());
        memberWithdrawAccount.setMerchantId(merchantId);
        memberWithdrawAccount.setMemberId(memberWithdrawAccountDto.getMemberId());
        memberWithdrawAccount.setMemberName(memberWithdrawAccountDto.getMemberName());
        memberWithdrawAccount.setPaymentBankId(memberWithdrawAccountDto.getPaymentBankId());
        memberWithdrawAccount.setAccount(memberWithdrawAccountDto.getAccount());
        memberWithdrawAccount.setDefaultUseEnum(defaultUseEnum);
        memberWithdrawAccount.setPlatformEnum(memberWithdrawAccountDto.getPlatformEnum());
        memberWithdrawAccount.setRemark(memberWithdrawAccountDto.getRemark());
        memberWithdrawAccount.setAdditional(memberWithdrawAccountDto.getAdditional());
        memberWithdrawAccount.setEnableEnum(EnableEnum.TRUE);
        memberWithdrawAccount.setCreateTime(memberWithdrawAccountDto.getUpdateTime());
        memberWithdrawAccount.setCreateBy(memberWithdrawAccountDto.getMemberName());
        memberWithdrawAccount.setUpdateTime(memberWithdrawAccountDto.getUpdateTime());
        memberWithdrawAccount.setUpdateBy(memberWithdrawAccountDto.getMemberName());
        boolean isSave = super.save(memberWithdrawAccount);
        if (!isSave) {
            throw new ApiException(CommonCode.FAILED);
        }

        updateBindingMemberInfo(memberWithdrawAccountDto);

        //save log
        Member member = memberService.getMemberById(memberWithdrawAccountDto.getMemberId(), merchantId);
        String extString = String.format("会员名称:%s,銀行類型:%s,銀行帳號/錢包地址:%s,備註:%s,啟/停用:%s"
                ,memberWithdrawAccount.getMemberName()
                ,memberWithdrawAccount.getPlatformEnum()
                ,memberWithdrawAccount.getAccount()
                ,memberWithdrawAccount.getRemark()
                ,memberWithdrawAccount.getEnableEnum());
        commonLoginLogService.saveCommonLoginLog(member, MemberLoginLogTypeConstants.MEMBER_BAND_PAYMENT_ACCOUNT , extString);
        return memberWithdrawAccount.getId();
    }

    private void validateIFSCRequirement(MemberWithdrawAccountDTO memberWithdrawAccountDto, PaymentBank paymentBank) {
        if (memberWithdrawAccountDto.getPlatformEnum() == PlatformEnum.BANK &&
                paymentBank.getCurrencyEnum() == CurrencyEnum.INR &&
                StringUtils.isBlank(memberWithdrawAccountDto.getAdditional())) {
            throw new ApiException(CommonCode.IFSC_NOT_ENTERED);
        }
    }

    private void validateAccountRequiredField(MemberWithdrawAccountDTO memberWithdrawAccountDto, PaymentBank paymentBank) {
        MerchantConfig withdrawPlatformMerchantConfig = this.merchantConfigService.getByMerchantIdAndDictKey(memberWithdrawAccountDto.getMerchantId(), CommonConstants.COMMON_DICT_KEY_WITHDRAW_PLATFORM, memberWithdrawAccountDto.getCurrencyEnum());
        if (withdrawPlatformMerchantConfig == null) {
            throw new ApiException(CommonCode.PARAM_INVALID, "withdrawPlatformConfig is null");
        }
        List<MerchantConfigWithdrawPlatformDTO> merchantConfigWithdrawPlatformDTOList = withdrawPlatformMerchantConfig.merchantConfigToListNotEmpty(MerchantConfigWithdrawPlatformDTO.class);

        Optional<MerchantConfigWithdrawPlatformDTO> withdrawPlatformDTO = Optional.ofNullable(merchantConfigWithdrawPlatformDTOList)
                .flatMap(list -> list.stream()
                        .filter(it -> Objects.equals(it.getPlatformEnum(), memberWithdrawAccountDto.getPlatformEnum()))
                        .findFirst());

        boolean hasPlatformDTO = withdrawPlatformDTO.isPresent();

        boolean needRealNameField = !hasPlatformDTO || withdrawPlatformDTO.get().getRealNameRequiredEnum() == BooleanEnum.TRUE;
        boolean needPhoneNumberRequired = hasPlatformDTO && withdrawPlatformDTO.get().getPhoneNumberRequiredEnum() == BooleanEnum.TRUE;
        boolean needEmailRequired = hasPlatformDTO && withdrawPlatformDTO.get().getEmailRequiredEnum() == BooleanEnum.TRUE;
        boolean needAliasRequired = hasPlatformDTO && withdrawPlatformDTO.get().getAccountAliasRequiredEnum() == BooleanEnum.TRUE;

        if (needRealNameField && StringUtils.isBlank(memberWithdrawAccountDto.getRealName())) {
            throw new ApiException(CommonCode.PARAM_INVALID, "Real name is required");
        }
        if (needPhoneNumberRequired && StringUtils.isBlank(memberWithdrawAccountDto.getPhone())) {
            throw new ApiException(CommonCode.PARAM_INVALID, "Phone number is required");
        }
        if (needEmailRequired && StringUtils.isBlank(memberWithdrawAccountDto.getEmail())) {
            throw new ApiException(CommonCode.PARAM_INVALID, "Email is required");
        }
        if (needAliasRequired && StringUtils.isBlank(memberWithdrawAccountDto.getRemark())) {
            throw new ApiException(CommonCode.PARAM_INVALID, "Account alias is required");
        }
    }

    private void updateBindingMemberInfo(MemberWithdrawAccountDTO memberWithdrawAccountDto) {
        MemberInfoUpdateParam memberInfoUpdateParam = new MemberInfoUpdateParam();
        memberInfoUpdateParam.setMemberId(memberWithdrawAccountDto.getMemberId());
        memberInfoUpdateParam.setMerchantId(memberWithdrawAccountDto.getMerchantId());
        memberInfoUpdateParam.setRealName(memberWithdrawAccountDto.getRealName());
        memberInfoUpdateParam.setMobile(memberWithdrawAccountDto.getPhone());
        memberInfoUpdateParam.setEmail(memberWithdrawAccountDto.getEmail());
        memberInfoUpdateParam.setAreaCode(memberWithdrawAccountDto.getAreaCode());
        memberService.updateMemberInfo(memberInfoUpdateParam);
    }

    private Integer getBindingLimitCount(MerchantConfigWithdrawBankAccountBindDTO merchantConfigWithdrawBankAccountBindDto, PlatformEnum platformEnum) {
        switch (platformEnum) {
            case BANK:
                return merchantConfigWithdrawBankAccountBindDto.getBindLimit();
            case VIRTUAL:
                return merchantConfigWithdrawBankAccountBindDto.getVirtualBindLimit();
            case WALLET:
                return merchantConfigWithdrawBankAccountBindDto.getWalletBindLimit();
            default:
                return Integer.MAX_VALUE;
        }
    }

    /**
     * b端新增銀行卡
     *
     * @param memberWithdrawAccountInsertDto dto
     * @return uid
     */
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_WITHDRAW_ACCOUNT, subType = LogSubTypeConstants.CREATE,
            success = "卡号, 币种:{{#memberWithdrawAccountInsertDto.currencyEnum}}, uid:{{#memberWithdrawAccountInsertDto.memberId}}, " +
                    "类型:{{#memberWithdrawAccountInsertDto.platformEnum}}, 渠道/银行名称:{{#paymentBankName}}, " +
                    "卡号/地址:{{#memberWithdrawAccountInsertDto.account}}, 附加栏位:{{#memberWithdrawAccountInsertDto.additional}}")
    public Long saveDataByB(MemberWithdrawAccountInsertDTO memberWithdrawAccountInsertDto) {
        Member member = memberService.getMemberById(memberWithdrawAccountInsertDto.getMemberId(), memberWithdrawAccountInsertDto.getMerchantId());
        if (member == null) {
            throw new ApiException(CommonCode.MEMBER_NOT_EXIST);
        }

        PaymentBank paymentBank = paymentBankService.findById(memberWithdrawAccountInsertDto.getPaymentBankId());
        if (paymentBank == null) {
            log.error("後台添加銀行卡異常 無此資料, paymentBankId:{}", memberWithdrawAccountInsertDto.getPaymentBankId());
            throw new ApiException(CommonCode.FAILED);
        }

        if (memberWithdrawAccountInsertDto.getCurrencyEnum() != paymentBank.getCurrencyEnum()) {
            log.error("後台添加銀行卡異常, paymentBankId:{}, currencyEnum:{}, dto currencyEnum:{}",
                    memberWithdrawAccountInsertDto.getPaymentBankId(), paymentBank.getCurrencyEnum(), memberWithdrawAccountInsertDto.getCurrencyEnum());
            throw new ApiException(CommonCode.FAILED);
        }

        if (hasExistingCardNumber(memberWithdrawAccountInsertDto.getMerchantId(), memberWithdrawAccountInsertDto.getPaymentBankId(), memberWithdrawAccountInsertDto.getAccount())) {
            throw new ApiException(CommonCode.ACCOUNT_DUPLICATE);
        }

        if (hasDuplicateWithdrawAccountTypeBinding(memberWithdrawAccountInsertDto.getMerchantId(), memberWithdrawAccountInsertDto.getMemberId(), memberWithdrawAccountInsertDto.getPaymentBankId())) {
            throw new ApiException(CommonCode.BANK_BIND_ERROR);
        }

        if (memberWithdrawAccountInsertDto.getPlatformEnum() == PlatformEnum.BANK &&
                paymentBank.getCurrencyEnum() == CurrencyEnum.INR &&
                StringUtils.isBlank(memberWithdrawAccountInsertDto.getAdditional())) {
            throw new ApiException(CommonCode.IFSC_NOT_ENTERED);
        }

        MemberWithdrawAccount memberWithdrawAccount = new MemberWithdrawAccount();
        memberWithdrawAccount.setId(IdWorker.getId());
        memberWithdrawAccount.setMerchantId(memberWithdrawAccountInsertDto.getMerchantId());
        memberWithdrawAccount.setMemberId(memberWithdrawAccountInsertDto.getMemberId());
        memberWithdrawAccount.setMemberName(member.getMemberName());
        memberWithdrawAccount.setPaymentBankId(memberWithdrawAccountInsertDto.getPaymentBankId());
        memberWithdrawAccount.setAccount(memberWithdrawAccountInsertDto.getAccount());
        memberWithdrawAccount.setDefaultUseEnum(EnableEnum.FALSE);
        memberWithdrawAccount.setPlatformEnum(paymentBank.getPlatformEnum());
        memberWithdrawAccount.setAdditional(memberWithdrawAccountInsertDto.getAdditional());
        memberWithdrawAccount.setEnableEnum(memberWithdrawAccountInsertDto.getEnableEnum());
        memberWithdrawAccount.setUpdateBy(memberWithdrawAccountInsertDto.getAdminName());
        memberWithdrawAccount.setUpdateTime(memberWithdrawAccountInsertDto.getUpdateTime());
        memberWithdrawAccount.setCreateBy(memberWithdrawAccountInsertDto.getAdminName());
        memberWithdrawAccount.setCreateTime(memberWithdrawAccountInsertDto.getUpdateTime());
        boolean isSave = super.save(memberWithdrawAccount);
        if (!isSave) {
            throw new ApiException(CommonCode.FAILED);
        }

        String bankName = paymentBank.getName();
        if (!paymentBank.getName().equals(paymentBank.getChannel())) {
            bankName += "-" + paymentBank.getChannel();
        }
        LogRecordContext.putVariable("paymentBankName", bankName);

        return memberWithdrawAccount.getId();
    }

    /**
     * 更新
     *
     * @param memberWithdrawAccountUpdateDto dto
     * @return uid
     */
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_WITHDRAW_ACCOUNT, subType = LogSubTypeConstants.UPDATE,
            success = "卡号, 卡号uid:{{#memberWithdrawAccountUpdateDto.id}}, 卡号/地址:{{#memberWithdrawAccountUpdateDto.account}}, " +
                    "附加栏位:{{#memberWithdrawAccountUpdateDto.additional}}")
    public Long updateData(MemberWithdrawAccountUpdateDTO memberWithdrawAccountUpdateDto) {
        PaymentBank paymentBank = paymentBankService.findById(memberWithdrawAccountUpdateDto.getPaymentBankId());
        if (paymentBank == null) {
            log.error("後台添加銀行卡異常 無此資料, paymentBankId:{}", memberWithdrawAccountUpdateDto.getPaymentBankId());
            throw new ApiException(CommonCode.FAILED);
        }

        boolean isUpdate = super.lambdaUpdate()
                .set(MemberWithdrawAccount::getPaymentBankId, memberWithdrawAccountUpdateDto.getPaymentBankId())
                .set(MemberWithdrawAccount::getAccount, memberWithdrawAccountUpdateDto.getAccount())
                .set(MemberWithdrawAccount::getAdditional, memberWithdrawAccountUpdateDto.getAdditional())
                .set(CreateAndUpdateEntity::getUpdateTime, memberWithdrawAccountUpdateDto.getUpdateTime())
                .set(CreateAndUpdateEntity::getUpdateBy, memberWithdrawAccountUpdateDto.getAdminName())
                .eq(MemberWithdrawAccount::getId, memberWithdrawAccountUpdateDto.getId())
                .eq(MemberWithdrawAccount::getMerchantId, memberWithdrawAccountUpdateDto.getMerchantId())
                .update();
        if (!isUpdate) {
            throw new ApiException(CommonCode.FAILED);
        }

        return memberWithdrawAccountUpdateDto.getId();
    }

    /**
     * 預設啟用
     *
     * @param id       member_bank uid
     * @param memberId member uid
     * @return member_bank uid
     */
    @Override
    @Transactional
    public Long defaultUseEnable(Long id, Long memberId, PlatformEnum platformEnum, Long merchantId) {
        memberWithdrawAccountMapper.batchUpdateMemberWithdrawAccount(id, memberId, platformEnum.getCode(), merchantId, LocalDateTime.now(), MemberTokenInfoUtil.getMemberName());
        return id;
    }

    /**
     * 預設啟用
     *
     * @param memberWithdrawAccountEnableDto dto
     * @return uid
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.MEMBER_WITHDRAW_ACCOUNT, subType = LogSubTypeConstants.UPDATE,
            success = "{{#title}} 卡号, 卡号uid:{{#memberWithdrawAccountEnableDto.id}}")
    public Long enable(MemberWithdrawAccountEnableDTO memberWithdrawAccountEnableDto) {
        if (ObjectUtil.equal(memberWithdrawAccountEnableDto.getEnableEnum(), EnableEnum.TRUE)) {
            //启用时需要判断用户下已启用的银行卡是否超出上限
            checkAccountBindLimit(memberWithdrawAccountEnableDto,memberWithdrawAccountEnableDto.getPlatformEnum());
        }

        boolean isUpdate = this.lambdaUpdate()
                .set(MemberWithdrawAccount::getDefaultUseEnum, EnableEnum.FALSE)
                .set(MemberWithdrawAccount::getEnableEnum, memberWithdrawAccountEnableDto.getEnableEnum())
                .set(CreateAndUpdateEntity::getUpdateTime, memberWithdrawAccountEnableDto.getUpdateTime())
                .set(CreateAndUpdateEntity::getUpdateBy, memberWithdrawAccountEnableDto.getAdminName())
                .eq(MemberWithdrawAccount::getId, memberWithdrawAccountEnableDto.getId())
                .eq(MemberWithdrawAccount::getMerchantId, memberWithdrawAccountEnableDto.getMerchantId())
                .update();

        if (!isUpdate) {
            throw new ApiException(CommonCode.FAILED);
        }

        LogRecordContext.putVariable("title", memberWithdrawAccountEnableDto.getEnableEnum() == EnableEnum.TRUE ? "启用" : "停用");

        return memberWithdrawAccountEnableDto.getId();
    }

    private void checkAccountBindLimit(MemberWithdrawAccountEnableDTO memberWithdrawAccountEnableDto,PlatformEnum platformEnum) {
        long count = this.count(new LambdaQueryWrapper<MemberWithdrawAccount>().eq(MemberWithdrawAccount::getMerchantId, memberWithdrawAccountEnableDto.getMerchantId())
                .eq(MemberWithdrawAccount::getMemberId, memberWithdrawAccountEnableDto.getMemberId())
                .eq(MemberWithdrawAccount::getPlatformEnum, memberWithdrawAccountEnableDto.getPlatformEnum())
                .eq(MemberWithdrawAccount::getEnableEnum, EnableEnum.TRUE));

        MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(memberWithdrawAccountEnableDto.getMerchantId(),
                CommonConstants.COMMON_DICT_KEY_WITHDRAW_BANK_ACCOUNT_BIND, memberWithdrawAccountEnableDto.getCurrencyEnum());
        MerchantConfigWithdrawBankAccountBindDTO merchantConfigWithdrawBankAccountBindDto = merchantConfig.merchantConfigListGetFirstNotNull(MerchantConfigWithdrawBankAccountBindDTO.class);
        Integer limit = getBindingLimitCount(merchantConfigWithdrawBankAccountBindDto, platformEnum);
        if (Objects.nonNull(limit) && count >= limit) {
            throw new ApiException(CommonCode.BIND_LIMIT);
        }
    }

    @Override
    public Boolean existMemberWithdrawAccount(Long merchantId, Long memberId) {
        LambdaQueryWrapper<MemberWithdrawAccount> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberWithdrawAccount::getMerchantId, merchantId)
                .eq(MemberWithdrawAccount::getMemberId, memberId);
        return super.exists(wrapper);
    }

    private boolean hasExistingCardNumber(Long merchantId, Long paymentBankId, String account) {
        LambdaQueryWrapper<MemberWithdrawAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberWithdrawAccount::getMerchantId, merchantId);
        queryWrapper.eq(MemberWithdrawAccount::getAccount, account);
        queryWrapper.eq(MemberWithdrawAccount::getPaymentBankId, paymentBankId);
        return super.exists(queryWrapper);
    }

    private boolean hasDuplicateWithdrawAccountTypeBinding(Long merchantId, Long memberId, Long paymentBankId) {
        LambdaQueryWrapper<MemberWithdrawAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberWithdrawAccount::getMemberId, memberId);
        queryWrapper.eq(MemberWithdrawAccount::getMerchantId, merchantId);
        queryWrapper.eq(MemberWithdrawAccount::getPaymentBankId, paymentBankId);
        return super.exists(queryWrapper);
    }


}
