package com.wd.lottery.module.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.BCrypt;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.*;
import com.wd.lottery.common.util.*;
import com.wd.lottery.module.cash.dto.CashMemberWalletDTO;
import com.wd.lottery.module.cash.service.CashMemberWalletService;
import com.wd.lottery.module.common.constants.CommonDictKeyEnum;
import com.wd.lottery.module.common.constants.MemberLoginLogTypeConstants;
import com.wd.lottery.module.common.dto.SelectOptionDTO;
import com.wd.lottery.module.common.service.CommonLoginLogService;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.member.constants.MemberRedisConstants;
import com.wd.lottery.module.member.constants.MemberStateEnum;
import com.wd.lottery.module.member.constants.VerificationActionEnum;
import com.wd.lottery.module.member.dto.GoogleUserDTO;
import com.wd.lottery.module.member.dto.MemberRegisterDTO;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.dto.MemberWalletAndThirdBalanceDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.entity.MemberRefer;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.service.*;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.service.*;
import com.wd.lottery.module.member.vo.MemberInfoVO;
import com.wd.lottery.module.merchant.dto.MerchantConfigWithdrawPasswordErrorLimitDTO;
import com.wd.lottery.module.merchant.entity.Merchant;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.merchant.service.MerchantService;
import com.wd.lottery.module.report.entity.ReportMemberTotal;
import com.wd.lottery.module.report.service.ReportMemberTotalService;
import com.wd.lottery.module.third.dto.ThirdUserBalanceDTO;
import com.wd.lottery.module.third.dto.ThirdUserDTO;
import com.wd.lottery.module.third.service.ThirdSiteUserInnerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 *
 * <p> Created on 2024/4/29.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class CMemberServiceImpl implements CMemberService {

    @Autowired
    private MemberService memberService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private RedisService redisService;
    @Resource
    private MemberTokenService memberTokenService;
    @Resource
    private CashMemberWalletService cashMemberWalletService;

    @Resource
    private ThirdSiteUserInnerService thirdSiteUserInnerService;

    @Resource
    private MemberInnerService memberInnerService;
    @Resource
    private ReportMemberTotalService reportMemberTotalService;
    @Resource
    private MemberReferService memberReferService;

    @Resource
    private MemberVerificationService memberVerificationService;

    @Value("${rsa.private-key: xxx}")
    private String rsaPrivateKey;

    @Autowired
    private MerchantConfigService merchantConfigService;

    @Resource
    private CommonLoginLogService commonLoginLogService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMemberInfo(MemberInfoUpdateParam param) {
        memberService.updateMemberInfo(param);
    }



    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApiResult<?> registerWithMobile(MemberMobileRegisterParam param) {

        ApiResult<?> apiResult = memberInnerService.registerWithMobile(param);
        if (!apiResult.isSuccess()) {
            return apiResult;
        }
        String token = this.memberLogin(param.getUid(), param.getMerchantId(), param.getRegisterIp(), param.getRegisterDeviceEnum(), param.getRegisterDomain());

        return ApiResult.success(token);
    }

    private Long toMobileNumber(String mobile) {
        if (StringUtils.startsWith(mobile, "+")) {
            mobile = mobile.substring(1);
        }
        return Long.parseLong(mobile);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApiResult<?> registerWithEmail(MemberEmailRegisterParam param) {

        ApiResult<?> apiResult = memberInnerService.registerWithEmail(param);
        if (!apiResult.isSuccess()) {
            return apiResult;
        }
        String token = this.memberLogin(param.getUid(), param.getMerchantId(), param.getRegisterIp(), param.getRegisterDeviceEnum(), param.getRegisterDomain());
        return ApiResult.success(token);
    }

    @Override
    public ApiResult<?> loginWithMemberName(MemberNameLoginParam param) {
        // 根据用户名查询会员
        Member member = memberService.getMemberByName(param.getMemberName(), param.getMerchantId());
        param.setLoginName(param.getMemberName());
        return doLogin(param, member);
    }


    @Override
    public ApiResult<?> loginWithMobile(MemberMobileLoginParam param) {
        // 根据手机查询会员
        String mobile = param.getMobile();
        Member member = memberService.getByMobile(toMobileNumber(mobile), param.getMerchantId());
        param.setLoginName(mobile);
        return doLogin(param, member);
    }

    @Override
    public ApiResult<?> loginWithEmail(MemberEmailLoginParam param) {
        // 根据邮箱查询会员
        Member member = memberService.getByEmail(param.getEmail(), param.getMerchantId());
        param.setLoginName(param.getEmail());
        return doLogin(param, member);
    }

    @Override
    public ApiResult<MemberRegisterDTO> initRegister(Long referCode, Long merchantId) {
        String ipAddress = RequestUtil.getRequestIpFromRequest();
        // 每次调用接口记录ip，同一ip多次调用则需要验证码
        boolean showVerificationCode = isShowRegisterVerificationCode(ipAddress);

        MemberRegisterDTO registerDTO = new MemberRegisterDTO();
        // 查询商户信息
        Merchant merchant = merchantService.getById(merchantId);
        if (Objects.isNull(merchant) || EnableEnum.FALSE == merchant.getEnableEnum()) {
            throw new ApiException(CommonCode.MERCHANT_ILLEGAL_STATUS);
        }

        List<SelectOptionDTO> currencies = new ArrayList<>();
        String currenciesJson = merchant.getCurrencyEnumListJson();

        if (Objects.nonNull(referCode)) {
            // 查询会员信息
            Member member = memberService.getByReferCode(referCode, merchant.getId());
            if (Objects.isNull(member) || MemberStateEnum.DISABLE == member.getMemberStateEnum()) {
                throw new ApiException(CommonCode.MEMBER_INVALID_REFER_CODE);
            }

            CurrencyEnum currencyEnum = member.getCurrencyEnum();
            currencies.add(new SelectOptionDTO(currencyEnum.name(), String.valueOf(currencyEnum.getCode())));
        } else {
            List<String> list = JSONUtil.toBean(currenciesJson, new TypeReference<List<String>>() {
            }, false);
            list.forEach(i -> {
                CurrencyEnum currencyEnum = CurrencyEnum.valueOf(i);
                currencies.add(new SelectOptionDTO(currencyEnum.name(), String.valueOf(currencyEnum.getCode())));
            });
        }
        // fill regions
        List<SelectOptionDTO> regions = parseMerchantRegions(merchant);

        registerDTO.setShowVerifyCode(showVerificationCode);
        registerDTO.setCurrencies(currencies);
        registerDTO.setRegions(regions);

        return ApiResult.success(registerDTO);
    }

    @Override
    public void changePasswd(CMemberChangePasswdParam param) {
        Member member = memberService.getMemberById(param.getMemberId(), param.getMerchantId());
        String old = decodePassword(param.getOldPassword());
        boolean isMatch = BCrypt.checkpw(old, member.getPasswd());
        if(!isMatch){
            throw new ApiException(CommonCode.MEMBER_ILLEGAL_ACCOUNT);
        }
        String newPassword = BCrypt.hashpw(decodePassword(param.getNewPassword()));

        memberService.updatePasswd(newPassword, member.getId(), member.getMerchantId());
        memberService.clearMemberTokenInfo(member.getToken());
        //save log
        commonLoginLogService.saveCommonLoginLog(member,MemberLoginLogTypeConstants.MEMBER_CHANGE_PASSWD , "");

    }

    @Override
    public void logout(Long memberId, Long merchantId) {
        Member member = memberService.getMemberById(memberId, merchantId);
        if (Objects.isNull(member)) {
            return;
        }
        memberService.clearMemberTokenInfo(member.getToken());
        //save log
        commonLoginLogService.saveCommonLoginLog(member,MemberLoginLogTypeConstants.MEMBER_LOGIN_OUT , "");
    }

    @Override
    public MemberInfoVO getMemberInfo(Long memberId, Long merchantId) {
        Member member = memberService.getMemberByIdNotCache(memberId, merchantId);
        if (Objects.isNull(member)) {
            throw new ApiException(CommonCode.MEMBER_NOT_EXIST);
        }
        MemberInfoVO memberInfoVO = new MemberInfoVO();
        if (StringUtils.isBlank(member.getWithdrawPasswd())) {
            memberInfoVO.setIsSetWithdrawPassword(BooleanEnum.FALSE);
        } else {
            memberInfoVO.setIsSetWithdrawPassword(BooleanEnum.TRUE);
        }
        BeanUtils.copyProperties(member, memberInfoVO);
        Long mobile = member.getMobile();
        if (Objects.nonNull(mobile)) {
            memberInfoVO.setMobile(String.valueOf(mobile));
        }

        // 获取邀请码
        MemberRefer memberRefer = memberReferService.getByMemberId(memberId, merchantId);
        memberInfoVO.setReferCode(memberRefer.getReferCode());

        // 获取渠道广告资讯
        MemberRefer upperMemberRefer = memberReferService.getByMemberId(member.getDirectHighMemberId(), merchantId);
        if (Objects.nonNull(upperMemberRefer)) {
            memberInfoVO.setAdvertiserEnum(upperMemberRefer.getAdvertiserEnum());
            memberInfoVO.setApiKey(upperMemberRefer.getApiKey());
            memberInfoVO.setMode(upperMemberRefer.getMode());
        } else {
            memberInfoVO.setAdvertiserEnum(memberRefer.getAdvertiserEnum());
            memberInfoVO.setApiKey(memberRefer.getApiKey());
            memberInfoVO.setMode(memberRefer.getMode());
        }

        return memberInfoVO;
    }

    @Override
    public String memberLogin(Long id, Long merchantId, BigInteger requestIp, DeviceEnum deviceEnum, String domain) {
        Assert.notNull(id, "id not null");
        Assert.notNull(merchantId, "merchant not null");
        Member member = memberService.getLoginMemberByIdAndMerchantId(id, merchantId);

        MemberTokenInfoDTO memberTokenInfoDTO = BeanUtil.copyProperties(member, MemberTokenInfoDTO.class);

        memberService.clearMemberTokenInfo(member.getToken());

        String token = IdWorker.get32UUID();

        member.setToken(token);
        member.setLoginIp(requestIp);
        member.setLoginUrl(domain);
        memberService.updateToken(member);

        memberTokenInfoDTO.setToken(token);
        memberTokenService.refreshRedisMemberTokenInfoDTO(memberTokenInfoDTO);

        //save log
        commonLoginLogService.saveCommonLoginLog(member,MemberLoginLogTypeConstants.MEMBER_LOGIN_SUCCEED , "");
        return token;
    }

    private boolean isShowRegisterVerificationCode(String ipAddress) {
        // todo 缓存ip， 相同ip多次注册则要求验证码
        return false;
    }

    private List<SelectOptionDTO> parseMerchantRegions(Merchant merchant) {

        List<SelectOptionDTO> regions = new ArrayList<>();

        List<String> countryCodes = CountryRegionUtil.countryCodes();
        // todo filter merchant regions
        countryCodes.forEach(code -> {
            regions.add(new SelectOptionDTO(code, CountryRegionUtil.getRegion(code)));
        });
        return regions;
    }

    private String decodePassword(String passwd) {
        // rsa 解码
        return RSAUtil.decode(passwd, rsaPrivateKey);
    }

    private ApiResult<?> doLogin(MemberLoginParam param, Member member) {
        if (Objects.isNull(member) || MemberStateEnum.DISABLE == member.getMemberStateEnum()) {
            if(Objects.nonNull(member)){
                commonLoginLogService.saveCommonLoginLog(member, MemberLoginLogTypeConstants.MEMBER_LOGIN_FAIL , "账号被禁用");
            }
            return ApiResult.failed(CommonCode.MEMBER_ILLEGAL_ACCOUNT);
        }
        checkLoginVerificationCode(param, member);

        checkMemberPasswd(param.getPasswd(), member);
        // login
        String token = this.memberLogin(member.getId(), param.getMerchantId(), param.getRequestIp(), param.getDeviceEnum(), param.getDomain());
        memberVerificationService.resetCountOfAction(param.getRequestIp(), VerificationActionEnum.LOGIN);
        return ApiResult.success(token);
    }

    @Override
    // todo 密码多次错误要禁用账号
    public void checkMemberPasswd(String rsaPasswd, Member member) {
        String password = decodePassword(rsaPasswd);
        if (!BCrypt.checkpw(password, member.getPasswd())) {
            commonLoginLogService.saveCommonLoginLog(member,MemberLoginLogTypeConstants.MEMBER_LOGIN_FAIL , "密码错误");
            throw new ApiException(CommonCode.MEMBER_ILLEGAL_ACCOUNT);
        }
    }

    @Override
    public void checkMemberWithdrawPasswd(String rsaPasswd, Member member) {
        if (StringUtils.isBlank(member.getWithdrawPasswd())) {
            throw new ApiException(CommonCode.MEMBER_CASH_WITHDRAW_PASSWD_NOT_SET);
        }
        String password = decodePassword(rsaPasswd);
        if (!BCrypt.checkpw(password, member.getWithdrawPasswd())) {
            commonLoginLogService.saveCommonLoginLog(member,MemberLoginLogTypeConstants.MEMBER_WITHDRAW_PASSWD_CHECK , "提现密码错误");
            //错误次数+1,总次数超限,则禁用当前用户
            increaseErrorNumAndDisable(member);
        }
    }

    private void increaseErrorNumAndDisable(Member member) {
        String key = String.format(MemberRedisConstants.MEMBER_WITHDRAW_PASSWD_ERROR_NUM, LocalDate.now(),member.getId());
        MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(member.getMerchantId(),
                CommonDictKeyEnum.WITHDRAW_PASSWORD_ERROR_LIMIT.name(), member.getCurrencyEnum());
        if (Objects.isNull(merchantConfig)) {
            return;
        }
        List<MerchantConfigWithdrawPasswordErrorLimitDTO> merchantConfigWithdrawPasswordErrorLimitDTOS = merchantConfig.merchantConfigToList(MerchantConfigWithdrawPasswordErrorLimitDTO.class);
        if (CollectionUtils.isEmpty(merchantConfigWithdrawPasswordErrorLimitDTOS)) {
            return;
        }
        Long errorLimit = Long.parseLong(merchantConfigWithdrawPasswordErrorLimitDTOS.get(0).getErrorLimit());
        if (errorLimit <= 0) {
            //配置次数<=0表示不限制
            return;
        }
        Long incr = redisService.incr(key, Constants.ONE_LONG);
        //计算当前时间到当天23.59.59的秒数
        long secondsUntilMidnight = DateUtil.between(DateUtil.date(), DateUtil.endOfDay(DateUtil.date()), DateUnit.SECOND);
        redisService.expire(key, secondsUntilMidnight ,TimeUnit.SECONDS);
        if (Objects.nonNull(incr) && incr >= (errorLimit-1)) {
            //禁用用户
            memberService.disableMemberByWithdrawErrorLimit(member);

            //禁用用户后返回消息
            // 提现密码输入次数达到风控值，账户被停用
            throw new ApiException(CommonCode.MEMBER_CASH_WITHDRAW_PASSWD_ERROR_COUNT_LIMIT);
        } else {
            //提现密码错误,但次数未超出,提示 提现密码错误，累计输入错误达到X次将会被封禁
            throw new ApiException(CommonCode.MEMBER_CASH_WITHDRAW_PASSWD_ERROR);
        }
    }

    private void checkLoginVerificationCode(MemberLoginParam loginParam, Member member) {
        MemberVerificationParam memberVerificationParam = new MemberVerificationParam();
        memberVerificationParam.setVerificationCode(loginParam.getVerificationCode());
        memberVerificationParam.setRequestIp(loginParam.getRequestIp());
        memberVerificationParam.setDomain(loginParam.getDomain());
        memberVerificationParam.setVerificationActionEnum(VerificationActionEnum.LOGIN);
        memberVerificationService.verify(memberVerificationParam);
    }

    @Override
    public MemberWalletAndThirdBalanceDTO getBalance(Long memberId, Long merchantId) {
        MemberWalletAndThirdBalanceDTO balanceDTO = new MemberWalletAndThirdBalanceDTO();
        // 钱包余额
        CashMemberWalletDTO walletDTO = cashMemberWalletService.getByMemberId(merchantId, memberId);
        // 充值总金额
        ReportMemberTotal depositTotal = reportMemberTotalService.getByMemberIdAndMerchantId(memberId, merchantId);
        // 三方余额
        List<ThirdUserDTO> thirdUsers = thirdSiteUserInnerService.listActiveThirdUser(memberId, merchantId);
        List<ThirdUserBalanceDTO> thirdUserBalanceDTOS = thirdSiteUserInnerService.listThirdUserBalance(thirdUsers);

        balanceDTO.setWallet(walletDTO.getCashBalance());
        balanceDTO.setThirdBalances(thirdUserBalanceDTOS);
        if (Objects.isNull(depositTotal)) {
            balanceDTO.setTotalDepositAmount(0L);
        }else {
            balanceDTO.setTotalDepositAmount(depositTotal.getDepositAmount());
        }

        balanceDTO.setMerchantId(merchantId);
        balanceDTO.setUid(memberId);

        balanceDTO.calculateTotal();

        return balanceDTO;

    }

    @Override
    public List<ThirdUserBalanceDTO> getSingleBalance(Long memberId, Long merchantId, String platformCode) {
        ThirdUserDTO thirdSiteUser = thirdSiteUserInnerService.getThirdSiteUser(platformCode, merchantId, memberId);
        List<ThirdUserDTO> thirdUserDTOS = Collections.singletonList(thirdSiteUser);
        thirdSiteUserInnerService.fillPlatformStatus(thirdUserDTOS);
        return thirdSiteUserInnerService.listThirdUserBalance(thirdUserDTOS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setWithdrawPwd(CMemberWithdrawPasswordParam param) {
        Boolean flag = checkMerchantWithdrawPwdConfig(param.getMerchantId(),param.getCurrencyEnum(),CommonDictKeyEnum.SWITCH_WITHDRAW_PASSWORD_VERIFICATION);
        if (!flag) {
            throw new ApiException(CommonCode.MEMBER_CASH_WITHDRAW_PASSWD_SET_SWITCH_CLOSE);
        }
        Member member = memberService.getMemberById(param.getMemberId(), param.getMerchantId());
        checkMemberPasswd(param.getLoginPassword(), member);
        if (StringUtils.isNotBlank(member.getWithdrawPasswd())) {
            throw new ApiException(CommonCode.MEMBER_CASH_WITHDRAW_PASSWD_SET_EXIST);
        }
        String passwd = decodePassword(param.getNewPassword());
        final int minLen = 6;
        if (passwd.length() < minLen) {
            throw new ApiException(CommonCode.MEMBER_CASH_WITHDRAW_PASSWD_LENGTH_ERROR);
        }
        String newPassword = BCrypt.hashpw(passwd);
        memberService.setWithdrawPasswd(newPassword, param);
        commonLoginLogService.saveCommonLoginLog(member,MemberLoginLogTypeConstants.MEMBER_SET_WITHDRAW_PASSWD , "");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeWithdrawPwd(CMemberWithdrawPasswordParam param) {
        Member member = memberService.getMemberById(param.getMemberId(), param.getMerchantId());
        Boolean flag = checkMerchantWithdrawPwdConfig(param.getMerchantId(),param.getCurrencyEnum(),CommonDictKeyEnum.SWITCH_WITHDRAW_PASSWORD_VERIFICATION);
        if (!flag) {
            throw new ApiException(CommonCode.MEMBER_CASH_WITHDRAW_PASSWD_SET_SWITCH_CLOSE);
        }
        String old = decodePassword(param.getOldPassword());
        boolean isMatch = BCrypt.checkpw(old, member.getWithdrawPasswd());
        if(!isMatch){
            throw new ApiException(CommonCode.MEMBER_ILLEGAL_ACCOUNT);
        }
        String newPassword = BCrypt.hashpw(decodePassword(param.getNewPassword()));
        memberService.setWithdrawPasswd(newPassword, param);
        commonLoginLogService.saveCommonLoginLog(member,MemberLoginLogTypeConstants.MEMBER_CHANGE_WITHDRAW_PASSWD , "");
    }

    public Boolean checkMerchantWithdrawPwdConfig(Long merchantId,CurrencyEnum currencyEnum, CommonDictKeyEnum commonDictKeyEnum) {
        MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, commonDictKeyEnum.name(), currencyEnum);
        if (Objects.isNull(merchantConfig) || ObjectUtil.equal(merchantConfig.getEnableEnum(), EnableEnum.FALSE)) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public ApiResult googleRegisterOrLogin(GoogleLoginInfoParam param, GoogleUserDTO googleUserDTO) {
        //查询此设备码是否已注册过
        Member member = memberService.getOne(new LambdaQueryWrapper<Member>().eq(Member::getMerchantId, param.getMerchantId())
                .eq(Member::getMemberName, googleUserDTO.getEmail()));
        if (Objects.isNull(member)) {
            //未注册过,调用注册
            ApiResult apiResult = memberInnerService.googleRegister(param,googleUserDTO);
            if (!apiResult.isSuccess()) {
                return apiResult;
            }
            String token = this.memberLogin(param.getMemberId(), param.getMerchantId(), param.getRegisterIp(), param.getRegisterDeviceEnum(), param.getRegisterDomain());
            return ApiResult.success(token);
        } else {
            //已注册过
            return googleLogin(member,param);
        }
    }

    private ApiResult googleLogin(Member member, GoogleLoginInfoParam param) {
        if (MemberStateEnum.DISABLE == member.getMemberStateEnum()) {
            return ApiResult.failed(CommonCode.MEMBER_ILLEGAL_ACCOUNT);
        }
        String token = this.memberLogin(member.getId(), param.getMerchantId(), param.getRegisterIp(), param.getRegisterDeviceEnum(), param.getRegisterDomain());
        return ApiResult.success(token);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<?> quickRegisterOrLogin(MemberQuickRegisterOrLoginParam param) {
        //先检查快速注册/登录的商户开关
        checkQuickRegisterConfig(param.getMerchantId(),param.getCurrencyEnum());
        //校验设备码
        checkDeviceCode(param);
        //查询此设备码是否已注册过
        Member member = memberService.getOne(new LambdaQueryWrapper<Member>().eq(Member::getMerchantId, param.getMerchantId())
                .eq(Member::getDeviceCode, param.getDeviceCode()));
        if (Objects.nonNull(member)) {
            //已注册过,调用登录
            return quickLogin(param, member);
        } else {
            //未注册过,调用注册
            ApiResult<?> apiResult = memberInnerService.quickRegister(param);
            if (!apiResult.isSuccess()) {
                return apiResult;
            }
            String token = this.memberLogin(param.getUid(), param.getMerchantId(), param.getRegisterIp(), param.getRegisterDeviceEnum(), param.getRegisterDomain());
            return ApiResult.success(token);
        }
    }

    private void checkDeviceCode(MemberQuickRegisterOrLoginParam param) {
        try {
            String deviceCode = RSAUtil.decode(param.getDeviceCode(), rsaPrivateKey);
            log.info("RSAUtil.decode...{}",deviceCode);
            log.info("Md5Util.getMd5HexLowerCase...{}", Md5Util.getMd5HexLowerCase(deviceCode));
            param.setDeviceCode(Md5Util.getMd5HexLowerCase(deviceCode));
            param.setRegisterDeviceCode(Md5Util.getMd5HexLowerCase(deviceCode));
        }catch (Exception e) {
            throw new ApiException(CommonCode.MEMBER_FAST_REGISTER_DEVICE_CODE_ERROR);
        }
    }

    private void checkQuickRegisterConfig(Long merchantId,CurrencyEnum currencyEnum) {
        MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonDictKeyEnum.SWITCH_QUICK_REGISTER.name(), currencyEnum);
        if (Objects.isNull(merchantConfig) || ObjectUtil.equal(merchantConfig.getEnableEnum(),EnableEnum.FALSE)) {
            log.error("quickRegister/login...config..is..close, merchantId:{}, currencyEnum:{}", merchantId, currencyEnum.name());
            throw new ApiException(CommonCode.MEMBER_FAST_REGISTER_LOGIN_SWITCH_CLOSE);
        }
    }

    private ApiResult<?> quickLogin(MemberQuickRegisterOrLoginParam param, Member member) {
        if (MemberStateEnum.DISABLE == member.getMemberStateEnum()) {
            return ApiResult.failed(CommonCode.MEMBER_ILLEGAL_ACCOUNT);
        }
        String token = this.memberLogin(member.getId(), param.getMerchantId(), param.getRegisterIp(), param.getRegisterDeviceEnum(), param.getRegisterDomain());
        return ApiResult.success(token);
    }

    @Override
    public Boolean bindMobileAndSetPwd(MemberQuickRegisterBindMobileParam param) {
        // check area code earlier
        Integer area = CountryRegionUtil.getIntegerRegion(param.getAreaCode());
        checkQuickRegisterConfig(param.getMerchantId(),param.getCurrencyEnum());
        String mobile = param.getMobile();
        PhoneNumberValidateUtil.isValidPhoneNumber(mobile, param.getAreaCode());
        Long mobileNumber = toMobileNumber(mobile);

        // 查询手机号是否存在
        boolean exists = memberService.existsMobile(mobileNumber, param.getMerchantId());
        if (exists) {
            throw new ApiException(CommonCode.MEMBER_MOBILE_ALREADY_BIND);
        }
        String passwd = decodePassword(param.getPwd());
        final int minLen = 8;
        if (passwd.length() < minLen) {
            throw new ApiException(CommonCode.MEMBER_PASSWD_TOO_SHORT);
        }
        String newPassword = BCrypt.hashpw(passwd);
        return memberService.lambdaUpdate().set(Member::getPasswd, newPassword)
                .set(Member::getUpdateTime, LocalDateTime.now())
                .set(Member::getMobile, mobileNumber)
                .set(Member::getAreaCode, area)
                .eq(Member::getId, param.getMemberId())
                .eq(Member::getMerchantId, param.getMerchantId()).update();

    }
}
