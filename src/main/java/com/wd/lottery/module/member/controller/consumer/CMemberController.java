package com.wd.lottery.module.member.controller.consumer;

import cn.hutool.core.lang.Assert;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.CountryRegionUtil;
import com.wd.lottery.common.util.MemberTokenInfoUtil;
import com.wd.lottery.common.util.RequestUtil;
import com.wd.lottery.component.auth.ApiNotAuth;
import com.wd.lottery.module.common.constants.CommonConstants;
import com.wd.lottery.module.member.constants.MemberConstants;
import com.wd.lottery.module.member.constants.MemberRedisConstants;
import com.wd.lottery.module.member.constants.VerificationActionEnum;
import com.wd.lottery.module.member.dto.MemberRegisterDTO;
import com.wd.lottery.module.member.dto.MemberWalletAndThirdBalanceDTO;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.service.CMemberService;
import com.wd.lottery.module.member.service.MemberVerificationService;
import com.wd.lottery.module.member.vo.MemberInfoVO;
import com.wd.lottery.module.member.vo.MemberVerificationVo;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.merchant.service.MerchantSmsService;
import com.wd.lottery.module.third.dto.ThirdUserBalanceDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 用户 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
@Slf4j
@RestController
@RequestMapping("${consumer-path}/${module-path.member}/member")
@Tag(name = "会员模块（C端）")
public class CMemberController {
    @Resource
    private CMemberService cMemberService;

    @Resource
    private RedisService redisService;

    @Resource
    private MemberVerificationService memberVerificationService;

    @Resource
    private MerchantConfigService merchantConfigService;

    @Resource
    private MerchantSmsService merchantSmsService;

    @PostMapping("setWithdrawPwd")
    @Operation(summary = "设置提现密码")
    public ApiResult<?> setWithdrawPwd(@RequestBody @Valid CMemberWithdrawPasswordParam param) {
        Assert.notNull(param.getLoginPassword(),"登录密码不能为空");
        Assert.notNull(param.getNewPassword(),"提现密码不能为空");
        log.info("setWithdrawPwd, params:{}, merchantId: {}", param, MemberTokenInfoUtil.getMerchantId());
        param.setMerchantId(MemberTokenInfoUtil.getMerchantId());
        param.setMemberId(MemberTokenInfoUtil.getMemberId());
        param.setCurrencyEnum(MemberTokenInfoUtil.getCurrencyEnum());
        cMemberService.setWithdrawPwd(param);
        return ApiResult.success();

    }

    @PostMapping("changeWithdrawPwd")
    @Operation(summary = "设置提现密码")
    public ApiResult<?> changeWithdrawPwd(@RequestBody @Valid CMemberWithdrawPasswordParam param) {
        Assert.notNull(param.getOldPassword(),"旧提现密码不能为空");
        Assert.notNull(param.getNewPassword(),"新提现密码不能为空");

        log.info("changeWithdrawPwd, params:{}, merchantId: {}", param, MemberTokenInfoUtil.getMerchantId());
        param.setMerchantId(MemberTokenInfoUtil.getMerchantId());
        param.setMemberId(MemberTokenInfoUtil.getMemberId());
        param.setCurrencyEnum(MemberTokenInfoUtil.getCurrencyEnum());
        cMemberService.changeWithdrawPwd(param);
        return ApiResult.success();
    }

    @GetMapping("getInfo")
    @Operation(summary = "获取会员信息")
    public ApiResult<MemberInfoVO> getInfo() {

        MemberInfoVO memberInfo = cMemberService.getMemberInfo(MemberTokenInfoUtil.getMemberId(), MemberTokenInfoUtil.getMerchantId());
        return ApiResult.success(memberInfo);
    }

    @PostMapping("update")
    @Operation(summary = "修改会员信息")
    public ApiResult<Boolean> updateMemberInfo(@RequestBody MemberInfoUpdateParam param) {
        Long memberId = MemberTokenInfoUtil.getMemberId();
        Long merchantId = MemberTokenInfoUtil.getMerchantId();

        param.setMemberId(memberId);
        param.setMerchantId(merchantId);

        cMemberService.updateMemberInfo(param);
        return ApiResult.success(true);
    }

    @ApiNotAuth
    @PostMapping("initRegister")
    @Operation(summary = "初始化注册项")
    public ApiResult<MemberRegisterDTO> initRegister(@RequestParam(required = false) Long referCode, @NotNull @RequestHeader Long merchantId) {
        return cMemberService.initRegister(referCode, merchantId);
    }


    @ApiNotAuth
    @PostMapping("registerWithMobile")
    @Operation(summary = "会员C端注册（手机号）")
    public ApiResult<?> registerWithMobile(@RequestBody @Valid MemberMobileRegisterParam param,
                                           @NotNull @RequestHeader Long merchantId,
                                           @RequestHeader(name = Constants.SMS_CODE, required = false) String smsCode,
                                           @RequestHeader(value = MemberConstants.USER_AGENT_HEADER, defaultValue = MemberConstants.DEFAULT_USER_AGENT) String userAgent,
                                           @RequestHeader(value = MemberConstants.FACEBOOK_FBC_HEADER, required = false) String fbc,
                                           @RequestHeader(value = MemberConstants.FACEBOOK_FBP_HEADER, required = false) String fbp) {
        log.debug("register with mobile, params:{}, merchantId: {}, smsCode: {}", param, merchantId, smsCode);
        //如果开启了短信验证码，首先进行sms校验
        //用户还没登录，所以没法直接使用 @SmsVerify 来实现功能
        MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonConstants.SWITCH_SMS_VERIFICATION_CODE, param.getCurrencyEnum());
        if (Objects.equals(merchantConfig.getEnableEnum(), EnableEnum.TRUE)) {
            Integer area = CountryRegionUtil.getIntegerRegion(param.getAreaCode());
            String mobile = param.getMobile().replace("+", "");
            merchantSmsService.checkCode(merchantId, param.getCurrencyEnum(),
                    String.valueOf(area), mobile, smsCode);
        }

        param.setMerchantId(merchantId);
        param.setRegisterIp(RequestUtil.getBigIntegerIpFromRequest());
        param.setRegisterDeviceCode(RequestUtil.getRequestHeaderByHeaderName(MemberConstants.DEVICE_CODE_HEADER));
        param.setRegisterDomain(RequestUtil.getHostname());
        param.setUserAgent(userAgent);
        param.setFbc(fbc);
        param.setFbp(fbp);

        return cMemberService.registerWithMobile(param);
    }

    @ApiNotAuth
    @PostMapping("quickRegisterOrLogin")
    @Operation(summary = "会员C端快速注册/登录")
    public ApiResult<?> quickRegisterOrLogin(@RequestBody @Valid MemberQuickRegisterOrLoginParam param,
                                             @NotNull @RequestHeader Long merchantId,
                                             @RequestHeader(value = MemberConstants.USER_AGENT_HEADER, defaultValue = MemberConstants.DEFAULT_USER_AGENT) String userAgent,
                                             @RequestHeader(value = MemberConstants.FACEBOOK_FBC_HEADER, required = false) String fbc,
                                             @RequestHeader(value = MemberConstants.FACEBOOK_FBP_HEADER, required = false) String fbp) {
        log.debug("quickRegisterOrLogin, params:{}, merchantId: {}", param, merchantId);
        String quickRegisterKey = String.format(MemberRedisConstants.MEMBER_QUICK_REGISTER, param.getMerchantId(), param.getDeviceCode());
        boolean flag = redisService.setIfAbsent(quickRegisterKey, param.getDeviceCode(), MemberRedisConstants.MEMBER_QUICK_REGISTER_TIME);
        if (!flag) {
            throw new ApiException(CommonCode.FREQUENT_REQUESTS);
        }
        try {
            param.setMerchantId(merchantId);
            param.setRegisterIp(RequestUtil.getBigIntegerIpFromRequest());
            param.setRegisterDomain(RequestUtil.getHostname());
            param.setUserAgent(userAgent);
            param.setFbc(fbc);
            param.setFbp(fbp);
            return cMemberService.quickRegisterOrLogin(param);
        }catch (Exception e) {
            log.error("快速注册账户异常", e);
            return ApiResult.failed(CommonCode.MEMBER_FAST_REGISTER_LOGIN_ERROR);
        } finally {
            // 删除缓存锁
            redisService.del(quickRegisterKey);
        }
    }

    @PostMapping("bindMobileAndSetPwd")
    @Operation(summary = "会员C端快速注册/登录用户绑定手机号设置密码")
    public ApiResult<?> bindMobileAndSetPwd(@RequestBody @Valid MemberQuickRegisterBindMobileParam param) {
        log.debug("bindMobileAndSetPwd, params:{}, merchantId: {}", param, MemberTokenInfoUtil.getMerchantId());
        param.setMerchantId(MemberTokenInfoUtil.getMerchantId());
        param.setMemberId(MemberTokenInfoUtil.getMemberId());
        param.setCurrencyEnum(MemberTokenInfoUtil.getCurrencyEnum());
        return ApiResult.success(cMemberService.bindMobileAndSetPwd(param));

    }

    @ApiNotAuth
    @PostMapping("registerWithEmail")
    @Operation(summary = "会员C端注册（邮箱）")
    public ApiResult<?> registerWithEmail(@RequestBody @Valid MemberEmailRegisterParam param,
                                          @NotNull @RequestHeader Long merchantId,
                                          @RequestHeader(value = MemberConstants.USER_AGENT_HEADER, defaultValue = MemberConstants.DEFAULT_USER_AGENT) String userAgent,
                                          @RequestHeader(value = MemberConstants.FACEBOOK_FBC_HEADER, required = false) String fbc,
                                          @RequestHeader(value = MemberConstants.FACEBOOK_FBP_HEADER, required = false) String fbp) {
        log.debug("register with email, params:{}, merchantId: {},deviceCode..{}", param, merchantId,RequestUtil.getRequestHeaderByHeaderName(MemberConstants.DEVICE_CODE_HEADER));
        param.setMerchantId(merchantId);
        param.setRegisterIp(RequestUtil.getBigIntegerIpFromRequest());
        param.setRegisterDomain(RequestUtil.getHostname());
        param.setRegisterDeviceCode(RequestUtil.getRequestHeaderByHeaderName(MemberConstants.DEVICE_CODE_HEADER));
        param.setUserAgent(userAgent);
        param.setFbc(fbc);
        param.setFbp(fbp);
        return cMemberService.registerWithEmail(param);
    }

    @ApiNotAuth
    @PostMapping("login")
    @Operation(summary = "会员C端登录(用户名）")
    public ApiResult<?> login(@RequestBody @Valid MemberNameLoginParam param, @NotNull @RequestHeader Long merchantId) {
        log.debug("login with name, params:{}, merchantId: {}", param, merchantId);
        param.setMerchantId(merchantId);
        param.setRequestIp(RequestUtil.getBigIntegerIpFromRequest());
        param.setDomain(RequestUtil.getHostname());
        memberVerificationService.increaseCountOfAction(param.getRequestIp(), VerificationActionEnum.LOGIN);
        return cMemberService.loginWithMemberName(param);
    }

    @ApiNotAuth
    @PostMapping("loginWithMobile")
    @Operation(summary = "会员登录（手机号）")
    public ApiResult<?> loginWithMobile(@RequestBody @Valid MemberMobileLoginParam param, @NotNull @RequestHeader Long merchantId) {
        log.debug("login with mobile, params:{}, merchantId: {}", param, merchantId);
        param.setMerchantId(merchantId);
        param.setRequestIp(RequestUtil.getBigIntegerIpFromRequest());
        param.setDomain(RequestUtil.getHostname());
        memberVerificationService.increaseCountOfAction(param.getRequestIp(), VerificationActionEnum.LOGIN);
        return cMemberService.loginWithMobile(param);
    }

    @ApiNotAuth
    @PostMapping("loginWithEmail")
    @Operation(summary = "会员登录（邮箱）")
    public ApiResult<?> loginWithEmail(@RequestBody @Valid MemberEmailLoginParam param, @NotNull @RequestHeader Long merchantId) {
        log.debug("login with email, params:{}, merchantId: {}", param, merchantId);
        param.setMerchantId(merchantId);
        param.setRequestIp(RequestUtil.getBigIntegerIpFromRequest());
        param.setDomain(RequestUtil.getHostname());
        memberVerificationService.increaseCountOfAction(param.getRequestIp(), VerificationActionEnum.LOGIN);
        return cMemberService.loginWithEmail(param);
    }

    @PostMapping("changePasswd")
    @Operation(summary = "修改会员密码")
    public ApiResult<Void> changePasswd(@RequestBody @Valid CMemberChangePasswdParam param) {

        Long memberId = MemberTokenInfoUtil.getMemberId();
        Long merchantId = MemberTokenInfoUtil.getMerchantId();

        param.setMemberId(memberId);
        param.setMerchantId(merchantId);

        cMemberService.changePasswd(param);
        return ApiResult.success();
    }

    @PostMapping("logout")
    @Operation(summary = "会员登出")
    public ApiResult<Void> logout() {
        Long memberId = MemberTokenInfoUtil.getMemberId();
        Long merchantId = MemberTokenInfoUtil.getMerchantId();

        cMemberService.logout(memberId, merchantId);
        return ApiResult.success();
    }

    @Operation(summary = "查询会员钱包以及三方余额")
    @GetMapping("getBalance")
    public ApiResult<MemberWalletAndThirdBalanceDTO> getMemberBalance(){
        Long memberId = MemberTokenInfoUtil.getMemberId();
        Long merchantId = MemberTokenInfoUtil.getMerchantId();
        String lockKey = MemberRedisConstants.GET_BALANCE_LOCK + memberId;
        redisService.frequencyControl(lockKey, MemberRedisConstants.GET_BALANCE_LOCK_SECONDS);
        MemberWalletAndThirdBalanceDTO balance = cMemberService.getBalance(memberId, merchantId);
        redisService.del(lockKey);
        return ApiResult.success(balance);
    }

    @Operation(summary = "查询会员钱包以及单个三方余额")
    @GetMapping("getSingleBalance")
    public ApiResult<List<ThirdUserBalanceDTO>> getSingleMemberBalance(@RequestParam String platformCode){
        Long memberId = MemberTokenInfoUtil.getMemberId();
        Long merchantId = MemberTokenInfoUtil.getMerchantId();
        String lockKey = MemberRedisConstants.GET_SINGLE_BALANCE_LOCK + memberId;
        redisService.frequencyControl(lockKey, MemberRedisConstants.GET_BALANCE_LOCK_SECONDS);
        List<ThirdUserBalanceDTO> thirdUserBalanceDTOS = cMemberService.getSingleBalance(memberId, merchantId,platformCode);
        redisService.del(lockKey);
        return ApiResult.success(thirdUserBalanceDTOS);
    }

    @ApiNotAuth
    @GetMapping("getVerificationInfo")
    @Operation(summary = "获取验证相关讯息")
    public ApiResult<MemberVerificationVo> getVerificationInfo(@RequestParam(name = "type") VerificationActionEnum verificationActionEnum) {
        MemberVerificationVo memberInfo = memberVerificationService.getMemberVerificationData(RequestUtil.getBigIntegerIpFromRequest(), verificationActionEnum);
        return ApiResult.success(memberInfo);
    }
}
