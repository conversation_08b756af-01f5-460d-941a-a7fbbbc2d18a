package com.wd.lottery.module.member.service;

import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.CommonDictKeyEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.member.dto.GoogleUserDTO;
import com.wd.lottery.module.member.dto.MemberRegisterDTO;
import com.wd.lottery.module.member.dto.MemberWalletAndThirdBalanceDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.vo.MemberInfoVO;
import com.wd.lottery.module.third.dto.ThirdUserBalanceDTO;

import jakarta.validation.Valid;
import java.math.BigInteger;
import java.util.List;

/**
 * Description: C端会员服务
 *
 * <p> Created on 2024/4/29.
 *
 * <AUTHOR>
 * @version 0.1
 */
public interface CMemberService {

    void updateMemberInfo(MemberInfoUpdateParam param);

    ApiResult<?> registerWithMobile(MemberMobileRegisterParam param);

    ApiResult<?> registerWithEmail(MemberEmailRegisterParam param);

    ApiResult<?> loginWithMemberName(MemberNameLoginParam param);

    ApiResult<?> loginWithMobile(MemberMobileLoginParam param);

    ApiResult<?> loginWithEmail(MemberEmailLoginParam param);

    ApiResult<MemberRegisterDTO> initRegister(Long referCode, Long merchantId);

    void changePasswd(CMemberChangePasswdParam param);

    /**
     * This method is used to log out a member.
     *
     * @param memberId The ID of the member to be logged out.
     * @param merchantId The ID of the merchant to which the member belongs.
     */
    void logout(Long memberId, Long merchantId);

    /**
     * This method is used to get the information of a member.
     *
     * @param memberId The ID of the member.
     * @param merchantId The ID of the merchant to which the member belongs.
     * @return The information of the member.
     */
    MemberInfoVO getMemberInfo(Long memberId, Long merchantId);

    String memberLogin(Long id, Long merchantId, BigInteger requestIp, DeviceEnum deviceEnum, String domain);

    void checkMemberPasswd(String rsaPasswd, Member member);
    void checkMemberWithdrawPasswd(String rsaPasswd, Member member);

    MemberWalletAndThirdBalanceDTO getBalance(Long memberId, Long merchantId);

    ApiResult<?> quickRegisterOrLogin(MemberQuickRegisterOrLoginParam param);

    Boolean bindMobileAndSetPwd(MemberQuickRegisterBindMobileParam param);

    List<ThirdUserBalanceDTO> getSingleBalance(Long memberId, Long merchantId, String platformCode);

    void setWithdrawPwd(@Valid CMemberWithdrawPasswordParam param);

    void changeWithdrawPwd(@Valid CMemberWithdrawPasswordParam param);

    Boolean checkMerchantWithdrawPwdConfig(Long merchantId, CurrencyEnum currencyEnum, CommonDictKeyEnum commonDictKeyEnum);

    ApiResult googleRegisterOrLogin(GoogleLoginInfoParam param, GoogleUserDTO googleUserDTO);
}
