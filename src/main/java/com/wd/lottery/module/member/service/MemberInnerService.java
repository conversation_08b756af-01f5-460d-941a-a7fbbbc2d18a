package com.wd.lottery.module.member.service;

import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.module.member.dto.GoogleUserDTO;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.param.*;

/**
 * Description: member inner service
 *
 * <p> Created on 2024/7/10.
 *
 * <AUTHOR>
 * @version 0.1
 */
public interface MemberInnerService {

    ApiResult<?> registerWithMobile(MemberMobileRegisterParam param);

    ApiResult<?> registerWithEmail(MemberEmailRegisterParam param);

    void changeMemberPasswd(BMemberChangePasswdParam param);

    ApiResult<?> quickRegister(MemberQuickRegisterOrLoginParam param);

    ApiResult<?> googleRegister(GoogleLoginInfoParam param, GoogleUserDTO googleUserDTO);
}
