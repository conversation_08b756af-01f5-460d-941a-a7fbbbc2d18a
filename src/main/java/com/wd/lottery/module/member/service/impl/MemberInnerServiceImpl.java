package com.wd.lottery.module.member.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.*;
import com.wd.lottery.common.util.*;
import com.wd.lottery.module.activity.service.ActivityVipMemberService;
import com.wd.lottery.module.cash.service.CashAuditService;
import com.wd.lottery.module.common.constants.CommonDictKeyEnum;
import com.wd.lottery.module.common.constants.MemberLoginLogTypeConstants;
import com.wd.lottery.module.common.service.CommonLoginLogService;
import com.wd.lottery.module.common.service.CommonMessageMemberService;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.member.constants.InviteTypeEnum;
import com.wd.lottery.module.member.constants.MemberConstants;
import com.wd.lottery.module.member.constants.MemberRedisConstants;
import com.wd.lottery.module.member.dto.GoogleUserDTO;
import com.wd.lottery.module.member.dto.MemberDailyRegisterIpLimitDTO;
import com.wd.lottery.module.member.dto.MemberInviteRegisterDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.entity.MemberGroup;
import com.wd.lottery.module.member.entity.MemberRefer;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.service.*;
import com.wd.lottery.module.member.param.*;
import com.wd.lottery.module.member.service.*;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.wd.lottery.module.common.constants.CommonDictKeyEnum.COMMON_DAILY_REGISTER_IP_LIMIT;

/**
 * Description: member inner service impl
 *
 * <p> Created on 2024/7/10.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class MemberInnerServiceImpl implements MemberInnerService {

    private final MemberService memberService;
    private final MemberProxyService memberProxyService;
    private final MemberReferService memberReferService;
    private final MemberGroupService memberGroupService;
    private final CashAuditService cashAuditService;
    private final CommonMessageMemberService commonMessageMemberService;
    private final ActivityVipMemberService activityVipMemberService;
    private final MerchantConfigService merchantConfigService;
    private final StringRedisTemplate redisTemplate;
    @Resource
    private AmqpTemplate amqpTemplate;
    @Autowired
    private CommonLoginLogService commonLoginLogService;


    @Value("${rsa.private-key: xxx}")
    private String rsaPrivateKey;

    public MemberInnerServiceImpl(MemberService memberService,
                                  MemberProxyService memberProxyService,
                                  MemberReferService memberReferService,
                                  MemberGroupService memberGroupService,
                                  CashAuditService cashAuditService,
                                  CommonMessageMemberService commonMessageMemberService,
                                  ActivityVipMemberService activityVipMemberService,
                                  MerchantConfigService merchantConfigService,
                                  StringRedisTemplate redisTemplate) {
        this.memberService = memberService;
        this.memberProxyService = memberProxyService;
        this.memberReferService = memberReferService;
        this.memberGroupService = memberGroupService;
        this.cashAuditService = cashAuditService;
        this.commonMessageMemberService = commonMessageMemberService;
        this.activityVipMemberService = activityVipMemberService;
        this.merchantConfigService = merchantConfigService;
        this.redisTemplate = redisTemplate;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApiResult<?> registerWithMobile(MemberMobileRegisterParam param) {
        Integer area = CountryRegionUtil.getIntegerRegion(param.getAreaCode());

        checkMerchantRegisterConfig(param, CommonDictKeyEnum.SWITCH_MOBILE_REGISTER);
        // validate mobile
        String mobile = param.getMobile();
        PhoneNumberValidateUtil.isValidPhoneNumber(mobile, param.getAreaCode());
        Long mobileNumber = toMobileNumber(mobile);

        // 查询手机号是否存在
        boolean exists = memberService.existsMobile(mobileNumber, param.getMerchantId());
        if (exists) {
            return ApiResult.failed(CommonCode.MEMBER_EXIST);
        }

        Member member = buildRegisterMember(param);
        member.setMobile(mobileNumber);
        member.setAreaCode(area);
        member.setMemberName(mobileNumber + "");

        ApiResult<?> apiResult = doRegister(member);
        // 回传uid
        param.setUid(member.getId());
        return apiResult;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApiResult<?> registerWithEmail(MemberEmailRegisterParam param) {
        checkMerchantRegisterConfig(param, CommonDictKeyEnum.SWITCH_EMAIL_REGISTER);
        // 邮箱查会员信息
        boolean exists = memberService.existsEmail(param.getEmail(), param.getMerchantId());
        if (exists) {
            return ApiResult.failed(CommonCode.MEMBER_EXIST);
        }

        Member member = buildRegisterMember(param);
        member.setEmail(param.getEmail());
        member.setMemberName(param.getEmail());

        ApiResult<?> apiResult = doRegister(member);
        // 回传uid
        param.setUid(member.getId());
        return apiResult;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeMemberPasswd(BMemberChangePasswdParam param) {
        Member member = memberService.getMemberById(param.getMemberId(), param.getMerchantId());
        if (Objects.isNull(member)) {
            throw new ApiException(CommonCode.MEMBER_NOT_EXIST);
        }
        String decode = RSAUtil.decode(param.getNewPassword(), rsaPrivateKey);
        String hashpw = BCrypt.hashpw(decode);

        memberService.lambdaUpdate().set(Member::getPasswd, hashpw)
                .set(Member::getUpdateTime, LocalDateTime.now())
                .eq(Member::getMerchantId, param.getMerchantId())
                .eq(Member::getId, param.getMemberId())
                .update();
        memberService.clearMemberTokenInfo(member.getToken());
        //save log
        String extString = AdminTokenInfoUtil.getAdminName();
        commonLoginLogService.saveCommonLoginLog(member, MemberLoginLogTypeConstants.MEMBER_UPDATE_PASSWD , extString);
    }

    private ApiResult<?> doRegister(Member member) {
        //验证IP限制
        ApiResult<Void> checkRegisterIpLimit = checkRegisterIpLimit(member.getMerchantId(), member.getRegisterIp(), member.getCurrencyEnum());
        if (!checkRegisterIpLimit.isSuccess()) {
            return checkRegisterIpLimit;
        }

        // 保存玩家
        memberService.createBaseData(member);

        createExternalData(member);
        //save log
        String extString = member.getDirectHighMemberId()+"";
        commonLoginLogService.saveCommonLoginLog(member,MemberLoginLogTypeConstants.MEMBER_REGISTER , extString);

        return ApiResult.success();
    }

    private ApiResult<Void> checkRegisterIpLimit(Long merchantId, BigInteger ip, CurrencyEnum currencyEnum) {
        MemberDailyRegisterIpLimitDTO registerIpLimitDTO = merchantConfigService.getFirstByMerchantIdAndDictKey(merchantId, COMMON_DAILY_REGISTER_IP_LIMIT.name(),
                currencyEnum, MemberDailyRegisterIpLimitDTO.class);

        if (registerIpLimitDTO == null) {
            return ApiResult.success();
        }

        Long registerIpLimit = registerIpLimitDTO.getRegisterIpLimit();
        if (registerIpLimit == null || registerIpLimit == 0L) {
            return ApiResult.success();
        }

        String key = String.format(MemberRedisConstants.MEMBER_REGISTER_LIMIT_IP, merchantId, currencyEnum.getCode(), DateUtil.format(new Date(), "yyyyMMdd"), ip.toString());
        Long increment = redisTemplate.opsForValue().increment(key, 1);
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
        log.debug("checkRegisterIpLimit key: {}, increment: {}, registerIpLimit: {}", key, increment, registerIpLimit);
        if (increment > registerIpLimit) {
            return ApiResult.failed(CommonCode.MEMBER_REGISTER_SAME_IP_FAIL);
        }
        return ApiResult.success();
    }

    private void createExternalData(Member member) {
        // 保存VIP等级
        activityVipMemberService.saveByMember(member);

        // 保存玩家代理
        memberProxyService.saveByMember(member);

        // 保存推广链接
        memberReferService.saveByMember(member);

        // 初始化打码量
        cashAuditService.saveByMember(member);

        // 发送
        commonMessageMemberService.sendRegisterMessageByMember(member);

        //发送注册成功用户到mq
        sendRegisterMember(member);
    }

    private void sendRegisterMember(Member member) {

        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 事务提交后的逻辑
                    MemberInviteRegisterDTO memberInviteRegisterDTO = new MemberInviteRegisterDTO();
                    BeanUtils.copyProperties(member,memberInviteRegisterDTO);
                    // 处理读写超时异常
                    final int retry = 0;
                    MessagePostProcessor messagePostProcessor = msg -> {
                        msg.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                        msg.getMessageProperties().setHeader("replayCount", retry);
                        // 延迟时间
                        long time = Duration.ofSeconds(MemberConstants.MEMBER_INVITE_ROULETTE_REGISTER_DELIVER_TIME_OUT).toMillis();
                        msg.getMessageProperties().setHeader("x-delay", (int) time);
                        return msg;
                    };
                    amqpTemplate.convertAndSend(RabbitMQConstants.MEMBER_INVITE_REGISTER_EXCHANGE, RabbitMQConstants.MEMBER_INVITE_ROULETTE_REGISTER_ROUTE_KEY, JSONUtil.toJsonStr(memberInviteRegisterDTO), messagePostProcessor);
                }
            });
        }
    }

    private Long toMobileNumber(String mobile) {
        if (StringUtils.startsWith(mobile, "+")) {
            mobile = mobile.substring(1);
        }
        return Long.parseLong(mobile);
    }

    @Override
    public ApiResult<?> quickRegister(MemberQuickRegisterOrLoginParam param) {
        Member member = buildQuickRegisterMember(param);
        ApiResult<?> apiResult = doRegister(member);
        param.setUid(member.getId());
        return apiResult;
    }

    @Override
    public ApiResult<?> googleRegister(GoogleLoginInfoParam param, GoogleUserDTO googleUserDTO) {
        Member member = buildGoogleRegisterMember(param,googleUserDTO);
        ApiResult<?> apiResult = doRegister(member);
        param.setMemberId(member.getId());
        return apiResult;
    }

    private Member buildGoogleRegisterMember(GoogleLoginInfoParam param, GoogleUserDTO googleUserDTO) {
        Member member = new Member();
        member.setNickName(generateMemberNickName());
        member.setMerchantId(param.getMerchantId());
        member.setCurrencyEnum(param.getCurrencyEnum());
        member.setMemberName(googleUserDTO.getEmail());
        member.setEmail(googleUserDTO.getEmail());
        member.setRegisterDomain(RequestUtil.getHostname());
        member.setRegisterIp(param.getRegisterIp());
        member.setRegisterDomain(param.getRegisterDomain());
        member.setRegisterDeviceEnum(param.getRegisterDeviceEnum());
        member.setRegisterDeviceCode(param.getRegisterDeviceCode());
        member.setRegisterType(MemberConstants.GOOGLE_REGISTER_TYPE);
        member.setRegisterReferCode(param.getInviteCode());
        member.setFbc(param.getFbc());
        member.setFbp(param.getFbp());
        member.setUserAgent(param.getUserAgent());
        if (Objects.isNull(param.getInviteTypeEnum())) {
            member.setInviteTypeEnum(InviteTypeEnum.OFFICIAL);
        } else {
            member.setInviteTypeEnum(param.getInviteTypeEnum());
        }

        Member upperMember = getInviteMember(member);
        fillHighMemberInfoAndSyncHighMemberCurrencyEnum(member, upperMember);
        fillMemberGroup(member, upperMember);
        fillChannelInfo(member, upperMember);
        return member;
    }

    private Member buildQuickRegisterMember(MemberQuickRegisterOrLoginParam param) {
        Member member = new Member();
        member.setMerchantId(param.getMerchantId());
        CurrencyEnum currencyEnum = param.getCurrencyEnum();
        if (Objects.isNull(currencyEnum)) {
            throw new ApiException(CommonCode.CURRENCY_ERROR);
        }
        member.setCurrencyEnum(currencyEnum);
        member.setMemberName("r" + RandomStringUtils.randomAlphanumeric(10).toLowerCase());
        member.setRegisterDomain(RequestUtil.getHostname());
        member.setRegisterIp(param.getRegisterIp());
        member.setDeviceCode(param.getDeviceCode());

        member.setNickName(generateMemberNickName());
        member.setRegisterDomain(param.getRegisterDomain());
        member.setRegisterDeviceEnum(param.getRegisterDeviceEnum());
        member.setRegisterDeviceCode(param.getRegisterDeviceCode());
        member.setRegisterType(MemberConstants.QUICK_REGISTER_TYPE);
        member.setFbc(param.getFbc());
        member.setFbp(param.getFbp());
        member.setUserAgent(param.getUserAgent());

        Long inviteCode = param.getInviteCode();
        member.setRegisterReferCode(inviteCode);
        member.setInviteTypeEnum(param.getInviteTypeEnum());

        Member upperMember = getInviteMember(member);
        fillHighMemberInfoAndSyncHighMemberCurrencyEnum(member, upperMember);
        fillMemberGroup(member, upperMember);
        fillChannelInfo(member, upperMember);

        return member;
    }

    private Member buildRegisterMember(MemberRegisterBaseParam param) {
        Member member = new Member();
        member.setMerchantId(param.getMerchantId());
        CurrencyEnum currencyEnum = param.getCurrencyEnum();
        if (Objects.isNull(currencyEnum)) {
            throw new ApiException(CommonCode.CURRENCY_ERROR);
        }
        member.setCurrencyEnum(currencyEnum);

        String passwd = decodePassword(param.getPasswd());
        // check passwd length
        final int minLen = 8;
        if (passwd.length() < minLen) {
            throw new ApiException(CommonCode.MEMBER_PASSWD_TOO_SHORT);
        }
        member.setPasswd(BCrypt.hashpw(passwd));

        member.setRegisterIp(param.getRegisterIp());

        member.setNickName(generateMemberNickName());
        member.setRealName(param.getRealName());
        member.setRegisterDomain(param.getRegisterDomain());
        DeviceEnum deviceEnum = param.getRegisterDeviceEnum() == null ? DeviceEnum.PC : param.getRegisterDeviceEnum();
        member.setRegisterDeviceEnum(deviceEnum);
        member.setRegisterDeviceCode(param.getRegisterDeviceCode());
        member.setRemark(param.getRemark());
        member.setAvatar(randomAvatar(member.getCurrencyEnum()));
        member.setFbc(param.getFbc());
        member.setFbp(param.getFbp());
        member.setUserAgent(param.getUserAgent());

        Long inviteCode = param.getInviteCode();
        member.setRegisterReferCode(inviteCode);
        member.setInviteTypeEnum(param.getInviteTypeEnum());
        member.setMemberTypeEnum(param.getMemberTypeEnum());

        Member upperMember = getInviteMember(member);
        if (Objects.isNull(upperMember)) {
            upperMember = memberService.getMemberById(param.getHighMemberId(), param.getMerchantId());
        }
        fillHighMemberInfoAndSyncHighMemberCurrencyEnum(member, upperMember);
        fillMemberGroup(member, upperMember);
        fillChannelInfo(member, upperMember);
        return member;
    }

    private String randomAvatar(CurrencyEnum currencyEnum) {
        int avatarId = RandomUtil.randomInt(1, 60, true, true);
        // KG-1988 头像根据特定币种显示
        if (currencyEnum == CurrencyEnum.PKR && avatarId < 22) {
            avatarId = RandomUtil.randomInt(22, 60, true, true);
        }
        return String.valueOf(avatarId);
    }

    private String decodePassword(String passwd) {
        // rsa 解码
        return RSAUtil.decode(passwd, rsaPrivateKey);
    }

    private String generateMemberNickName() {
        String suffix = RandomStringUtils.randomAlphabetic(8).toLowerCase();
        return "Member" + suffix;
    }

    private Member getInviteMember(Member member) {
        if (Objects.isNull(member.getRegisterReferCode())) {
            Long referCodeByDomain = memberReferService.getReferCodeByDomain();
            if (Objects.isNull(referCodeByDomain)) {
                return null;
            }
            member.setRegisterReferCode(referCodeByDomain);
        }
        Member upperMember = memberService.getByReferCode(member.getRegisterReferCode(), member.getMerchantId());
        if (Objects.isNull(upperMember)) {
            throw new ApiException(CommonCode.MEMBER_REFER_CODE_ERROR);
        }
        return upperMember;
    }

    private void fillHighMemberInfoAndSyncHighMemberCurrencyEnum(Member member, Member upperMember) {
        if (Objects.isNull(upperMember)) {
            member.setDirectHighMemberId(Constants.DEFAULT_HIGH_MEMBER_ID);
            return;
        }
        member.setInviteMemberId(upperMember.getId());
        member.setDirectHighMemberId(upperMember.getId());
        member.setCurrencyEnum(upperMember.getCurrencyEnum());
        member.setMemberTypeEnum(upperMember.getMemberTypeEnum());
    }

    private void fillMemberGroup(Member member, Member upperMember) {
        if (Objects.nonNull(upperMember)) {
            MemberRefer memberRefer = memberReferService.getByReferCode(member.getRegisterReferCode(), member.getMerchantId());
            // check member refer enable
            if (EnableEnum.FALSE == memberRefer.getEnableEnum()) {
                throw new ApiException(CommonCode.MEMBER_INVALID_REFER_CODE);
            }

            member.setGroupId(upperMember.getGroupId());
            member.setGroupName(upperMember.getGroupName());

        }
        // get default currency group
        MemberGroup memberGroup = memberGroupService.getDefaultCurrencyGroup(member.getCurrencyEnum(), member.getMerchantId());
        if (Objects.isNull(memberGroup)) {
            throw new ApiException(CommonCode.MEMBER_GROUP_NOT_EXIST);
        }
        member.setGroupId(memberGroup.getId());
        member.setGroupName(memberGroup.getGroupName());
    }

    private void fillChannelInfo(Member member, Member upperMember) {
        if (Objects.isNull(upperMember)) {
            member.setInviteTypeEnum(InviteTypeEnum.OFFICIAL);
            member.setIsDirect(BooleanEnum.TRUE);
            member.setChannelId(InviteTypeEnum.OFFICIAL.getCode());
            return;
        }
        if (Objects.isNull(member.getInviteTypeEnum())) {
            member.setInviteTypeEnum(InviteTypeEnum.OFFICIAL);
        }
        // 若最上层邀请码有取名，则为独立渠道，否则为本来的类型
        Member rootMember = memberService.getRootMember(upperMember.getId(), upperMember.getMerchantId());
        MemberRefer rootMemberRefer = memberReferService.getByMemberId(rootMember.getId(), rootMember.getMerchantId());
        if (StrUtil.isNotBlank(rootMemberRefer.getChannelName())) {
            member.setInviteTypeEnum(InviteTypeEnum.CUSTOM);
            member.setChannelId(rootMemberRefer.getId());
        } else {
            member.setChannelId(member.getInviteTypeEnum().getCode());
        }
        // 直属裂变判断
        if (Objects.equals(rootMember.getId(), upperMember.getId())) {
            member.setIsDirect(BooleanEnum.TRUE);
        } else {
            member.setIsDirect(BooleanEnum.FALSE);
        }
    }

    private void checkMerchantRegisterConfig(MemberRegisterBaseParam param, CommonDictKeyEnum commonDictKeyEnum) {
        Map<String, MerchantConfig> switchConfig = merchantConfigService.getMerchantSwitchConfig(param.getMerchantId(), param.getCurrencyEnum());
        if (CommonDictKeyEnum.SWITCH_MOBILE_REGISTER == commonDictKeyEnum) {
            MerchantConfig merchantConfig = switchConfig.get(CommonDictKeyEnum.SWITCH_MOBILE_REGISTER.getValue());
            if (!getSwitchStatus(merchantConfig)) {
                throw new ApiException(CommonCode.MEMBER_MOBILE_REGISTER_DISABLED);
            }
        }
        if (CommonDictKeyEnum.SWITCH_EMAIL_REGISTER == commonDictKeyEnum) {
            MerchantConfig merchantConfig = switchConfig.get(CommonDictKeyEnum.SWITCH_EMAIL_REGISTER.getValue());
            if (!getSwitchStatus(merchantConfig)) {
                throw new ApiException(CommonCode.MEMBER_EMAIL_REGISTER_DISABLED);
            }
        }
        MerchantConfig merchantConfig = switchConfig.get(CommonDictKeyEnum.SWITCH_INVITE_CODE_REQUIRED.getValue());
        if (getSwitchStatus(merchantConfig)) {
            Long inviteCode = param.getInviteCode();
            if (Objects.isNull(inviteCode)) {
                throw new ApiException(CommonCode.MEMBER_REGISTER_REFER_CODE_REQUIRED);
            }
        }
    }

    private boolean getSwitchStatus(MerchantConfig merchantConfig) {
        if (Objects.isNull(merchantConfig)) {
            return false;
        }
        return merchantConfig.getEnableEnum() == EnableEnum.TRUE;
    }

}
