package com.wd.lottery.module.member.service;


import com.wd.lottery.module.cash.constatns.PlatformEnum;
import com.wd.lottery.module.member.dto.*;
import com.wd.lottery.module.member.dto.*;
import com.wd.lottery.module.member.entity.MemberWithdrawAccount;
import com.wd.lottery.module.member.vo.MemberDetailsMemberWithdrawAccountVO;

import java.util.List;

public interface MemberWithdrawAccountService {

    List<MemberWithdrawAccount> findByMemberId(Long memberId, Long merchantId);

    MemberWithdrawAccount findByIdAndMemberIdAndMerchantIdNotNull(Long id, Long memberId, Long merchantId);

    List<MemberDetailsMemberWithdrawAccountVO> findByMemberDetails(MemberDetailsDTO memberDetailsDto, Long merchantId);

    Long saveDataByC(MemberWithdrawAccountDTO memberWithdrawAccountDto);

    Long saveDataByB(MemberWithdrawAccountInsertDTO memberWithdrawAccountInsertDto);

    Long updateData(MemberWithdrawAccountUpdateDTO memberWithdrawAccountUpdateDto);

    Long defaultUseEnable(Long id, Long memberId, PlatformEnum platformEnum, Long merchantId);

    Long enable(MemberWithdrawAccountEnableDTO memberWithdrawAccountEnableDto);

    Boolean existMemberWithdrawAccount(Long merchantId, Long memberId);

}
