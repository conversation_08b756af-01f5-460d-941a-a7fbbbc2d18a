package com.wd.lottery.module.third.dto;

import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.constants.ThirdLoginTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * <p> Created on 2024/6/4.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
public class ThirdPlatformCategoryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "游戏分类")
    private GameCategoryEnum categoryEnum;

    @Schema(description = "分类图标")
    private String categoryImg;

    @Schema(description = "登入方式")
    private ThirdLoginTypeEnum thirdLoginTypeEnum;

    @Schema(description = "是否支持试玩模式")
    private BooleanEnum isSupportDemo = BooleanEnum.FALSE;

    @Schema(description = "是否热门  FALSE 否 TRUE 是")
    private BooleanEnum enableHot = BooleanEnum.FALSE;

    @Schema(description = "排序字段，越大越靠前")
    private Integer sort = 0;
}
