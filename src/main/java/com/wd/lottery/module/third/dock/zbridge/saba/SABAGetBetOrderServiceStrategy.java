package com.wd.lottery.module.third.dock.zbridge.saba;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.constants.BetOrderWinTypeEnum;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.saba.common.*;
import com.wd.lottery.module.third.dock.zbridge.saba.common.*;
import com.wd.lottery.module.third.dock.zbridge.saba.res.SABAResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * Description: saba game service strategy
 *
 * <p> Created on 2024/7/1.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component(BridgeConstant.SABA_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class SABAGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {



    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        log.debug("SABA:游戏注单[入参]:{}", JSONUtil.toJsonStr(flag, JSONConfig.create().setDateFormat(DatePattern.NORM_DATETIME_PATTERN)));
        // 平台配置
        SABARequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), SABARequestConfig.class);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(flag.getPlatformCode());

        // 自动拉单
        if (!flag.isManual()) {
            List<BetDetail> betOrders = this.requestBetOrder(true, flag, requestConfig, flag.getLastFlag());
            return this.processBetOrderDetail(betOrders, flag.getPlatformCode(), flag.getCurrencyEnum());
        }

        // 手动拉单
        boolean isFirst = StrUtil.isEmpty(flag.getRemark());
        String minVersionKey = null;
        if (isFirst) {
            minVersionKey = this.requestPullOrderMinVersionKey(flag, requestConfig, configJson);
        }
        // 无注单记录
        if (StrUtil.equals("0", minVersionKey)) {
            flag.setFinished(true);
            return Collections.emptyList();
        }
        // 有注单记录
        boolean isRemarkEmpty = StrUtil.isEmpty(flag.getRemark());
        String versionKey = isRemarkEmpty ? StrUtil.toString(Convert.toLong(minVersionKey) - 1) : StrUtil.split(flag.getRemark(), StrPool.AT).get(0);
        List<BetDetail> betOrders = this.requestBetOrder(false, flag, requestConfig, versionKey);
        return this.processBetOrderDetail(betOrders, flag.getPlatformCode(), flag.getCurrencyEnum());
    }

    private List<BetDetail> requestBetOrder(boolean isAuto, BetOrderPullFlag flag, SABARequestConfig requestConfig, String versionKey) {
        log.debug("SABA:游戏注单[{}]:{}-{}", isAuto ? "自动" : "手动", JSONUtil.toJsonStr(flag, JSONConfig.create().setDateFormat(DatePattern.NORM_DATETIME_PATTERN)), versionKey);
        int count = 500;
        SABAHttpRequestTemplate requestTemplate = new SABAHttpRequestTemplate(requestConfig);
        // 请求注单
        SABAResponse<GetBetDetail> response = requestTemplate
                .api("/api/GetBetDetail/")
                .addParameter("version_key", versionKey)
                .toBeanAndCall(new TypeReference<SABAResponse<GetBetDetail>>() {
                });
        log.debug("SABA:游戏注单响应[{}]:{}", isAuto ? "自动" : "手动", JSONUtil.toJsonStr(response));
        GetBetDetail data = response.getData();
        // 请求正常:无数据
        if (ObjectUtil.isNull(data)) {
            flag.setFinished(true);
            return Collections.emptyList();
        }
        // 请求正常:有数据
        List<BetDetail> betOrders = Lists.newArrayListWithExpectedSize(count);
        List<BetDetail> betDetails = data.getBetDetails();
        if (CollUtil.isNotEmpty(data.getBetDetails())) {
            betOrders.addAll(data.getBetDetails());
        }
        if (CollUtil.isNotEmpty(data.getBetNumberDetails())) {
            betOrders.addAll(data.getBetNumberDetails());
        }
        if (CollUtil.isNotEmpty(data.getBetVirtualSportDetails())) {
            betOrders.addAll(data.getBetVirtualSportDetails());
        }
        String lastVersionKey = data.getLast_version_key();
        if (isAuto) {
            flag.setLastFlag(lastVersionKey);
            flag.setFinished(CollUtil.size(betDetails) < count);
        } else {
            LocalDateTime maxWinLostDatetime = betOrders.stream()
                    .map(BetDetail::getWinlost_datetime)
                    .map(item -> StrUtil.split(item, StrPool.DOT).get(0))
                    .map(item -> ThirdPlatformMappingConverter.parseThirdOrderTime(item, flag.getPlatformCode()))
                    .max(LocalDateTime::compareTo)
                    .orElseThrow(() -> new ThirdPlatformException("获取最大决胜时间失败"));
            flag.setRemark(StrUtil.join(StrPool.AT, lastVersionKey, DateUtil.format(maxWinLostDatetime, DatePattern.NORM_DATETIME_PATTERN)));
            boolean isFinished = CollUtil.size(betDetails) < count || maxWinLostDatetime.isAfter(flag.getEnd());
            flag.setFinished(isFinished);
        }
        return betOrders;
    }

    private String requestPullOrderMinVersionKey(BetOrderPullFlag flag, SABARequestConfig requestConfig, ThirdPlatformConfigDTO configJson) {
        // 三方时区
        String timeZone = configJson.getPlatformTimeZone();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN);

        // 拉单时间
        LocalDateTime begin = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).toLocalDateTime();
        LocalDateTime end = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).toLocalDateTime();
        // log.debug("SABA:游戏注单:手动时间转三方时间:{}至{}", begin.format(formatter), end.format(formatter));

        // 日期分割，比如拉取多天，要考虑开始天和结束时间有游戏注单记录吗
        Date fromDate = DateUtil.parse(begin.format(formatter), DatePattern.NORM_DATE_PATTERN);
        Date toDate = DateUtil.parse(end.format(formatter), DatePattern.NORM_DATE_PATTERN);
        List<DateTime> dateTimes = DateUtil.rangeToList(fromDate, toDate, DateField.DAY_OF_YEAR, 1);

        // 查找开始日期versionKey，结束判断用:没数据||当前记录数据>=拉单截止时间，不用接口获取结束时间的versionKey，因为可能不存在注单返回空或者0。
        String versionKey = "0";
        SABAHttpRequestTemplate requestTemplate = new SABAHttpRequestTemplate(requestConfig);
        for(DateTime date : dateTimes) {
            String queryDate = DateUtil.format(date,DatePattern.NORM_DATE_PATTERN);
            // 请求versionKey
            SABAResponse<String> response = requestTemplate.toPOST()
                    .api("/api/GetVersionkeyByDate")
                    .addParameter("winlost_date", queryDate)
                    .toBeanAndCall(new TypeReference<SABAResponse<String>>() {
                    });
            if (log.isDebugEnabled()) {
                log.debug("SABA:游戏注单:根据日期读取VersionKey:{}-{}", queryDate, JSONUtil.toJsonStr(response));
            }
            versionKey = response.getData();
            if (StrUtil.isNotEmpty(versionKey)) {
                break;
            }
            ThreadUtil.sleep(500);
        }
        return versionKey;
    }

    private List<DockBetOrder> processBetOrderDetail(List<BetDetail> list, String platformCode, CurrencyEnum currencyEnum) {

        String matchIds = list.parallelStream().map(BetDetail::getMatch_id).distinct().collect(Collectors.joining(","));
        Map<String, String> resultMap = getGameResult(matchIds, platformCode, currencyEnum);

        List<DockBetOrder> resultList = new ArrayList<>(list.size());

        for (BetDetail betDetail : list) {
            // 未结算的订单暂不保存
            if (!isSettledBetOrder(betDetail)) {
                continue;
            }
            DockBetOrder dockOrder = toDockBetOrder(betDetail, resultMap, platformCode, currencyEnum);
            String orderTimeStr = betDetail.getSettlement_time();
            if (StringUtils.isBlank(orderTimeStr)) {
                orderTimeStr = betDetail.getTransaction_time();
            }
            String chr = ".";
            if (StringUtils.contains(orderTimeStr, chr)) {
                orderTimeStr = StringUtils.substringBefore(orderTimeStr, chr);
            }
            LocalDateTime orderTime = ThirdPlatformMappingConverter.parseThirdOrderTime(orderTimeStr, platformCode);
            dockOrder.setOrderTime(orderTime);

            long betMoney = toPlatformMoney(betDetail.getStake());
            dockOrder.setBetMoney(betMoney);
            dockOrder.setValidBetMoney(betMoney);
            long payMoney = toPlatformMoney(betDetail.getWinlost_amount());
            long winMoney = betMoney + payMoney;
            dockOrder.setWinMoney(winMoney);

            dockOrder.setWinTypeEnum(BetOrderWinTypeEnum.NORMAL);

            resultList.add(dockOrder);
        }

        return resultList;

    }

    private boolean isSettledBetOrder(BetDetail betDetail) {
        return Objects.nonNull(betDetail)
                && StringUtils.equalsAny(betDetail.getTicket_status(),
                    SABABetOrderStatusEnum.WON.getStatus(),
                    SABABetOrderStatusEnum.LOSE.getStatus(),
                    SABABetOrderStatusEnum.DRAW.getStatus(),
                    SABABetOrderStatusEnum.HALF_WON.getStatus(),
                    SABABetOrderStatusEnum.HALF_LOSE.getStatus());
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        SABARequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), SABARequestConfig.class);
        SABAHttpRequestTemplate requestTemplate = new SABAHttpRequestTemplate(requestConfig);

        try {
            Token token = requestTemplate
                    .host(requestConfig.getAuthUrl())
                    .api("/api/auth")
                    .toPOST()
                    .addHeader(HttpHeaders.CONTENT_TYPE, APPLICATION_JSON_VALUE)
                    .addParameter("username", requestConfig.getUsername())
                    .addParameter("password", requestConfig.getPassword())
                    .toCustomObject(Token.class);
            String url = requestConfig.getAuthUrl() +
                    "/BetList/Refno" +
                    "?access_token=" +
                    token.getAccessToken() +
                    "&refid=" +
                    dto.getOrderNo() +
                    "&urs=" +
                    dto.getThirdUserName();
            log.debug("SABA注单详情信息:{}-{}", JacksonUtil.toJSONString(token), url);
            return url;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    /**
     * 根据比赛id查找比赛结果
     *
     * @param matchIds     比赛id, 多个逗号(,)隔开
     * @param currencyEnum 币种
     * @return key: 比赛id, value: 主队比分-客队比分
     */
    private Map<String, String> getGameResult(String matchIds, String platformCode, CurrencyEnum currencyEnum) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(matchIds)) {
            return new HashMap<>();
        }

        SABARequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, SABARequestConfig.class);
        SABAHttpRequestTemplate requestTemplate = new SABAHttpRequestTemplate(requestConfig);

        Map<String, String> map = new HashMap<>();
        try {
            SABAResponse<List<GameDetail>> response = requestTemplate
                    .toPOST()
                    .api("/api/GetGameDetail/")
                    .addParameter("vendor_id", requestConfig.getVendorId())
                    .addParameter("match_ids", matchIds)
                    .toBeanAndCall(new TypeReference<SABAResponse<List<GameDetail>>>() {
                    });
            if (CollectionUtils.isNotEmpty(response.getData())) {
                map = response.getData().parallelStream()
                        .collect(Collectors.toMap(GameDetail::getMatch_id, detail -> detail.getHome_score() + "-" + detail.getAway_score(), (v1, v2) -> v1));
            }
            log.debug("{} - getGameResult, matchIds: {}, result: {}, response: {}", platformCode, matchIds, map, response);
        } catch (Exception e) {
            log.error("{} - getGameResult fail, matchIds: {}", platformCode, matchIds);
        }
        return map;
    }

    @Data
    static class GameDetail {
        /**
         * 赛事编号
         **/
        private String match_id;
        /**
         * 联盟编号
         **/
        private String league_id;

        /**
         * 主队编号
         **/
        private String home_id;

        /**
         * 客队编号
         **/
        private String away_id;

        /**
         * 主队全场得分
         **/
        private String home_score;

        /**
         * 客队全场得分
         **/
        private String away_score;

        /**
         * 百练赛、快乐 5 开出的第一颗球
         * 百练赛返回范例:
         * 35_r 代表开出球号为 35, 颜色为红色
         * 17_b 代表开出球号为 17, 颜色为蓝色
         * 快乐 5 返回范例:
         * 35 代表开出球号为 35,不返回颜色.
         **/
        private String first_ball;
        /**
         * 百练赛、快乐5开出的第二颗球(范例同上).
         **/
        private String second_ball;

        /**
         * 百练赛、快乐5开出的第三颗球(范例同上).
         **/
        private String third_ball;

        /**
         * 快乐5开出的第四颗球
         **/
        private String fourth_ball;

        /**
         * 快乐5开出的第五颗球
         **/
        private String fifth_ball;

        /**
         * 主队上半场得分,上半场结算后返回比分
         **/
        private String ht_home_score;

        /**
         * 客队上半场得分, 上半场结算后返回比分
         **/
        private String ht_away_score;

        /**
         * running: 赛事尚未开始或正在进行中
         * closed: 赛事在未开赛前即关闭
         * postponed: 赛事延赛
         * completed: 赛事已结算
         * refund: 赛事取消
         **/
        private String game_status;

        /**
         * 体育种类。请参考附件〝体育种类表”
         **/
        private String sport_type;

        /**
         * 场次信息(仅支持虚拟运动2)
         **/
        private String virtualSport_info;

        /**
         * 百练赛、快乐5的赛事编号
         **/
        private String gameNo;

        /**
         * 赛事开始时间(仅支持百练赛)
         **/
        private String match_datetime;

        /**
         * 百练赛、快乐5开出的球号总和
         **/
        private String total_sum;

        /**
         * 百练赛开出比37.5大的球数
         **/
        private String over_count;

        /**
         * 百练赛开出比37.5小的球数
         **/
        private String under_count;

        /**
         * 赛事是否为中立场
         **/
        private String is_neutral;
    }

    @Data
    public static class Token {

        @JsonProperty("access_token")
        private String accessToken;

        @JsonProperty("token_type")
        private String tokenType;

        @JsonProperty("expires_in")
        private String expiresIn;
    }

    @Data
    static class GetBetDetail {
        @JsonProperty("last_version_key")
        private String last_version_key;
        @JsonProperty("BetDetails")
        private List<BetDetail> BetDetails;
        @JsonProperty("BetNumberDetails")
        private List<BetNumberDetail> BetNumberDetails;
        @JsonProperty("BetVirtualSportDetails")
        private List<BetVirtualSportDetail> BetVirtualSportDetails;
    }

    @Data
    static class LangName {
        private String lang;
        private String name;
    }

    @Data
    static class BetDetail {
        /**
         * 注单识别码
         **/
        private String trans_id;
        /**
         * 厂商会员识别码
         **/
        private String vendor_member_id;
        /**
         * 厂商ID。请带入sitename
         **/
        private String operator_id;
        /**
         * 联盟名称
         **/
        private List<LangName> leaguename;
        /**
         * 比赛识别码
         **/
        private String match_id;
        /**
         * 队伍识别码
         **/
        private String team_id;
        /**
         * 主队识别码
         **/
        private String home_id;
        /**
         * 主队名称
         **/
        private List<LangName> hometeamname;
        /**
         * 客队识别码
         **/
        private String away_id;
        /**
         * 客队名称
         **/
        private List<LangName> awayteamname;
        private String bet_type;
        /**
         * 下注类型
         **/
        private List<LangName> bettypename;
        /**
         * 比赛开球时间
         **/
        private String match_datetime;
        /**
         * 体育种类
         **/
        private String sport_type;
        /**
         * 会员投注金额
         **/
        private BigDecimal stake;
        /**
         * 折扣后的投注金额
         **/
        private BigDecimal discount_stake;
        /**
         * 有效投注金额, 捕鱼世界专用
         **/
        private BigDecimal validbetamount;
        /**
         * 投注交易时间
         **/
        private String transaction_time;
        /**
         * 注单状态:
         * waiting(等待中): 我们交易员因为可能因为赔率的转换等因素，还未接受这张注单。
         * running(进行中): 此注单还没有结算。 （注单还没有结算的状态有可能是这场比赛还没有结算之类的情形.）
         * void (作废): 在注单为running的状态下，玩家下注注金返回。原因可能为我们交易员对此场赛事有些疑虑。可与我们联系询问发生什么状况。
         * refund(退款): 在注单为running的状态下，玩家下注注金返回。原因有可能是赛事取消或发生什么意外。
         * reject(已取消): 在注单为waiting的状态下，玩家下注注金返回。可能状况很多。
         * lose(输): 此注单已结算且玩家输了此注单。
         * won(赢): 此注单已结算且玩家赢了此注单。
         * draw(和局): 此注单已结算且此注单为和局。
         * half won(半赢): 此注单已结算且玩家赢了下注额一半。
         * half lose(半输): 此注单已结算且玩家输了下注额一半。
         **/
        private String ticket_status;
        /**
         * 此注输或赢的金额
         **/
        private BigDecimal winlost_amount;
        /**
         * 会员设置币别
         **/
        private String currency;
        /**
         * 决胜时间(仅显示日期),请依此字段做为后台报表对帐使用.
         **/
        private String winlost_datetime;
        /**
         * 彩票游戏类别
         **/
        private String lottery_bettype;
        /**
         * 下注对象
         **/
        private String bet_team;
        /**
         * 下注时主队得分
         **/
        private int home_score;
        /**
         * 下注时客队得分
         **/
        private int away_score;
        /**
         * 注单结算的时间
         **/
        private String settlement_time;
        /**
         * 版本号
         **/
        private String version_key;

        /**
         * 混合过关资料
         */
        @JsonProperty("ParlayData")
        private List<ParlayDetail> ParlayData;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    static class BetNumberDetail extends BetDetail {
        /**
         * 注单识别码
         **/
        private String trans_id;
        /**
         * 厂商会员识别码
         **/
        private String vendor_member_id;
        /**
         * 厂商ID。请带入sitename
         **/
        private String operator_id;
        /**
         * 比赛识别码，当BetType = 10时，隐藏栏位
         **/
        private String match_id;
        /**
         * 投注交易时间
         **/
        private String transaction_time;
        /**
         * 注单赔率
         **/
        private String odds;
        /**
         * 注单状态
         * ※half won/half lose/won/lose/void/running/draw/reject/refund/waiting
         * 请参考以下说明。
         * waiting(等待中): 我们交易员因为可能因为赔率的转换等因素，还未接受这张注单。
         * running(进行中): 此注单还没有结算。（注单还没有结算的状态有可能是这场比赛还没有结算之类的情形.）
         * void (作废): 在注单为 running 的状态下，玩家下注注金返回。原因可能为我们交易员对此场赛事有些疑虑。可与我们联系询问发生什么状况。
         * refund(退款): 在注单为 running 的状态下，玩家下注注金返回。原因有可能是赛事取消或发生什么意外。
         * reject(已取消): 在注单为 waiting 的状态下，玩家下注注金返回。可能状况很多。
         * lose(输): 此注单已结算且玩家输了此注单。
         * won(赢): 此注单已结算且玩家赢了此注单。
         * draw(和局): 此注单已结算且此注单为和局。
         * half won(半赢): 此注单已结算且玩家赢了下注额一半。
         * half lose(半输): 此注单已结算且玩家输了下注额一半。
         */
        private String ticket_status;
        /**
         * 下注平台表
         **/
        private String betfrom;
        /**
         * 比赛是否为live
         **/
        private String islive;
        /**
         * 下注对象
         * For CB, Pick name
         **/
        private String bet_team;
        /**
         * 决胜时间(仅显示日期),请依此字段做为后台报表对帐使用.
         **/
        private String winlost_datetime;
        /**
         * 下注类型。请参考附件"下注类型表"
         **/
        private String bet_type;
        /**
         * 为此会员设置币别。请参考附件"币别表"
         **/
        private String currency;
        /**
         * 赔率类型。请参考附件中"赔率类型表"，当sport_type=245或bet_type=468、469 返回字段odds_type=0
         **/
        private String odds_type;
        /**
         * 体育种类。请参考附件"体育种类表"
         **/
        private String sport_type;
        /**
         * 下注后的余额
         **/
        private String after_amount;
        /****/
        private String customInfo1;
        /****/
        private String customInfo2;
        /****/
        private String customInfo3;
        /****/
        private String customInfo4;
        /****/
        private String customInfo5;
        /**
         * Flag of Bet Aggregator, 1: 是/ 0: 否
         **/
        private String ba_status;
        /**
         * 版本号
         **/
        private String version_key;
        /**
         * For CB, 当 sport_type=168 返回此字段. ex: 大,大/小,小,大,大.
         **/
        private String betchoice;
        /**
         * 下注时，前一颗的球号
         **/
        private String last_ball_no;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    static class BetVirtualSportDetail extends BetDetail {
        /**
         * 注单识别码
         **/
        private String trans_id;
        /**
         * 厂商会员识别码
         **/
        private String vendor_member_id;
        /**
         * 厂商ID。请带入sitename
         **/
        private String operator_id;
        /**
         * 联盟识别码
         **/
        private String league_id;
        /**
         * 比赛识别码，当BetType = 10时，隐藏栏位
         * For CB, Indicates its pool id
         * For Keno Lottery/ Keno, its Game No
         **/
        private String match_id;
        /**
         * 主队识别码
         **/
        private String home_id;
        /**
         * 客队识别码
         **/
        private String away_id;
        /**
         * 比赛开球时间
         **/
        private String match_datetime;
        /**
         * 体育种类。请参考附件"体育种类表"
         **/
        private String sport_type;
        /**
         * 下注类型。请参考附件"下注类型表"
         **/
        private String bet_type;
        /**
         * 混合过关注单号码，使用此号码于GetSystemParlayDetail取得混合过关注单内容
         **/
        private String parlay_ref_no;
        /**
         * 注单赔率
         **/
        private String odds;
        /**
         * 投注交易时间
         **/
        private String transaction_time;
        /**
         * 注单状态
         * ※half won/half lose/won/lose/void/running/draw/reject/refund/waiting
         * 请参考以下说明。
         * waiting(等待中): 我们交易员因为可能因为赔率的转换等因素，还未接受这张注单。
         * running(进行中): 此注单还没有结算。（注单还没有结算的状态有可能是这场比赛还没有结算之类的情形.）
         * void (作废): 在注单为 running 的状态下，玩家下注注金返回。原因可能为我们交易员对此场赛事有些疑虑。可与我们联系询问发生什么状况。
         * refund(退款): 在注单为 running 的状态下，玩家下注注金返回。原因有可能是赛事取消或发生什么意外。
         * reject(已取消): 在注单为 waiting 的状态下，玩家下注注金返回。可能状况很多。
         * lose(输): 此注单已结算且玩家输了此注单。
         * won(赢): 此注单已结算且玩家赢了此注单。
         * draw(和局): 此注单已结算且此注单为和局。
         * half won(半赢): 此注单已结算且玩家赢了下注额一半。
         * half lose(半输): 此注单已结算且玩家输了下注额一半。
         */
        private String ticket_status;
        /**
         * 下注后的余额
         **/
        private String after_amount;
        /**
         * 为此会员设置币别。请参考附件"币别表"
         **/
        private String currency;
        /**
         * 决胜时间(仅显示日期),请依此字段做为后台报表对帐使用.
         **/
        private String winlost_datetime;
        /**
         * 赔率类型。请参考附件中"赔率类型表"，当sport_type=245或bet_type=468、469 返回字段odds_type=0
         **/
        private String odds_type;
        /**
         * 下注对象
         **/
        private String bet_team;
        /**
         * 主队让球
         **/
        private String home_hdp;
        /**
         * 客队让球
         **/
        private String away_hdp;
        /**
         * 让球
         **/
        private String hdp;
        /**
         * 下注平台表。请参考附件中"下注平台表"
         **/
        private String betfrom;
        /**
         * 比赛是否为live, 1: 是/ 0: 否
         **/
        private String islive;
        /****/
        private String os;
        /****/
        private String browser;
        /****/
        private String customInfo1;
        /****/
        private String customInfo2;
        /****/
        private String customInfo3;
        /****/
        private String customInfo4;
        /****/
        private String customInfo5;
        /**
         * Flag of Bet Aggregator, 1: 是/ 0: 否
         **/
        private String ba_status;
        /**
         * 版本号
         **/
        private String version_key;
        /**
         * 当bet_team=aos时,才返回此字段,返回的值代表会员投注的正确比分不为列出的这些.
         **/
        private String exculding;

        /**
         * 赛马号码
         **/
        private String race_number;
        /**
         * 赛道
         **/
        private String race_lane;
    }

    @Data
    static class ParlayDetail {
        private String parlay_id;
        private String league_id;
        private List<LangName> leaguename;
        private String match_id;
        private String home_id;
        private List<LangName> hometeamname;
        private String away_id;
        private List<LangName> awayteamname;
        private String match_datetime;
        private String odds;
        private String bet_type;
        private List<LangName> bettypename;
        private String bet_team;
        private String sport_type;
        private List<LangName> sportname;
        private String home_hdp;
        private String away_hdp;
        private String hdp;
        private String islive;
        private String home_score;
        private String away_score;
        private String ticket_status;
        private String winlost_datetime;
        private String bet_tag;
        /**
         * 彩票游戏类别
         **/
        private String lottery_bettype;
    }


    private DockBetOrder toDockBetOrder(BetDetail betOrder, Map<String, String> resultMap, String platformCode, CurrencyEnum currencyEnum) {

        DockBetOrder dockOrder = new DockBetOrder();
        dockOrder.setThirdUserName(betOrder.getVendor_member_id());
        dockOrder.setPlatformCode(platformCode);
        dockOrder.setOrderNum(betOrder.getTrans_id());

        dockOrder.setGameId(betOrder.getSport_type());
        try {

            String detail = buildDetail(betOrder, resultMap.get(betOrder.getMatch_id()), platformCode, currencyEnum);
            if (Objects.nonNull(detail)) {
                final String splitFlag = "__";
                String[] arr = detail.split(splitFlag);
                dockOrder.setGameDetail(arr[0]);
                if (arr.length > 1) {
                    dockOrder.setBetDetail(arr[1]);
                }
            }
        } catch (Exception e) {
            log.warn("parse bet order detail failed, orderCode: {}", dockOrder.getOrderNum(), e);
        }

        return dockOrder;

    }

    /**
     * 联盟: 主队(分数)-客队(分数)__投注类型|投注内容, e.g. 中国足协杯: 昆山FC(0)-天津津门虎(4)__大小盘|h
     */
    private static final String pattern = "%s: %s(%s)-%s(%s)__%s|%s";

    /**
     * 解析构造投注详情
     *
     * @param order        投注详情
     * @param result       比分, 主队-客队
     * @param platformCode 三方平台编码
     * @param currencyEnum 当前币种
     */
    private String buildDetail(BetDetail order, String result, String platformCode, CurrencyEnum currencyEnum) {
        if (StringUtils.isEmpty(result)) {
            return null;
        }
        String detail;
        Map<String, String> leaguename;
        Map<String, String> hometeamname;
        Map<String, String> awayteamname;
        Map<String, String> bettypename = null;
        String betContent = null;
        String lang = "en";
        try {
            if (CollectionUtils.isNotEmpty(order.getParlayData())) {
                return buildParlayDetail(order, lang, platformCode, currencyEnum);
            }
            betContent = SABAUtils.getBetContent(order.getBet_type(), order.getBet_team());
            bettypename = order.getBettypename().stream().collect(Collectors.toMap(LangName::getLang, LangName::getName, (v1, v2) -> v1));
            leaguename = order.getLeaguename().stream().collect(Collectors.toMap(LangName::getLang, LangName::getName, (v1, v2) -> v1));
            hometeamname = order.getHometeamname().stream().collect(Collectors.toMap(LangName::getLang, LangName::getName, (v1, v2) -> v1));
            awayteamname = order.getAwayteamname().stream().collect(Collectors.toMap(LangName::getLang, LangName::getName, (v1, v2) -> v1));
            String[] scores = result.split("-");

            detail = String.format(pattern, leaguename.get(lang), hometeamname.get(lang), scores[0],
                    awayteamname.get(lang), scores[1], bettypename.get(lang), betContent);
        } catch (Exception e) {
            log.warn("{} - 构建投注详情失败, order: {}, result: {}", platformCode, order, result, e);
            String lotteryType = "";
            if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getLottery_bettype())) {
                lotteryType = order.getLottery_bettype() + "|";
            }
            String betType = bettypename == null ? SABATypes.betTypes.get(order.getBet_type()) : MapUtils.getString(bettypename, lang, order.getBet_type());
            detail = betType + "__" + (betContent == null ? order.getBet_team() : lotteryType + betContent);
        }
        return detail;
    }

    private String buildParlayDetail(BetDetail order, String lang, String platformCode, CurrencyEnum currencyEnum) {
        List<ParlayDetail> parlayData = order.getParlayData();
        String matchIds = parlayData.stream().map(ParlayDetail::getMatch_id).distinct().collect(Collectors.joining(","));

        Map<String, String> gameResult = getGameResult(matchIds, platformCode, currencyEnum);
        String type = parlayData.stream().map(pd -> {
            String result = gameResult.get(pd.getMatch_id());
            String[] scores = result.split("-");
            Map<String, String> honeteamname = pd.getHometeamname().stream().collect(Collectors.toMap(LangName::getLang, LangName::getName, (v1, v2) -> v1));
            Map<String, String> awayteamname = pd.getAwayteamname().stream().collect(Collectors.toMap(LangName::getLang, LangName::getName, (v1, v2) -> v1));
            return honeteamname.get(lang) + "(" + scores[0] + ")-" + awayteamname.get(lang) + "(" + scores[1] + ")";
        }).collect(Collectors.joining(", "));
        String content = parlayData.stream()
                .map(pd -> {
                    String betContent = SABAUtils.getBetContent(pd.getBet_type(), pd.getBet_team());
                    if (CollectionUtils.isNotEmpty(pd.getBettypename())) {
                        Map<String, String> bettypename = pd.getBettypename().stream().collect(Collectors.toMap(LangName::getLang, LangName::getName, (v1, v2) -> v1));
                        betContent = bettypename.get(lang) + "|" + betContent;
                    } else if (org.apache.commons.lang3.StringUtils.isNotBlank(pd.getLottery_bettype())) {
                        betContent = pd.getLottery_bettype() + "|" + betContent;
                    } else {
                        betContent = pd.getBet_type() + "|" + betContent;
                    }
                    return betContent;
                }).collect(Collectors.joining(", "));
        return type + "__" + content;
    }
}
