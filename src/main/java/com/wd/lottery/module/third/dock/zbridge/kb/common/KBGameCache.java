package com.wd.lottery.module.third.dock.zbridge.kb.common;

import com.wd.lottery.module.common.constants.GameCategoryEnum;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: kb game cache
 *
 * <p> Created on 2024/12/18.
 *
 * <AUTHOR>
 * @version 0.1
 */
public class KBGameCache {
    private static final Map<String, String> miniGames = new HashMap<>();
    public static final Map<GameCategoryEnum, Map<String, String>> gameMap = Collections.singletonMap(GameCategoryEnum.MINI, miniGames);


    static{
        miniGames.put("101", "aviator");
        miniGames.put("102", "andarbahar");
        miniGames.put("103", "aviator-pro");
        miniGames.put("104", "sevenupsevendown");
        miniGames.put("201", "teenpatti");
        miniGames.put("202", "threepatti");
        miniGames.put("204", "dragonvstiger");
    }


    private KBGameCache() {
    }
}
