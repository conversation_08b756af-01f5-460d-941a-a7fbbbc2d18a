package com.wd.lottery.module.third.dock.zbridge.jili;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.base.LangEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.user.DockCreateUser;
import com.wd.lottery.module.third.dock.user.DockUser;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.jili.common.JILIApiEnum;
import com.wd.lottery.module.third.dock.zbridge.jili.common.JILIHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.jili.common.JILIRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.jili.common.JILIResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import com.wd.lottery.module.third.param.ThirdUserUpdateParam;
import com.wd.lottery.module.third.service.ThirdSiteUserUpdateService;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: jili game strategy
 *
 * <p> Created on 2024/5/13.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component(BridgeConstant.JILI_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class JILIGetGameServiceStrategy extends AbstractGetGameStrategy {

    @Autowired
    private JILIUserServiceStrategy jiliUserServiceStrategy;
    @Autowired
    private ThirdSiteUserUpdateService thirdSiteUserUpdateService;

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        JILIRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, JILIRequestConfig.class);
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);
        JILIHttpRequestTemplate requestTemplate = new JILIHttpRequestTemplate(requestConfig, platformConfig);

        List<JILIGame> excelGameList = new ArrayList<>();
        try {
            JILIResponse<List<JILIGame>> resultRoot = requestTemplate.api(JILIApiEnum.GAME_LIST.getPath())
                    .toPOST()
                    .toBeanAndCall(new TypeReference<JILIResponse<List<JILIGame>>>() {
                    });

            excelGameList = resultRoot.getData();
        } catch (Exception e) {
            log.warn("JILI get  game failed with exception", e);
        }
        return toDockGameList(excelGameList, platformCode);
    }

    @Data
    @ToString
    static public class JILIGame {
        @JsonProperty("GameId")
        private String gameId;  // 游戏唯一ID

        @JsonProperty("name")
        private Map<String, String> name; // 游戏名 key值分别为zh-CN,zh-TW,en-US

        @JsonProperty("GameCategoryId")
        private String gameCategoryId; // 游戏唯一code 游戏类型
    }

    private List<DockGame> toDockGameList(List<JILIGame> games, String platformCode) {
        if (CollectionUtils.isEmpty(games)) {
            return new ArrayList<>();
        }
        return games.stream()
                .map(game -> {
                    DockGame dockGame = new DockGame();
                    dockGame.setPlatformCode(platformCode);
                    dockGame.setThirdGameId(game.getGameId());
                    dockGame.setGameName(game.getName().get("en-US"));
                    dockGame.setGameCode(game.getGameId());
                    dockGame.setGameCategoryEnum(parseGameCategory(game.getGameCategoryId()));
                    dockGame.setRemark(game.getName().get("zh-CN"));
                    return dockGame;
                })
                .collect(Collectors.toList());
    }

    private GameCategoryEnum parseGameCategory(String gameCategoryId) {
        switch (gameCategoryId) {
            case "1":
                return GameCategoryEnum.SLOT;
            case "2":
                return GameCategoryEnum.POKER;
            case "5":
                return GameCategoryEnum.FISH;
            case "8":
                return GameCategoryEnum.MINI;
            default:

        }
        return GameCategoryEnum.SLOT;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        JILIRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), JILIRequestConfig.class);
        JILIResponse<String> response = execGetGameUrlRequest(dto, requestConfig);
        // 获取成功直接打开游戏
        if (Objects.nonNull(response) && response.isSuccess()) {
            return response.getData();
        }
        Integer userNotExistsCode = 14;
        if (Objects.nonNull(response) && userNotExistsCode.equals(response.getErrorCode())) {
            // create new third user
            DockCreateUser createUserParam = buildCreateUserParam(dto);
            DockUser user = jiliUserServiceStrategy.createUser(createUserParam);
            // update third user
            ThirdUserUpdateParam updateParam = buildUpdateUserInfoParam(user);
            thirdSiteUserUpdateService.updateThirdUserInfo(updateParam);
            // re get game url
            dto.setThirdUserId(user.getThirdUserId());
            dto.setThirdUserName(user.getThirdUserName());
            dto.setThirdUserPasswd(dto.getThirdUserPasswd());
            JILIResponse<String> newRes = execGetGameUrlRequest(dto, requestConfig);
            if (Objects.nonNull(newRes) && newRes.isSuccess()) {
                return newRes.getData();
            }
        }
        throw new ThirdPlatformException("get game url failed");
    }

    private ThirdUserUpdateParam buildUpdateUserInfoParam(DockUser user) {
        ThirdUserUpdateParam param = new ThirdUserUpdateParam(user.getMerchantId(), user.getMemberId(), user.getPlatformCode());
        param.setThirdUserId(user.getThirdUserId());
        param.setThirdUserName(user.getThirdUserName());
        param.setThirdUserPasswd(user.getThirdPassword());
        param.setOpenId(user.getOpenId());
        param.setLastLoginTime(LocalDateTime.now());
        return param;
    }

    private DockCreateUser buildCreateUserParam(DockGetGameUrl dto) {
        DockCreateUser dc = new DockCreateUser();
        dc.setMerchantId(dto.getMerchantId());
        dc.setMemberId(dto.getMemberId());
        dc.setPlatformCode(dto.getPlatformCode());
        dc.setCurrencyEnum(dto.getCurrencyEnum());
        dc.setIp(dto.getIp());
        return dc;
    }

    private JILIResponse<String> execGetGameUrlRequest(DockGetGameUrl dto, JILIRequestConfig requestConfig) {
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(dto.getPlatformCode());
        JILIHttpRequestTemplate requestTemplate = new JILIHttpRequestTemplate(requestConfig, platformConfig);

        String platform = "web";
        if (dto.getDeviceEnum() != DeviceEnum.PC) {
            platform = "app";
        }
        final String defaultLang = LangEnum.EN_US.code;
        return requestTemplate.api(JILIApiEnum.LOGIN.getPath())
                .toGET()
                .addParameter("Account", dto.getThirdUserName())
                .addParameter("GameId", dto.getThirdGameId())
                .addParameter("Lang", ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang))
                .addParameter("platform", platform)
                .addParameter("HomeUrl", dto.getLobbyUrl())
                .toBeanAndCall(new TypeReference<JILIResponse<String>>() {
                },false);
    }

    @Override
    public String getDemoGameUrl(DockGetGameUrl param) {
        JILIRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(param.getPlatformCode(), param.getCurrencyEnum(), JILIRequestConfig.class);
        final String defaultLang = LangEnum.EN_US.code;
        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, param.getLang(), defaultLang);

        return requestConfig.getDemoGameUrl() + "/plusplayer/PlusTrial/" + param.getThirdGameId() + "/"+ thirdLang;
    }
}
