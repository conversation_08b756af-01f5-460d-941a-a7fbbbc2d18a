package com.wd.lottery.module.third.dock.zbridge.joker;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.joker.common.JOKERHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.joker.common.JOKERRequestConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;


@Slf4j
@Component(BridgeConstant.JOKER_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class JOKERGetGameServiceStrategy extends AbstractGetGameStrategy {

    private final Set<String>  domainUrlSet = new HashSet<>();

    List<String > gameTypes = Arrays.asList("Slot","Fishing");

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        JOKERRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, JOKERRequestConfig.class);
        JOKERHttpRequestTemplate requestTemplate = new JOKERHttpRequestTemplate(requestConfig);
        GameRes response = requestTemplate
                .toPOST()
                .addParameter("Method", "ListGames")
                .toCustomObject(GameRes.class);
        log.debug("http getGameList response:{} ",response);

        List<Game> listGames = response.getListGames();
        if(CollectionUtil.isNotEmpty(listGames)){
            return parseAndFilterThirdGame( listGames, platformCode);
        }
        return null;
    }

    private List<DockGame> parseAndFilterThirdGame( List<Game> listGames ,  String platformCode) {
        List<DockGame> resultList = new ArrayList<>(100);
        DockGame dockGame = null;
        for (Game game : listGames) {
            dockGame = new DockGame();
            String gameType = game.getGameType();
            if (gameTypes.contains(gameType)) {
                if ("Slot".equals(gameType)) {
                    dockGame.setGameCategoryEnum(GameCategoryEnum.SLOT);
                } else if ("Fishing".equals(gameType)) {
                    dockGame.setGameCategoryEnum(GameCategoryEnum.FISH);
                } else {
                    continue;
                }
                dockGame.setPlatformCode(platformCode);
                dockGame.setThirdGameId(game.getGameCode());
                dockGame.setGameName(game.getGameName());
                dockGame.setGameCode(game.getGameCode());
                resultList.add(dockGame);
            }
        }
        return resultList;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        JOKERRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), JOKERRequestConfig.class);
        JOKERHttpRequestTemplate requestTemplate = new JOKERHttpRequestTemplate(requestConfig);
        //打开游戏时,注册域名
        regDomain(dto , dto.getLobbyUrl());
        GameUrlRes response = requestTemplate
                .addParameter("Method", "PLAY")
                .addParameter("Username", dto.getThirdUserName())
                .toCustomObject(GameUrlRes.class);
        log.debug("http getOpenGameUrl response:{} ",response);
        String token = response.getToken();

        final String defaultLang = "en";
        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang);

        String gameUrl = String.format(requestConfig.getGameUrl(),token,dto.getGameCode(),dto.getLobbyUrl(),false, thirdLang);
        log.debug("joker gameUrl:{}",gameUrl);
        return gameUrl;
    }

    /**
     *
     * 注册域名
     * <AUTHOR>
     * @Date 16:16 2024/9/14
     **/
    public void regDomain(DockGetGameUrl dto , String lobbyUrl) {
        log.debug("register domain:{}", lobbyUrl);
        String domainUrl = lobbyUrl.split("//")[1];
        if(!domainUrlSet.contains(domainUrl)){
            JOKERRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), JOKERRequestConfig.class);
            JOKERHttpRequestTemplate requestTemplate = new JOKERHttpRequestTemplate(requestConfig);
            StatusRes response = requestTemplate
                    .addParameter("Method", "RDS")
                    .addParameter("Domain", domainUrl)
                    .toCustomObject(StatusRes.class);
            log.debug("http regDomain response:{} ",response);
            if(!Constants.OK.equals(response.getStatus())){
                log.warn("joker request regDomain method fail :{}",lobbyUrl);
            }else{
                domainUrlSet.add(domainUrl);
            }
        }
    }

    @Data
    public static class GameRes{
        @JsonProperty("ListGames")
       private List<Game> listGames;
    }
    @Data
    public static class Game{
        //游戏类型：Slot、Fishing、E-Casino、
        //Single Player、Multiplayer v.v
        @JsonProperty("GameType")
        private String gameType;
        //游戏别名
        @JsonProperty("GameCode")
        private String gameCode;
        //游戏名称
        @JsonProperty("GameName")
        private String gameName;
        //游戏次序
        @JsonProperty("Order")
        private String order;
        //横向模式下的游戏图像
        @JsonProperty("Image1")
        private String image1;
        @JsonProperty("Image2")
        //纵向模式下的游戏图像
        private String image2;
    }

    @Data
    public static class GameUrlRes{
        @JsonProperty("Token")
        private String token;
        @JsonProperty("Username")
        private String username;
    }

    @Data
    public static class StatusRes{
        @JsonProperty("Status")
        private String status;
    }

}
