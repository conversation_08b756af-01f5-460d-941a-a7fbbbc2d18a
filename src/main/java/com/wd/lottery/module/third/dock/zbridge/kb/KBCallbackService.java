package com.wd.lottery.module.third.dock.zbridge.kb;

import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.common.util.JavaxValidationUtil;
import com.wd.lottery.common.util.Md5Util;
import com.wd.lottery.module.third.constants.BetOrderWinTypeEnum;
import com.wd.lottery.module.third.constants.ThirdBetOrderStatusEnum;
import com.wd.lottery.module.third.constants.ThirdRedisConstants;
import com.wd.lottery.module.third.dock.zbridge.kb.common.KBCallbackException;
import com.wd.lottery.module.third.dock.zbridge.kb.common.KBConvertUtil;
import com.wd.lottery.module.third.dock.zbridge.kb.common.KBResCodeEnum;
import com.wd.lottery.module.third.dock.zbridge.kb.common.KBTokenContent;
import com.wd.lottery.module.third.dock.zbridge.kb.param.*;
import com.wd.lottery.module.third.dock.zbridge.kb.param.*;
import com.wd.lottery.module.third.dock.zbridge.kb.res.KBAuthRes;
import com.wd.lottery.module.third.dock.zbridge.kb.res.KBTxRes;
import com.wd.lottery.module.third.dto.ThirdUserDTO;
import com.wd.lottery.module.third.entity.ThirdSeamlessBetOrderEntity;
import com.wd.lottery.module.third.service.ThirdSiteUserInnerService;
import com.wd.lottery.module.third.service.inner.ThirdSeamlessBetOrderInnerService;
import com.wd.lottery.module.third.service.inner.ThirdSeamlessWalletInnerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 *
 * <p> Created on 2024/12/18.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class KBCallbackService {

    private static final String KB_SESSION_BET_ORDER_FLAG = "sessionBetOrder";
    private static final String KB_BET_AND_SETTLED_ORDER_FLAG = "betAndSettledOrder";

    private final KBTokenService kbTokenService;
    private final ThirdSeamlessBetOrderInnerService betOrderInnerService;
    private final ThirdSeamlessWalletInnerService walletInnerService;
    private final ThirdSiteUserInnerService thirdSiteUserInnerService;
    private final StringRedisTemplate stringRedisTemplate;

    public KBCallbackService(KBTokenService kbTokenService,
                             ThirdSeamlessBetOrderInnerService betOrderInnerService,
                             ThirdSeamlessWalletInnerService walletInnerService,
                             ThirdSiteUserInnerService thirdSiteUserInnerService,
                             StringRedisTemplate stringRedisTemplate) {
        this.kbTokenService = kbTokenService;
        this.betOrderInnerService = betOrderInnerService;
        this.walletInnerService = walletInnerService;
        this.thirdSiteUserInnerService = thirdSiteUserInnerService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 回调 验证用户
     *
     * @param authParam 回调参数
     * @return 用户信息
     */
    public KBAuthRes auth(KBCallbackParam authParam) throws Exception {

        // validate param
        validateRequiredParam(authParam, "auth");
        KBTokenContent tokenContent = verifyToken(authParam.getToken());

        String platformCode = tokenContent.getPlatformCode();
        CurrencyEnum currency = tokenContent.getCurrency();

        ThirdUserDTO thirdSiteUser = thirdSiteUserInnerService.getThirdSiteUser(platformCode, tokenContent.getThirdUserId());
        // get balance
        long balance = walletInnerService.getBalanceFromMaster(platformCode, tokenContent.getThirdUserId());

        KBAuthRes res = new KBAuthRes();
        res.setStatus(KBResCodeEnum.SUCCESS);
        res.setCurrency(KBConvertUtil.toThirdCurrency(platformCode, currency));
        res.setBalance(KBConvertUtil.toThirdMoney(platformCode, currency, balance));
        res.setUsername(thirdSiteUser.getThirdUserName());

        return res;
    }

    /**
     * 下注并结算（订单已结算，只回调一次）
     *
     * @param betParam 回调参数
     * @return 回调结果
     */
    public KBTxRes bet(KBBetAndSettledParam betParam) {
        // validate param
        validateRequiredParam(betParam, "bet");
        // verify token
        KBTokenContent tokenContent = verifyToken(betParam.getToken());
        // verify bet param
        validateBetParam(betParam, tokenContent);

        // 校验结算时间
        validateSettleTime(betParam.getWagersTime());

        // 构建订单
        ThirdSeamlessBetOrderEntity thirdOrder = buildBetAndSettleOrder(betParam, tokenContent);
        // 检查余额
        long oldBalance = getBalanceByToken(tokenContent);
        if (oldBalance < KBConvertUtil.toSysMoney(tokenContent.getPlatformCode(), tokenContent.getCurrency(), betParam.getBetAmount())) {
            KBTxRes res = buildKBTxRes(oldBalance, tokenContent);
            res.setStatus(KBResCodeEnum.NOT_ENOUGH_BALANCE);
            return res;
        }
        try {
            long newBalance = betOrderInnerService.addSettledOrder(thirdOrder);
            return buildKBTxRes(newBalance, tokenContent);
        } catch (Exception e) {
            log.debug("bet failed", e);
            handleException(e);
        }
        // 未知异常
        throw new KBCallbackException(KBResCodeEnum.OTHER_ERROR);
    }

    /**
     * 取消结算订单
     *
     * @param cancelBetParam 取消订单参数
     * @return 取消结果
     */
    public KBTxRes cancelBet(KBCancelBetParam cancelBetParam) {
        // validate param
        validateRequiredParam(cancelBetParam, "cancelBet");
        // verify token
        KBTokenContent tokenContent = verifyToken(cancelBetParam.getToken());
        // 校验押注参数
        validateBetParam(cancelBetParam, tokenContent);
        // 校验三方用户
        validateField(StringUtils.equals(cancelBetParam.getUserId(), tokenContent.getThirdUserId()), "userId must be equal, expect: " + tokenContent.getThirdUserId(), cancelBetParam.getUserId());

        // query his order
        ThirdSeamlessBetOrderEntity betOrder = betOrderInnerService.getPlatformBetOrderByOrderNum(tokenContent.getPlatformCode(), String.valueOf(cancelBetParam.getRound()));
        if (betOrder == null) {
            log.error("bet order not found, orderNum: {}", cancelBetParam.getRound());
            throw new KBCallbackException(KBResCodeEnum.ROUND_NOT_FOUND);
        }
        String remark = betOrder.getRemark();
        if (!KB_BET_AND_SETTLED_ORDER_FLAG.equals(remark)) {
            log.error("can only cancel bet and settled order, orderNum: {}, remark: {}", cancelBetParam.getRound(), remark);
            throw new KBCallbackException(KBResCodeEnum.INVALID_PARAMETER);
        }
        // 撤销订单用户必须匹配
        String thirdUserName = betOrder.getThirdUserName();
        validateField(StringUtils.equals(cancelBetParam.getUserId(), thirdUserName), "userId must be equal, expect: " + thirdUserName, cancelBetParam.getUserId());
        // 已结算订单不允许撤销
        ThirdBetOrderStatusEnum orderStatusEnum = betOrder.getOrderStatusEnum();
        if (orderStatusEnum == ThirdBetOrderStatusEnum.COMPLETED || orderStatusEnum == ThirdBetOrderStatusEnum.CANCELED) {
            throw new KBCallbackException(KBResCodeEnum.CANNOT_BE_CANCELED);
        }
        // 未知异常
        throw new KBCallbackException(KBResCodeEnum.OTHER_ERROR);
    }

    /**
     * 投注或结算, 内部可能包含多个订单修改，同一事物内处理
     *
     * @param sessionBetParam 投注结算参数
     * @return 投注结果
     */
    @Transactional(rollbackFor = Exception.class)
    public KBTxRes sessionBet(KBSessionBetParam sessionBetParam) {
        // check param
        validateRequiredParam(sessionBetParam, "sessionBet");
        // verify token
        KBTokenContent tokenContent = verifyToken(sessionBetParam.getToken());
        // 校验参数
        validateSessionBetParam(sessionBetParam, tokenContent);

        try {
            // 1 下注， 2 结算
            Integer type = sessionBetParam.getType();
            if (Integer.valueOf(1).equals(type)) {
                return doSessionBet(sessionBetParam, tokenContent);
            }
            if (Integer.valueOf(2).equals(type)) {
                return doSessionSettle(sessionBetParam, tokenContent);
            }
        } catch (Exception e) {
            log.error("session bet failed", e);
            handleException(e);
        }
        // 未知类型，参数错误
        throw new KBCallbackException(KBResCodeEnum.INVALID_PARAMETER);
    }

    /**
     * 结算回调
     *
     * @param sessionBetParam 结算参数
     * @param tokenContent    玩家token
     * @return 回调结果 {@link KBTxRes}
     */
    private KBTxRes doSessionSettle(KBSessionBetParam sessionBetParam, KBTokenContent tokenContent) {

        List<Long> betOrders = sessionBetParam.getBetOrder();
        if (CollectionUtils.isEmpty(betOrders)) {
            log.error("session settle callback bet order is empty");
            throw new KBCallbackException(KBResCodeEnum.INVALID_PARAMETER);
        }
        // 校验结算时间
        validateSettleTime(sessionBetParam.getWagersTime());
        // 构建结算订单
        ThirdSeamlessBetOrderEntity settleOrder = buildSessionSettleOrder(sessionBetParam, tokenContent);
        if (betOrders.size() == 1) {
            settleOrder.setOrderNum(String.valueOf(betOrders.get(0)));
            // 结算单个订单，直接在原订单上更新派彩金额
            long balance = betOrderInnerService.addPayoutOrder(settleOrder);
            return buildKBTxRes(balance, tokenContent);
        }
        // 结算多个订单
        return settleMultiOrders(settleOrder, betOrders, tokenContent);
    }

    private KBTxRes settleMultiOrders(ThirdSeamlessBetOrderEntity settleOrder, List<Long> betOrders, KBTokenContent tokenContent) {
        // 逐个结算前置订单
        for (Long orderNum : betOrders) {
            ThirdSeamlessBetOrderEntity refOrder = new ThirdSeamlessBetOrderEntity();
            refOrder.setOrderNum(String.valueOf(orderNum));
            refOrder.setPlatformCode(settleOrder.getPlatformCode());
            refOrder.setSettleTime(settleOrder.getSettleTime());
            refOrder.setWinMoney(0L);

            betOrderInnerService.addPayoutOrder(refOrder);
        }
        // 前置订单已扣减投注金额
        settleOrder.setBetMoney(0L);
        settleOrder.setValidBet(0L);
        settleOrder.setBetTime(settleOrder.getSettleTime());
        // 增加结算订单
        long balance = betOrderInnerService.addSettledOrder(settleOrder);
        return buildKBTxRes(balance, tokenContent);
    }

    /**
     * 下注回调
     *
     * @param sessionBetParam 下注参数
     * @param tokenContent    玩家token
     * @return 回调结果 {@link KBTxRes}
     */
    private KBTxRes doSessionBet(KBSessionBetParam sessionBetParam, KBTokenContent tokenContent) {
        // 投建投注订单
        ThirdSeamlessBetOrderEntity betOrder = buildSessionBetOrder(sessionBetParam, tokenContent);
        // 校验余额
        long oldBalance = getBalanceByToken(tokenContent);
        if (oldBalance < betOrder.getBetMoney()) {
            KBTxRes res = buildKBTxRes(oldBalance, tokenContent);
            res.setStatus(KBResCodeEnum.NOT_ENOUGH_BALANCE);
            return res;
        }
        long newBalance = betOrderInnerService.addBetOrder(betOrder);
        return buildKBTxRes(newBalance, tokenContent);
    }

    /**
     * 取消 下注/结算
     *
     * @param cancelSessionBetParam 取消下注参数
     * @return 回调结果 {@link KBTxRes}
     */
    public KBTxRes cancelSessionBet(KBCancelSessionBetParam cancelSessionBetParam) {
        // check param
        validateRequiredParam(cancelSessionBetParam, "cancelSessionBet");
        // verify token
        KBTokenContent tokenContent = verifyToken(cancelSessionBetParam.getToken());

        // 校验押注参数
        validateCancelSessionBetParam(cancelSessionBetParam, tokenContent);

        // query his order
        ThirdSeamlessBetOrderEntity betOrder = betOrderInnerService.getPlatformBetOrderByOrderNum(tokenContent.getPlatformCode(), String.valueOf(cancelSessionBetParam.getRound()));
        if (betOrder == null) {
            log.error("bet order not found, orderNum: {}", cancelSessionBetParam.getRound());
            throw new KBCallbackException(KBResCodeEnum.ROUND_NOT_FOUND);
        }
        String remark = betOrder.getRemark();
        if (!KB_SESSION_BET_ORDER_FLAG.equals(remark)) {
            log.error("can only cancel session order, orderNum: {}, remark: {}", cancelSessionBetParam.getRound(), remark);
            throw new KBCallbackException(KBResCodeEnum.INVALID_PARAMETER);
        }
        // 撤销订单用户必须匹配
        String thirdUserName = betOrder.getThirdUserName();
        validateField(StringUtils.equals(cancelSessionBetParam.getUserId(), thirdUserName), "userId must be equal, expect: " + thirdUserName, cancelSessionBetParam.getUserId());

        // 已结算不允许取消
        ThirdBetOrderStatusEnum statusEnum = betOrder.getOrderStatusEnum();
        if(statusEnum == ThirdBetOrderStatusEnum.COMPLETED) {
            log.error("order already completed, orderNum: {}, remark: {}", cancelSessionBetParam.getRound(), remark);
            throw new KBCallbackException(KBResCodeEnum.CANNOT_BE_CANCELED);
        }
        try {
            long balance = betOrderInnerService.rollbackOrder(tokenContent.getPlatformCode(), String.valueOf(cancelSessionBetParam.getRound()));
            return buildKBTxRes(balance, tokenContent);
        } catch (Exception e) {
            log.debug("cancel session bet failed", e);
            handleException(e);
        }
        // 未知异常
        throw new KBCallbackException(KBResCodeEnum.OTHER_ERROR);
    }

    public KBTxRes getBalance(KBCallbackParam callbackParam) {
        // check param
        validateRequiredParam(callbackParam, "getBalance");
        // check token
        KBTokenContent tokenContent = verifyToken(callbackParam.getToken());
        long balance = getBalanceByToken(tokenContent);

        return buildKBTxRes(balance, tokenContent);
    }

    public KBTxRes reward(KBRewardParam rewardParam) {
        // check param
        validateRequiredParam(rewardParam, "reward");
        // verify token
        KBTokenContent tokenContent = verifyToken(rewardParam.getToken());
        ThirdSeamlessBetOrderEntity entity = buildRewardOrder(rewardParam, tokenContent);
        try {
            long newBalance = betOrderInnerService.addSettledOrder(entity);
            return buildKBTxRes(newBalance, tokenContent);
        } catch (Exception e) {
            log.error("reward failed", e);
            handleException(e);
        }
        throw new KBCallbackException(KBResCodeEnum.ALREADY_ACCEPTED);
    }

    /**
     * 根据token 获取余额
     *
     * @param tokenContent 玩家token
     * @return 单一钱包余额
     */
    private long getBalanceByToken(KBTokenContent tokenContent) {
        return walletInnerService.getBalanceFromMaster(tokenContent.getPlatformCode(), tokenContent.getThirdUserId());
    }

    /**
     * 构造交易返回结果
     *
     * @param balance      会员余额
     * @param tokenContent 会员 token 内容
     * @return 交易结果 {@link KBTxRes}
     */
    private KBTxRes buildKBTxRes(long balance, KBTokenContent tokenContent) {

        String platformCode = tokenContent.getPlatformCode();
        CurrencyEnum currency = tokenContent.getCurrency();

        KBTxRes res = new KBTxRes();
        res.setStatus(KBResCodeEnum.SUCCESS);
        res.setUsername(tokenContent.getThirdUserId());
        res.setBalance(KBConvertUtil.toThirdMoney(platformCode, currency, balance));
        res.setCurrency(KBConvertUtil.toThirdCurrency(platformCode, currency));

        return res;
    }

    /**
     * 构建下注并结算订单
     */
    private ThirdSeamlessBetOrderEntity buildBetAndSettleOrder(KBBetAndSettledParam betParam, KBTokenContent tokenContent) {
        String platformCode = tokenContent.getPlatformCode();
        CurrencyEnum currency = tokenContent.getCurrency();

        ThirdSeamlessBetOrderEntity entity = new ThirdSeamlessBetOrderEntity();
        entity.setPlatformCode(platformCode);
        entity.setThirdUserName(tokenContent.getThirdUserId());
        // 订单号
        entity.setOrderNum(String.valueOf(betParam.getRound()));
        // gameId
        entity.setThirdGameId(String.valueOf(betParam.getGame()));
        // 下注金额

        long betMoney = KBConvertUtil.toSysMoney(platformCode, currency, betParam.getBetAmount());
        entity.setBetMoney(betMoney);
        entity.setValidBet(betMoney);
        // 派奖金额
        entity.setWinMoney(KBConvertUtil.toSysMoney(platformCode, currency, betParam.getWinloseAmount()));
        // 结算时间
        LocalDateTime settleTime = Instant.ofEpochSecond(betParam.getWagersTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
        entity.setBetTime(settleTime);
        entity.setSettleTime(settleTime);
        entity.setOrderStatusEnum(ThirdBetOrderStatusEnum.COMPLETED);
        entity.setWinTypeEnum(BetOrderWinTypeEnum.NORMAL);
        // 备注
        entity.setRemark(KB_BET_AND_SETTLED_ORDER_FLAG);

        return entity;
    }

    /**
     * 构建预扣款下注订单
     *
     * @param sessionBetParam 预扣款订单参数
     * @param tokenContent    token 用户信息
     */
    public ThirdSeamlessBetOrderEntity buildSessionBetOrder(KBSessionBetParam sessionBetParam, KBTokenContent tokenContent) {
        String platformCode = tokenContent.getPlatformCode();
        CurrencyEnum currency = tokenContent.getCurrency();

        ThirdSeamlessBetOrderEntity entity = new ThirdSeamlessBetOrderEntity();
        entity.setPlatformCode(platformCode);
        entity.setThirdUserName(tokenContent.getThirdUserId());
        // 订单号
        entity.setOrderNum(String.valueOf(sessionBetParam.getRound()));
        // 父订单号
        entity.setParentOrderNum(String.valueOf(sessionBetParam.getSessionId()));
        // gameId
        entity.setThirdGameId(String.valueOf(sessionBetParam.getGame()));

        // 下注金额
        long betMoney = KBConvertUtil.toSysMoney(platformCode, currency, sessionBetParam.getBetAmount());
        long preserve = KBConvertUtil.toSysMoney(platformCode, currency, sessionBetParam.getPreserve());
        // 预扣款
        if (preserve > 0) {
            betMoney = preserve;
        }
        entity.setBetMoney(betMoney);
        entity.setValidBet(betMoney);
        // 派奖金额
        entity.setWinMoney(0L);

        // 投注时间
        LocalDateTime betTime = Instant.ofEpochSecond(sessionBetParam.getWagersTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
        entity.setBetTime(betTime);

        entity.setOrderStatusEnum(ThirdBetOrderStatusEnum.PENDING);
        entity.setWinTypeEnum(BetOrderWinTypeEnum.NORMAL);

        // 备注
        entity.setRemark(KB_SESSION_BET_ORDER_FLAG);

        return entity;
    }

    /**
     * 构建预扣款结算订单
     *
     * @param sessionBetParam 预扣款订单参数
     * @param tokenContent    token 用户信息
     */
    public ThirdSeamlessBetOrderEntity buildSessionSettleOrder(KBSessionBetParam sessionBetParam, KBTokenContent tokenContent) {
        String platformCode = tokenContent.getPlatformCode();
        CurrencyEnum currency = tokenContent.getCurrency();

        ThirdSeamlessBetOrderEntity entity = new ThirdSeamlessBetOrderEntity();
        entity.setPlatformCode(platformCode);
        entity.setThirdUserName(tokenContent.getThirdUserId());
        // 订单号
        entity.setOrderNum(String.valueOf(sessionBetParam.getRound()));
        // 父订单号
        entity.setParentOrderNum(String.valueOf(sessionBetParam.getSessionId()));
        // gameId
        entity.setThirdGameId(String.valueOf(sessionBetParam.getGame()));

        long betMoney = KBConvertUtil.toSysMoney(platformCode, currency, sessionBetParam.getBetAmount());
        // 派彩金额
        long winMoney = KBConvertUtil.toSysMoney(platformCode, currency, sessionBetParam.getWinloseAmount());
        long preserve = KBConvertUtil.toSysMoney(platformCode, currency, sessionBetParam.getPreserve());
        // 预扣款
        if (preserve > 0) {
            // 派彩金额
            winMoney = preserve - betMoney + winMoney;
        }
        entity.setWinMoney(winMoney);
        // 结算时间
        LocalDateTime settleTime = Instant.ofEpochSecond(sessionBetParam.getWagersTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
        entity.setSettleTime(settleTime);

        entity.setOrderStatusEnum(ThirdBetOrderStatusEnum.COMPLETED);
        entity.setWinTypeEnum(BetOrderWinTypeEnum.NORMAL);
        entity.setRemark(KB_SESSION_BET_ORDER_FLAG);

        return entity;
    }

    private ThirdSeamlessBetOrderEntity buildRewardOrder(KBRewardParam rewardParam, KBTokenContent tokenContent) {
        ThirdSeamlessBetOrderEntity entity = new ThirdSeamlessBetOrderEntity();
        entity.setPlatformCode(tokenContent.getPlatformCode());
        entity.setThirdUserName(tokenContent.getThirdUserId());
        // 订单号使用 requestId md5 hash, 避免同个请求多次插入
        entity.setOrderNum(Md5Util.getMd5HexLowerCase(rewardParam.getReqId()));
        entity.setRefOrderNum(String.valueOf(rewardParam.getOrderid()));
        // gameId
        entity.setThirdGameId(String.valueOf(rewardParam.getGame()));

        // 下注金额
        long betMoney = 0L;
        entity.setBetMoney(betMoney);
        entity.setValidBet(betMoney);
        // 派彩金额
        long winMoney = KBConvertUtil.toSysMoney(tokenContent.getPlatformCode(), tokenContent.getCurrency(), rewardParam.getAmount());
        entity.setWinMoney(winMoney);

        // 结算时间
        LocalDateTime settleTime = Instant.ofEpochSecond(rewardParam.getWagersTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
        entity.setSettleTime(settleTime);

        entity.setOrderStatusEnum(ThirdBetOrderStatusEnum.COMPLETED);
        entity.setWinTypeEnum(BetOrderWinTypeEnum.JACKPOT);
        entity.setRemark("reward");

        return entity;
    }

    /**
     * 参数完整性校验
     *
     * @param callbackParam 回调参数
     * @param action        回调动作
     */
    private void validateRequiredParam(KBCallbackParam callbackParam, String action) {
        if (!JavaxValidationUtil.isValid(callbackParam)) {
            log.error("validate callback param failed, action: {}, param: {}", action, JacksonUtil.toJSONString(callbackParam));
            throw new KBCallbackException(KBResCodeEnum.INVALID_PARAMETER);
        }
        String key = ThirdRedisConstants.KB_IDEMPOTENT_REQUEST_ID + Md5Util.getMd5HexLowerCase(callbackParam.getReqId());
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(key))) {
            log.warn("requestId already exists, action: {}, param: {}", action, JacksonUtil.toJSONString(callbackParam));
            throw new KBCallbackException(KBResCodeEnum.ALREADY_ACCEPTED);
        }
        stringRedisTemplate.opsForValue().set(key, "1", 7, TimeUnit.DAYS);
    }

    /**
     * Token校验
     *
     * @param token 玩家Token
     * @return Token 内容
     */
    private KBTokenContent verifyToken(String token) {
        // token cannot be null
        validateField(StringUtils.isNotBlank(token), "token cannot be null", token);
        try {
            return kbTokenService.verifyToken(token);
        } catch (Exception e) {
            log.error("verify token failed: {}", token);
            throw new KBCallbackException(KBResCodeEnum.TOKEN_EXPIRED);
        }
    }

    /**
     * 校验下注参数
     *
     * @param betParam     下注参数
     * @param tokenContent token 内容
     */
    private void validateBetParam(KBBetCallbackParam betParam, KBTokenContent tokenContent) {
        // 1. 校验押注金额 >= 0
        validateField(betParam.getBetAmount().compareTo(BigDecimal.ZERO) >= 0, "bet amount not less than 0", betParam.getBetAmount());
        // 2. 校验派奖金额 >= 0
        validateField(betParam.getWinloseAmount().compareTo(BigDecimal.ZERO) >= 0, "winloss amount not less than 0", betParam.getWinloseAmount());
        // 3. 检验押注最大值
        BigDecimal maxValue = BigDecimal.valueOf(Integer.MAX_VALUE);
        validateField(betParam.getBetAmount().compareTo(maxValue) < 0, "bet amount can not bigger than inter max value", betParam.getBetAmount());
        // 4. 校验派奖最大值
        validateField(betParam.getWinloseAmount().compareTo(maxValue) < 0, "winloss amount can not bigger than inter max value", betParam.getWinloseAmount());
        // 5. 校验币种
        CurrencyEnum currency = tokenContent.getCurrency();
        String thirdCurr = KBConvertUtil.toThirdCurrency(tokenContent.getPlatformCode(), currency);
        validateField(StringUtils.equals(thirdCurr, betParam.getCurrency()), "currency must be equal, expect: " + thirdCurr, betParam.getCurrency());

        // 6. 校验游戏ID
        String thirdGameId = tokenContent.getThirdGameId();
        validateField(StringUtils.equals(thirdGameId, String.valueOf(betParam.getGame())), "gameId must be equal, expect: " + thirdGameId, betParam.getGame());
    }

    /**
     * 校验SessionBetParam
     *
     * @param sessionBetParam   下注、结算参数
     * @param tokenContent      token 内容
     */
    private void validateSessionBetParam(KBSessionBetParam sessionBetParam, KBTokenContent tokenContent) {
        // 校验下注参数
        validateBetParam(sessionBetParam, tokenContent);
        // 1. 校验预扣减金额 >= 0
        validateField(sessionBetParam.getPreserve().compareTo(BigDecimal.ZERO) >= 0, "preserve amount not less than 0", sessionBetParam.getPreserve());
        // 2. 校验预扣减小于 Integer.MAX_VALUE
        validateField(sessionBetParam.getPreserve().compareTo(BigDecimal.valueOf(Integer.MAX_VALUE)) < 0, "preserve amount can not bigger than inter max value", sessionBetParam.getPreserve());
    }

    /**
     * 校验取消注单参数
     *
     * @param cancelSessionBetParam 取消注单参数
     * @param tokenContent token 内容
     */
    private void validateCancelSessionBetParam(KBCancelSessionBetParam cancelSessionBetParam, KBTokenContent tokenContent) {
        // 校验下注参数
        validateBetParam(cancelSessionBetParam, tokenContent);
        // 1. 校验预扣减金额 >= 0
        validateField(cancelSessionBetParam.getPreserve().compareTo(BigDecimal.ZERO) >= 0, "preserve amount not less than 0", cancelSessionBetParam.getPreserve());
        // 2. 校验预扣减小于 Integer.MAX_VALUE
        validateField(cancelSessionBetParam.getPreserve().compareTo(BigDecimal.valueOf(Integer.MAX_VALUE)) < 0, "preserve amount can not bigger than inter max value", cancelSessionBetParam.getPreserve());
        // 3. 校验 userId
        validateField(StringUtils.equals(tokenContent.getThirdUserId(), cancelSessionBetParam.getUserId()), "userId must be equal, expect: " + tokenContent.getThirdUserId(), cancelSessionBetParam.getUserId());
    }

    private void validateSettleTime(Long settleTime) {
        if (Objects.isNull(settleTime)) {
            log.warn("settle time cannot be null");
            throw new KBCallbackException(KBResCodeEnum.INVALID_PARAMETER);
        }
        long nowSeconds = Instant.now().plusSeconds(30).toEpochMilli() / 1000;
        long hisSeconds = Instant.now().minus(30, ChronoUnit.DAYS).toEpochMilli() / 1000;
        validateField( settleTime > hisSeconds && settleTime < nowSeconds, "settle time must in 30 days", Instant.ofEpochSecond(settleTime));

    }

    /**
     * 处理异常
     *
     * @param e 业务异常
     */
    private static void handleException(Exception e) {
        if (e instanceof DuplicateKeyException) {
            throw new KBCallbackException(KBResCodeEnum.ALREADY_ACCEPTED);
        }
        if (e instanceof ApiException) {
            ApiException ae = (ApiException) e;
            IErrorCode errorCode = ae.getErrorCode();
            if (CommonCode.SEAMLESS_BET_ORDER_NOT_EXISTS.equals(errorCode)) {
                throw new KBCallbackException(KBResCodeEnum.ROUND_NOT_FOUND);
            }
            if (CommonCode.SEAMLESS_BET_ORDER_ACCEPTED.equals(errorCode)) {
                throw new KBCallbackException(KBResCodeEnum.ALREADY_ACCEPTED);
            }
            if(CommonCode.SEAMLESS_BET_ORDER_STATUS_NOT_MATCH.equals(errorCode)) {
                throw new KBCallbackException(KBResCodeEnum.OTHER_ERROR);
            }
            // 余额不足
            if (CommonCode.SEAMLESS_WALLET_NOT_ENOUGH_MONEY.equals(errorCode)) {
                throw new KBCallbackException(KBResCodeEnum.NOT_ENOUGH_BALANCE);
            }
        }
        // 未知异常
        throw new KBCallbackException(KBResCodeEnum.OTHER_ERROR);
    }

    private void validateField(boolean condition, String expectMsg, Object actualValue){
        if (!condition) {
            log.warn("invalid filed, expectMsg: {}, actual value: {}", expectMsg, actualValue);
            throw new KBCallbackException(KBResCodeEnum.INVALID_PARAMETER);
        }
    }
}
