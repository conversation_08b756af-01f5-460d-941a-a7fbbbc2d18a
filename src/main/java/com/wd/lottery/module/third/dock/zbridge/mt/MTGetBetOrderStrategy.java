package com.wd.lottery.module.third.dock.zbridge.mt;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.mt.common.MTAPIEnum;
import com.wd.lottery.module.third.dock.zbridge.mt.common.MTHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.mt.common.MTRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.mt.res.MTResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * 游戏注单
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.MT_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class MTGetBetOrderStrategy extends AbstractGetBetOrderStrategy {

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        log.debug("MT:游戏注单:{}", JSONUtil.toJsonStr(flag));
        MTRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), MTRequestConfig.class);
        MTHttpRequestTemplate requestTemplate = new MTHttpRequestTemplate(requestConfig, flag.getCurrencyEnum());
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(flag.getPlatformCode());
        // 拉单参数
        String timeZone = configJson.getPlatformTimeZone();
        DateTimeFormatter paramPattern = DateTimeFormatter.ofPattern(configJson.getRequestTimePattern());
        // String lastFlag = flag.getLastFlag();
        String start = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
        String end = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
        // 拉取注单
        OrderList response = requestTemplate.toPOST()
                .api(MTAPIEnum.GET_BET_ORDER.getPath())
                // .addParameter("recordID", lastFlag)
                .addParameter("gameType", StrUtil.EMPTY)
                .addParameter("startTime", start)
                .addParameter("endTime", end)
                .addParameter("currency", "")
                .toBeanAndCall(OrderList.class);
        log.debug("MT:游戏注单响应:{}-({}-{})-{}", ZoneId.systemDefault(), start, end, JSONUtil.toJsonStr(response));
        if (CollUtil.isEmpty(response.getTransList())) {
            flag.setFinished(true);
            return ListUtil.empty();
        }
        // 游戏记录最多一次回传25000条记录
        List<OrderList.BetOrder> betOrders = response.getTransList();
        flag.setFinished(CollUtil.size(betOrders) < 25000);
        OrderList.BetOrder last = CollUtil.getLast(betOrders);
        // flag.setLastFlag(Convert.toStr(Math.max(Long.parseLong(lastFlag), Long.parseLong(last.getRecordId()))));
        return CollStreamUtil.toList(betOrders, betOrder -> this.toDockBetOrder(flag.getPlatformCode(), configJson, betOrder));
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        log.debug("MT:对局详情:{}", JSONUtil.toJsonStr(dto));
        MTRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), MTRequestConfig.class);
        MTHttpRequestTemplate requestTemplate = new MTHttpRequestTemplate(requestConfig, dto.getCurrencyEnum());
        try {
            BetDetailUrl response = requestTemplate.toPOST()
                    .api(MTAPIEnum.GET_BET_ORDER_DETAIL.getPath())
                    .addParameter("rowID", dto.getOrderNo())
                    .toBeanAndCall(BetDetailUrl.class);
            return ObjectUtil.isNull(response) ? StrUtil.EMPTY : response.getUrl();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    /**
     * TO DOCK ORDER
     *
     * @param platformCode 平台码
     * @param configJson   平台配置
     * @param betOrder     游戏注单
     * @return {@link DockBetOrder}
     */
    private DockBetOrder toDockBetOrder(String platformCode, ThirdPlatformConfigDTO configJson, OrderList.BetOrder betOrder) {
        DockBetOrder dockBetOrder = new DockBetOrder();
        dockBetOrder.setPlatformCode(platformCode);
        dockBetOrder.setOrderNum(betOrder.getRecordId());
        dockBetOrder.setOrderNumParent(betOrder.getRecordId());
        dockBetOrder.setGameId(betOrder.getGameCode());
        dockBetOrder.setThirdUserName(betOrder.getPlayerName().toLowerCase());

        // BigDecimal moneyUnit = ObjectUtil.isNull(configJson.getMoneyUnit()) ? new BigDecimal(100) : new BigDecimal(configJson.getMoneyUnit());
        dockBetOrder.setBetMoney(super.toPlatformMoney(betOrder.getBetAmount()));
        dockBetOrder.setWinMoney(super.toPlatformMoney(betOrder.getWinAmount().subtract(betOrder.getRoomFee())));
        dockBetOrder.setValidBetMoney(super.toPlatformMoney(betOrder.getCommissionable()));

        String dateStr = betOrder.getGameDate();
        int dotIndex = dateStr.indexOf(".");
        dateStr = dotIndex > 0 ? dateStr.substring(0, dotIndex) : dateStr;
        LocalDateTime dateTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(configJson.getOrderTimePattern()));
        Date date = Date.from(dateTime.atZone(ZoneId.of(configJson.getPlatformTimeZone())).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime().toInstant(OffsetDateTime.now().getOffset()));
        dockBetOrder.setOrderTime(DateUtil.toLocalDateTime(date));
        return dockBetOrder;
    }

    /**
     * 游戏注单列表
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class OrderList extends MTResponse<Void> {
        private List<BetOrder> transList;

        /**
         * 游戏注单详情
         */
        @Data
        public static class BetOrder {
            @JsonProperty(value = "rowID")
            private String rowId;//（美天棋牌交易流水号）
            private String playerName;//（商户用户名称）
            private String gameDate;//（游戏时间）
            private String gameCode;//（游戏代码）
            private String period;//（游戏局ID）
            private BigDecimal betAmount;//（下注金额）
            private BigDecimal winAmount;//（赢得金额）
            private BigDecimal commissionable;//（有效投注量）
            private BigDecimal roomFee;//（房费）
            private BigDecimal income;//(赢得金额-下注金额-房费)
            private String timeZone;//（时区）
            @JsonProperty(value = "recordID")
            private String recordId;//（游戏记录ID）
            private String gameType;//（游戏类型：1表示百人场，2表示对战，3表示捕鱼，4表示街机）
            @JsonProperty(value = "progressive_share")
            private String progressiveShare;//（贡献彩金）
            @JsonProperty(value = "progressive_wins")
            private String progressiveWins;//（获得彩金）
            private String currency;//（币种）
        }
    }

    /**
     * 对局详情
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class BetDetailUrl extends MTResponse<Void> {
        private String url;
    }
}