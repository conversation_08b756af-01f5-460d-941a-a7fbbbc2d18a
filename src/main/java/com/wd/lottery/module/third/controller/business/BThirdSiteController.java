package com.wd.lottery.module.third.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.module.third.dto.ThirdSiteBalanceDTO;
import com.wd.lottery.module.third.dto.ThirdSiteGameDTO;
import com.wd.lottery.module.third.dto.ThirdSitePlatformDTO;
import com.wd.lottery.module.third.param.*;
import com.wd.lottery.module.third.param.*;
import com.wd.lottery.module.third.service.ThirdSiteBalanceInnerService;
import com.wd.lottery.module.third.service.ThirdSiteInnerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Description: 三方站点管理
 *
 * <p> Created on 2024/7/11.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Tag(name = "商户站点管理")
@RestController
@RequestMapping("${business-path}/${module-path.third}/site")
public class BThirdSiteController {

    private final ThirdSiteInnerService thirdSiteInnerService;
    private final ThirdSiteBalanceInnerService thirdSiteBalanceInnerService;

    public BThirdSiteController(ThirdSiteInnerService thirdSiteInnerService,
                                ThirdSiteBalanceInnerService thirdSiteBalanceInnerService) {
        this.thirdSiteInnerService = thirdSiteInnerService;
        this.thirdSiteBalanceInnerService = thirdSiteBalanceInnerService;
    }

    @Operation(summary = "查询商户三方平台")
    @GetMapping("listMerchantPlatform")
    public ApiResult<List<ThirdSitePlatformDTO>> listMerchantPlatform(ThirdSitePlatformQueryParam param) {
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        param.setMerchantId(merchantId);
        param.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        List<ThirdSitePlatformDTO> list = thirdSiteInnerService.listMerchantPlatform(param);
        return ApiResult.success(list);
    }

    @Operation(summary = "批量更新商户三方平台")
    @PostMapping("updateThirdSitePlatformConfig")
    public ApiResult<Boolean> updateThirdSitePlatformConfig(@RequestBody @Valid ThirdSitePlatformUpdateParam param){
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        param.setMerchantId(merchantId);
        thirdSiteInnerService.updateThirdSitePlatformBatch(param);
        return ApiResult.success(true);
    }

    @Operation(summary = "分页查询商户三方游戏")
    @GetMapping("getThirdGamePage")
    public ApiResult<Page<ThirdSiteGameDTO>> getThirdGamePage(ThirdSiteGameQueryParam param) {
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        param.setMerchantId(merchantId);
        param.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        Page<ThirdSiteGameDTO> gamePage = thirdSiteInnerService.getThirdGamePage(param);
        return ApiResult.success(gamePage);
    }

    @Operation(summary = "批量更新商户三方游戏")
    @PostMapping("updateMerchantGame")
    public ApiResult<Boolean> updateMerchantGame(@RequestBody @Valid ThirdSiteGameUpdateParam param) {
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        param.setMerchantId(merchantId);
        thirdSiteInnerService.updateThirdSiteGameBatch(param);
        return ApiResult.success(true);
    }

    @Operation(summary = "查询所有站点额度")
    @GetMapping("listAllSiteBalance")
    public ApiResult<List<ThirdSiteBalanceDTO>> listAllSiteBalance(){
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        boolean superAdminMerchant = AdminTokenInfoUtil.superAdminMerchant(merchantId);
        List<ThirdSiteBalanceDTO> dtos = thirdSiteBalanceInnerService.listAllThirdSiteBalance(merchantId, superAdminMerchant);

        return ApiResult.success(dtos);
    }

    @Operation(summary = "增加站点额度")
    @PostMapping("incSiteBalance")
    public ApiResult<Boolean> incSiteBalance(@RequestBody @Valid ThirdSiteBalanceParam param){
        return ApiResult.success(thirdSiteBalanceInnerService.incBalance(param.getMerchantId(), param.getAmount()));
    }

    @Operation(summary = "扣减站点额度")
    @PostMapping("decSiteBalance")
    public ApiResult<Boolean> decSiteBalance(@RequestBody @Valid ThirdSiteBalanceParam param){
        return ApiResult.success(thirdSiteBalanceInnerService.deductionBalance(param.getMerchantId(), param.getAmount()));
    }
    @Operation(summary = "设置预警额度")
    @PostMapping("setWarnBalance")
    public ApiResult<Boolean> setWarnBalance(@RequestBody @Valid ThirdSiteBalanceParam param){
        return ApiResult.success(thirdSiteBalanceInnerService.setWarnBalance(param.getMerchantId(), param.getAmount()));
    }

}
