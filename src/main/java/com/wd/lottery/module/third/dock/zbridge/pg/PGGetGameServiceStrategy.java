package com.wd.lottery.module.third.dock.zbridge.pg;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.common.util.URLEncodeUtil;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.pg.component.PGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.pg.component.PGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.pg.component.PGResultRoot;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: pg game strategy
 *
 * <p> Created on 2024/5/13.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.PG_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class PGGetGameServiceStrategy extends AbstractGetGameStrategy {

    private final PGCallbackService pgCallbackService;

    public PGGetGameServiceStrategy(PGCallbackService pgCallbackService) {
        this.pgCallbackService = pgCallbackService;
    }


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        PGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, PGRequestConfig.class);
        PGHttpRequestTemplate requestTemplate = new PGHttpRequestTemplate(requestConfig);

        PGGameRoot rootResult = requestTemplate
                .host("$pgSoftAPIDomain")
                .toPOST()
                .api("/Game/v2/Get")
                .addParameter("currency", ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, currencyEnum))
                .addParameter("language", "en-us")
                .addParameter("status", "1")
                .toBeanAndCall(PGGameRoot.class);

        return rootResult.getData()
                .stream()
                .filter(Objects::nonNull)
                .map(i -> this.convertToDockGame(i, platformCode))
                .collect(Collectors.toList());
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        PGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), PGRequestConfig.class);
        PGHttpRequestTemplate requestTemplate = new PGHttpRequestTemplate(requestConfig);

        return getLaunchGameHtml(dto, requestTemplate, requestConfig);
    }

    private DockGame convertToDockGame(PGGame game, String platformCode){
        DockGame dockGame = new DockGame();
        dockGame.setPlatformCode(platformCode);
        dockGame.setThirdGameId(String.valueOf(game.gameId));
        dockGame.setGameName(game.gameName);
        dockGame.setGameCode(game.gameCode);
        GameCategoryEnum categoryEnum = GameCategoryEnum.SLOT;
        dockGame.setGameCategoryEnum(categoryEnum);
        return dockGame;
    }


    public String getLaunchGameHtml(DockGetGameUrl dto, PGHttpRequestTemplate requestTemplate, PGRequestConfig pgRequestConfig) {
        final String defaultLang = "en-us";
        String extraArgs = "btt=1" +
                "&ops=" + generateOpsToken(dto) +
                "&op=" + dto.getPlatformCode() +
                "&l=" + ThirdPlatformMappingConverter.toThirdLang(pgRequestConfig, dto.getLang(), defaultLang) +
                "&f=" + URLEncodeUtil.encodeURLParam(dto.getLobbyUrl());

        String gameId = dto.getThirdGameId();
        try {
            return requestTemplate
                    // apiDomain 包含 /external 前缀，
                    .api("-game-launcher/api/v1/GetLaunchURLHTML")
                    .toPOST()
                    .addParameter("operator_token", pgRequestConfig.getOperatorToken())
                    .addParameter("path", "/" + gameId + "/index.html")
                    .addParameter("extra_args", extraArgs)
                    .addParameter("url_type", "game-entry")
                    .addParameter("client_ip", dto.getIp())
                    .toCustomObject(String.class);
        } catch (Exception e) {
            throw new ThirdPlatformException("getOpenGameUrl error", e);
        }
    }

    private String generateOpsToken(DockGetGameUrl dto) {
        return pgCallbackService.generateToken(dto.getThirdUserId(), dto.getCurrencyEnum());
    }

    public static class PGGameRoot extends PGResultRoot<List<PGGame>> {}
    @Data
    public static class PGGame {
        private Integer gameId;  // 游戏唯一ID
        private String gameName; // 游戏名
        private String gameCode; // 游戏唯一code
        private Integer status;  // 全球游戏状态 0-无效 1-活跃 2-暂停 唯有在游戏状态和发布状态都显示活跃时才能进入游戏
        private Integer releaseStatus; //设备类型
        private boolean IsSupportFreeGame; //表示是否支持免费游戏：True：支持 False：不支持
        private Integer category; //1：视频老虎机游戏 2：卡牌游
    }
}
