package com.wd.lottery.module.third.dock.zbridge.bng;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.bng.common.BNGApiEnum;
import com.wd.lottery.module.third.dock.zbridge.bng.common.BNGGameTypes;
import com.wd.lottery.module.third.dock.zbridge.bng.common.BNGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.bng.common.BNGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.bng.res.BNGResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 游戏策略
 *
 * <AUTHOR> by iceants
 */
@Slf4j
@Component(BridgeConstant.BNG_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class BNGGetGameStrategy extends AbstractGetGameStrategy {

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        log.debug("BNG:下载游戏:{}-{}", platformCode, currencyEnum);
        BNGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, BNGRequestConfig.class);
        BNGHttpRequestTemplate requestTemplate = new BNGHttpRequestTemplate(requestConfig);
        // 请求游戏
        BNGResponse<List<GameInfo>> response = requestTemplate
                .toPOST()
                .api(String.format(BNGApiEnum.GET_GAME.getPath(), requestConfig.getProjectName()))
                .addParameter("api_token", requestConfig.getApiToken())
                .addParameter("provider_id", StrUtil.EMPTY)
                .toBeanAndCall(new TypeReference<BNGResponse<List<GameInfo>>>() {
                }, false);
        log.debug("BNG:下载游戏响应:{}", JSONUtil.toJsonStr(response));
        // 校验接口调用成功?
        boolean isSuccess = ObjectUtil.isNotNull(response) && StrUtil.isEmpty(response.getFetchState());
        Assert.isTrue(isSuccess, () -> new ThirdPlatformException(response.getErrorMsg()));
        // 转系统游戏
        return Opt.ofEmptyAble(response.getData())
                .map(items -> items.stream()
                        .filter(item -> BNGGameTypes.SLOT.equals(item.getType()))
                        .map(item -> {
                            DockGame dockGame = new DockGame();
                            dockGame.setPlatformCode(platformCode);
                            dockGame.setGameCategoryEnum(GameCategoryEnum.SLOT);
                            dockGame.setThirdGameId(item.getGameId());
                            dockGame.setGameName(item.getGameName());
                            dockGame.setGameCode(item.getGameId());
                            return dockGame;
                        }).collect(Collectors.toList())
                ).orElseGet(ListUtil::empty);
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("BNG:打开游戏:{}", JSONUtil.toJsonStr(dto));
        BNGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), BNGRequestConfig.class);
        BNGHttpRequestTemplate requestTemplate = new BNGHttpRequestTemplate(requestConfig);

        final String defaultLang = "en";
        // 玩家登录
        UserLoginToken response = requestTemplate
                .toPOST()
                .api(String.format(BNGApiEnum.GET_USER_TOKEN.getPath(), requestConfig.getProjectName(), requestConfig.getWl()))
                .addParameter("api_token", requestConfig.getApiToken())
                .addParameter("player_id", dto.getThirdUserName())
                .addParameter("currency", ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, dto.getCurrencyEnum()))
                .addParameter("mode", "REAL")
                .addParameter("brand", requestConfig.getProjectName())
                .toBeanAndCall(UserLoginToken.class, false);
        log.debug("BNG:打开游戏:登录游戏响应:{}", JSONUtil.toJsonStr(response));
        // 校验接口调用成功?
        boolean isSuccess = ObjectUtil.isNotNull(response) && StrUtil.isNotEmpty(response.getPlayerToken());
        Assert.isTrue(isSuccess, () -> new ThirdPlatformException(response.getErrorMsg()));
        // 游戏路径
        String currentSeconds = Convert.toStr(DateUtil.currentSeconds());
        Integer deviceType = this.getDeviceType(dto.getDeviceEnum());
        String splatform = (ObjectUtil.isNotNull(deviceType) && deviceType == 1) ? "mobile" : "splatform";
        String gameUrl = requestConfig.getApiUrl() +
                String.format(BNGApiEnum.GET_GAME_URL.getPath(), requestConfig.getProjectName(), response.getPlayerToken(), dto.getThirdGameId(), currentSeconds, splatform,
                        ThirdPlatformMappingConverter.toThirdLang(dto.getPlatformCode(), dto.getLang(), defaultLang));
        log.debug("BNG:打开游戏路径:{}", gameUrl);
        return gameUrl;
    }

    /**
     * 登录设备
     *
     * @param deviceEnum 设备类型
     * @return {@link Integer}
     */
    private Integer getDeviceType(DeviceEnum deviceEnum) {
        switch (deviceEnum) {
            case ANDROID:
                return 1;
            case IOS:
                return 2;
            case PC:
                return 3;
            default:
                return null;
        }
    }

    /**
     * 游戏信息
     */
    @Data
    public static class GameInfo {

        /**
         * 游戏ID
         */
        @JsonProperty("game_id")
        private String gameId;

        /**
         * 游戏名称
         */
        @JsonProperty("game_name")
        private String gameName;

        /**
         * 游戏供应商ID
         */
        @JsonProperty("provider_id")
        private String providerId;

        /**
         * 游戏供应商名称
         */
        @JsonProperty("provider_name")
        private String providerName;

        /**
         * 游戏类型
         */
        @JsonProperty("type")
        private String type;

        /**
         * 游戏的发布日期
         */
        @JsonProperty("release_date")
        private String releaseDate;

        /**
         * array[integer]
         */
        @JsonProperty(value = "bet_factors")
        private List<Integer> betFactors;

        /**
         * 支持的奖励类型列表。可能的值
         * <p>FIXED_FREEBET:一般的免费赌注 FLEXIBLE_FREEBET:没有回合数量限制的免费赌注 FEATURE_FREESPINS:启动免费游戏的系列开始</p>
         */
        @JsonProperty(value = "supported_bonuses")
        private List<String> supportedBonuses;

        /**
         * 支持对应语系包括游戏标题
         */
        @JsonProperty("i18n")
        private GameNameI18n i18n;

        /**
         * 游戏名称i18n
         */
        @Data
        static class GameNameI18n {

            @JsonProperty("zh-hant")
            private GameNameInfo zhHant;

            @JsonProperty("zh")
            private GameNameInfo zh;

            @JsonProperty("th")
            private GameNameInfo th;

            @JsonProperty("en")
            private GameNameInfo en;
        }

        /**
         * 游戏名称
         */
        @Data
        static class GameNameInfo {

            @JsonProperty("title")
            private String title;

            @JsonProperty("banner_path")
            private String bannerPath;
        }
    }

    /**
     * 玩家登录
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class UserLoginToken extends BNGResponse<Void> {
        @JsonProperty("player_token")
        private String playerToken;
    }
}