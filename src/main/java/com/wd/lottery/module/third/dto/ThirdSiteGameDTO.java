package com.wd.lottery.module.third.dto;

import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * <p> Created on 2024/6/4.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
public class ThirdSiteGameDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "游戏ID")
    private Long id;
    @Schema(description = "平台编码")
    private String platformCode;
    @Schema(description = "三方游戏ID")
    private String thirdGameId;
    @Schema(description = "游戏编码")
    private String gameCode;
    @Schema(description = "游戏名称")
    private String gameName;
    /**
     * 游戏所属分类
     */
    @Schema(description = "游戏分类")
    private GameCategoryEnum gameCategoryEnum;
    /**
     * 所属分类名称
     */
    @Schema(description = "游戏分类名称")
    private String gameCategoryName;
    /**
     * 游戏图片
     */
    @Schema(description = "游戏图片")
    private String gameImg;

    @Schema(description = "是否热门游戏， TRUE 是， FALSE: 否")
    private BooleanEnum enableHot;
    /**
     * 排序字段，越大越靠前
     */
    @Schema(description = "排序字段，越大越靠前")
    private Integer sort;

    /**
     * 支持币种，多个逗号间隔
     */
    @Schema(description = "游戏支持币种，多个以逗号间隔")
    private String supportedCurrency;
    @Schema(description = "是否RTP调控， TRUE  可以， FALSE 不可以")
    private BooleanEnum isRTP;

    @Schema(description = "是否展示 0-否 1-是")
    private EnableEnum showGame;

    @Schema(description = "是否维护")
    private BooleanEnum isMaintain;
}
