package com.wd.lottery.module.third.dock.zbridge.pp;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.pp.common.PPApiEnum;
import com.wd.lottery.module.third.dock.zbridge.pp.common.PPHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.pp.common.PPRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.pp.res.GameInfoRes;
import com.wd.lottery.module.third.dock.zbridge.pp.res.StartGameResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: pp game service strategy
 *
 * <p> Created on 2024/5/23.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.PP_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class PPGetGameServiceStrategy extends AbstractGetGameStrategy {



    @Override
    public  List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        PPRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, PPRequestConfig.class);
        PPHttpRequestTemplate requestTemplate = new PPHttpRequestTemplate(requestConfig);

        GameInfoRes response = requestTemplate
                .toPOST()
                .api(PPApiEnum.GET_GAME_LIST.getPath())
                /*
                游戏列表（String）。可能的值为：•all –所有游戏类别中的游戏•new –新游戏类别中的游戏•hot –热门游戏类别中的游戏*如果要使用多个值，则用逗号分隔指定它们。
                 */
                .addParameter("categories", "all")
                .toBeanAndCall(GameInfoRes.class);

        return response.getGames().getAll().stream()
                .map(i -> this.toDockGame(i, platformCode))
                .collect(Collectors.toList());
    }

    private DockGame toDockGame(GameInfoRes.GameItem gameItem, String platformCode) {
        DockGame game = new DockGame();
        game.setPlatformCode(platformCode);
        game.setThirdGameId(gameItem.getGameID());
        game.setGameName(gameItem.getGameName());
        game.setGameCode(gameItem.getGameID());
        game.setGameCategoryEnum(GameCategoryEnum.SLOT);
        return game;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        PPRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), PPRequestConfig.class);
        PPHttpRequestTemplate requestTemplate = new PPHttpRequestTemplate(requestConfig);

        final String defaultLang = "en";
        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang);

        StartGameResp resp = requestTemplate
                .toPOST()
                .api(PPApiEnum.START_GAME.getPath())
                .addParameter("externalPlayerId", dto.getThirdUserName())
                .addParameter("gameId", dto.getGameCode())
                // 大厅地址
                .addParameter("lobbyURL", dto.getLobbyUrl())
                .addParameter("language", thirdLang)
                .toBeanAndCall(StartGameResp.class);

        return resp.getGameURL();
    }

}
