package com.wd.lottery.module.third.controller.business;

import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.module.third.dto.PlatformItemBalanceDTO;
import com.wd.lottery.module.third.param.*;
import com.wd.lottery.module.third.service.BThirdUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "三方用户相关")
@RestController
@RequestMapping("${business-path}/${module-path.third}/user")
public class BThirdUserController {

    private final BThirdUserService thirdUserService;

    public BThirdUserController(BThirdUserService thirdUserService) {
        this.thirdUserService = thirdUserService;
    }

    @Operation(summary = "资金归集")
    @PostMapping("collectMoney")
    public ApiResult<?> listMerchantPlatform(@RequestParam Long uid) {
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        thirdUserService.collectMoney(uid, merchantId);
        return ApiResult.success();
    }


    @Operation(summary = "获取所有平台资金信息")
    @GetMapping("allPlatformBalance")
    public ApiResult<List<PlatformItemBalanceDTO>> allPlatformBalance(@RequestParam Long uid) {
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        List<PlatformItemBalanceDTO> balanceDTOS = thirdUserService.allPlatformBalance(uid, merchantId);
        return ApiResult.success(balanceDTOS);
    }

}
