package com.wd.lottery.module.third.dock.zbridge.ug;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.base.PeachLang;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.ug.common.*;
import com.wd.lottery.module.third.dock.zbridge.ug.req.OpenGameReqParam;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.List;


@Slf4j
@Component(BridgeConstant.UG_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class UGGetGameServiceStrategy extends AbstractGetGameStrategy {

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        List<UGExcelGameDto> gameList = Lists.newArrayList();
        ClassPathResource resource = new ClassPathResource("third/ug_game_list.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            gameList.addAll(reader.readAll(UGExcelGameDto.class));
        } catch (Exception e) {
            log.error("getGameList error", e);
        }
        return CollStreamUtil.toList(gameList, game -> this.toDockGame(game,platformCode));
    }

    private DockGame toDockGame(UGExcelGameDto gameItem, String platformCode) {
        DockGame dockGame = new DockGame();
        dockGame.setGameCategoryEnum(GameCategoryEnum.SPORT);
        dockGame.setPlatformCode(platformCode);
        dockGame.setThirdGameId(gameItem.getGameId());
        dockGame.setGameCode(gameItem.getGameId());
        dockGame.setGameName(gameItem.getGameName());
        return dockGame;
    }


    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        UGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), UGRequestConfig.class);
        UGHttpRequestTemplate requestTemplate = new UGHttpRequestTemplate(requestConfig);
        String endpoint = dto.getLobbyUrl();
        String splatForm = DeviceEnum.PC==dto.getDeviceEnum()?"PC":"MOBILE";

        OpenGameReqParam openGameReqParam = OpenGameReqParam.builder().apiKey(requestConfig.getApiKey()).operatorId(requestConfig.getOperatorId())
                .userId(dto.getThirdUserName()).returnUrl(endpoint).webType(splatForm).language(getLanguageCode(dto.getLang())).build();

        UGResponse<String> response = requestTemplate
                .toPOST()
                .api(UGApiEnum.GET_GAMEURL.getPath())
                .body(JSONUtil.toJsonStr(openGameReqParam))
                .toBeanAndCall(new TypeReference<UGResponse<String>>() {
                }, true);
        log.debug("UG http getOpenGameUrl response:{} ",response);
        return response.getData();
    }

    /**
     * 用户语种 1：英文 2：中文 3：越南语，4：泰语,5.印尼
     * 非必传字段
     */
    private String getLanguageCode(String language){
        String languageCode = "en";
        if(PeachLang.ZH_TW.equals(language)){
            languageCode = "zh_cn";
        }else if(PeachLang.VI_VN.equals(language)){
            languageCode = "vi";
        }else if(PeachLang.TH_TH.equals(language)){
            languageCode = "th";
        }else if(PeachLang.ID_ID.equals(language)){
            languageCode = "id";
        }
        return languageCode;
    }
}
