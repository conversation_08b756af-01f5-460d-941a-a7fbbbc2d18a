package com.wd.lottery.module.third.dto;

import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * Description: B 端用三方游戏DTO
 *
 * <p> Created on 2024/6/4.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
public class PlayerLastRecordGameDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "游戏ID")
    private Long id;
    @Schema(description = "平台编码")
    private String platformCode;
    @Schema(description = "三方游戏ID")
    private String thirdGameId;
    @Schema(description = "游戏编码")
    private String gameCode;
    @Schema(description = "游戏名称")
    private String gameName;
    /**
     * 标签（备用） eg: hot, new ...
     */
    @Schema(description = "游戏标签", example = "hot, new ...")
    private String tag;
    /**
     * 游戏所属分类
     */
    @Schema(description = "游戏分类")
    private GameCategoryEnum gameCategoryEnum;
    /**
     * 所属分类名称
     */
    @Schema(description = "游戏分类名称")
    private String gameCategoryName;
    /**
     * 游戏图片
     */
    @Schema(description = "游戏图片")
    private String gameImg;

    @Schema(description = "是否维护")
    private BooleanEnum isMaintain;
}