package com.wd.lottery.module.third.dock.zbridge.w9;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.URLEncodeUtil;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.w9.common.W9ApiEnum;
import com.wd.lottery.module.third.dock.zbridge.w9.common.W9EvenType;
import com.wd.lottery.module.third.dock.zbridge.w9.common.W9HttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.w9.common.W9RequestConfig;
import com.wd.lottery.module.third.dock.zbridge.w9.res.W9GetKeyRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 9W game strategy
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@RequiredArgsConstructor
@Component(BridgeConstant.W9_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class W9GetGameServiceStrategy extends AbstractGetGameStrategy {

    private final W9DomainService domainService;

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        return W9EvenType.eventMap.entrySet()
                .stream()
                .map(i -> toDockGame(i, platformCode))
                .collect(Collectors.toList());

    }

    private DockGame toDockGame(Map.Entry<String, String> entry, String platformCode) {
        DockGame dockGame = new DockGame();

        dockGame.setPlatformCode(platformCode);
        dockGame.setThirdGameId(entry.getKey());
        dockGame.setGameCode(entry.getKey());
        dockGame.setGameName(entry.getValue());
        dockGame.setGameCategoryEnum(GameCategoryEnum.SPORT);

        return dockGame;
    }


    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        String platformCode = dto.getPlatformCode();
        CurrencyEnum curr = dto.getCurrencyEnum();
        W9RequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, curr, W9RequestConfig.class);
        // 1. get key
        W9GetKeyRes keyRes = getKey(dto, curr, requestConfig);
        // 2. get login html
        return loginGame(dto, curr, requestConfig, keyRes.getKey());
    }

    private String loginGame(DockGetGameUrl dto, CurrencyEnum curr, W9RequestConfig requestConfig, String key) {
        String exchangeHost = domainService.getExchangeHost(dto.getPlatformCode(), curr);
        return exchangeHost + W9ApiEnum.login.path.replace("$website", requestConfig.getBrandName())
                + "?userId=" + dto.getThirdUserId()
                +  "&key=" + URLEncodeUtil.encodeURLParam(key)
                + "&returnUrl=" + URLEncodeUtil.encodeURLParam(dto.getLobbyUrl());
    }

    private W9GetKeyRes getKey(DockGetGameUrl dto, CurrencyEnum curr, W9RequestConfig requestConfig) {
        String apiServerHost = domainService.getApiServerHost(dto.getPlatformCode(), curr);
        W9HttpRequestTemplate requestTemplate = new W9HttpRequestTemplate(requestConfig);
        // get key
        return requestTemplate.host(apiServerHost)
                .api(W9ApiEnum.get_key.path)
                .addParameter("userId", dto.getThirdUserId())
                .addParameter("userName", dto.getThirdUserName())
                .addParameter("timeZone", "0")
                .addParameter("agent", requestConfig.getAgentId())
                .addParameter("currency", ThirdPlatformMappingConverter.toThirdCurrency(dto.getPlatformCode(), curr))
                .toBeanAndCall(W9GetKeyRes.class);
    }
}
