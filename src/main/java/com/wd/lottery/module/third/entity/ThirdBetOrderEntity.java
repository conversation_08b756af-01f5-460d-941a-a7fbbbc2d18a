package com.wd.lottery.module.third.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.InviteTypeEnum;
import com.wd.lottery.module.member.constants.MemberTypeEnum;
import com.wd.lottery.module.third.constants.BetOrderWinTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("third_bet_order")
public class ThirdBetOrderEntity {

    private Long id;

    private Long memberId;

    private String platformCode;

    private String platformName;

    private Long merchantId;

    private String orderNum;

    private String thirdUserName;

    private String memberName;

    /**
     * 父单号
     */
    private String orderNumParent;

    private String gameId;

    private String gameCode;

    private String gameName;

    private GameCategoryEnum gameCategoryEnum;

    /**
     * 游戏分类名称（冗余字段）
     */
    private String gameCategoryName;

    /**
     * 对局详情
     */
    private String gameDetail;

    /**
     * 投注详情
     */
    private String betDetail;

    private Long betMoney;

    private Long winMoney;

    private Long validBetMoney;

    private Long jackpot;
    private Long progressive;

    /**
     * 是否中奖，0：未中奖，1：中奖
     */
    private BooleanEnum onTarget;

    private BetOrderWinTypeEnum winTypeEnum;

    private CurrencyEnum currencyEnum;

    private String remark;
    /**
     * 是否删除： 0 正常， 1 已删除
     */
    private BooleanEnum isDel;

    /**
     * 0:正常有效数据，1：模拟数据
     **/
    private BooleanEnum isMock;

    private LocalDateTime orderTime;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @Schema(description = "邀请类型", example = "OFFICIAL")
    private InviteTypeEnum inviteTypeEnum;

    @Schema(description = "是否是直屬邀請", example = "TRUE")
    private BooleanEnum isDirect;

    @Schema(description = "渠道id", example = "123")
    private Long channelId;

    @Schema(description = "会员类型")
    private MemberTypeEnum memberTypeEnum;
}
