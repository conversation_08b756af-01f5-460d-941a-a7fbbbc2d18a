package com.wd.lottery.module.third.dock.zbridge.saba;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.saba.common.SABAHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.saba.common.SABARequestConfig;
import com.wd.lottery.module.third.dock.zbridge.saba.common.SABATypes;
import com.wd.lottery.module.third.dock.zbridge.saba.res.SABAResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: saba game service strategy
 *
 * <p> Created on 2024/7/01.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.SABA_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class SABAGetGameServiceStrategy extends AbstractGetGameStrategy {


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {

        return SABATypes.sportTypes.entrySet()
                .parallelStream()
                .map(i -> {
                    DockGame game = new DockGame();
                    game.setPlatformCode(platformCode);
                    game.setGameCode(i.getKey());
                    game.setThirdGameId(i.getKey());
                    game.setGameName(i.getValue());
                    game.setGameCategoryEnum(GameCategoryEnum.SPORT);
                    return game;
                })
                .collect(Collectors.toList());
    }


    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        SABARequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), SABARequestConfig.class);
        SABAHttpRequestTemplate requestTemplate = new SABAHttpRequestTemplate(requestConfig);

        DeviceEnum deviceEnum = dto.getDeviceEnum();
        String platform = "1";
        if (DeviceEnum.PC != deviceEnum) {
            platform = "2";
        }
        SABAResponse<String> res = requestTemplate.api("/api/GetSabaUrl")
                .addParameter("vendor_member_id", dto.getThirdUserId())
                .addParameter("platform", platform)
                .toBeanAndCall(new TypeReference<SABAResponse<String>>() {
                });
        String url = res.getData();
        final String defaultLang = "en";
        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang);
        if(url.contains("?")){
            url = url + "&lang=" + thirdLang;
        }else{
            url = url + "?lang=" + thirdLang;
        }

        if (DeviceEnum.PC != deviceEnum) {
            String endpoint = encodeUrl(dto.getLobbyUrl());
            url += "&homeUrl=" + endpoint + "&extendSessionUrl=" + endpoint;
        }

        return url;
    }

    private String encodeUrl(String urlParam){
        if (StringUtils.isBlank(urlParam)) {
            return "";
        }
        try {
            return URLEncoder.encode(urlParam, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new IllegalStateException(e);
        }
    }


}
