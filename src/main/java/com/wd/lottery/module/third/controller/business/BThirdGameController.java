package com.wd.lottery.module.third.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.module.third.dock.zbridge.omg.OMGRTPService;
import com.wd.lottery.module.third.dock.zbridge.omg.req.OMGRTPRequest;
import com.wd.lottery.module.third.dto.ThirdGameDTO;
import com.wd.lottery.module.third.dto.ThirdSiteGameDTO;
import com.wd.lottery.module.third.param.*;
import com.wd.lottery.module.third.param.*;
import com.wd.lottery.module.third.service.BThirdGameService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Description: 三方平台游戏管理controller
 *
 * <p> Created on 2024/7/22.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Tag(name = "三方游戏")
@RestController
@RequestMapping("${business-path}/${module-path.third}/game")
public class BThirdGameController {

    private final BThirdGameService bThirdGameService;
    @Autowired
    private OMGRTPService omgrtpService;

    public BThirdGameController(BThirdGameService bThirdGameService) {
        this.bThirdGameService = bThirdGameService;
    }

    @Operation(summary = "分页查询三方平台游戏")
    @GetMapping("page")
    public ApiResult<Page<ThirdGameDTO>> gamePage(@Valid ThirdGamePageParam param) {
        param.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        Page<ThirdGameDTO> page = bThirdGameService.getPage(param);
        return ApiResult.success(page);
    }

    @Operation(summary = "查询分配到商户的游戏")
    @GetMapping("queryAssignedGame")
    public ApiResult<List<ThirdSiteGameDTO>> queryAssignedGame(@Valid QueryAssignedGameParam param) {
        return ApiResult.success(bThirdGameService.queryAssignedGame(param));
    }

    @Operation(summary = "更新游戏")
    @PostMapping("update")
    public ApiResult<Boolean> updateThirdGame(@RequestBody @Valid ThirdGameUpdateParam param){
        param.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        bThirdGameService.updateGame(param);
        return ApiResult.success(true);
    }

    @Operation(summary = "更新游戏币种")
    @PostMapping("updateGameCurrency")
    public ApiResult<Boolean> updateGameCurrency(@RequestBody @Valid ThirdGameUpdateCurrencyParam param){
        bThirdGameService.updateGameCurrency(param);
        return ApiResult.success(true);
    }

    @Operation(summary = "设置游戏图片")
    @PostMapping("updateImg")
    public ApiResult<Boolean> updateImg(@RequestBody @Valid ThirdGameImgUpdateParam param){
        return ApiResult.success(bThirdGameService.updateGameImg(param));
    }

    @Operation(summary = "更新游戏维护状态")
    @PostMapping("updateMaintain")
    public ApiResult<Boolean> updateMaintainStatus(@RequestBody @Valid ThirdGameMaintainUpdateParam param){
        return ApiResult.success(bThirdGameService.updateGameMaintainStatus(param));
    }

    @Operation(summary = "更新游戏启用状态")
    @PostMapping("updateEnable")
    public ApiResult<Boolean> updateEnableStatus(@RequestBody @Valid ThirdGameEnableUpdateParam param){
        return ApiResult.success(bThirdGameService.updateGameEnableStatus(param));
    }

    @Operation(summary = "手动增加游戏")
    @PostMapping("addGame")
    public ApiResult<Boolean> addGame(@RequestBody @Valid ThirdGameAddParam param){
        return ApiResult.success(bThirdGameService.addGame(param));
    }

    @Operation(summary = "批量上传游戏图片")
    @PostMapping("batchUpload")
    public ApiResult<Boolean> batchUpload(@RequestBody @Valid ThirdGameImgBatchUploadParam param){
        bThirdGameService.batchUpload(param);
        return ApiResult.success(true);
    }

    @Operation(summary = "玩家RTP调控")
    @PostMapping("updateRTP")
    public ApiResult<Object>  updateRTP(@RequestBody @Valid OMGRTPRequest param){
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        param.setMerchantId(merchantId);
        return omgrtpService.updateRTP(param);
    }
}
