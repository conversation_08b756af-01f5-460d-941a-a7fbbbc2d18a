package com.wd.lottery.module.third.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.dto.SelectOptionDTO;
import com.wd.lottery.module.third.dto.ThirdPlatformAssignDTO;
import com.wd.lottery.module.third.dto.ThirdPlatformDTO;
import com.wd.lottery.module.third.dto.ThirdPlatformPageItemDTO;
import com.wd.lottery.module.third.param.*;
import com.wd.lottery.module.third.param.*;

import java.util.List;

/**
 * Description: 三方平台B端服务
 *
 * <p> Created on 2024/5/29.
 *
 * <AUTHOR>
 * @version 0.1
 */
public interface BThirdPlatformService {

    /**
     * 查询可新增三方平台下拉列表
     *
     * @return 三方平台下拉列表
     */
    List<SelectOptionDTO> getNewPlatformOptions();


    /**
     * 查询三方平台模板
     *
     * @param platformCode 平台编码
     * @return 三方平台模板
     */
    ThirdPlatformDTO getPlatformTemplate(String platformCode);

    /**
     * 查询平台详情
     *
     * @param platformCode 平台编码
     * @return 平台详情
     */
    ThirdPlatformDTO getThirdPlatformDetail(String platformCode);

    /**
     * 保存三方平台
     *
     * @param param 三方平台参数
     */
    void saveThirdPlatform(ThirdPlatformDTO param);

    /**
     * 查询三方平台列表
     *
     * @param param 查询参数
     * @return 三方平台列表
     */
    Page<ThirdPlatformPageItemDTO> getPlatformPage(ThirdPlatformPageParam param);

    /**
     * 同步三方平台游戏
     *
     * @param platformCode 平台编码
     * @param currencyEnum 币种
     */
    void syncPlatformGame(String platformCode, CurrencyEnum currencyEnum);

    /**
     * 分配三方平台到商户
     *
     * @param param 分配参数
     */
    void assignPlatform(AssignPlatformParam param);

    /**
     * 分配三方游戏到商户
     *
     * @param param 分配游戏参数
     */
    void assignGame(AssignGameParam param);

    /**
     * 更新平台维护状态
     *
     * @param param 维护状态参数
     */
    void updatePlatformMaintainStatus(MaintainStatusParam param);

    /**
     * 更新平台额度转换状态
     *
     * @param param 额度转换状态参数
     */
    void updatePlatformTransferStatus(TransferStatusParam param);

    /**
     * 手动拉单
     *
     * @param platformCode 平台编码
     * @param begin        开始时间： eg: 2024-06-30 12:00:00
     * @param end          结束时间 eg: 2024-06-30 12:30:00
     */
    void pullBetOrderManually(String platformCode, String begin, String end);

    /**
     * 更新平台启用状态
     *
     * @param param 状态参数
     */
    void updatePlatformEnableStatus(EnableStatusParam param);

    /**
     * 根据商户id查询商户已分配和未分配的平台
     * @return {@link List< ThirdPlatformAssignDTO >} 平台
     */
    List<ThirdPlatformAssignDTO> findAllPlatform(Long merchantId);

    /**
     * 根据商户id给商户分配选择的多个平台
     * @param param 参数
     */
    void assignMorePlatform(AssignMorePlatformParam param);

    /**
     * 手动拉单（根据拉单标记参数）
     * @param platformCode 平台编码
     * @param flag         拉单标记
     */
    void pullBetOrderByFlagManually(String platformCode, String flag);

    /**
     * 创建新代理线路
     *
     * @param param create agent param
     */
    void newAgent(NewAgentParam param);

    /**
     * 校验平台不同币种配置
     *
     * @param platformCode 平台代码
     * @param currencyEnum 币种类别
     */
    void validatePlatformConfig(String platformCode, CurrencyEnum currencyEnum);
}
