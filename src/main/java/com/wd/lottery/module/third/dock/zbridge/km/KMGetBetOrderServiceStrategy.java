package com.wd.lottery.module.third.dock.zbridge.km;

import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.km.common.KMHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.km.common.KMRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.km.common.KMResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Component(BridgeConstant.KM_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class KMGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {


    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        String platformCode = flag.getPlatformCode();
        KMRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, flag.getCurrencyEnum(), KMRequestConfig.class);
        KMHttpRequestTemplate requestTemplate = new KMHttpRequestTemplate(requestConfig);

        String startDate = ThirdPlatformMappingConverter.parsePullOrderParamTime(flag.getBegin(), flag.getPlatformCode());
        String endDate = ThirdPlatformMappingConverter.parsePullOrderParamTime(flag.getEnd(), flag.getPlatformCode());
        BetOrderRes res = getBetOrderList(requestTemplate, startDate , endDate);
        flag.setFinished(true);

        return toDockBetOrderList(res, flag, requestConfig);
    }

    private List<DockBetOrder> toDockBetOrderList(BetOrderRes res, BetOrderPullFlag flag, KMRequestConfig requestConfig) {
        if(Objects.isNull(res) || CollectionUtils.isEmpty(res.getBets())){
            return Collections.emptyList();
        }
        BigDecimal rate = ThirdPlatformMappingConverter.getCurrencyExchangeRate(requestConfig, flag.getCurrencyEnum());
        return res.getBets().stream().map(bet -> {
                DockBetOrder dockBetOrder = new DockBetOrder();
                dockBetOrder.setThirdUserName(bet.getUserid());
                dockBetOrder.setPlatformCode(flag.getPlatformCode());
                dockBetOrder.setOrderNum(bet.getId());
                dockBetOrder.setOrderNumParent(bet.getRoundid());
                dockBetOrder.setGameId(bet.getGcode());
                dockBetOrder.setBetMoney(Math.abs(ThirdPlatformMappingConverter.toSysMoneyLong(bet.getRiskamt(), rate)));
                dockBetOrder.setWinMoney(ThirdPlatformMappingConverter.toSysMoneyLong(bet.getWinamt(), rate));
                dockBetOrder.setValidBetMoney(ThirdPlatformMappingConverter.toSysMoneyLong(bet.getValidbet(), rate));
                if (ObjectUtils.isNotEmpty(bet.getJackpot())) {
                    dockBetOrder.setJackpot(bet.getJackpot().getWinamt().longValue());
                }
                String datetimeStr = bet.getBeton().substring(0, 19);
                dockBetOrder.setOrderTime(ThirdPlatformMappingConverter.parseThirdOrderTime(datetimeStr, flag.getPlatformCode()));

                return dockBetOrder;
            })
            .collect(Collectors.toList());
    }

    private BetOrderRes getBetOrderList(KMHttpRequestTemplate requestTemplate, String startDate , String endDate){
        log.info("pullOrder, startDate:{} , endDate:{}", startDate, endDate);
        BetOrderRes betOrderRes = requestTemplate
                .toGET()
                .api("/api/v2/history/bets")
                .addParameter("startdate", startDate)
                .addParameter("enddate", endDate)
                .addParameter("issettled", "true")
                .addParameter("includetestplayers", "true")
                .toCustomObject(BetOrderRes.class);
        log.debug("http getBetOrderList response:{} ",betOrderRes);
        return betOrderRes;
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        KMRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), KMRequestConfig.class);
        KMHttpRequestTemplate requestTemplate = new KMHttpRequestTemplate(requestConfig);
        String api = "/api/history/users/"+dto.getThirdUserId()+"/rounds/"+dto.getParentOrderNo();
        try {
            OrderDetailRes orderDetailRes = requestTemplate
                    .toGET()
                    .api(api)
                    .toCustomObject(OrderDetailRes.class);

            log.debug("http getOrderDetailUrl response:{} ", orderDetailRes);
            if (ObjectUtils.isEmpty(orderDetailRes)) {
                throw new ThirdPlatformException("cannot get bet detail url, historyId = " + dto.getOrderNo());
            }
            String url = orderDetailRes.getUrl();
            log.debug("KM getGameDetailUrl:{}", url);
            return url.startsWith("//") ? String.format("https:%s", url) : url;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
   public static class BetOrderRes extends KMResponse<Object> {
      private  List<Bet> bets;
    }

    @Data
    static class Bet{
        private String id;
        private String beton;
        private String closedon;
        private String roundid;
        private String externalroundid;
        private String gpcode;
        private String gcode;
        private String platformtype;
        private String userid;
        private String cur;
        private BigDecimal riskamt;
        private BigDecimal winamt;
        private BigDecimal validbet;
        private BigDecimal commission;
        private Jackpot jackpot;
    }
    @Data
    static class Jackpot{
        private BigDecimal contrib;
        private BigDecimal winamt;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    static class OrderDetailRes extends KMResponse<Object>  {
       private String url;
    }
}
