package com.wd.lottery.module.third.dock.zbridge.evo;


import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.evo.common.EVOApiEnum;
import com.wd.lottery.module.third.dock.zbridge.evo.common.EVOHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.evo.common.EVORequestConfig;
import com.wd.lottery.module.third.dock.zbridge.evo.req.EVOOpenGameParam;
import com.wd.lottery.module.third.dock.zbridge.evo.res.ApiTokenRes;
import com.wd.lottery.module.third.dock.zbridge.evo.res.EVOGameItem;
import com.wd.lottery.module.third.dock.zbridge.evo.res.EVOGameRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: evo game strategy
 *
 * <p> Created on 2024/5/13.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.EVO_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class EVOGetGameServiceStrategy extends AbstractGetGameStrategy {

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        EVORequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, EVORequestConfig.class);
        EVOHttpRequestTemplate requestTemplate = new EVOHttpRequestTemplate(requestConfig);

        EVOGameRes gameRes = requestTemplate
                .host(requestConfig.getGameHost())
                .toGET()
                .addHeader("Authorization", requestConfig.getAuthorization())
                .api(EVOApiEnum.GAME_LIST.getPath())
                .toCustomObject(EVOGameRes.class);
        if(Objects.isNull(gameRes) || CollectionUtils.isEmpty(gameRes.getData())){
            return Collections.emptyList();
        }

        Map<String, DockGame> collect = gameRes.getData()
                .stream()
                .map(i -> toDockGame(i, platformCode))
                .collect(Collectors.toMap(DockGame::getThirdGameId, Function.identity(), (v1, v2) -> v1));

        return new ArrayList<>(collect.values());
    }


    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        CurrencyEnum currencyEnum = dto.getCurrencyEnum();
        EVORequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), currencyEnum, EVORequestConfig.class);
        EVOHttpRequestTemplate requestTemplate = new EVOHttpRequestTemplate(requestConfig);

        final String defaultLang = "en";

        EVOOpenGameParam param = new EVOOpenGameParam();
        param.setUuid(UUID.randomUUID().toString());
        EVOOpenGameParam.PlayerParam playerParam = new EVOOpenGameParam.PlayerParam();
        playerParam.setId(dto.getThirdUserId());
        playerParam.setLanguage(ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang));
        playerParam.setCurrency(ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, dto.getCurrencyEnum()));
        playerParam.setUpdate(true);

        EVOOpenGameParam.PlayerSession playerSession = new EVOOpenGameParam.PlayerSession();

        playerSession.setId(UUID.randomUUID().toString());
        playerSession.setIp(dto.getIp());

        playerParam.setSession(playerSession);
        param.setPlayer(playerParam);

        ApiTokenRes res = requestTemplate
                .toPOST()
                .api(EVOApiEnum.API_TOKEN.getPath())
                .addHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .body(JacksonUtil.toJSONString(param))
                .toBeanAndCall(ApiTokenRes.class);

        return res.getEntry();
    }

    private DockGame toDockGame(EVOGameItem thirdGame, String platformCode) {
        DockGame dockGame = new DockGame();
        dockGame.setPlatformCode(platformCode);
        dockGame.setThirdGameId(thirdGame.getGameId());
        dockGame.setGameName(thirdGame.getGameName());
        dockGame.setGameCode(thirdGame.getGameId());
        dockGame.setGameCategoryEnum(GameCategoryEnum.CASINO);
        dockGame.setRemark(thirdGame.getGameType());
        return dockGame;
    }

}
