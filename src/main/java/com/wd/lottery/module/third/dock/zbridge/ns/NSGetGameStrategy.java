package com.wd.lottery.module.third.dock.zbridge.ns;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.ns.common.NSAPIEnum;
import com.wd.lottery.module.third.dock.zbridge.ns.common.NSGameType;
import com.wd.lottery.module.third.dock.zbridge.ns.common.NSHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.ns.common.NSRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.ns.req.NSAcctInfo;
import com.wd.lottery.module.third.dock.zbridge.ns.res.NSResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 游戏策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.NS_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class NSGetGameStrategy extends AbstractGetGameStrategy {

    @Autowired
    private NSTokenService tokenService;

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        log.debug("NS:下载游戏:{}-{}", platformCode, currencyEnum);
        NSRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, NSRequestConfig.class);
        NSHttpRequestTemplate requestTemplate = new NSHttpRequestTemplate(requestConfig, NSAPIEnum.GET_GAME.getPath(), true);
        // 下载游戏
        GameList response = requestTemplate.toPOST().toBeanAndCall(GameList.class);
        log.debug("NS:下载游戏响应:{}", JSONUtil.toJsonStr(response));
        return Opt.ofEmptyAble(response.getGames())
                .map(items -> items.stream()
                        .filter(item -> NSGameType.SLOT.contains(item.getCategory()))
                        .map(item -> {
                            DockGame dockGame = new DockGame();
                            dockGame.setPlatformCode(platformCode);
                            dockGame.setGameCategoryEnum(GameCategoryEnum.SLOT);
                            dockGame.setThirdGameId(item.getGameCode());
                            dockGame.setGameName(item.getGameName());
                            dockGame.setGameCode(item.getGameCode());
                            return dockGame;
                        }).collect(Collectors.toList())
                ).orElseGet(ListUtil::empty);
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("NS:玩家打开游戏:{}", JSONUtil.toJsonStr(dto));
        NSRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), NSRequestConfig.class);
        String openGameUrl = requestConfig.getOpenGameUrl();
        boolean isSatisfied = StrUtil.endWithIgnoreCase(openGameUrl, "/");
        if (!isSatisfied) {
            openGameUrl = openGameUrl + "/";
        }
        final String defaultLang = "en";
        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang);
        NSAcctInfo acctInfo = this.initAccInfo(dto);
        String token = this.tokenService.generateToken(acctInfo);
        String gameUrl = String.format(NSAPIEnum.OPEN_GAME.getPath(), openGameUrl, requestConfig.getMerchantCode(), dto.getThirdUserId(), thirdLang, token, dto.getGameCode());
        gameUrl += "&returnURL=" + dto.getLobbyUrl();
        log.debug("NS:玩家打开游戏URL:{}", gameUrl);
        return gameUrl;
    }

    @Override
    public String getDemoGameUrl(DockGetGameUrl getGameUrlParam) {
        log.debug("NS:玩家打开游戏试玩:{}", JSONUtil.toJsonStr(getGameUrlParam));
        NSRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(getGameUrlParam.getPlatformCode(), getGameUrlParam.getCurrencyEnum(), NSRequestConfig.class);
        final String defaultLang = "en";
        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, getGameUrlParam.getLang(), defaultLang);
        return requestConfig.getDemoGameUrl() + "/game?merchantCode=" + requestConfig.getMerchantCode() + "&game=" + getGameUrlParam.getGameCode() + "&lang=" + thirdLang;
    }

    /**
     * 玩家账号信息
     *
     * @param dto 打开游戏入参
     * @return {@link NSAcctInfo}
     */
    private NSAcctInfo initAccInfo(DockGetGameUrl dto) {
        NSAcctInfo info = new NSAcctInfo();
        info.setAcctId(dto.getThirdUserId());
        info.setCurrency(dto.getCurrencyEnum().name());
        info.setBalance(BigDecimal.ZERO);
        info.setSiteId(Convert.toStr(0));
        info.setUserName(dto.getThirdUserId());
        return info;
    }

    /**
     * 游戏列表
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class GameList extends NSResponse<Void> {

        private List<Game> games;

        /**
         * 游戏详情
         */
        @Data
        public static class Game {

            /**
             * 游戏代码
             */
            private String gameCode;

            /**
             * 游戏名称
             */
            private String gameName;

            /**
             * 游戏中文名称
             */
            private String cnName;

            /**
             * 游戏线
             */
            private Integer line;

            /**
             * 游戏种类
             */
            private String category;

            /**
             * ranking
             */
            private Integer ranking;
        }
    }
}
