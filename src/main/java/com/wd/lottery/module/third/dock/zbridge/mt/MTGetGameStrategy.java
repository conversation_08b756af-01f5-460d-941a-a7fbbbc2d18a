package com.wd.lottery.module.third.dock.zbridge.mt;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.mt.common.MTAPIEnum;
import com.wd.lottery.module.third.dock.zbridge.mt.common.MTHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.mt.common.MTRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.mt.res.MTResponse;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.List;

/**
 * 游戏策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.MT_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class MTGetGameStrategy extends AbstractGetGameStrategy {


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        log.debug("MT:下载游戏:{}-{}", platformCode, currencyEnum);
        ClassPathResource resource = new ClassPathResource("third/mt_game_list.xlsx");
        List<Game> games = Lists.newArrayList();
        try (InputStream inputStream = resource.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            games.addAll(reader.readAll(Game.class));
        } catch (Exception e) {
            log.error(StrUtil.format("MT:下载游戏:{}-{}", platformCode, currencyEnum), e);
        }
        return CollStreamUtil.toList(games, game -> {
            DockGame dockGame = new DockGame();
            dockGame.setPlatformCode(platformCode);
            GameCategoryEnum category;
            switch (Convert.toInt(game.getGameType())) {
                case 1:
                    category = GameCategoryEnum.THREE;
                    break;
                case 2:
                    category = GameCategoryEnum.POKER;
                    break;
                case 3:
                    category = GameCategoryEnum.FISH;
                    break;
                default:
                    category = GameCategoryEnum.SLOT;
            }
            dockGame.setGameCategoryEnum(category);
            dockGame.setThirdGameId(game.getGameCode());
            dockGame.setGameName(game.getGameName());
            dockGame.setGameCode(game.getGameCode());
            return dockGame;
        });
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("MT:玩家打开游戏:{}", JSONUtil.toJsonStr(dto));
        MTRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), MTRequestConfig.class);
        MTHttpRequestTemplate requestTemplate = new MTHttpRequestTemplate(requestConfig, dto.getCurrencyEnum());
        String password = dto.getThirdUserPasswd();

        final String defaultLang = "EN-US";
        // 请求游戏
        GameLink response = requestTemplate.toPOST()
                .api(MTAPIEnum.OPEN_GAME.getPath())
                .addUrlParameter("playerName", dto.getThirdUserName())
                .addUrlParameter("pwd", password)
                .addParameter("gameCode", dto.getGameCode())
                .addParameter("lang", ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang))
                .toBeanAndCall(GameLink.class);
        log.debug("MT:玩家打开游戏响应:{}", JSONUtil.toJsonStr(response));
        Assert.isFalse(StrUtil.isEmpty(response.getUrl()), () -> new RuntimeException("获取游戏路径失败"));
        return response.getUrl();
    }

    /**
     * 游戏详情
     */
    @Data
    public static class Game {
        private String gameType;
        private String gameChineseName;
        private String gameName;
        private String gameCode;
    }

    /**
     * 打开游戏
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class GameLink extends MTResponse<Void> {
        private String url;
    }
}