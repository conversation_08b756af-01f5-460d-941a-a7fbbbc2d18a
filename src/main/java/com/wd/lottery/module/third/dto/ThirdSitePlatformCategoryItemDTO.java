package com.wd.lottery.module.third.dto;

import com.wd.lottery.module.common.constants.GameCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * Description: 商户分类配置 DTO
 *
 * <p> Created on 2024/8/13.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
public class ThirdSitePlatformCategoryItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "游戏分类")
    private GameCategoryEnum categoryEnum;

    @Schema(description = "分类图标")
    private String categoryImg;
}
