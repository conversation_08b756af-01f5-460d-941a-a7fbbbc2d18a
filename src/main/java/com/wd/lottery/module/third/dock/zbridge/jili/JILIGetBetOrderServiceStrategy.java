package com.wd.lottery.module.third.dock.zbridge.jili;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.third.constants.BetOrderWinTypeEnum;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.jili.common.JILIApiEnum;
import com.wd.lottery.module.third.dock.zbridge.jili.common.JILIHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.jili.common.JILIRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.jili.common.JILIResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: bet order faced
 *
 * <p> Created on 2024/5/13.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.JILI_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class JILIGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {


    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        String platformCode = flag.getPlatformCode();
        JILIRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, flag.getCurrencyEnum(), JILIRequestConfig.class);
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);
        log.info("pull jili bet order, config: {}, param: {}", requestConfig, flag);
        JILIHttpRequestTemplate requestTemplate = new JILIHttpRequestTemplate(requestConfig, platformConfig);


        String start = ThirdPlatformMappingConverter.parsePullOrderParamTime(flag.getBegin(), platformCode);
        String end = ThirdPlatformMappingConverter.parsePullOrderParamTime(flag.getEnd(), platformCode);

        final int pageSize = 20_000;

        int currentIndex = flag.getIndex();
        JILIResponse<ResultData> response = requestTemplate
                .api(JILIApiEnum.BET_LIST.getPath())
                .toGET()
                .addParameter("StartTime", start)
                .addParameter("EndTime", end)
                .addParameter("FilterAgent", "1")
                .addParameter("Page", String.valueOf(currentIndex))
                .addParameter("PageLimit", String.valueOf(pageSize))
                .toBeanAndCall(new TypeReference<JILIResponse<ResultData>>() {
                });

        List<BetOrderData> dataList = response.getData().getResult();
        Integer totalIndex = response.getData().getPagination().getTotalPages();
        if(Objects.isNull(totalIndex) || currentIndex >= totalIndex || CollectionUtils.isEmpty(dataList)){
            flag.setFinished(true);
        }
        final BigDecimal rate = ThirdPlatformMappingConverter.getCurrencyExchangeRate(requestConfig, flag.getCurrencyEnum());
        List<DockBetOrder> resultList = dataList.stream().map(i -> this.toDockBetOrder(i, platformCode, rate)).collect(Collectors.toList());
        // 更新下一页参数
        flag.setIndex(currentIndex + 1);
        return resultList;

    }

    @Data
    static public class ResultData {
        @JsonProperty("Pagination")
        private Pagination pagination;

        @JsonProperty("Result")
        private List<BetOrderData> result;
    }

    @Data
    public static class Pagination {

        // 当前页数
        @JsonProperty("CurrentPage")
        private Integer currentPage;

        // 总页数
        @JsonProperty("TotalPages")
        private Integer totalPages;

        // 每页笔数
        @JsonProperty("PageLimit")
        private Integer pageLimit;

        // 总笔数
        @JsonProperty("TotalNumber")
        private Integer totalNumber;
    }

    @Data
    public static class BetOrderData {
        // 会员唯一识别值
        @JsonProperty("Account")
        private String account;

        // 在游戏内注单唯一值 注单ID
        @JsonProperty("WagersId")
        private String wagersId;

        // 游戏的唯一识别值
        @JsonProperty("GameId")
        private String gameId;

        // 投注时间
        @JsonProperty("WagersTime")
        private String wagersTime;

        // 投注金额
        @JsonProperty("BetAmount")
        private BigDecimal betAmount;

        /**
         * 有效投注金额
         */
        @JsonProperty("Turnover")
        private BigDecimal turnover;

        // PayoffTime
        @JsonProperty("PayoffTime")
        private String payoffTime;

        // 派彩金额
        @JsonProperty("PayoffAmount")
        private BigDecimal payoffAmount;

        // 注单状态：1: 赢 2: 输
        @JsonProperty("Status")
        private Integer status;

        // 对帐时间
        @JsonProperty("SettlementTime")
        private String settlementTime;

        // 游戏类型：
        @JsonProperty("GameCategoryId")
        private String gameCategoryId;

        // 注单类型：1: main game 9: free game 11: 道具卡 12: 游戏内购
        @JsonProperty("Type")
        private Integer type;

        // 会员所属站长唯一识别值
        @JsonProperty("AgentId")
        private String agentId;

    }

    private DockBetOrder toDockBetOrder(BetOrderData thirdOrder, String platformCode, BigDecimal rate) {
        DockBetOrder dockBetOrder = new DockBetOrder();
        dockBetOrder.setThirdUserName(thirdOrder.getAccount());
        dockBetOrder.setPlatformCode(platformCode);
        dockBetOrder.setOrderNum(thirdOrder.getWagersId());
        dockBetOrder.setGameId(thirdOrder.getGameId());

        dockBetOrder.setBetMoney(Math.abs(toPlatformMoney(thirdOrder.getBetAmount())));
        dockBetOrder.setWinMoney(toPlatformMoney(thirdOrder.getPayoffAmount()));
        dockBetOrder.setValidBetMoney(toPlatformMoney(thirdOrder.getTurnover()));

        LocalDateTime dateTime = ThirdPlatformMappingConverter.parseThirdOrderTime(thirdOrder.getSettlementTime(), platformCode);
        dockBetOrder.setOrderTime(dateTime);

        if (thirdOrder.getType() == 11 || thirdOrder.getType() == 28) {
            dockBetOrder.setJackpot(dockBetOrder.getWinMoney());
            dockBetOrder.setWinTypeEnum(BetOrderWinTypeEnum.JACKPOT);
            // 道具卡，订单增加 prize 前缀，奖金保存至 jackpot
            if (thirdOrder.getType() == 11) {
                dockBetOrder.setOrderNum("prize" + dockBetOrder.getOrderNum());
            }
            // gift code , 订单号增加 gift 前缀，奖金保存至 jackpot
            if (thirdOrder.getType() == 28) {
                dockBetOrder.setOrderNum("gift" + dockBetOrder.getOrderNum());
            }
        }

        return dockBetOrder;
    }



    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        JILIRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), JILIRequestConfig.class);
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(dto.getPlatformCode());
        JILIHttpRequestTemplate requestTemplate = new JILIHttpRequestTemplate(requestConfig, platformConfig);

        try {
            JILIResponse<JILIBetUrl> response = requestTemplate
                    .api(JILIApiEnum.GET_BET_URL.getPath())
                    .toPOST()
                    .addParameter("WagersId", dto.getOrderNo())
                    .toBeanAndCall(new TypeReference<JILIResponse<JILIBetUrl>>() {
                    });

            return response.getData().getUrl();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    @Data
    static public class JILIBetUrl {
        @JsonProperty("Url")
        private String url;
    }



}
