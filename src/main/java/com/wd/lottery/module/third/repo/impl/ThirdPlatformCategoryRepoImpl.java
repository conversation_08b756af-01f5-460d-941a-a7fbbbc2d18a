package com.wd.lottery.module.third.repo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dto.ThirdPlatformCategoryDTO;
import com.wd.lottery.module.third.entity.ThirdPlatformCategoryEntity;
import com.wd.lottery.module.third.entity.ThirdPlatformCategoryStatusConfigEntity;
import com.wd.lottery.module.third.mapper.ThirdPlatformCategoryMapper;
import com.wd.lottery.module.third.repo.ThirdPlatformCategoryRepo;
import com.wd.lottery.module.third.repo.ThirdPlatformCategoryStatusConfigRepo;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 三方平台分类配置表 repo
 *
 * <p> Created on 2024/5/23.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class ThirdPlatformCategoryRepoImpl extends ServiceImpl<ThirdPlatformCategoryMapper, ThirdPlatformCategoryEntity> implements ThirdPlatformCategoryRepo {

    private final ThirdPlatformCategoryStatusConfigRepo categoryStatusConfigRepo;

    public ThirdPlatformCategoryRepoImpl(ThirdPlatformCategoryStatusConfigRepo categoryStatusConfigRepo) {
        this.categoryStatusConfigRepo = categoryStatusConfigRepo;
    }

    @Override
    public Map<String, ThirdPlatformCategoryEntity> getPlatformCategoryMap() {
        return this.list()
                .stream()
                .filter(i -> Objects.nonNull(i.getCategoryEnum()))
                .collect(Collectors.toMap(k -> k.getPlatformCode() + "_" + k.getCategoryEnum().name(), Function.identity()));
    }

    @Override
    public List<ThirdPlatformCategoryDTO> getThirdPlatformCategories(String platformCode, CurrencyEnum currencyEnum) {
        List<ThirdPlatformCategoryEntity> list = this.lambdaQuery()
                .eq(ThirdPlatformCategoryEntity::getPlatformCode, platformCode)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        Map<GameCategoryEnum, ThirdPlatformCategoryStatusConfigEntity> statusMap = categoryStatusConfigRepo.lambdaQuery()
                .eq(ThirdPlatformCategoryStatusConfigEntity::getPlatformCode, platformCode)
                .eq(ThirdPlatformCategoryStatusConfigEntity::getCurrencyEnum, currencyEnum)
                .list()
                .stream()
                .collect(Collectors.toMap(ThirdPlatformCategoryStatusConfigEntity::getCategoryEnum, Function.identity()));
        return list.stream()
                .map(i -> {
                    ThirdPlatformCategoryDTO dto = new ThirdPlatformCategoryDTO();
                    BeanUtils.copyProperties(i, dto);
                    ThirdPlatformCategoryStatusConfigEntity statusEntity = statusMap.get(i.getCategoryEnum());
                    if (Objects.nonNull(statusEntity)) {
                        dto.setEnableHot(statusEntity.getEnableHot());
                        dto.setSort(statusEntity.getSort());
                    }
                    return dto;
                })
                .sorted(Comparator.comparing(ThirdPlatformCategoryDTO::getSort).reversed())
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateCategoryBatch(String platformCode, List<ThirdPlatformCategoryDTO> categoryDTOList, CurrencyEnum currencyEnum) {
        // 新增集合
        List<ThirdPlatformCategoryDTO> newList = new ArrayList<>();
        // 更新集合
        List<ThirdPlatformCategoryDTO> updateList = new ArrayList<>();

        // 历史记录
        Set<GameCategoryEnum> hisList = this.lambdaQuery()
                .select(ThirdPlatformCategoryEntity::getCategoryEnum)
                .eq(ThirdPlatformCategoryEntity::getPlatformCode, platformCode)
                .list()
                .stream().map(ThirdPlatformCategoryEntity::getCategoryEnum)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(hisList)) {
            saveNewCategoryBatch(platformCode, categoryDTOList, currencyEnum);
            return;
        }
        for (ThirdPlatformCategoryDTO dto : categoryDTOList) {
            if (hisList.contains(dto.getCategoryEnum())) {
                updateList.add(dto);
            }else{
                newList.add(dto);
            }
        }
        // 新增
        if (CollectionUtils.isNotEmpty(newList)) {
            saveNewCategoryBatch(platformCode, newList, currencyEnum);
        }
        // 更新
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateCategoryBatch(platformCode, updateList, currencyEnum);
        }
        Set<GameCategoryEnum> paramCategories = categoryDTOList.stream()
                .map(ThirdPlatformCategoryDTO::getCategoryEnum)
                .collect(Collectors.toSet());
        // 删除
        Set<GameCategoryEnum> deleteCategories = new HashSet<>(Sets.difference(hisList, paramCategories));
        if (CollectionUtils.isNotEmpty(deleteCategories)) {
            deletePlatformCategoryBatch(platformCode, deleteCategories);
        }
    }

    private void saveNewCategoryBatch(String platformCode, List<ThirdPlatformCategoryDTO> categoryDTOList, CurrencyEnum currencyEnum) {
        List<ThirdPlatformCategoryEntity> list = new ArrayList<>();
        List<ThirdPlatformCategoryStatusConfigEntity> statusList = new ArrayList<>();
        for (ThirdPlatformCategoryDTO dto : categoryDTOList) {
            ThirdPlatformCategoryEntity entity = new ThirdPlatformCategoryEntity();
            BeanUtils.copyProperties(dto, entity);
            entity.setPlatformCode(platformCode);
            entity.setUpdateTime(LocalDateTime.now());
            list.add(entity);

            ThirdPlatformCategoryStatusConfigEntity statusEntity = ThirdPlatformCategoryStatusConfigEntity.getDefaultInstance();
            statusEntity.setPlatformCode(platformCode);
            statusEntity.setCategoryEnum(dto.getCategoryEnum());
            statusEntity.setCurrencyEnum(currencyEnum);
            statusEntity.setEnableHot(dto.getEnableHot());
            statusEntity.setSort(dto.getSort());

            statusList.add(statusEntity);
        }
        this.saveBatch(list);
        categoryStatusConfigRepo.saveBatch(statusList);
    }

    private void updateCategoryBatch(String platformCode, List<ThirdPlatformCategoryDTO> updateList, CurrencyEnum currencyEnum) {
        Set<GameCategoryEnum> categoryEnums = updateList.stream().map(ThirdPlatformCategoryDTO::getCategoryEnum).collect(Collectors.toSet());
        // 删除历史
        this.remove(new LambdaQueryWrapper<>(ThirdPlatformCategoryEntity.class).eq(ThirdPlatformCategoryEntity::getPlatformCode, platformCode)
                .in(ThirdPlatformCategoryEntity::getCategoryEnum, categoryEnums));
        categoryStatusConfigRepo.remove(new LambdaQueryWrapper<>(ThirdPlatformCategoryStatusConfigEntity.class)
                .eq(ThirdPlatformCategoryStatusConfigEntity::getPlatformCode, platformCode)
                .eq(ThirdPlatformCategoryStatusConfigEntity::getCurrencyEnum, currencyEnum)
                .in(ThirdPlatformCategoryStatusConfigEntity::getCategoryEnum, categoryEnums));

        saveNewCategoryBatch(platformCode, updateList, currencyEnum);
    }

    private void deletePlatformCategoryBatch(String platformCode, Collection<GameCategoryEnum> deleteCategories) {
        log.debug("remove platform category config, platform:{}, categories: {}", platformCode, JacksonUtil.toJSONString(deleteCategories));

        this.remove(new LambdaQueryWrapper<>(ThirdPlatformCategoryEntity.class)
                .eq(ThirdPlatformCategoryEntity::getPlatformCode, platformCode)
                .in(ThirdPlatformCategoryEntity::getCategoryEnum, deleteCategories));

        categoryStatusConfigRepo.remove(new LambdaQueryWrapper<>(ThirdPlatformCategoryStatusConfigEntity.class)
                .eq(ThirdPlatformCategoryStatusConfigEntity::getPlatformCode, platformCode)
                .in(ThirdPlatformCategoryStatusConfigEntity::getCategoryEnum, deleteCategories));
    }

}
