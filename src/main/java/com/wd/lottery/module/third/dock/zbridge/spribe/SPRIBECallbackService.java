package com.wd.lottery.module.third.dock.zbridge.spribe;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.constants.BetOrderWinTypeEnum;
import com.wd.lottery.module.third.constants.ThirdBetOrderStatusEnum;
import com.wd.lottery.module.third.dock.zbridge.spribe.common.SPRIBEDepositActionEnum;
import com.wd.lottery.module.third.dock.zbridge.spribe.common.SPRIBEException;
import com.wd.lottery.module.third.dock.zbridge.spribe.common.SPRIBEResCodeEnum;
import com.wd.lottery.module.third.dock.zbridge.spribe.common.SPRIBETokenContent;
import com.wd.lottery.module.third.dock.zbridge.spribe.req.*;
import com.wd.lottery.module.third.dock.zbridge.spribe.req.*;
import com.wd.lottery.module.third.dock.zbridge.spribe.res.BalanceOptRes;
import com.wd.lottery.module.third.dock.zbridge.spribe.res.PlayerInfo;
import com.wd.lottery.module.third.dto.ThirdUserDTO;
import com.wd.lottery.module.third.entity.ThirdSeamlessBetOrderEntity;
import com.wd.lottery.module.third.service.ThirdSiteUserInnerService;
import com.wd.lottery.module.third.service.inner.ThirdSeamlessBetOrderInnerService;
import com.wd.lottery.module.third.service.inner.ThirdSeamlessWalletInnerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * Description: spribe callback
 *
 * <p> Created on 2024/7/3.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class SPRIBECallbackService {

    private final ThirdSiteUserInnerService thirdSiteUserInnerService;
    private final ThirdSeamlessBetOrderInnerService betOrderInnerService;
    private final ThirdSeamlessWalletInnerService walletInnerService;
    private final SPRIBETokenService tokenService;

    private static final int moneyUnit = 1000;


    public SPRIBECallbackService(ThirdSiteUserInnerService thirdSiteUserInnerService,
                                 ThirdSeamlessBetOrderInnerService betOrderInnerService,
                                 ThirdSeamlessWalletInnerService walletInnerService,
                                 SPRIBETokenService tokenService) {
        this.thirdSiteUserInnerService = thirdSiteUserInnerService;
        this.betOrderInnerService = betOrderInnerService;
        this.walletInnerService = walletInnerService;
        this.tokenService = tokenService;
    }



    public PlayerInfo auth(AuthParam param){
        log.debug("spribe auth callback, param: {}", param);
        // check user token
        String token = param.getUser_token();

        SPRIBETokenContent content = tokenService.checkUserToken(token, param.getPlatformCode());

        // cache session token
        tokenService.saveSessionToken(param.getSession_token(), content);

        return getThirdUserBalance(content.getThirdUserId(), param.getPlatformCode());
    }

    public PlayerInfo getPlayInfo(PlayerInfoParam param){
        log.debug("spribe get player info callback, param: {}", param);
        tokenService.checkSessionToken(param.getSession_token(),param.getPlatformCode(), param.getUser_id(), null);
        return getThirdUserBalance(param.getUser_id(), param.getPlatformCode());
    }

    public BalanceOptRes withdraw(WithdrawParam param){
        log.debug("spribe withdraw callback, param: {}", param);
        tokenService.checkSessionToken(param.getSession_token(), param.getPlatformCode(), param.getUser_id(), param.getGame().toLowerCase());
        ThirdSeamlessBetOrderEntity entity = buildWithdrawBetOrderEntity(param);
        long oldBalance = walletInnerService.getBalance(param.getPlatformCode(), param.getUser_id());
        if (oldBalance < entity.getBetMoney()) {
            throw new SPRIBEException(SPRIBEResCodeEnum.INSUFFICIENT_FUND);
        }
        try {
            // newBalance + betMoney = oldBalance
            long newBalance = betOrderInnerService.addBetOrder(entity);
            return buildWithdrawResult(newBalance, newBalance + entity.getBetMoney(), param);
        } catch (Exception e){
            log.error("spribe withdraw failed, param: {}", JacksonUtil.toJSONString(param), e);
            handleException(e);
        }
        throw new SPRIBEException(SPRIBEResCodeEnum.INTERNAL_ERROR);
    }

    public BalanceOptRes deposit(DepositParam param){
        log.debug("spribe deposit callback, param: {}", param);
        tokenService.checkSessionToken(param.getSession_token(), param.getPlatformCode(), param.getUser_id(), param.getGame().toLowerCase());
        ThirdSeamlessBetOrderEntity entity = buildDepositBetOrderEntity(param);
        try {
            // newBalance - winMoney = oldBalance
            long newBalance = betOrderInnerService.addPayoutOrder(entity);
            return buildDepositResult(newBalance, newBalance - entity.getWinMoney(), param);
        } catch (Exception e) {
            log.error("spribe deposit failed, param: {}", JacksonUtil.toJSONString(param), e);
            handleException(e);
        }
        throw new SPRIBEException(SPRIBEResCodeEnum.INTERNAL_ERROR);
    }

    public BalanceOptRes rollback(RollbackParam param){
        log.debug("spribe rollback callback, param: {}", param);
        tokenService.checkSessionToken(param.getSession_token(), param.getPlatformCode(), param.getUser_id(), param.getGame().toLowerCase());
        String refId = param.getRollback_provider_tx_id();

        try {
            betOrderInnerService.rollbackOrder(param.getPlatformCode(), refId);
            PlayerInfo playerInfo = getThirdUserBalance(param.getUser_id(), param.getPlatformCode());
            // newBalance - betMoney = oldBalance
            return buildRollbackResult(playerInfo, param);
        } catch (Exception e) {
            log.error("spribe rollback failed, param: {}", JacksonUtil.toJSONString(param), e);
            handleException(e);
        }
        throw new SPRIBEException(SPRIBEResCodeEnum.INTERNAL_ERROR);
    }


    private PlayerInfo getThirdUserBalance(String thirdUserId, String platformCode) {
        PlayerInfo playerInfo = new PlayerInfo();
        try {
            ThirdUserDTO thirdSiteUser = thirdSiteUserInnerService.getThirdSiteUser(platformCode, thirdUserId);
            playerInfo.setUser_id(thirdUserId);
            playerInfo.setUsername(thirdSiteUser.getThirdUserName());
            playerInfo.setCurrency(mappingCurrency(thirdSiteUser.getCurrencyEnum()));

            long balance = walletInnerService.getBalance(platformCode, thirdUserId);
            playerInfo.setBalance(toSpribeMoney(balance));
        } catch (Exception e) {
            throw new SPRIBEException(SPRIBEResCodeEnum.TOKEN_INVALID);
        }

        return playerInfo;
    }

    private String mappingCurrency(CurrencyEnum currencyEnum) {
        if (CurrencyEnum.KVND == currencyEnum) {
            return "VND";
        }
        return currencyEnum.name();
    }

    private long toSpribeMoney(long money){

        return new BigDecimal(money)
                .divide(new BigDecimal(100), 2, RoundingMode.DOWN)
                .multiply(new BigDecimal(moneyUnit))
                .longValue();
    }

    private long toSystemMoney(long money){
        return new BigDecimal(money)
                .divide(new BigDecimal(moneyUnit), 2, RoundingMode.DOWN)
                .multiply(new BigDecimal(100))
                .longValue();
    }

    private ThirdSeamlessBetOrderEntity buildWithdrawBetOrderEntity(WithdrawParam param) {
        ThirdSeamlessBetOrderEntity entity = new ThirdSeamlessBetOrderEntity();
        entity.setPlatformCode(param.getPlatformCode());
        entity.setThirdUserName(param.getUser_id());
        entity.setOrderNum(param.getProvider_tx_id());
        entity.setThirdGameId(param.getGame());
        long betMoney = toSystemMoney(param.getAmount());
        entity.setBetMoney(betMoney);
        entity.setValidBet(betMoney);
        entity.setBetTime(LocalDateTime.now());
        entity.setOrderStatusEnum(ThirdBetOrderStatusEnum.PENDING);
        entity.setWinTypeEnum(BetOrderWinTypeEnum.NORMAL);
        return entity;
    }

    private BalanceOptRes buildWithdrawResult(long newBalance, long oldBalance, WithdrawParam param) {
        BalanceOptRes res =  new BalanceOptRes();
        res.setUser_id(param.getUser_id());
        res.setCurrency(param.getCurrency());
        res.setProvider(param.getProvider());
        res.setProvider_tx_id(param.getProvider_tx_id());
        res.setNew_balance(toSpribeMoney(newBalance));
        res.setOld_balance(toSpribeMoney(oldBalance));

        res.setOperator_tx_id(IdWorker.get32UUID());
        return res;
    }

    private ThirdSeamlessBetOrderEntity buildDepositBetOrderEntity(DepositParam param) {
        ThirdSeamlessBetOrderEntity entity = new ThirdSeamlessBetOrderEntity();
        entity.setPlatformCode(param.getPlatformCode());
        // 结算订单号
        entity.setOrderNum(param.getWithdraw_provider_tx_id());
        // 派奖金额
        long payoutMoney = toSystemMoney(param.getAmount());
        entity.setWinMoney(payoutMoney);
        entity.setSettleTime(LocalDateTime.now());
        entity.setOrderStatusEnum(ThirdBetOrderStatusEnum.COMPLETED);

        BetOrderWinTypeEnum winTypeEnum = parseWinType(param.getAction());
        entity.setWinTypeEnum(winTypeEnum);

        return entity;
    }

    private BetOrderWinTypeEnum parseWinType(SPRIBEDepositActionEnum action) {

        switch (action) {
            case freebet:
            case rainfreebet:
            case promofreebet:
                return BetOrderWinTypeEnum.FREE;
            default:
                return BetOrderWinTypeEnum.NORMAL;
        }
    }

    private BalanceOptRes buildDepositResult(long newBalance, long oldBalance, DepositParam param) {
        BalanceOptRes res =  new BalanceOptRes();
        res.setUser_id(param.getUser_id());
        res.setCurrency(param.getCurrency());
        res.setProvider(param.getProvider());
        res.setProvider_tx_id(param.getProvider_tx_id());
        res.setNew_balance(toSpribeMoney(newBalance));
        res.setOld_balance(toSpribeMoney(oldBalance));

        res.setOperator_tx_id(IdWorker.get32UUID());
        return res;
    }

    private BalanceOptRes buildRollbackResult(PlayerInfo playerInfo, RollbackParam param) {
        BalanceOptRes res =  new BalanceOptRes();
        res.setUser_id(param.getUser_id());
        res.setProvider(param.getProvider());
        res.setProvider_tx_id(param.getProvider_tx_id());
        res.setCurrency(playerInfo.getCurrency());

        long spribeBalance = playerInfo.getBalance();

        res.setNew_balance(spribeBalance);
        long spribeAmount = param.getAmount();
        res.setOld_balance(spribeBalance - spribeAmount);

        res.setOperator_tx_id(IdWorker.get32UUID());
        return res;
    }


    /**
     * 处理异常
     *
     * @param e 业务异常
     */
    private static void handleException(Exception e) {
        log.error("handle spribe callback exception", e);
        if (e instanceof DuplicateKeyException) {
            throw new SPRIBEException(SPRIBEResCodeEnum.DUPLICATE_TRANSACTION);
        }
        if (e instanceof ApiException) {
            ApiException ae = (ApiException) e;
            IErrorCode errorCode = ae.getErrorCode();
            if (CommonCode.SEAMLESS_BET_ORDER_NOT_EXISTS.equals(errorCode)) {
                throw new SPRIBEException(SPRIBEResCodeEnum.TRANSACTION_NOT_FOUND);
            }
            if (CommonCode.SEAMLESS_BET_ORDER_ACCEPTED.equals(errorCode)) {
                throw new SPRIBEException(SPRIBEResCodeEnum.DUPLICATE_TRANSACTION);
            }
            // 余额不足
            if (CommonCode.SEAMLESS_WALLET_NOT_ENOUGH_MONEY.equals(errorCode)) {
                throw new SPRIBEException(SPRIBEResCodeEnum.INSUFFICIENT_FUND);
            }
        }
        // 未知异常
        throw new SPRIBEException(SPRIBEResCodeEnum.INTERNAL_ERROR);
    }
}
