package com.wd.lottery.module.third.controller.callback;

import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.dock.zbridge.smartsoft.SMARTSOFTCallbackService;
import com.wd.lottery.module.third.dock.zbridge.smartsoft.param.*;
import com.wd.lottery.module.third.dock.zbridge.smartsoft.res.*;
import com.wd.lottery.module.third.dock.zbridge.smartsoft.param.*;
import com.wd.lottery.module.third.dock.zbridge.smartsoft.res.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("${callback-path}/${module-path.third}/smartsoft")
public class SMARTSOFTCallbackController {

    @Autowired
    private SMARTSOFTCallbackService smartsoftCallbackService;

    @PostMapping(value = "ActivateSession")
    public void activateSession(@RequestBody ActivateSessionParam param, HttpServletResponse response) {
        log.debug("smart soft activate session: {}", param);
        try {
            ActivateSessionResponse result = this.smartsoftCallbackService.activateSession(param);
            this.sendResult(response, result);
        } catch (Exception e) {
            log.error("smart soft activate session error", e);
            this.smartsoftCallbackService.invokeException(response, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Verify Token Failure!");
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }

    @GetMapping(value = "GetBalance")
    public void getBalance(HttpServletResponse response) {
        try {
            GetBalanceResponse result = this.smartsoftCallbackService.getBalance();
            this.sendResult(response, result);
        } catch (Exception e) {
            log.error("smart soft getBalance error", e);
            this.smartsoftCallbackService.invokeException(response, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Get Balance Failure!");
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }

    @PostMapping(value = "Withdraw")
    public void withdraw(@RequestBody WithdrawParam param, HttpServletResponse response) {
        log.debug("smart soft withdraw: {}", param);
        try {
            WithdrawResponse result = this.smartsoftCallbackService.withdraw(param);
            this.sendResult(response, result);
        } catch (Exception e) {
            boolean isAccepted = (e instanceof ApiException) || ((ApiException) e).getErrorCode() == CommonCode.SEAMLESS_BET_ORDER_ACCEPTED;
            if (isAccepted) {
                CommonParam commonParam = this.smartsoftCallbackService.analyzeCommonParam();
                BigDecimal balance = this.smartsoftCallbackService.getBalance(commonParam.getClientExternalKey(), commonParam.getUserName());
                WithdrawResponse result = WithdrawResponse.builder().transactionId(param.getTransactionId()).balance(balance).build();
                this.sendResult(response, result);
            } else {
                log.error("smart soft withdraw error", e);
                this.smartsoftCallbackService.invokeException(response, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Withdraw Failure!");
                response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            }
        }
    }

    @PostMapping(value = "Deposit")
    public void deposit(@RequestBody DepositParam param, HttpServletResponse response) {
        log.debug("smart soft deposit: {}", param);
        try {
            DepositResponse result = this.smartsoftCallbackService.deposit(param);
            this.sendResult(response, result);
        } catch (Exception e) {
            log.error("smart soft deposit error", e);
            this.smartsoftCallbackService.invokeException(response, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Deposit Failure!");
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }

    @PostMapping(value = "RollbackTransaction")
    public void rollbackTransaction(@RequestBody RollbackTransactionParam param, HttpServletResponse response) {
        log.debug("smart soft RollbackTransaction: {}", param);
        try {
            RollbackTransactionResponse result = this.smartsoftCallbackService.rollbackTransaction(param);
            this.sendResult(response, result);
        } catch (Exception e) {
            boolean isAccepted = (e instanceof ApiException) || ((ApiException) e).getErrorCode() == CommonCode.SEAMLESS_BET_ORDER_ACCEPTED;
            if (isAccepted) {
                CommonParam commonParam = this.smartsoftCallbackService.analyzeCommonParam();
                BigDecimal balance = this.smartsoftCallbackService.getBalance(commonParam.getClientExternalKey(), commonParam.getUserName());
                RollbackTransactionResponse result = RollbackTransactionResponse.builder().transactionId(param.getTransactionId()).balance(balance).build();
                this.sendResult(response, result);
            } else {
                log.error("smart soft rollback transaction error", e);
                this.smartsoftCallbackService.invokeException(response, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Rollback Transaction Failure!");
                response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            }
        }
    }

    private <T> void sendResult(HttpServletResponse response, T result) {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json");
            response.setStatus(HttpStatus.OK.value());
            PrintWriter out = response.getWriter();
            out.print(JacksonUtil.toJSONString(result));
            out.flush();
        } catch (IOException e) {
            this.smartsoftCallbackService.invokeException(response, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Send Result Failure!");
        }
    }
}