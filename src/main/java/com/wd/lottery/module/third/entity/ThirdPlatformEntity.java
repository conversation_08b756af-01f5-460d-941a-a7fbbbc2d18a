package com.wd.lottery.module.third.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

@Data
@TableName("third_platform")
@Slf4j
public class ThirdPlatformEntity {

  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  /**
   * 平台code
   */
  private String code;
  /**
   * 平台名称
   */
  private String name;

  /**
   * 桥接平台编码
   */
  private String bridgePlatformCode;

  /**
   * 平台logo
   */
  private String platformImg;

  /**
   * 开通币种
   */
  private String currencies;

  /**
   * 游戏种类, 多个逗号间隔
   * @see GameCategoryEnum
   */
  private String categories;

  /**
   * 平台状态, 0 禁用 1 启用
   */
  private EnableEnum enableEnum;
  /**
   * 是否删除 0 未删除 1 已删除
   */
  private BooleanEnum isDel;

  /**
   * 平台配置
   */
  private String configJson;

  /**
   * 创建时间
   */
  private LocalDateTime createTime;

  /**
   * 更新时间
   */
  private LocalDateTime updateTime;

}
