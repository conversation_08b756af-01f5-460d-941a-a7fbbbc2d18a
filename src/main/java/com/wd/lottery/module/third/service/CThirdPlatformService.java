package com.wd.lottery.module.third.service;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.dto.IndexDataDTO;
import com.wd.lottery.module.common.vo.InitDataVO;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dto.PlayerLastRecordGameDTO;
import com.wd.lottery.module.third.dto.ThirdSiteGameDTO;
import com.wd.lottery.module.third.dto.ThirdSitePlatformItemDTO;

import java.util.List;

/**
 * Description: C端三方模块 service
 *
 * <p> Created on 2024/5/31.
 *
 * <AUTHOR>
 * @version 0.1
 */
public interface CThirdPlatformService {

    /**
     * 查询三方支持的平台
     *
     * @param currencyEnum 币种
     * @param merchantId   商户ID
     * @return 平台列表
     */
    List<ThirdSitePlatformItemDTO> listPlatform(CurrencyEnum currencyEnum, Long merchantId);

    void getThirdSitePlatformList(IndexDataDTO indexDataDTO, InitDataVO initDataVO);

    void getHotThirdSiteGameList(IndexDataDTO indexDataDTO, InitDataVO initDataVO);

    /**
     * 查询商户平台游戏
     *
     * @param platformCode 平台编码
     * @param categoryEnum 游戏分类
     * @param currencyEnum 币种
     * @param merchantId   商户ID
     * @return 游戏列表
     */
    List<ThirdSiteGameDTO> listGameByPlatform(String platformCode, GameCategoryEnum categoryEnum, CurrencyEnum currencyEnum, Long merchantId);

    /**
     * 获取游戏登录地址
     *
     * @param platformCode  平台编码
     * @param gameId        游戏ID
     * @param deviceEnum    设备类型
     * @param merchantId    商户ID
     * @return 游戏登录地址
     */
    String getGameLoginUrl(String platformCode, Long gameId, DeviceEnum deviceEnum, Long merchantId);

    /**
     * 资金归集）
     *
     * @param memberId 会员ID
     * @param merchantId 商户ID
     */
    void collectMoney(Long memberId, Long merchantId);

    /**
     * getAllGameList
     *
     * @param merchantId    merchantId
     * @param currencyEnum  currencyEnum
     * @return game list
     */
    List<ThirdSiteGameDTO> getAllGameList(Long merchantId, CurrencyEnum currencyEnum);

    /**
     * 获取玩家最近的游戏记录，最多30条，排序为游戏时间降序
     * @param merchantId 商户id
     * @return {@link List}
     */
    List<PlayerLastRecordGameDTO> getPlayerLastGameRecord(Long merchantId);

    void transOutThirdMoney(String platformCode, Long memberId, Long merchantId);

    void quickCollectMoney(Long memberId, Long merchantId);
}
