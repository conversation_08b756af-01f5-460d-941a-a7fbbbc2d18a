package com.wd.lottery.module.third.controller.consumer;

import cn.hutool.core.util.StrUtil;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.MemberTokenInfoUtil;
import com.wd.lottery.component.auth.ApiNotAuth;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.constants.ThirdConstants;
import com.wd.lottery.module.third.constants.ThirdRedisConstants;
import com.wd.lottery.module.third.dto.PlayerLastRecordGameDTO;
import com.wd.lottery.module.third.dto.ThirdSiteGameDTO;
import com.wd.lottery.module.third.dto.ThirdSitePlatformItemDTO;
import com.wd.lottery.module.third.service.CThirdPlatformService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Description: c 端三方 controller
 *
 * <p> Created on 2024/5/31.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Tag(name = "C端三方模块")
@RestController
@RequestMapping("${consumer-path}/${module-path.third}/"+ThirdConstants.THIRD_PLATFORM_PATH)
// todo 检查会员是否进入三方后续会把数据放在token info内用于判断
public class CThirdPlatformController {

    private final CThirdPlatformService cThirdPlatformService;
    private final StringRedisTemplate stringRedisTemplate;

    public CThirdPlatformController(CThirdPlatformService cThirdPlatformService, StringRedisTemplate stringRedisTemplate) {
        this.cThirdPlatformService = cThirdPlatformService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @ApiNotAuth
    @Operation(summary = "查询商户平台列表")
    @Parameters({@Parameter(name = "currencyEnum", description = "币种")})
    @GetMapping("listPlatform")
    public ApiResult<List<ThirdSitePlatformItemDTO>> listPlatform(@RequestParam CurrencyEnum currencyEnum,
                                                                  @RequestHeader Long merchantId){
        List<ThirdSitePlatformItemDTO> list = cThirdPlatformService.listPlatform(currencyEnum, merchantId);

        return ApiResult.success(list);
    }

    @ApiNotAuth
    @Operation(summary = "查询商户平台游戏")
    @Parameters({@Parameter(name = "platformCode", description = "平台编码"),
            @Parameter(name = "categoryEnum", description = "游戏分类")
    })
    @GetMapping("listGameByPlatform")
    public ApiResult<List<ThirdSiteGameDTO>> listGameByPlatform(@RequestParam String platformCode,
                                                                @RequestParam(required = false) GameCategoryEnum categoryEnum,
                                                                @RequestParam CurrencyEnum currencyEnum,
                                                                @RequestHeader Long merchantId){
        List<ThirdSiteGameDTO> list = cThirdPlatformService.listGameByPlatform(platformCode, categoryEnum, currencyEnum, merchantId);
        return ApiResult.success(list);
    }

    @ApiNotAuth
    @Operation(summary = "查询所有游戏列表(不含大厅登录模式)")
    @GetMapping("getAllGameList")
    public ApiResult<List<ThirdSiteGameDTO>> getAllGameList(@RequestHeader Long merchantId, @RequestParam CurrencyEnum currencyEnum){
        List<ThirdSiteGameDTO> list = cThirdPlatformService.getAllGameList(merchantId, currencyEnum);
        return ApiResult.success(list);
    }

    @Operation(summary = "获取游戏登录地址")
    @Parameters({@Parameter(name = "platformCode", description = "平台编码"),
            @Parameter(name = "gameId", description = "游戏ID"),
            @Parameter(name = "deviceEnum", description = "设备类型")
    })
    @GetMapping(ThirdConstants.THIRD_GAME_OPEN_URL_PATH)
    public ApiResult<String> getGameLoginUrl(@RequestParam String platformCode, @RequestParam Long gameId, @RequestParam DeviceEnum deviceEnum,  @RequestHeader Long merchantId){
        String url = cThirdPlatformService.getGameLoginUrl(platformCode, gameId, deviceEnum, merchantId);
        return ApiResult.success(url);
    }

    @ApiNotAuth
    @GetMapping(ThirdConstants.THIRD_GAME_OPEN_HTML_PATH)
    @Operation(summary = "查询三方游戏打开链接")
    public String getThirdGameOpenHtml(@RequestParam String token, HttpServletResponse httpServletResponse) {
        httpServletResponse.setHeader(ThirdConstants.CACHE_CONTROL_HEADER, ThirdConstants.CACHE_CONTROL_NOT_CACHE);
        httpServletResponse.setHeader("Content-Type", MediaType.TEXT_HTML_VALUE);
        if(StrUtil.isBlank(token)){
            log.debug("getThirdGameOpenHtml token is blank");
            return ThirdConstants.GAME_OPEN_GET_HTML_FAIL_MESSAGE;
        }
        String thirdUserGameDtoJson = stringRedisTemplate.opsForValue().get(ThirdRedisConstants.OPEN_GAME_HTML_TOKEN + token);
        if(StrUtil.isBlank(thirdUserGameDtoJson)){
            log.debug("getThirdGameOpenHtml thirdUserGameDtoJson is blank, token:{}", token);
            return ThirdConstants.GAME_OPEN_GET_HTML_FAIL_MESSAGE;
        }
        return thirdUserGameDtoJson;
    }

    @Operation(summary = "资金归集")
    @PostMapping("collectMoney")
    public ApiResult<Boolean> collectMoney() {
        cThirdPlatformService.collectMoney(MemberTokenInfoUtil.getMemberId(), MemberTokenInfoUtil.getMerchantId());
        return ApiResult.success(true);
    }

    @Operation(summary = "获取玩家最近的游戏记录，最多30条，排序为游戏时间降序")
    @GetMapping("getPlayerLastGameRecord")
    public ApiResult<List<PlayerLastRecordGameDTO>> getPlayerLastGameRecord(@RequestHeader Long merchantId) {
        List<PlayerLastRecordGameDTO> games = this.cThirdPlatformService.getPlayerLastGameRecord(merchantId);
        return ApiResult.success(games);
    }

    @Operation(summary = "归集指定三方资金")
    @PostMapping("transOutThirdMoney")
    public ApiResult<Boolean> transOutThirdMoney(@RequestParam(required = false) String platformCode) {
        cThirdPlatformService.transOutThirdMoney(platformCode, MemberTokenInfoUtil.getMemberId(), MemberTokenInfoUtil.getMerchantId());
        return ApiResult.success(true);
    }

    @Operation(summary = "快速归集资金")
    @PostMapping("quickCollectMoney")
    public ApiResult<Boolean> quickCollectMoney() {
        cThirdPlatformService.quickCollectMoney(MemberTokenInfoUtil.getMemberId(), MemberTokenInfoUtil.getMerchantId());
        return ApiResult.success(true);
    }
}
