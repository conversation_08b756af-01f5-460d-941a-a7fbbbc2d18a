package com.wd.lottery.module.third.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.constants.GameSourceEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("third_game")
public class ThirdGameEntity {

  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  private String platformCode;
  private String thirdGameId;
  private String gameCode;
  private String gameName;
  /**
   * 游戏所属分类
   */
  private GameCategoryEnum gameCategoryEnum;
  /**
   * 所属分类名称
   */
  private String gameCategoryName;

  /**
   * 支持币种，多个逗号间隔
   */
  private String supportedCurrency;

  @Schema(description = "游戏图片")
  private String gameImg;

  private EnableEnum enableEnum;
  /**
   * 0:不维护，1：维护
   */
  private BooleanEnum isMaintain;
  /**
   * 是否删除 0:不删除，1：删除
   */
  private BooleanEnum isDel;
  /**来源，1：三方接口 ，2：人工录入**/
  private GameSourceEnum gameSourceEnum;

  private String remark;

  /**
   * 创建时间
   */
  private LocalDateTime createTime;

  /**
   * 更新时间
   */
  private LocalDateTime updateTime;


  public void setGameCategoryEnum(GameCategoryEnum gameCategoryEnum) {
    this.gameCategoryEnum = gameCategoryEnum;
    this.gameCategoryName = gameCategoryEnum.name();
  }
}
