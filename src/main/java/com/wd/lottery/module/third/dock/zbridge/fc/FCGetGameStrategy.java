package com.wd.lottery.module.third.dock.zbridge.fc;


import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.fc.common.FCApiEnum;
import com.wd.lottery.module.third.dock.zbridge.fc.common.FCHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.fc.common.FCRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.fc.res.FCGameListRes;
import com.wd.lottery.module.third.dock.zbridge.fc.res.FCGameUrlRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


@Component(BridgeConstant.FC_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
@Slf4j
public class FCGetGameStrategy extends AbstractGetGameStrategy {


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        FCRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, FCRequestConfig.class);
        FCHttpRequestTemplate requestTemplate = new FCHttpRequestTemplate(requestConfig, currencyEnum);
        FCGameListRes execute = requestTemplate
                .toPOST()
                .api(FCApiEnum.GET_GAMELIST.getPath())
                .toBeanAndCall(FCGameListRes.class);
        Map<String, Map<String, FCGameListRes.GameItem>> thirdGameMap = execute.getGetGameIconList();
        if (MapUtils.isEmpty(thirdGameMap)) {
            return Collections.emptyList();
        }
        List<DockGame> dockGames = new ArrayList<>(100);
        thirdGameMap.forEach((k, v) -> {
            GameCategoryEnum gameCategory = parseGameCategory(k);
            if (!MapUtils.isEmpty(v)) {
                v.forEach((k1, v1) -> {
                    DockGame dockGame = toDockGame(platformCode, gameCategory, k1, v1);
                    dockGames.add(dockGame);
                });
            }
        });

        return dockGames;
    }




    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("FC SLOT getOpenGameUrl dto:{} ", dto);

        CurrencyEnum currencyEnum = dto.getCurrencyEnum();
        FCRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), currencyEnum, FCRequestConfig.class);
        FCHttpRequestTemplate requestTemplate = new FCHttpRequestTemplate(requestConfig, currencyEnum);

        // HomeUrl:回首页按钮的 URL（非必要），此参数结合游戏表现
        final String defaultLang = "1";
        FCGameUrlRes execute = requestTemplate
                .api(FCApiEnum.GET_GAMEURL.getPath())
                .toPOST()
                .addParameter("MemberAccount", dto.getThirdUserName())
                .addParameter("GameID", dto.getThirdGameId())
                .addParameter("LanguageID", ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang))
                .addParameter("HomeUrl", dto.getLobbyUrl())
                .toBeanAndCall(FCGameUrlRes.class);
        return execute.getUrl();
    }

    @Override
    public String getDemoGameUrl(DockGetGameUrl param) {
        FCRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(param.getPlatformCode(), param.getCurrencyEnum(), FCRequestConfig.class);
        FCHttpRequestTemplate requestTemplate = new FCHttpRequestTemplate(requestConfig, param.getCurrencyEnum());

        final String defaultLang = "1";
        FCGameUrlRes execute = requestTemplate
                .host(requestConfig.getDemoGameUrl())
                .api(FCApiEnum.DEMO_GAME_URL.getPath())
                .toPOST()
                .addParameter("GameID", param.getThirdGameId())
                .addParameter("LanguageID", ThirdPlatformMappingConverter.toThirdLang(requestConfig, param.getLang(), defaultLang))
                .toBeanAndCall(FCGameUrlRes.class);
        return execute.getUrl();
    }

    private GameCategoryEnum parseGameCategory(String thirdGameCategory) {
        if (StringUtils.isBlank(thirdGameCategory)) {
            return GameCategoryEnum.SLOT;
        }
        switch (thirdGameCategory) {
            case "fishing":
                return GameCategoryEnum.FISH;
            case "arcade":
            case "table":
                return GameCategoryEnum.MINI;
            default:
                //
        }
        return GameCategoryEnum.SLOT;
    }
    private DockGame toDockGame(String platformCode, GameCategoryEnum gameCategory, String thirdGameId, FCGameListRes.GameItem gameItem) {
        DockGame dockGame = new DockGame();
        dockGame.setPlatformCode(platformCode);
        dockGame.setThirdGameId(thirdGameId);
        dockGame.setGameName(gameItem.getGameNameOfEnglish());
        dockGame.setGameCode(gameItem.getGameNameOfEnglish());
        dockGame.setGameCategoryEnum(gameCategory);

        return dockGame;
    }
}
