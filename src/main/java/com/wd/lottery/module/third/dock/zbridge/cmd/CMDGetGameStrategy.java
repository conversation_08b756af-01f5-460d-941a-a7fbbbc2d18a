package com.wd.lottery.module.third.dock.zbridge.cmd;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.cmd.common.CMDAPIEnum;
import com.wd.lottery.module.third.dock.zbridge.cmd.common.CMDRequestConfig;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 游戏策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.CMD_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class CMDGetGameStrategy extends AbstractGetGameStrategy {

    @Autowired
    private CMDVerifyTokenService tokenService;

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        log.debug("CMD:下载游戏:{}-{}", platformCode, currencyEnum);
        List<DockGame> games = Lists.newArrayList();
        ClassPathResource resource = new ClassPathResource("third/cmd_game.json");
        try (InputStream inputStream = resource.getInputStream()) {
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, String>> data = mapper.readValue(inputStream, new TypeReference<List<Map<String, String>>>() {
            });
            List<DockGame> tmpGames = data.stream()
                    .map(game -> {
                        DockGame dockGame = new DockGame();
                        dockGame.setPlatformCode(platformCode);
                        dockGame.setGameCategoryEnum(GameCategoryEnum.SPORT);
                        dockGame.setThirdGameId(game.get("code"));
                        dockGame.setGameName(game.get("name"));
                        dockGame.setGameCode(game.get("code"));
                        return dockGame;
                    }).collect(Collectors.toList());
            games.addAll(tmpGames);
        } catch (Exception e) {
            log.error(StrUtil.format("CMD:下载游戏:{}-{}", platformCode, currencyEnum), e);
        }
        return games;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("CMD:玩家打开游戏:{}", JSONUtil.toJsonStr(dto));
        CurrencyEnum currencyEnum = dto.getCurrencyEnum();
        CMDRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), currencyEnum, CMDRequestConfig.class);
        final String defaultLang = "en-US";
        String thirdCurr = ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, currencyEnum);
        // 移动端?
        boolean isMobile = DeviceEnum.IOS.equals(dto.getDeviceEnum()) || DeviceEnum.ANDROID.equals(dto.getDeviceEnum());
        String link = isMobile ? requestConfig.getMobileLink() : requestConfig.getPcLink();
        String token = this.tokenService.generateToken(dto.getPlatformCode(), dto.getThirdUserName(), thirdCurr, requestConfig.getSecretKey());
        // 游戏打开路径
        String openGameUrl = String.format(CMDAPIEnum.OPEN_GAME.getPath(),
                ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang)
                , dto.getThirdUserName(),
                token,
                thirdCurr,
                "green",
                "v1");
        openGameUrl = link + openGameUrl;
        log.debug("CMD:玩家打开游戏链接:{}", openGameUrl);
        return openGameUrl;
    }
}