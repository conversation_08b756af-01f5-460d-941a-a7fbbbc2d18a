package com.wd.lottery.module.third.dock.zbridge.jdb;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.jdb.common.JDBHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.jdb.common.JDBRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.jdb.res.JDBBetDetailUrl;
import com.wd.lottery.module.third.dock.zbridge.jdb.res.JDBBetOrderData;
import com.wd.lottery.module.third.dock.zbridge.jdb.res.JDBResponse;
import com.wd.lottery.module.third.entity.ThirdGameEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Description: game service strategy
 *
 * <p> Created on 2024/5/23.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.JDB_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class JDBGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {


    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        JDBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), JDBRequestConfig.class);
        JDBHttpRequestTemplate requestTemplate = new JDBHttpRequestTemplate(requestConfig);

        LocalDateTime towHoursAgo = LocalDateTime.now().withSecond(0).plusHours(-2);

        LocalDateTime begin = flag.getBegin().withSecond(0);
        LocalDateTime end = flag.getEnd().withSecond(0);
        if (begin.isAfter(towHoursAgo)) {
            return getLatestOrder(requestTemplate, flag);
        }
        if (end.isBefore(towHoursAgo)) {
            return getHistoryOrder(requestTemplate, flag);
        }
        return getMixOrder(requestTemplate, flag);

    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        JDBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), JDBRequestConfig.class);
        JDBHttpRequestTemplate requestTemplate = new JDBHttpRequestTemplate(requestConfig);

        String gameTypeCode = getGameTypeCode(dto.getThirdGameId(), dto.getPlatformCode());

        try {
            JDBResponse<List<JDBBetDetailUrl>> jdbResponse = requestTemplate.toPOST()
                    .addParameter("action", "54") // 历史游戏数据接口
                    .addParameter("ts", System.currentTimeMillis() + "")
                    .addParameter("uid", dto.getThirdUserId())
                    .addParameter("lang", "en")
                    .addParameter("gType", gameTypeCode)
                    .addParameter("historyId", dto.getOrderNo())
                    .toBeanAndCall(new TypeReference<JDBResponse<List<JDBBetDetailUrl>>>() {
                    });

            List<JDBBetDetailUrl> data = jdbResponse.getData();
            if (CollectionUtils.isEmpty(data)) {
                throw new ThirdPlatformException("cannot get bet detail url, historyId = " + dto.getOrderNo());
            }
            return data.get(0).getPath();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }


    private List<DockBetOrder> getLatestOrder(JDBHttpRequestTemplate requestTemplate, BetOrderPullFlag flag) {
        List<JDBBetOrderData> list = getThirdOrder(requestTemplate, flag.getBegin(), flag.getEnd(), flag.getPlatformCode(), "29");
        flag.setFinished(true);
        return toDockBetOrderList(list, flag);
    }


    private List<DockBetOrder> getHistoryOrder(JDBHttpRequestTemplate requestTemplate, BetOrderPullFlag flag) {
        List<JDBBetOrderData> list = getThirdOrder(requestTemplate, flag.getBegin(), flag.getEnd(), flag.getPlatformCode(), "64");
        flag.setFinished(true);
        return toDockBetOrderList(list, flag);
    }

    private List<DockBetOrder> getMixOrder(JDBHttpRequestTemplate requestTemplate, BetOrderPullFlag flag) {

        List<JDBBetOrderData> list = new ArrayList<>(100);
        LocalDateTime towHoursAgo = LocalDateTime.now().withSecond(0).plusHours(-2);
        // his
        List<JDBBetOrderData> hisList = getThirdOrder(requestTemplate, flag.getBegin(), towHoursAgo, flag.getPlatformCode(), "64");
        // latest
        List<JDBBetOrderData> latestList = getThirdOrder(requestTemplate, towHoursAgo, flag.getEnd(), flag.getPlatformCode(), "29");
        if (CollectionUtils.isNotEmpty(hisList)) {
            list.addAll(hisList);
        }
        if (CollectionUtils.isNotEmpty(latestList)) {
            list.addAll(latestList);
        }
        flag.setFinished(true);
        return toDockBetOrderList(list, flag);
    }

    private List<JDBBetOrderData> getThirdOrder(JDBHttpRequestTemplate requestTemplate, LocalDateTime begin, LocalDateTime end, String platformCode, String action){

        String startTime = ThirdPlatformMappingConverter.parsePullOrderParamTime(begin.withSecond(0), platformCode);
        String endTime = ThirdPlatformMappingConverter.parsePullOrderParamTime(end.withSecond(0), platformCode);

        log.debug("request jdb bet order, begin: {}, end: {}, action: {}", startTime, endTime, action);

        JDBResponse<List<JDBBetOrderData>> jdbResponse = requestTemplate.toPOST()
                .addParameter("action", action) // 历史游戏数据接口
                .addParameter("ts", System.currentTimeMillis() + "")

                .addParameter("starttime", startTime)
                .addParameter("endtime", endTime)
                .toBeanAndCall(new TypeReference<JDBResponse<List<JDBBetOrderData>>>() {
                });

        return jdbResponse.getData();
    }



    private List<DockBetOrder> toDockBetOrderList(List<JDBBetOrderData> betOrderData, BetOrderPullFlag flag) {
        if (CollectionUtils.isEmpty(betOrderData)) {
            return Collections.emptyList();
        }
        String platformCode = flag.getPlatformCode();

        List<DockBetOrder> resultList = new ArrayList<>(betOrderData.size());

        for (JDBBetOrderData order : betOrderData) {
            DockBetOrder dockOrder = new DockBetOrder();
            dockOrder.setThirdUserName(order.getPlayerId());
            dockOrder.setPlatformCode(platformCode);
            dockOrder.setOrderNum(order.getHistoryId());

            dockOrder.setGameId(order.getMtype());

            BigDecimal betMoney = calculateBetMoney(order);

            dockOrder.setBetMoney(toPlatformMoney(betMoney));
            dockOrder.setWinMoney(toPlatformMoney(order.getWin()));
            dockOrder.setValidBetMoney(toPlatformMoney(betMoney));

            LocalDateTime orderTime = ThirdPlatformMappingConverter.parseThirdOrderTime(order.getLastModifyTime(), platformCode);

            dockOrder.setOrderTime(orderTime);

            BigDecimal jackpot = order.getJackpot();
            if (Objects.nonNull(jackpot)) {
                dockOrder.setJackpot(toPlatformMoney(jackpot));
            }

            resultList.add(dockOrder);
        }

        return resultList;

    }

    private  BigDecimal calculateBetMoney(JDBBetOrderData order) {
        BigDecimal betMoney;
        if(order.getValidBet() != null && !Objects.equals(order.getValidBet().doubleValue(), 0d)){
            betMoney = order.getValidBet().abs();
        }else if(order.getGambleBet() != null && !Objects.equals(order.getGambleBet().doubleValue(), 0d)){
            betMoney = order.getGambleBet().abs();
        } else{
            BigDecimal bet = order.getBet() != null ? order.getBet() : BigDecimal.ZERO;
            betMoney = bet.abs();
        }
        return betMoney;
    }

    private String getGameTypeCode(String thirdGameId, String platformCode) {
        ThirdGameEntity thirdGame = ThirdPlatformLocalCacheUtil.getThirdGame(platformCode, thirdGameId);
        if (Objects.isNull(thirdGame)) {
            throw new IllegalStateException("third game not found, id: " + thirdGameId);
        }
        return thirdGame.getRemark();
    }

}
