package com.wd.lottery.module.third.dock.game;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import lombok.Data;

@Data
public class DockGetGameUrl {

    private Long merchantId;
    private Long memberId;
    // 平台编码
    private String platformCode;
    //货币类型
    private CurrencyEnum currencyEnum;

    private String gameCode;
    private String thirdGameId;
    private GameCategoryEnum categoryEnum;
    private String remark;

    private String thirdUserId;
    private String thirdUserName;
    private String thirdUserPasswd;

    private String lang;

    private DeviceEnum deviceEnum;

    private String ip;

    private String lobbyUrl;
}
