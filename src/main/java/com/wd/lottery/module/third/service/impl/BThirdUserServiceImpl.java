package com.wd.lottery.module.third.service.impl;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dto.*;
import com.wd.lottery.module.third.dto.*;
import com.wd.lottery.module.third.service.BThirdUserService;
import com.wd.lottery.module.third.service.CThirdPlatformService;
import com.wd.lottery.module.third.service.ThirdSiteUserInnerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BThirdUserServiceImpl implements BThirdUserService {

    private final ThirdSiteUserInnerService thirdSiteUserInnerService;

    private final CThirdPlatformService thirdPlatformService;

    private final MemberService memberService;

    public BThirdUserServiceImpl(ThirdSiteUserInnerService thirdSiteUserInnerService,
                                 CThirdPlatformService thirdPlatformService,
                                 MemberService memberService
                                 ) {
        this.thirdSiteUserInnerService = thirdSiteUserInnerService;
        this.thirdPlatformService = thirdPlatformService;
        this.memberService = memberService;
    }

    @Override
    public void collectMoney(Long memberId, Long merchantId) {
        thirdPlatformService.collectMoney(memberId, merchantId);
    }

    @Override
    public List<PlatformItemBalanceDTO> allPlatformBalance(Long memberId, Long merchantId) {
        // 三方余额
        List<ThirdUserDTO> thirdUsers = thirdSiteUserInnerService.listActiveThirdUser(memberId, merchantId);
        List<ThirdUserBalanceDTO> thirdUserBalanceList = thirdSiteUserInnerService.listThirdUserBalance(thirdUsers);

        List<PlatformItemBalanceDTO> result = thirdUserBalanceList.stream()
                .map(it -> {
                    PlatformItemBalanceDTO balanceDTO = new PlatformItemBalanceDTO();
                    BeanUtils.copyProperties(it, balanceDTO);
                    return balanceDTO;
                })
                .collect(Collectors.toList());
        Set<String> resultPlatformCodeSet = result.stream().map(PlatformItemBalanceDTO::getPlatformCode).collect(Collectors.toSet());

        Map<String, ThirdPlatformBasicInfoDTO> platformBasicInfoMap = ThirdPlatformLocalCacheUtil.getAllThirdPlatformBasicInfoMap();
        for (Map.Entry<String, ThirdPlatformBasicInfoDTO> entry : platformBasicInfoMap.entrySet()) {
            String platformCode = entry.getKey();
            ThirdPlatformBasicInfoDTO thirdPlatform = entry.getValue();
            if (!resultPlatformCodeSet.contains(platformCode)) {
                PlatformItemBalanceDTO balanceDTO = new PlatformItemBalanceDTO();
                balanceDTO.setPlatformCode(platformCode);
                balanceDTO.setPlatformName(thirdPlatform.getPlatformName());
                balanceDTO.setThirdUserName("");
                balanceDTO.setBalance(0L);

                result.add(balanceDTO);
            }
        }
        Member member = memberService.getMemberById(memberId, merchantId);
        // fill platform status
        fillPlatformStatus(result, member.getCurrencyEnum());
        return result;
    }

    private void fillPlatformStatus(List<PlatformItemBalanceDTO> result, CurrencyEnum currencyEnum) {
        for (PlatformItemBalanceDTO balanceDTO : result) {
            ThirdPlatformStatusDTO status = ThirdPlatformLocalCacheUtil.getThirdPlatformStatusByCurrency(balanceDTO.getPlatformCode(), currencyEnum);
            balanceDTO.setEnableTransfer(status.getEnableTransfer());
            balanceDTO.setIsMaintain(status.getIsMaintain());
        }
    }

}
