package com.wd.lottery.module.third.dock.zbridge.sg;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.sg.common.SGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.sg.common.SGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.sg.res.GetBetHistoryResponse;
import com.wd.lottery.module.third.dock.zbridge.sg.res.GetDetailUrlResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: bet order faced
 *
 * <p> Created on 2024/5/13.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.SG_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class SGGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {


    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        String platformCode = flag.getPlatformCode();
        SGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, flag.getCurrencyEnum(), SGRequestConfig.class);
        SGHttpRequestTemplate requestTemplate = new SGHttpRequestTemplate(requestConfig);



        String start = ThirdPlatformMappingConverter.parsePullOrderParamTime(flag.getBegin(), platformCode);
        String end = ThirdPlatformMappingConverter.parsePullOrderParamTime(flag.getEnd(), platformCode);

        int currentIndex = flag.getIndex();
        GetBetHistoryResponse response = requestTemplate
                .addHeader("API", "getBetHistory")
                .addParameter("beginDate", start)
                .addParameter("endDate", end)
                .addParameter("pageIndex", String.valueOf(currentIndex))
                .addParameter("serialNo", IdWorker.getIdStr())
                .toBeanAndCall(GetBetHistoryResponse.class);

        List<GetBetHistoryResponse.BetInfo> list = response.getList();

        Integer totalIndex = response.getPageCount();

        if (CollectionUtils.isEmpty(list) || currentIndex >= totalIndex) {
            flag.setFinished(true);
        }
        List<DockBetOrder> resultList = list.stream().map(i -> this.toDockerOrder(i, flag, requestConfig)).collect(Collectors.toList());

        // 更新下一页参数
        flag.setIndex(currentIndex + 1);
        return resultList;

    }

    private DockBetOrder toDockerOrder(GetBetHistoryResponse.BetInfo order, BetOrderPullFlag flag, SGRequestConfig requestConfig) {
        String gameCode = order.getGameCode();
        DockBetOrder dockOrder = new DockBetOrder();
        dockOrder.setThirdUserName(order.getAcctId());
        dockOrder.setPlatformCode(flag.getPlatformCode());
        dockOrder.setOrderNum(order.getTicketId());
        dockOrder.setGameId(gameCode);


        BigDecimal rate = ThirdPlatformMappingConverter.getCurrencyExchangeRate(requestConfig, flag.getCurrencyEnum());
        long betMoney = ThirdPlatformMappingConverter.toSysMoneyLong(order.getBetAmount(), rate);
        dockOrder.setBetMoney(betMoney);
        // 中奖金额 = 投注金额 + 盈亏金额
        long winLossMoney = ThirdPlatformMappingConverter.toSysMoneyLong(order.getWinLoss(), rate);
        dockOrder.setWinMoney(betMoney + winLossMoney);
        dockOrder.setValidBetMoney(betMoney);

        LocalDateTime orderTime = ThirdPlatformMappingConverter.parseThirdOrderTime(order.getTicketTime(), flag.getPlatformCode());
        dockOrder.setOrderTime(orderTime);

        return dockOrder;
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {

        SGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), SGRequestConfig.class);
        SGHttpRequestTemplate requestTemplate = new SGHttpRequestTemplate(requestConfig);
        try {
            GetDetailUrlResponse response = requestTemplate
                    .addHeader("API", "getTicketLog")
                    .addParameter("acctId", dto.getThirdUserId())
                    .addParameter("ticketId", dto.getOrderNo())
                    .addParameter("merchantCode", requestConfig.getMerchantCode())
                    .addParameter("action", "ticketLog")
                    .addParameter("serialNo", IdWorker.getIdStr())
                    .addParameter("language", "en_US")
                    .toBeanAndCall(GetDetailUrlResponse.class);

            return response.getTicketUrl();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }


}
