package com.wd.lottery.module.third.dock.betorder;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.constants.BetOrderWinTypeEnum;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dto.ThirdPlatformBasicInfoDTO;
import com.wd.lottery.module.third.entity.ThirdBetOrderEntity;
import com.wd.lottery.module.third.entity.ThirdGameEntity;
import com.wd.lottery.module.third.entity.ThirdUserEntity;
import com.wd.lottery.module.third.service.ThirdBetOrderInnerService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component(DefaultBetOrderConsumer.BEAN_NAME_PREFIX + BetOrderConsumer.BEAN_NAME_SUFFIX)
@Slf4j
@AllArgsConstructor
public class DefaultBetOrderConsumer implements BetOrderConsumer {

    public final static String BEAN_NAME_PREFIX = "DEFAULT";

    private final ThirdBetOrderInnerService thirdBetOrderInnerService;


    @Override
    public void consume(List<DockBetOrder> dockBetOrderList, Map<String, ThirdUserEntity> userMap) {
        if (CollectionUtil.isEmpty(dockBetOrderList)) {
            return;
        }
        //注单补全字段
        List<ThirdBetOrderEntity> thirdBetOrderList = dockBetOrderList.stream().map(dockBetOrder -> {
            ThirdUserEntity thirdUser = userMap.get(dockBetOrder.getPlatformCode() + "_" + dockBetOrder.getThirdUserName());
            return buildThirdOrder(dockBetOrder, thirdUser);
        }).filter(Objects::nonNull).collect(Collectors.toList());
        //考虑数据的重复消费
        //处理为数据库数据
        thirdBetOrderInnerService.addThirdBetOrder(thirdBetOrderList, userMap);
    }


    private ThirdBetOrderEntity buildThirdOrder(DockBetOrder dockBetOrder, ThirdUserEntity thirdUser) {
        if (thirdUser == null) {
            log.warn("buildThirdOrder not found order:{}", dockBetOrder);
            return null;
        }

        ThirdBetOrderEntity thirdBetOrder = new ThirdBetOrderEntity();
        BeanUtils.copyProperties(dockBetOrder,thirdBetOrder);
        BeanUtils.copyProperties(thirdUser,thirdBetOrder);
        thirdBetOrder.setId(null);
        // fill parent order num if null
        String orderNumParent = thirdBetOrder.getOrderNumParent();
        if (StringUtils.isBlank(orderNumParent)) {
            thirdBetOrder.setOrderNumParent(dockBetOrder.getOrderNum());
        }

        ThirdPlatformBasicInfoDTO thirdPlatform = ThirdPlatformLocalCacheUtil.getThirdPlatformBasicInfo(dockBetOrder.getPlatformCode());
        if(Objects.nonNull(thirdPlatform)){
            thirdBetOrder.setPlatformName(thirdPlatform.getPlatformName());
            fillDefaultGameCategoryWhenThirdGameNotExist(thirdPlatform, thirdBetOrder);
        }

        ThirdGameEntity thirdGame = ThirdPlatformLocalCacheUtil.getThirdGame(dockBetOrder.getPlatformCode(), dockBetOrder.getGameId());
        if (Objects.nonNull(thirdGame)) {
            fillThirdGameInfo(thirdBetOrder, thirdGame);
        }


        //是否中奖,1:中奖,0:未中奖
        if (dockBetOrder.getWinMoney() > 0) {
            thirdBetOrder.setOnTarget(BooleanEnum.TRUE);
        } else {
            thirdBetOrder.setOnTarget(BooleanEnum.FALSE);
        }
        BetOrderWinTypeEnum winTypeEnum = dockBetOrder.getWinTypeEnum();
        if (Objects.isNull(winTypeEnum)) {
            winTypeEnum = BetOrderWinTypeEnum.NORMAL;
        }
        thirdBetOrder.setWinTypeEnum(winTypeEnum);
        thirdBetOrder.setCurrencyEnum(thirdUser.getCurrencyEnum());
        thirdBetOrder.setOrderTime(dockBetOrder.getOrderTime());
        thirdBetOrder.setCreateTime(LocalDateTime.now());
        thirdBetOrder.setUpdateTime(thirdBetOrder.getCreateTime());
        return thirdBetOrder;
    }

    private void fillDefaultGameCategoryWhenThirdGameNotExist(ThirdPlatformBasicInfoDTO thirdPlatform, ThirdBetOrderEntity thirdBetOrder) {
        try {
            List<GameCategoryEnum> gameCategoryEnumList = thirdPlatform.getSupportedCategories();
            if (CollUtil.isNotEmpty(gameCategoryEnumList)) {
                GameCategoryEnum gameCategoryEnum = gameCategoryEnumList.get(0);
                thirdBetOrder.setGameCategoryEnum(gameCategoryEnum);
                thirdBetOrder.setGameCategoryName(gameCategoryEnum.name());
            }
        } catch (Exception e) {
            log.warn("fillDefaultGameCategoryWhenThirdGameNotExist fail, thirdPlatform:{}, thirdBetOrder:{}", thirdPlatform, thirdBetOrder, e);
        }
    }

    private void fillThirdGameInfo(ThirdBetOrderEntity thirdBetOrder, ThirdGameEntity thirdGame) {
        thirdBetOrder.setGameId(thirdGame.getThirdGameId());
        thirdBetOrder.setGameCode(thirdGame.getGameCode());
        thirdBetOrder.setGameName(thirdGame.getGameName());
        thirdBetOrder.setGameCategoryEnum(thirdGame.getGameCategoryEnum());
        thirdBetOrder.setGameCategoryName(thirdGame.getGameCategoryName());
    }
}
