package com.wd.lottery.module.third.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelRuntimeException;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.SqlUtil;
import com.wd.lottery.module.common.config.ExportExcelConfig;
import com.wd.lottery.module.common.constants.ExcelExportParamEnum;
import com.wd.lottery.module.common.constants.ExportTypeEnum;
import com.wd.lottery.module.common.export.ThirdBetOrderExportExcelStrategy;
import com.wd.lottery.module.common.param.AsyncExportParam;
import com.wd.lottery.module.common.service.ExportExcelService;
import com.wd.lottery.module.merchant.entity.Merchant;
import com.wd.lottery.module.merchant.service.MerchantService;
import com.wd.lottery.module.third.dock.DockPlatformServiceFaced;
import com.wd.lottery.module.third.dock.betorder.DockGetBetOrderDetail;
import com.wd.lottery.module.third.dto.*;
import com.wd.lottery.module.third.dto.*;
import com.wd.lottery.module.third.entity.ThirdBetOrderEntity;
import com.wd.lottery.module.third.entity.ThirdUserEntity;
import com.wd.lottery.module.third.param.BThirdBetOrderPageQueryParam;
import com.wd.lottery.module.third.param.GetBetOrderDetailParam;
import com.wd.lottery.module.third.service.BThirdBetOrderService;
import com.wd.lottery.module.third.service.ThirdBetOrderInnerService;
import com.wd.lottery.module.third.service.ThirdSiteUserInnerService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <p> Created on 2024/7/10.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class BThirdBetOrderServiceImpl implements BThirdBetOrderService {

    @Resource
    private ExportExcelConfig exportExcelConfig;

    @Resource
    private ExportExcelService exportExcelService;

    private final ThirdBetOrderInnerService thirdBetOrderInnerService;
    private final ThirdSiteUserInnerService thirdSiteUserInnerService;
    private final DockPlatformServiceFaced dockPlatformServiceFaced;
    private final MerchantService merchantService;

    public BThirdBetOrderServiceImpl(ThirdBetOrderInnerService thirdBetOrderInnerService,
                                     ThirdSiteUserInnerService thirdSiteUserInnerService,
                                     DockPlatformServiceFaced dockPlatformServiceFaced,
                                     MerchantService merchantService) {
        this.thirdBetOrderInnerService = thirdBetOrderInnerService;
        this.thirdSiteUserInnerService = thirdSiteUserInnerService;
        this.dockPlatformServiceFaced = dockPlatformServiceFaced;
        this.merchantService = merchantService;
    }

    @Override
    public ApiResult export(BThirdBetOrderPageQueryParam param) {
        Map<String,Object> paramMap = Maps.newHashMap();
        buildExportParam(paramMap,param);
        AsyncExportParam asyncExportParam = new AsyncExportParam();
        asyncExportParam.setMerchantId(param.getMerchantId());
        asyncExportParam.setLanguage(param.getLanguage());
        asyncExportParam.setCreateBy(param.getCreateBy());
        asyncExportParam.setExportType(ExportTypeEnum.THIRD_BET_ORDER_RECORD);
        asyncExportParam.setFileName(ExportTypeEnum.THIRD_BET_ORDER_RECORD.getDesc() + "_" + DateUtil.today() + ".xlsx");
        log.info("exportParam..{},AsyncExportParam...{},map...{}", param, asyncExportParam, JSONUtil.toJsonStr(paramMap));
        ThirdBetOrderExportExcelStrategy strategy = new ThirdBetOrderExportExcelStrategy(ExcelExportParamEnum.THIRD_BET_ORDER_RECORD.getMapperMethod(),
                ExcelExportParamEnum.THIRD_BET_ORDER_RECORD.getNextSearchField(), paramMap, exportExcelConfig.getLimit(), param.getLanguage());
        exportExcelService.asyncExportExcel(asyncExportParam, strategy, param.getIsAll());
        return ApiResult.success();
    }

    private void buildExportParam(Map<String, Object> paramMap, BThirdBetOrderPageQueryParam param) {
        paramMap.put("merchantId", param.getMerchantId());
        paramMap.put("memberId",param.getUid());
        if (Objects.nonNull(param.getGameCategoryEnum())) {
            paramMap.put("gameCategoryEnum", param.getGameCategoryEnum().getCode());
        }
        if (StringUtils.isNotBlank(param.getPlatformCode())) {
            paramMap.put("platformCode", param.getPlatformCode());
        }
        if (StringUtils.isNotBlank(param.getOrderNumParent())) {
            paramMap.put("orderNumParent", param.getOrderNumParent());
        }
        if (StringUtils.isNotBlank(param.getOrderNum())) {
            paramMap.put("orderNum", param.getOrderNum());
        }
        if (StringUtils.isNotBlank(param.getGameName())) {
            paramMap.put("gameName", param.getGameName());
        }
        if (Objects.nonNull(param.getMinBet())) {
            paramMap.put("minBet", param.getMinBet());
        }
        if (Objects.nonNull(param.getMaxBet())) {
            paramMap.put("maxBet", param.getMaxBet());
        }
        if (Objects.nonNull(param.getMinWin())) {
            paramMap.put("minWin", param.getMinWin());
        }
        if (Objects.nonNull(param.getMaxWin())) {
            paramMap.put("maxWin", param.getMaxWin());
        }
        if (Objects.nonNull(param.getBeginTime())) {
            paramMap.put("beginTime", param.getBeginTime());
        }
        if (Objects.nonNull(param.getEndTime())) {
            paramMap.put("endTime", param.getEndTime());
        }
        if (Objects.nonNull(param.getCurrencyEnum())) {
            paramMap.put("currencyEnum", param.getCurrencyEnum().getCode());
        }
        if (param.getIsAll()) {
            //导出全部
            paramMap.put("limit", exportExcelConfig.getLimit());
            paramMap.put("id", 0);
        } else {
            //导出当前页
            paramMap.put("limit", Objects.nonNull(param.getSize()) ? param.getSize() : exportExcelConfig.getLimit());
        }
    }

    @Override
    public Page<ThirdBetOrderDTO> page(BThirdBetOrderPageQueryParam param) {
        // UID 必传
        Assert.notNull(param.getUid(), "UID cannot be null");

        LambdaQueryWrapper<ThirdBetOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        buildQueryWrapper(queryWrapper, param);

        Page<ThirdBetOrderEntity> page = thirdBetOrderInnerService.page(new Page<>(param.getCurrent(), param.getSize()), queryWrapper);

        Page<ThirdBetOrderDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(page, dtoPage);

        List<ThirdBetOrderEntity> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return dtoPage;
        }
        List<ThirdBetOrderDTO> collect = records.stream().map(this::toBetOrderDTO).collect(Collectors.toList());
        dtoPage.setRecords(collect);

        return dtoPage;

    }

    @Override
    public ThirdBetOrderStatisticDTO thirdBetOrderStatistic(BThirdBetOrderPageQueryParam param){
        LambdaQueryWrapper<ThirdBetOrderEntity> statisticsWrapper = Wrappers.query(ThirdBetOrderEntity.class)
                .select(
                        SqlUtil.selectSum(ThirdBetOrderEntity::getBetMoney),
                        SqlUtil.selectSum(ThirdBetOrderEntity::getWinMoney),
                        SqlUtil.selectSum(ThirdBetOrderEntity::getValidBetMoney)).lambda();
        buildQueryWrapper(statisticsWrapper, param);
        ThirdBetOrderEntity statisticEntity = thirdBetOrderInnerService.getOne(statisticsWrapper);

        return  convert2TotalStatisticDTO(statisticEntity);
    }

    private ThirdBetOrderStatisticDTO convert2TotalStatisticDTO(ThirdBetOrderEntity statisticEntity) {
        ThirdBetOrderStatisticDTO itemDTO = new ThirdBetOrderStatisticDTO();
        if (Objects.isNull(statisticEntity)) {
            return itemDTO;
        }
        itemDTO.setBet(statisticEntity.getBetMoney());
        itemDTO.setPayout(statisticEntity.getWinMoney());
        itemDTO.setValidBet(statisticEntity.getValidBetMoney());
        itemDTO.setProfit(statisticEntity.getWinMoney() - statisticEntity.getBetMoney());
        return itemDTO;
    }

    private ThirdBetOrderDTO toBetOrderDTO(ThirdBetOrderEntity entity) {
        ThirdBetOrderDTO dto = new ThirdBetOrderDTO();
        BeanUtils.copyProperties(entity, dto);

        String gameDetail = entity.getGameDetail();
        if (StringUtils.isBlank(gameDetail)) {
            dto.setGameDetail("查看详情");
        }

        return dto;
    }

    @Override
    public String getBetOrderDetail(GetBetOrderDetailParam param) {
        ThirdUserDTO thirdSiteUser = thirdSiteUserInnerService.getThirdSiteUser(param.getPlatformCode(), param.getMerchantId(), param.getMemberId());
        DockGetBetOrderDetail dockParam = new DockGetBetOrderDetail();
        BeanUtils.copyProperties(param, dockParam);
        dockParam.setThirdUserId(thirdSiteUser.getThirdUserId());
        dockParam.setThirdUserName(thirdSiteUser.getThirdUserName());
        dockParam.setCurrencyEnum(thirdSiteUser.getCurrencyEnum());
        dockParam.setPlatformCode(param.getPlatformCode());

        try {
            String url = dockPlatformServiceFaced.getBetOrderDetailUrl(param.getPlatformCode(), dockParam);
            return URI.create(url).toURL().toString();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    private void buildQueryWrapper(LambdaQueryWrapper<ThirdBetOrderEntity> queryWrapper, BThirdBetOrderPageQueryParam param) {
        // merchant id
        queryWrapper.eq(ThirdBetOrderEntity::getMerchantId, param.getMerchantId());
        // uid
        queryWrapper.eq(Objects.nonNull(param.getUid()), ThirdBetOrderEntity::getMemberId, param.getUid());
        // game category
        queryWrapper.eq(Objects.nonNull(param.getGameCategoryEnum()), ThirdBetOrderEntity::getGameCategoryEnum, param.getGameCategoryEnum());
        // platform
        queryWrapper.eq(Objects.nonNull(param.getPlatformCode()), ThirdBetOrderEntity::getPlatformCode, param.getPlatformCode());
        // parent order
        queryWrapper.eq(Objects.nonNull(param.getOrderNumParent()), ThirdBetOrderEntity::getOrderNumParent, param.getOrderNumParent());
        // order
        queryWrapper.eq(Objects.nonNull(param.getOrderNum()), ThirdBetOrderEntity::getOrderNum, param.getOrderNum());
        // game name
        queryWrapper.like(Objects.nonNull(param.getGameName()), ThirdBetOrderEntity::getGameName, param.getGameName());
        // bet
        queryWrapper.ge(Objects.nonNull(param.getMinBet()), ThirdBetOrderEntity::getBetMoney, param.getMinBet());
        queryWrapper.le(Objects.nonNull(param.getMaxBet()), ThirdBetOrderEntity::getBetMoney, param.getMaxBet());
        // win
        queryWrapper.ge(Objects.nonNull(param.getMinWin()), ThirdBetOrderEntity::getWinMoney, param.getMinWin());
        queryWrapper.le(Objects.nonNull(param.getMaxWin()), ThirdBetOrderEntity::getWinMoney, param.getMaxWin());
        // time
        queryWrapper.ge(Objects.nonNull(param.getBeginTime()), ThirdBetOrderEntity::getOrderTime, param.getBeginTime());
        queryWrapper.le(Objects.nonNull(param.getEndTime()), ThirdBetOrderEntity::getOrderTime, param.getEndTime());
        // currency
        queryWrapper.eq(Objects.nonNull(param.getCurrencyEnum()), ThirdBetOrderEntity::getCurrencyEnum, param.getCurrencyEnum());

        queryWrapper.orderByDesc(ThirdBetOrderEntity::getOrderTime);

    }


    @Override
    public Tuple analyseAward(MultipartFile file) {
        Tuple dataTuple = this.readAwardData(file);
        String message = dataTuple.get(0);
        if (StrUtil.isNotEmpty(message)) {
            return new Tuple(CommonCode.FAILED, message, Lists.newArrayListWithExpectedSize(0));
        }

        // 玩家名称不能为空
        List<ThirdAwardDataDTO> rowAwards = dataTuple.get(1);
        List<Integer> rowNumbers = rowAwards.stream()
                .filter(rowAward -> StrUtil.isEmpty(rowAward.getThirdUserName()))
                .map(ThirdAwardDataDTO::getRowNumber).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(rowNumbers)) {
            // message = StrUtil.format("第{}行三方用户名为空", StrUtil.join(StrUtil.COMMA, rowNumbers));
            return new Tuple(CommonCode.THIRD_AWARD_ANALYSE_MEMBER_NULL, StrUtil.join(StrUtil.COMMA, rowNumbers), Lists.newArrayListWithExpectedSize(0));
        }

        // 根据玩家名称查询会员
        StopWatch stopWatch = StopWatch.create("TimeWatch");
        stopWatch.start();
        List<String> thirdUserNames = CollStreamUtil.toList(rowAwards, rowAward -> StrUtil.trim(rowAward.getThirdUserName()));
        List<ThirdUserEntity> players = this.thirdSiteUserInnerService.getThirdUserByThirdUserName(thirdUserNames);
        Map<String, Long> temporary = CollStreamUtil.toMap(players, ThirdUserEntity::getThirdUserName, ThirdUserEntity::getMerchantId);
        stopWatch.stop();
        log.debug("根据三方用户名查询玩家:{}-{}", stopWatch.getTotalTimeSeconds(), JSONUtil.toJsonStr(temporary));

        // 填充商户且补充奖励金额
        rowNumbers = this.fillMerchantIdAndDefaultAward(rowAwards, temporary);
        if (CollUtil.isNotEmpty(rowNumbers)) {
            // message = StrUtil.format("第{}行三方用户无归属商户", StrUtil.join(StrUtil.COMMA, rowNumbers));
            return new Tuple(CommonCode.THIRD_AWARD_ANALYSE_MEMBER_NO_MERCHANT, StrUtil.join(StrUtil.COMMA, rowNumbers), Lists.newArrayListWithExpectedSize(0));
        }

        // 奖励数据依据商户分组
        Map<Long, List<ThirdAwardDataDTO>> rowAwardGroup = rowAwards.stream().collect(Collectors.groupingBy(ThirdAwardDataDTO::getMerchantId, Collectors.toList()));

        // 查询商户
        stopWatch.start();
        List<Merchant> merchants = this.merchantService.lambdaQuery().select(Merchant::getId, Merchant::getCode, Merchant::getMname).in(Merchant::getId, rowAwardGroup.keySet()).list();
        Map<Long, Merchant> merchantMap = CollStreamUtil.toMap(merchants, Merchant::getId, Function.identity());
        stopWatch.stop();
        log.debug("根据商户id查询商户代码和名称:{}-{}", stopWatch.getTotalTimeSeconds(), JSONUtil.toJsonStr(merchantMap));

        List<ThirdAwardStatisticsDTO> response = Lists.newArrayListWithExpectedSize(rowAwardGroup.size());
        CollUtil.forEach(rowAwardGroup, (key, value, index) -> {
            ThirdAwardStatisticsDTO statistics = new ThirdAwardStatisticsDTO();
            statistics.setMerchantId(key);
            statistics.setMerchantCode(merchantMap.get(key).getCode());
            BigDecimal total = BigDecimal.ZERO;
            for (ThirdAwardDataDTO item : value) {
                BigDecimal decimal = new BigDecimal(item.getRewardAmount());
                total = total.add(decimal);
            }
            statistics.setAward(Convert.toStr(total.setScale(2, BigDecimal.ROUND_HALF_DOWN)));
            response.add(statistics);
        });

        return new Tuple(CommonCode.SUCCEED, StrUtil.EMPTY, response);
    }

    /**
     * 读取奖励数据
     *
     * @param file 上传的奖励文件
     * @return {@link Tuple} [错误信息，奖励数据]
     */
    private Tuple readAwardData(MultipartFile file) {
        String errorMessage = StrUtil.EMPTY;
        List<ThirdAwardDataDTO> rowAwards = Lists.newArrayList();
        ReadListener<ThirdAwardDataDTO> readListener = new ReadListener<ThirdAwardDataDTO>() {
            // 模版标题
            private final List<String> headers = com.google.common.collect.Lists.newArrayList("三方用户名", "日期", "奖励金额");

            // 校验标题
            @Override
            public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                log.debug("模版标题:{}", JSONUtil.toJsonStr(headMap));
                boolean isRight = CollUtil.isNotEmpty(headMap) && CollUtil.size(headMap) == this.headers.size();
                Assert.isTrue(isRight, "请勿修改模版");
                List<Integer> headerCells = Lists.newArrayList();
                CollUtil.forEach(headMap, ((key, value, index) -> {
                    if (!CollUtil.contains(headers, value.getStringValue())) {
                        headerCells.add(key + 1);
                    }
                }));
                isRight = CollUtil.isEmpty(headerCells);
                Assert.isTrue(isRight, () -> new ExcelHeaderException(StrUtil.format("请勿修改模版第{}列名称", StrUtil.join(StrUtil.COMMA, headerCells))));
            }

            // 读取每行
            @Override
            public void invoke(ThirdAwardDataDTO data, AnalysisContext context) {
                ReadRowHolder rowHolder = context.readRowHolder();
                data.setRowNumber(rowHolder.getRowIndex() + 1);
                log.debug("读取到一行:{}", JSONUtil.toJsonStr(data));
                rowAwards.add(data);
            }

            // 读取完毕
            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.debug("模版数据读取完毕:{}", JSONUtil.toJsonStr(rowAwards));
            }

            // 模版错误
            @Override
            public void onException(Exception exception, AnalysisContext context) throws Exception {
                if (exception instanceof ExcelHeaderException) {
                    throw exception;
                }
            }

            // 模版标题
            class ExcelHeaderException extends ExcelRuntimeException {
                public ExcelHeaderException(String message) {
                    super(message);
                }
            }
        };
        // 读取奖励
        try(InputStream inputStream = file.getInputStream()) {
            EasyExcel.read(inputStream, ThirdAwardDataDTO.class, readListener).sheet().doRead();
        } catch (IOException ex) {
            log.warn(StrUtil.format("读取导入的奖励数据失败:{}", ex.getMessage()), ex);
            errorMessage = ex.getMessage();
        }
        return new Tuple(errorMessage, rowAwards);
    }

    /**
     * 填充商户且补充奖励金额
     *
     * @param rowAwards 奖励
     * @param map 用户所在商户
     * @return {@link List<Integer>} 没有归属商户的玩家
     */
    private List<Integer> fillMerchantIdAndDefaultAward(List<ThirdAwardDataDTO> rowAwards, Map<String, Long> map) {
        List<Integer> rowNumbers = Lists.newArrayList();
        CollUtil.forEach(rowAwards, (rowAward, index) -> {
            String thirdUserName = rowAward.getThirdUserName();
            // 玩家归属商户
            if (map.containsKey(thirdUserName)) {
                rowAward.setMerchantId(map.get(thirdUserName));
            } else {
                rowNumbers.add(rowAward.getRowNumber());
            }
            // 空金额补充零
            rowAward.setRewardAmount(StrUtil.isEmpty(rowAward.getRewardAmount()) ? "0" : rowAward.getRewardAmount());
        });
        return rowNumbers;
    }
}
