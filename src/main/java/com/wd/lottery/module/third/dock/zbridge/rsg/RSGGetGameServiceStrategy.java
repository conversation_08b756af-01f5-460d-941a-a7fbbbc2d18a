package com.wd.lottery.module.third.dock.zbridge.rsg;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.rsg.common.RSGApiEnum;
import com.wd.lottery.module.third.dock.zbridge.rsg.common.RSGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.rsg.common.RSGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.rsg.req.RSGOpenGameReq;
import com.wd.lottery.module.third.dock.zbridge.rsg.res.RSGGameRes;
import com.wd.lottery.module.third.dock.zbridge.rsg.res.RSGLoginUrlRes;
import com.wd.lottery.module.third.dock.zbridge.rsg.res.RSGRes;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Description: rsg get game service strategy
 * <p> Created on 2024/8/10.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component(BridgeConstant.RSG_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class RSGGetGameServiceStrategy extends AbstractGetGameStrategy {



    @Override
    public  List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        RSGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, RSGRequestConfig.class);
        RSGHttpRequestTemplate requestTemplate = new RSGHttpRequestTemplate(requestConfig);

        Map<String, String> param = Collections.singletonMap("SystemCode", requestConfig.getSystemCode());

        RSGRes<RSGGameRes> response = requestTemplate
                .api(RSGApiEnum.GAME_LIST.url)
                .body(JacksonUtil.toJSONString(param))
                .toBeanAndCall(new TypeReference<RSGRes<RSGGameRes>>() {
                });

        return convert2DockGame(response, platformCode);
    }

    protected List<DockGame> convert2DockGame(RSGRes<RSGGameRes> gameRes, String platformCode) {
        if (Objects.isNull(gameRes.getData())
                || Objects.isNull(gameRes.getData().getGameList())) {
            return new ArrayList<>();
        }
        List<RSGGameRes.Game> gameList = gameRes.getData().getGameList();
        final String fishGameType = "2";

        List<DockGame> dockGames = Lists.newArrayListWithExpectedSize(gameList.size());
        for (RSGGameRes.Game game : gameList) {
            DockGame dockGame = toDockGame(platformCode, game);
            String gameType = game.getGameType();
            if (fishGameType.equals(gameType)) {
                dockGame.setGameCategoryEnum(GameCategoryEnum.FISH);
            } else {
                dockGame.setGameCategoryEnum(GameCategoryEnum.SLOT);
            }

            dockGames.add(dockGame);
        }
        return dockGames;
    }

    private static  DockGame toDockGame(String platformCode, RSGGameRes.Game game) {
        DockGame dockGame = new DockGame();
        dockGame.setPlatformCode(platformCode);
        dockGame.setThirdGameId(String.valueOf(game.getGameId()));
        dockGame.setGameCode(String.valueOf(game.getGameId()));
        // default en_us name
        dockGame.setGameName(game.getGameName().getUs());
        return dockGame;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        RSGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), RSGRequestConfig.class);
        RSGHttpRequestTemplate requestTemplate = new RSGHttpRequestTemplate(requestConfig);

        RSGOpenGameReq reqParam = buildOpenGameParam(dto, requestConfig);
        reqParam.setExitAction(dto.getLobbyUrl());

        RSGRes<RSGLoginUrlRes> res = requestTemplate.api(RSGApiEnum.GAME_LOGIN.url)
                .body(JacksonUtil.toJSONString(reqParam))
                .toBeanAndCall(new TypeReference<RSGRes<RSGLoginUrlRes>>() {
                });

        return res.getData().getUrl();
    }

    private RSGOpenGameReq buildOpenGameParam(DockGetGameUrl param, RSGRequestConfig requestConfig) {
        RSGOpenGameReq req = new RSGOpenGameReq();

        final String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, param.getLang(), "en-US");
        final String thirdCurr = ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, param.getCurrencyEnum());

        req.setGameId(Long.parseLong(param.getThirdGameId()));
        req.setUserId(param.getThirdUserId());
        req.setUserName(param.getThirdUserName());
        req.setLanguage(thirdLang);
        req.setCurrency(thirdCurr);

        req.setSystemCode(requestConfig.getSystemCode());
        req.setWebId(requestConfig.getClientId());

        return req;
    }


}
