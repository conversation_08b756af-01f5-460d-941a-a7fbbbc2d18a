package com.wd.lottery.module.third.dock.game;

import com.wd.lottery.module.common.constants.GameCategoryEnum;
import lombok.Data;

/**
 * Description: 游戏同步参数
 * <p> Created on 2024/5/23.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
public class DockGame {

    /**
     * 平台编码
     */
    private String platformCode;
    /**
     * 游戏分类
     */
    private GameCategoryEnum gameCategoryEnum;
    /**
     * 标签（备用） eg: hot, new ...
     */
    private String tag;
    /**
     * 三方游戏ID
     */
    private String thirdGameId;
    /**
     * 游戏编码
     */
    private String gameCode;
    /**
     * 游戏名称
     */
    private String gameName;
    /**
     * 备注
     */
    private String remark;

}
