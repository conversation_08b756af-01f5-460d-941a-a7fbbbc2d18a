package com.wd.lottery.module.third.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.constants.ThirdLoginTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * <p> Created on 2024/6/4.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
@JsonInclude
public class ThirdSitePlatformItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @Schema(description = "平台编码")
    private String platformCode;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "桥接平台编码")
    private String bridgePlatformCode;

    @Schema(description = "游戏分类")
    private GameCategoryEnum categoryEnum;

    @Schema(description = "登入方式")
    private ThirdLoginTypeEnum loginTypeEnum;

    @Schema(description = "是否维护")
    private BooleanEnum isMaintain;

    @Schema(description = "是否启用热门游戏")
    private BooleanEnum enableHot;

    @Schema(description = "平台ICON")
    private String platformImg;

    @Schema(description = "平台分类图片")
    private String categoryImg;

    @Schema(description = "排序字段，越大越靠前")
    private Integer sort;

    @Schema(description = "平台是否启用， TRUE  启用， FALSE 禁用")
    private EnableEnum enableEnum;

    @Schema(description = "是否支持试玩模式")
    private BooleanEnum isSupportDemo;

}
