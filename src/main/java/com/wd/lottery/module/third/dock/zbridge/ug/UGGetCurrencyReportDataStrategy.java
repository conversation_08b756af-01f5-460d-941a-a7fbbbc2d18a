package com.wd.lottery.module.third.dock.zbridge.ug;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.dock.currencyreport.DockCurrencyReport;
import com.wd.lottery.module.third.dock.currencyreport.DockGetCurrencyReportData;
import com.wd.lottery.module.third.dock.currencyreport.GetCurrencyReportDataStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.ug.common.*;
import com.wd.lottery.module.third.dock.zbridge.ug.common.*;
import com.wd.lottery.module.third.dto.ThirdPlatformBasicInfoDTO;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * UG币种报表
 *
 * <AUTHOR>
 */
@Slf4j
@Component(BridgeConstant.UG_PLATFORM_CODE + GetCurrencyReportDataStrategy.BEAN_NAME_SUFFIX)
public class UGGetCurrencyReportDataStrategy implements GetCurrencyReportDataStrategy {

    @Override
    public List<DockCurrencyReport> getCurrencyReport(DockGetCurrencyReportData dto) {
        log.debug("UG:币种报表-入参数:{}", dto);
        // 平台配置
        String platformCode = dto.getPlatformCode();
        ThirdPlatformBasicInfoDTO thirdPlatform = ThirdPlatformLocalCacheUtil.getThirdPlatformBasicInfo(platformCode);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);
        // 拉取时间
        String platformTimeZone = configJson.getPlatformTimeZone();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN);
        String begin = dto.getBeginTime().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(platformTimeZone)).format(formatter);
        // String end = dto.getEndTime().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(platformTimeZone)).format(formatter);
        // 请求报表
        // String currencies = thirdPlatform.getCurrencies();
        // List<CurrencyEnum> currencyEnums = CurrencyEnum.parseNamesToEnum(currencies);
        UGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, CurrencyEnum.ALL, UGRequestConfig.class);
        UGHttpRequestTemplate requestTemplate = new UGHttpRequestTemplate(requestConfig);
        CurrencyReportRequest param = CurrencyReportRequest.builder().apiKey(requestConfig.getApiKey()).operatorId(requestConfig.getOperatorId()).status(5).reportDate(begin).build();
        UGResponse<List<CurrencyReportResponse>> response = requestTemplate.toPOST()
                .api(UGApiEnum.CURRENCY_REPORT.getPath())
                .body(JacksonUtil.toJSONString(param))
                .toBeanAndCall(new TypeReference<UGResponse<List<CurrencyReportResponse>>>() {
                });
        log.debug("UG:币种报表-响应:{}-{}-{}", CurrencyEnum.ALL, begin, JSONUtil.toJsonStr(response));
        List<CurrencyReportResponse> reportData = response.getData();
        if (CollUtil.isEmpty(reportData)) {
            return Collections.emptyList();
        }
        Map<Integer, List<CurrencyReportResponse>> reportMap = CollStreamUtil.groupBy(reportData, CurrencyReportResponse::getCurrencyId, Collectors.toList());
        return this.toDockCurrencyReport(platformCode, thirdPlatform.getPlatformName(), reportMap);
    }

    /**
     * TO DOCK CURRENCY REPORT
     *
     * @param platformCode 平台代码
     * @param platformName 平台名称
     * @param reportMap    报表数据
     * @return {@link DockCurrencyReport}
     */
    private List<DockCurrencyReport> toDockCurrencyReport(String platformCode, String platformName, Map<Integer, List<CurrencyReportResponse>> reportMap) {
        List<DockCurrencyReport> response = Lists.newArrayList();
        final BigDecimal moneyUnit = new BigDecimal(100);
        for (Map.Entry<Integer, List<CurrencyReportResponse>> entry : reportMap.entrySet()) {
            if (CollUtil.isEmpty(entry.getValue())) {
                continue;
            }
            Long totalBetCount = Constants.ZERO_LONG;
            BigDecimal totalAmount = BigDecimal.ZERO, totalWin = BigDecimal.ZERO;
            for (CurrencyReportResponse report : entry.getValue()) {
                totalAmount = totalAmount.add(report.getStake());
                totalWin = totalWin.add(report.getWinLose());
                totalBetCount += report.getTicketCount();
            }
            DockCurrencyReport report = new DockCurrencyReport();
            report.setCurrencyEnum(UGCurrencyReportType.getCurrencyEnum(entry.getKey()));
            report.setPlatformCode(platformCode);
            report.setPlatformName(platformName);
            report.setBetMoney(totalAmount.multiply(moneyUnit).longValue());
            report.setWinMoney(totalWin.add(totalAmount).subtract(totalAmount).multiply(moneyUnit).longValue());
            report.setBetCount(totalBetCount);
            response.add(report);
        }
        return response;
    }

    @Data
    @Builder
    public static class CurrencyReportRequest {
        /**
         * API 密钥
         */
        private String apiKey;

        /**
         * 商户代码
         */
        private String operatorId;

        /**
         * 注单状态
         */
        private Integer status;

        /**
         * 报表日(isBetDate = false)、下注日(isBetDate = true)
         */
        private String reportDate;
    }

    @Data
    public static class CurrencyReportResponse {
        /**
         * 玩家帐号
         */
        private String userId;

        /**
         * 注单状态
         */
        private Integer status;

        /**
         * 货币代码
         */
        private Integer currencyId;

        /**
         * 总下注金额
         */
        private BigDecimal stake;

        /**
         * 实际总下注金额
         */
        private BigDecimal netStake;

        /**
         * 注单输赢结果
         */
        private String result;

        /**
         * 注单输赢金额(包含保险输赢金额)
         */
        private BigDecimal winLose;

        /**
         * 注单数量
         */
        private Long ticketCount;

        /**
         * 币种
         */
        private String currencyType;
    }
}