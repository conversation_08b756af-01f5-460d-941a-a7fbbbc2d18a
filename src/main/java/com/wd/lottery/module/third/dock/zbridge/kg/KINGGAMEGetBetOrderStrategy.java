package com.wd.lottery.module.third.dock.zbridge.kg;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.kg.common.KINGGAMEApiEnum;
import com.wd.lottery.module.third.dock.zbridge.kg.common.KINGGAMEHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.kg.common.KINGGAMERequestConfig;
import com.wd.lottery.module.third.dock.zbridge.kg.res.KINGGAMEResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 游戏注单
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.KINGGAME_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class KINGGAMEGetBetOrderStrategy extends AbstractGetBetOrderStrategy {

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        log.debug("KingGame:游戏注单:{}", JSONUtil.toJsonStr(flag));
        KINGGAMERequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), KINGGAMERequestConfig.class);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(flag.getPlatformCode());
        KINGGAMEHttpRequestTemplate requestTemplate = new KINGGAMEHttpRequestTemplate(requestConfig);

        // 三方时区
        ZoneId zoneId = ZoneId.of(configJson.getPlatformTimeZone());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(configJson.getRequestTimePattern());
        String startTime = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId).format(formatter);
        String endTime = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId).format(formatter);

        // 请求注单
        int pageNo = flag.getIndex();
        KINGGAMEResponse<PageRecord> response = requestTemplate.api(KINGGAMEApiEnum.GET_BET_ORDER.getPath())
                .addParameter("StartTime", startTime)
                .addParameter("EndTime", endTime)
                .addParameter("Page", String.valueOf(pageNo))
                .addParameter("PageLimit", "10000")
                .toBeanAndCall(new TypeReference<KINGGAMEResponse<PageRecord>>() {
                });
        log.debug("KingGame:游戏注单响应:({})-({})-{}", startTime, endTime, JSONUtil.toJsonStr(response));

        // 数据处理
        PageRecord data = response.getData();
        PageRecord.Pagination pagination = data.getPagination();
        List<PageRecord.BetOrderDetail> betOrders = data.getResult();
        if (CollectionUtils.isEmpty(betOrders) || pageNo >= pagination.getTotalPages()) {
            flag.setFinished(true);
        }
        List<DockBetOrder> dockBetOrders = betOrders.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PageRecord.BetOrderDetail::getWagersId))), ArrayList::new))
                .stream()
                .map(item -> this.toDockerOrder(flag.getPlatformCode(), configJson, item))
                .collect(Collectors.toList());
        flag.setIndex(pageNo + 1);
        return dockBetOrders;
    }

    /**
     * 转系统注单
     *
     * @param platformCode 平台代码
     * @param configJson   平台配置
     * @param betOrder     三方注单
     * @return {@link DockBetOrder}
     */
    private DockBetOrder toDockerOrder(String platformCode, ThirdPlatformConfigDTO configJson, PageRecord.BetOrderDetail betOrder) {
        DockBetOrder dockBetOrder = new DockBetOrder();
        dockBetOrder.setPlatformCode(platformCode);
        dockBetOrder.setOrderNum(betOrder.getWagersId());
        dockBetOrder.setOrderNumParent(betOrder.getWagersId());
        dockBetOrder.setGameId(betOrder.getGameId());
        dockBetOrder.setThirdUserName(betOrder.getAccount());

        dockBetOrder.setBetMoney(betOrder.getBetMoney());
        dockBetOrder.setWinMoney(betOrder.getWinMoney());
        dockBetOrder.setValidBetMoney(betOrder.getValidBetMoney());

        ZoneId zoneId = ZoneId.of(configJson.getPlatformTimeZone());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(configJson.getOrderTimePattern());

        LocalDateTime dateTime = LocalDateTime.parse(betOrder.getWagersTime(), formatter);
        dateTime = dateTime.atZone(zoneId).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
        dockBetOrder.setOrderTime(dateTime);
        return dockBetOrder;
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        log.debug("KingGame:对局详情:{}", JSONUtil.toJsonStr(dto));
        KINGGAMERequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), KINGGAMERequestConfig.class);
        KINGGAMEHttpRequestTemplate requestTemplate = new KINGGAMEHttpRequestTemplate(requestConfig);
        try {
            // 请求详情
            KINGGAMEResponse<BetDetailUrl> response = requestTemplate.api(KINGGAMEApiEnum.GET_BET_ORDER_DETAIL.getPath())
                    .addParameter("WagersId", dto.getOrderNo())
                    .toBeanAndCall(new TypeReference<KINGGAMEResponse<BetDetailUrl>>() {
                    });
            log.debug("KingGame:对局详情响应:{}", JSONUtil.toJsonStr(response));
            return response.getData().getUrl();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    /**
     * 游戏注单
     */
    @Data
    public static class PageRecord {

        @JsonProperty("Pagination")
        private Pagination pagination;

        @JsonProperty("Result")
        private List<BetOrderDetail> result;

        /**
         * Pagination
         */
        @Data
        public static class Pagination {

            /**
             * 当前页数
             */
            @JsonProperty("CurrentPage")
            private Integer currentPage;

            /**
             * 总页数
             */
            @JsonProperty("TotalPages")
            private Integer totalPages;

            /**
             * 每页笔数
             */
            @JsonProperty("PageLimit")
            private Integer pageLimit;

            /**
             * 总笔数
             */
            @JsonProperty("TotalNumber")
            private Integer totalNumber;
        }

        /**
         * BetOrderDetail
         */
        @Data
        public static class BetOrderDetail {

            /**
             * 会员唯一识别值
             */
            @JsonProperty("Account")
            private String account;

            /**
             * 在游戏内注单唯一值 注单ID
             */
            @JsonProperty("WagersId")
            private String wagersId;

            /**
             * 游戏的唯一识别值
             */
            @JsonProperty("GameId")
            private String gameId;

            /**
             * 投注时间
             */
            @JsonProperty("WagersTime")
            private String wagersTime;

            /**
             * 投注金额
             */
            @JsonProperty("BetAmount")
            private BigDecimal betAmount;

            /**
             * 有效投注金额
             */
            @JsonProperty("Turnover")
            private BigDecimal turnover;

            /**
             * PayoffTime
             */
            @JsonProperty("PayoffTime")
            private String payoffTime;

            /**
             * 派彩金额
             */
            @JsonProperty("PayoffAmount")
            private BigDecimal payoffAmount;

            /**
             * 注单状态：1: 赢 2: 输
             */
            @JsonProperty("Status")
            private Integer status;

            /**
             * 对帐时间
             */
            @JsonProperty("SettlementTime")
            @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
            private Date settlementTime;

            /**
             * 游戏类型
             */
            @JsonProperty("GameCategoryId")
            private String gameCategoryId;

            /**
             * 注单类型：1: main game 9: free game 11: 道具卡 12: 游戏内购
             */
            @JsonProperty("Type")
            private Integer type;

            /**
             * 会员所属站长唯一识别值
             */
            @JsonProperty("AgentId")
            private String agentId;

            public Long getBetMoney() {
                return betAmount.abs().longValue();
            }

            public Long getWinMoney() {
                return payoffAmount.abs().longValue();
            }

            public Long getValidBetMoney() {
                return turnover.abs().longValue();
            }
        }
    }

    /**
     * 对局详情
     */
    @Data
    public static class BetDetailUrl {
        @JsonProperty("Url")
        private String url;
    }
}