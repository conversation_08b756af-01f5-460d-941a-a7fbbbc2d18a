package com.wd.lottery.module.third.dock.zbridge.omg;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.AbstractGetBetOrderStrategy;
import com.wd.lottery.module.third.dock.betorder.BetOrderPullFlag;
import com.wd.lottery.module.third.dock.betorder.DockBetOrder;
import com.wd.lottery.module.third.dock.betorder.GetBetOrderStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.omg.common.OMGPGAPIEnum;
import com.wd.lottery.module.third.dock.zbridge.omg.common.OMGPGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.omg.common.OMGPGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.omg.common.OMGPGResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import com.wd.lottery.module.third.entity.ThirdGameEntity;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Component(BridgeConstant.OMGPG_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class OMGPGGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {


    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        String platformCode = flag.getPlatformCode();
        OMGPGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, flag.getCurrencyEnum(), OMGPGRequestConfig.class);
        OMGPGHttpRequestTemplate requestTemplate = new OMGPGHttpRequestTemplate(requestConfig);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);

        // 拉单时间
        String timeZone = configJson.getPlatformTimeZone();
        flag.getBegin().atZone(ZoneId.systemDefault()).toEpochSecond();
        long start = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).toEpochSecond();
        long end = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).toEpochSecond();
        log.debug("拉取注单开始时间start:{},end:{}",flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).toLocalDateTime(),flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).toLocalDateTime());
        // 拉取注单
        int pageNo = flag.getIndex();
        final int count = 1_000;
        Map<String , Object> map = new HashMap<>();
        map.put("app_id",requestConfig.getAppId());
        Long timestamp = LocalDateTimeUtil.toEpochMilli(LocalDateTime.now());
        map.put("timestamp",timestamp);
        map.put("start_time",start);
        map.put("end_time",end);
        map.put("page",pageNo);
        map.put("size",count);

        BetOrderDataRoot betOrderDataRoot = requestTemplate
                .host(requestConfig.getBetUrl())
                .toPOST()
                .api(OMGPGAPIEnum.GET_BET.getPath())
                .body(JSONUtil.toJsonStr(map))
                .toBeanAndCall(BetOrderDataRoot.class);

        log.debug("omg get bet response :{}",betOrderDataRoot);
        List<BetOrderData> data = betOrderDataRoot.getData();
        if (CollectionUtils.isEmpty(data)||data.size()<count) {
            flag.setFinished(true);
        }
        flag.setIndex(pageNo + 1);
        log.debug("请求返回的数据信息 size:{} , flag:{}",data.size() , JSONUtil.toJsonStr(flag));
        return data.stream()
                .map(i -> this.toDockBetOrder(i, platformCode))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }


    public DockBetOrder toDockBetOrder(BetOrderData betOrderData, String platformCode){
        if (Objects.isNull(betOrderData)) {
            return null;
        }
        ThirdGameEntity thirdGame = ThirdPlatformLocalCacheUtil.getThirdGame(platformCode, betOrderData.getGameId());
        if(Objects.isNull(thirdGame)){
            return null;
        }
        DockBetOrder betOrder = new DockBetOrder();
        betOrder.setPlatformCode(platformCode);
        betOrder.setThirdUserName(betOrderData.getAccount());
        betOrder.setOrderNum(betOrderData.getId() + "");
        betOrder.setOrderNumParent(betOrderData.getRoundId());
        betOrder.setGameId(betOrderData.getGameId());
        betOrder.setBetMoney(toPlatformMoney(betOrderData.getBet()));
        betOrder.setWinMoney(toPlatformMoney(betOrderData.getWin()));
        betOrder.setValidBetMoney(toPlatformMoney(betOrderData.getBet()));
        LocalDateTime dt = parseThirdOrderTime(betOrderData.getCreateTime());
        betOrder.setOrderTime(dt);
        return betOrder;
    }
    private LocalDateTime parseThirdOrderTime(long epochMilliSeconds) {
        return Instant.ofEpochSecond(epochMilliSeconds).atZone(ZoneId.systemDefault()).toLocalDateTime();

    }

    public static final class BetOrderDataRoot extends OMGPGResponse<List<BetOrderData>> {}

    @Data
    @ToString
    public static final class BetOrderData {

        @JsonProperty("id")
        private long id;
        @JsonProperty("round_id")
        private String roundId; //orderId
        @JsonProperty("account_id")
        private String accountId; //总代理id
        @JsonProperty("account")
        private String account; //玩家名称
        @JsonProperty("game_id")
        private String gameId;
        @JsonProperty("enter_money")
        private BigDecimal enterMoney;
        @JsonProperty("after_settlement_money")
        private BigDecimal afterSettlementMoney;
        @JsonProperty("bet")
        private BigDecimal bet;
        @JsonProperty("win")
        private BigDecimal win;
        @JsonProperty("create_time")
        private long createTime;
    }
}
