package com.wd.lottery.module.third.dto;

import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.InviteTypeEnum;
import com.wd.lottery.module.member.constants.MemberTypeEnum;
import com.wd.lottery.module.third.constants.BetOrderWinTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ThirdBetOrderDTO  implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "会员ID")
    private Long memberId;

    @Schema(description = "三方平台编码")
    private String platformCode;

    @Schema(description = "三方平台名称")
    private String platformName;

    @Schema(description = "商户ID")
    private Long merchantId;

    @Schema(description = "订单号")
    private String orderNum;

    @Schema(description = "三方用户名")
    private String thirdUserName;

    @Schema(description = "会员名")
    private String memberName;

    @Schema(description = "父单号")
    private String orderNumParent;

    @Schema(description = "游戏ID")
    private String gameId;

    @Schema(description = "游戏编码")
    private String gameCode;

    @Schema(description = "游戏名")
    private String gameName;

    @Schema(description = "游戏分类")
    private GameCategoryEnum gameCategoryEnum;

    @Schema(description = "游戏分类名")
    private String gameCategoryName;

    @Schema(description = "投注金额")
    private Long betMoney;

    @Schema(description = "派奖金额")
    private Long winMoney;

    @Schema(description = "有效投注")
    private Long validBetMoney;

    @Schema(description = "jackpot 获奖金额")
    private Long jackpot;
    @Schema(description = "progressive 获奖金额")
    private Long progressive;

    /**
     * 是否中奖，0：未中奖，1：中奖
     */
    private BooleanEnum onTarget;

    @Schema(description = "中奖类型")
    private BetOrderWinTypeEnum winTypeEnum;

    @Schema(description = "币种")
    private CurrencyEnum currencyEnum;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "对局详情")
    private String gameDetail;

    @Schema(description = "投注详情")
    private String betDetail;

    @Schema(description = "结算时间")
    private LocalDateTime orderTime;

    @Schema(description = "邀请类型", example = "OFFICIAL")
    private InviteTypeEnum inviteTypeEnum;

    @Schema(description = "是否是直屬邀請", example = "TRUE")
    private BooleanEnum isDirect;

    @Schema(description = "渠道id", example = "123")
    private Long channelId;

    @Schema(description = "会员类型")
    private MemberTypeEnum memberTypeEnum;

}
