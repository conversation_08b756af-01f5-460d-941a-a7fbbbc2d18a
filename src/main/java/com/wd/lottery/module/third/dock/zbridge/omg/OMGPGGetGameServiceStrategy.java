package com.wd.lottery.module.third.dock.zbridge.omg;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.MemberTokenInfoUtil;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.omg.common.OMGPGAPIEnum;
import com.wd.lottery.module.third.dock.zbridge.omg.common.OMGPGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.omg.common.OMGPGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.omg.common.OMGPGResponse;
import com.wd.lottery.module.third.dock.zbridge.omg.req.OMGRTPRequest;
import com.wd.lottery.module.third.entity.ThirdPlatformrRtpEntity;
import com.wd.lottery.module.third.repo.ThirdPlatformRtpRepo;
import com.wd.lottery.module.third.repo.ThirdUserRepo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component(BridgeConstant.OMGPG_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class OMGPGGetGameServiceStrategy extends AbstractGetGameStrategy {

    @Autowired
    private OMGCallbackService omgCallbackService;

    @Autowired
    private OMGRTPService omgrtpService;

    @Autowired
    private ThirdUserRepo  thirdUserRepo;

    @Autowired
    private ThirdPlatformRtpRepo thirdPlatformRtpRepo;

    private final Map<String,String> rtpUpdateCashMap = new HashMap<>();

    //key:third:商户ID:平台code:三方用户名:游戏code
    private static final String RTP_UPDATE_CASH_STR = "third:%s:%s:%s:%s";


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        OMGPGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, OMGPGRequestConfig.class);
        OMGPGHttpRequestTemplate requestTemplate = new OMGPGHttpRequestTemplate(requestConfig);
        Map<String , Object> map = new HashMap<>();
        map.put("app_id",requestConfig.getAppId());
        Long timestamp = LocalDateTimeUtil.toEpochMilli(LocalDateTime.now());
        map.put("timestamp",timestamp);
        OMGPGResponse<OMGGameRoot> response = requestTemplate
                .toPOST()
                .api(OMGPGAPIEnum.GET_GAME.getPath())
                .body(JSONUtil.toJsonStr(map))
                .toBeanAndCall(new TypeReference<OMGPGResponse<OMGGameRoot>>() {
                });
        log.debug("omg get game list response : {}" , response);
        return response.getData().getGlist()
                .stream()
                .map(i -> this.convertToDockGame(i, platformCode , requestConfig.getPlatform()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        OMGPGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), OMGPGRequestConfig.class);
        OMGPGHttpRequestTemplate requestTemplate = new OMGPGHttpRequestTemplate(requestConfig);
        String token = omgCallbackService.createToken(dto.getPlatformCode(), dto.getCurrencyEnum(), dto.getThirdUserName());
        //打开游戏修改rtp调控
        this.updateRtp(dto.getPlatformCode(),dto.getThirdUserName(),dto.getGameCode() , dto.getCurrencyEnum());
        Map<String , Object> map = new HashMap<>();
        map.put("app_id",requestConfig.getAppId());
        Long timestamp = LocalDateTimeUtil.toEpochMilli(LocalDateTime.now());
        map.put("timestamp",timestamp);
        map.put("token",token);
        map.put("gameid",dto.getGameCode());
        map.put("lang",getThirdLang(dto.getLang()));

        OMGPGResponse<GameUrlRes> response = requestTemplate
                .toPOST()
                .api(OMGPGAPIEnum.OPEN_GAME.getPath())
                .body(JSONUtil.toJsonStr(map))
                .toBeanAndCall(new TypeReference<OMGPGResponse<GameUrlRes>>() {
                });
        log.debug("omg Open game response : {}" , response);
        return response.getData().gameurl;
    }

    private DockGame convertToDockGame(OMGPGGame game, String platformCode , Integer platform){
        if(1==game.getStatus()&&game.getPlatform().equals(platform)){
            DockGame dockGame = new DockGame();
            dockGame.setPlatformCode(platformCode);
            dockGame.setThirdGameId(game.getGameid());
            dockGame.setGameName(game.getName());
            dockGame.setGameCode(game.getGameid());
            GameCategoryEnum categoryEnum = GameCategoryEnum.SLOT;
            dockGame.setGameCategoryEnum(categoryEnum);
            return dockGame;
        }
        return null;
    }

    private void updateRtp(String platformCode , String thirdUserName , String gameCode ,CurrencyEnum currencyEnum){
        try {
            Long merchantId = MemberTokenInfoUtil.getMerchantId();
            log.debug("修改rtp开始....platformCode:{} , thirdUserName:{} , gameCode:{} , merchantId:{}" , platformCode,thirdUserName,gameCode,merchantId);
            ThirdPlatformrRtpEntity rtpEntity = thirdPlatformRtpRepo.lambdaQuery().eq(ThirdPlatformrRtpEntity::getMerchantId, merchantId)
                    .eq(ThirdPlatformrRtpEntity::getPlatformCode, platformCode)
                    .eq(ThirdPlatformrRtpEntity::getGameCode, gameCode).one();
            if(ObjectUtils.isNotEmpty(rtpEntity)){
                if(BooleanEnum.TRUE==rtpEntity.getSwitchEnum()){
                    String rtpUpdateCashKey=String.format(RTP_UPDATE_CASH_STR,merchantId,platformCode,thirdUserName,gameCode);
                    log.debug("内存缓存rtpUpdateCashMap:{} , rtpUpdateCashStr : {}" , rtpUpdateCashMap,rtpUpdateCashKey);
                    OMGRTPRequest omgrtpRequest = getOmgrtpRequest(rtpEntity, thirdUserName , currencyEnum);
                    if(!rtpUpdateCashMap.containsKey(rtpUpdateCashKey)){
                        omgrtpService.updateRTPRequest(omgrtpRequest);
                        rtpUpdateCashMap.put(rtpUpdateCashKey , rtpEntity.getRtpValue());
                    }else{
                        if(!rtpUpdateCashMap.get(rtpUpdateCashKey).equals(rtpEntity.getRtpValue())){
                            omgrtpService.updateRTPRequest(omgrtpRequest);
                            rtpUpdateCashMap.put(rtpUpdateCashKey , rtpEntity.getRtpValue());
                        }
                    }
                }
            }
        }catch (Exception e){
            log.warn("omg OpenGame update rtp fail,platformCode:{},thirdUserName:{},gameCode{},currencyEnum:{}",platformCode,thirdUserName,gameCode,currencyEnum ,e);
        }
    }

    @NotNull
    private static OMGRTPRequest getOmgrtpRequest(ThirdPlatformrRtpEntity rtpEntity, String thirdUserName , CurrencyEnum currencyEnum) {
        OMGRTPRequest omgrtpRequest = new OMGRTPRequest();
        omgrtpRequest.setMerchantId(rtpEntity.getMerchantId());
        omgrtpRequest.setPlatformCode(rtpEntity.getPlatformCode());
        omgrtpRequest.setGameCode(rtpEntity.getGameCode());
        omgrtpRequest.setRtpStatus(rtpEntity.getSwitchEnum());
        omgrtpRequest.setRtp(Long.parseLong(rtpEntity.getRtpValue()));
        omgrtpRequest.setThirdUserName(thirdUserName);
        omgrtpRequest.setCurrencyEnum(currencyEnum);
        return omgrtpRequest;
    }


    @Data
    public static class OMGGameRoot{

        private List<OMGPGGame> glist;

    }
    @Data
    public static class OMGPGGame {
        private String gameid;  // 游戏唯一ID
        private String name; // 游戏名
        private Integer platform; // 游戏所属平台
        private Integer gametype;  // 游戏类型
        private Integer status; //游戏状态，0：关闭,1：开启
    }

    @Data
    public static class GameUrlRes{
        private String gameurl;  // 游戏地址
    }
}
