package com.wd.lottery.module.third.param;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 *
 * <p> Created on 2024/7/11.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
public class ThirdSitePlatformQueryParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(hidden = true)
    private Long merchantId;

    @Schema(description = "三方平台编码")
    private String platformCode;

    @Schema(description = "游戏分类")
    private GameCategoryEnum category;

    @Schema(hidden = true)
    private CurrencyEnum currencyEnum;

    @Schema(description = "是否启用")
    private EnableEnum enableEnum;
}
