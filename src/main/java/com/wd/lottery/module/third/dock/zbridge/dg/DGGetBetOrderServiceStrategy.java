package com.wd.lottery.module.third.dock.zbridge.dg;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.dg.common.DGApiEnum;
import com.wd.lottery.module.third.dock.zbridge.dg.common.DGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.dg.common.DGLiveCasinoToDetail;
import com.wd.lottery.module.third.dock.zbridge.dg.common.DGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.dg.res.DGBetOrderItem;
import com.wd.lottery.module.third.dock.zbridge.dg.res.DGResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: bet order faced
 *
 * <p> Created on 2024/5/13.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.DG_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class DGGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {

    @SuppressWarnings("unused")
    private static final List<String> USE_RESULT_GAME_LIST = Arrays.asList("4", "5", "6", "12");
    private static final List<String> USE_POKER_GAME_LIST = Arrays.asList("1", "2", "3", "7","8", "11", "16", "41", "42", "43", "44", "45");


    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        log.debug("DG:游戏注单:{}", JSONUtil.toJsonStr(flag));
        String platformCode = flag.getPlatformCode();
        DGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, flag.getCurrencyEnum(), DGRequestConfig.class);
        DGHttpRequestTemplate requestTemplate = new DGHttpRequestTemplate(requestConfig);
        
        DGResponse<DGBetOrderItem> response = requestTemplate
                .api(DGApiEnum.BET_ORDER.getPath())
                .toBeanAndCall(new TypeReference<DGResponse<DGBetOrderItem>>() {
                });

        flag.setFinished(true);
        log.debug("DG:游戏注单响应:{}", JSONUtil.toJsonStr(response));
        if (CollUtil.isEmpty(response.getList())) {
            return Collections.emptyList();
        }

        List<DockBetOrder> dockerOrderList = response.getList().stream()
                .filter(betOrder -> Objects.nonNull(betOrder.getIsRevocation())
                        && !Objects.equals(betOrder.getIsRevocation(), 0))
                .map(i ->this.toDockBetOrder(i, platformCode))
                .collect(Collectors.toList());

        // 标记已解析订单
        List<String> orderNums = dockerOrderList.stream().map(DockBetOrder::getOrderNum).collect(Collectors.toList());
        flag.setRemark(String.join(",", orderNums));

        return dockerOrderList;
    }

    @Override
    protected void finishGetAction(BetOrderPullFlag betOrderPullFlag) {
        markBetOrder(betOrderPullFlag);
        betOrderPullFlag.setRemark(null);
        super.finishGetAction(betOrderPullFlag);
    }

    private void markBetOrder(BetOrderPullFlag flag) {
        log.debug("DG:标记注单入参:{}", JSONUtil.toJsonStr(flag));
        DGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), DGRequestConfig.class);
        DGHttpRequestTemplate requestTemplate = new DGHttpRequestTemplate(requestConfig);

        String remark = flag.getRemark();
        if (StrUtil.isNotEmpty(remark)) {
            try {
                DGResponse<Object> response = requestTemplate
                        .api(DGApiEnum.MARK_ORDER.getPath())
                        .addParameter("list", JacksonUtil.toJSONString(remark.split(",")))
                        .toBeanAndCall(new TypeReference<DGResponse<Object>>() {
                        });
                log.debug("DG:标记注单响应:{}", JSONUtil.toJsonStr(response));
            } catch (Exception e) {
                log.error("mark bet order failed, orders: {}", flag.getRemark(), e);
            }
        }
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        DGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), DGRequestConfig.class);
        return requestConfig.getDetailOrderUrl() + "?language=en&agentFix=" + requestConfig.getAgentFix() + "&id=" + dto.getOrderNo();
    }

    private DockBetOrder toDockBetOrder(DGBetOrderItem betOrder, String platformCode){

        DockBetOrder dockOrder = new DockBetOrder();
        dockOrder.setPlatformCode(platformCode);
        dockOrder.setThirdUserName(betOrder.getUserName().toLowerCase());
        dockOrder.setOrderNumParent(betOrder.getExt());
        dockOrder.setOrderNum(betOrder.getId());
        dockOrder.setGameId(String.valueOf(betOrder.getTableId()));

        long betMoney = toPlatformMoney(betOrder.getBetPoints());
        dockOrder.setBetMoney(betMoney);
        long winMoney = toPlatformMoney(betOrder.getWinOrLoss());
        dockOrder.setWinMoney(winMoney);
        long validBetMoney = toPlatformMoney(betOrder.getAvailableBet());
        dockOrder.setValidBetMoney(validBetMoney);

        String calTime = betOrder.getCalTime();
        dockOrder.setOrderTime(parseThirdOrderTime(calTime, platformCode));

        try {
            processOrderDetail(betOrder, dockOrder);
        } catch (Exception e) {
            log.warn("DG process bet order detail failed, orderNum: {}", dockOrder.getOrderNum(), e);
        }

        return dockOrder;
    }

    private void processOrderDetail(DGBetOrderItem betOrder,  DockBetOrder dockOrder) {

        String result = betOrder.getResult();
        String gameId = betOrder.getGameId();
        if (StringUtils.isBlank(result)) {
            return;
        }

        if (USE_POKER_GAME_LIST.contains(gameId)) {
            processUsePokerGame(betOrder, dockOrder);
        }
    }

    private void processUsePokerGame(DGBetOrderItem order, DockBetOrder dockOrder) {
        String result = order.getResult();
        if (StringUtils.isBlank(result))
            return;

        JSONObject jsonObject = JSONUtil.parseObj(result);
        JSONObject poker = jsonObject.getJSONObject("poker");

        if (poker == null)
            return;

        StringBuilder msg = new StringBuilder();
        for (Map.Entry<String, Object> entry : poker.entrySet()) {
            String key = entry.getKey();
            String value = String.valueOf(entry.getValue());

            boolean match = ReUtil.isMatch("[0-9-]+", value);
            if (!match)
                continue;

            String[] split = value.split("-");
            String p = Arrays.stream(split).filter(StringUtils::isNotBlank)
                    .filter(v -> !"0".equals(v))
                    .map(v -> v.length() == 1 ? "0" + v : v)
                    .collect(Collectors.joining(", "));

            msg.append(String.format("%s: %s ", key, DGLiveCasinoToDetail.betNumberToDetail(p)));
        }


        if (msg.length() == 0)
            return;

        String betDetail = order.getBetDetail();
        if (StringUtils.isBlank(betDetail))
            return;

        JSONObject bet = JSONUtil.parseObj(betDetail);

        List<String> userBetList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : bet.entrySet()) {
            String key = entry.getKey();
            if (!key.isEmpty() && !Character.isUpperCase(key.charAt(key.length() - 1))) {
                userBetList.add(key);
            }
        }

        if (!userBetList.isEmpty()) {
            dockOrder.setGameDetail(String.join(", ", userBetList));
        }
    }

    private LocalDateTime parseThirdOrderTime(String dateTime, String platformCode) {
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);
        String timeZone = platformConfig.getPlatformTimeZone();
        String pattern = platformConfig.getOrderTimePattern();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);

        return LocalDateTime.parse(dateTime, formatter).atZone(ZoneId.of(timeZone))
                .withZoneSameInstant(ZoneId.systemDefault())
                .toLocalDateTime();
    }
}
