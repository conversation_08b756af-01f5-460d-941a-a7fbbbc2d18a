package com.wd.lottery.module.third.dock.zbridge.hc;


import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.base.PeachLang;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.hc.common.HCHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.hc.common.HCRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.hc.res.HCResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Component(BridgeConstant.HC_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
@Slf4j
public class HCGetGameStrategy extends AbstractGetGameStrategy {


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        HCRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, HCRequestConfig.class);
        HCHttpRequestTemplate requestTemplate = new HCHttpRequestTemplate(requestConfig);

        HCResponse<List<GameDto>> response = requestTemplate
                .toPOST()
                .addParameter("action", "20")
                .toBeanAndCall(new TypeReference<HCResponse<List<GameDto>>>() {
                });
        log.debug("HB:下载代理游戏响应:{}", JSONUtil.toJsonStr(response));
        List<DockGame> gameList = new ArrayList<>();
        response.getData().stream().filter(Objects::nonNull).forEach(game -> {
            DockGame dockGame = new DockGame();
            //4.电子
            if (game.getGameType() == 4) {
                dockGame.setPlatformCode(platformCode);
                dockGame.setThirdGameId(game.getGameCode());
                dockGame.setGameName(game.getName());
                dockGame.setGameCode(game.getGameCode());
                dockGame.setGameCategoryEnum(GameCategoryEnum.SLOT);
                gameList.add(dockGame);
            }
        });
        return gameList;
    }

    @Data
    public static class GameDto {
        private Integer gameType;
        private String gameId;
        private String name;
        private String gameCode;

    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("FC SLOT getOpenGameUrl dto:{} ", dto);
        String platformCode = dto.getPlatformCode();
        CurrencyEnum currencyEnum = dto.getCurrencyEnum();
        HCRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, HCRequestConfig.class);
        HCHttpRequestTemplate requestTemplate = new HCHttpRequestTemplate(requestConfig);

        HCResponse<OpenGameUrlDto> execute = requestTemplate
                .toPOST()
                .addParameter("action", "12")
                .addParameter("uid", dto.getThirdUserName())
                .addParameter("lang", getThirdLang(dto.getLang()))
                .addParameter("gameCode", dto.getGameCode())
                .addParameter("is_https", "1")
                .toBeanAndCall(new TypeReference<HCResponse<OpenGameUrlDto>>() {
                });
        log.debug("HC:打开游戏:登录游戏响应:{}", JSONUtil.toJsonStr(execute));
        return execute.getData().getPath();
    }

    @Override
    public String getDemoGameUrl(DockGetGameUrl param) {
        log.debug("HC get demo game url, param: {}", JSONUtil.toJsonStr(param));
        String platformCode = param.getPlatformCode();
        CurrencyEnum currencyEnum = param.getCurrencyEnum();
        HCRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, HCRequestConfig.class);
        HCHttpRequestTemplate requestTemplate = new HCHttpRequestTemplate(requestConfig);

        HCResponse<OpenGameUrlDto> execute = requestTemplate
                .toPOST()
                .addParameter("action", "15")
                .addParameter("lang", getThirdLang(param.getLang()))
                .addParameter("gameCode", param.getGameCode())
                .addParameter("is_https", "1")
                .toBeanAndCall(new TypeReference<HCResponse<OpenGameUrlDto>>() {
                });
        return execute.getData().getPath();
    }

    @Data
    public static class OpenGameUrlDto {
        private String path;
    }

    @Override
    public String getThirdLang(String language) {
        String languageCode = "en";
        if (PeachLang.ZH_TW.equals(language)) {
            languageCode = "cn";
        } else if (PeachLang.VI_VN.equals(language)) {
            languageCode = "vn";
        } else if (PeachLang.TH_TH.equals(language)) {
            languageCode = "th";
        }
        return languageCode;
    }

}
