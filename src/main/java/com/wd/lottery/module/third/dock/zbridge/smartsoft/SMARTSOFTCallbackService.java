package com.wd.lottery.module.third.dock.zbridge.smartsoft;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.wd.lottery.common.util.JavaxValidationUtil;
import com.wd.lottery.module.third.constants.BetOrderWinTypeEnum;
import com.wd.lottery.module.third.constants.ThirdBetOrderStatusEnum;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.smartsoft.param.*;
import com.wd.lottery.module.third.dock.zbridge.smartsoft.res.*;
import com.wd.lottery.module.third.dto.ThirdUserDTO;
import com.wd.lottery.module.third.entity.ThirdSeamlessBetOrderEntity;
import com.wd.lottery.module.third.service.ThirdSiteUserInnerService;
import com.wd.lottery.module.third.service.inner.ThirdSeamlessBetOrderInnerService;
import com.wd.lottery.module.third.service.inner.ThirdSeamlessWalletInnerService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Enumeration;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SMARTSOFTCallbackService {

    @Autowired
    private SMARTSOFTTokenService smartsoftTokenService;
    @Autowired
    private ThirdSiteUserInnerService thirdSiteUserInnerService;
    @Autowired
    private ThirdSeamlessWalletInnerService walletInnerService;
    @Autowired
    private ThirdSeamlessBetOrderInnerService betOrderInnerService;

    public ActivateSessionResponse activateSession(ActivateSessionParam param) {
        this.analyzeHttpServletRequest();
        boolean valid = JavaxValidationUtil.isValid(param);
        Assert.isTrue(valid, () -> new IllegalArgumentException("Invalid parameter"));
        return this.smartsoftTokenService.authentication(param);
    }

    public GetBalanceResponse getBalance() {
        CommonParam commonParam = this.analyzeCommonParam();
        this.smartsoftTokenService.verifyRequestHeader(commonParam);
        ThirdUserDTO thirdSiteUser = thirdSiteUserInnerService.getThirdSiteUser(commonParam.getClientExternalKey(), commonParam.getUserName());
        long balance = walletInnerService.getBalanceFromMaster(commonParam.getClientExternalKey(), commonParam.getUserName());
        return GetBalanceResponse.builder().currencyCode(thirdSiteUser.getCurrencyEnum().name()).amount(BigDecimal.valueOf(balance).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)).build();
    }

    public BigDecimal getBalance(String platformCode, String thirdUserId) {
        long balance = walletInnerService.getBalanceFromMaster(platformCode, thirdUserId);
        return BigDecimal.valueOf(balance).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);
    }

    @Transactional(rollbackFor = Exception.class)
    public WithdrawResponse withdraw(WithdrawParam param) {
        boolean valid = JavaxValidationUtil.isValid(param);
        Assert.isTrue(valid, () -> new IllegalArgumentException("Invalid parameter"));
        CommonParam commonParam = this.analyzeCommonParam();
        boolean isCloseRound = "CloseRound".equals(param.getTransactionType());
        long newBalance = 0L;
        if (isCloseRound) {
            ThirdSeamlessBetOrderEntity entity = this.buildCloseRoundWithdrawBetOrderEntity(commonParam.getClientExternalKey(), commonParam.getUserName(), param);
            newBalance = betOrderInnerService.updateSettledOrder(entity);
            return WithdrawResponse.builder().transactionId(param.getTransactionId()).balance(BigDecimal.valueOf(newBalance).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)).build();
        }
        ThirdSeamlessBetOrderEntity entity = this.buildWithdrawBetOrderEntity(commonParam.getClientExternalKey(), commonParam.getUserName(), param);
        newBalance = betOrderInnerService.addPayoutOrder(entity);
        return WithdrawResponse.builder().transactionId(param.getTransactionId()).balance(BigDecimal.valueOf(newBalance).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)).build();
    }

    @Transactional(rollbackFor = Exception.class)
    public DepositResponse deposit(DepositParam param) {
        boolean valid = JavaxValidationUtil.isValid(param);
        Assert.isTrue(valid, () -> new IllegalArgumentException("Invalid parameter"));
        CommonParam commonParam = this.analyzeCommonParam();
        ThirdSeamlessBetOrderEntity entity = this.buildDepositBetOrderEntity(commonParam.getClientExternalKey(), commonParam.getUserName(), param);
        long oldBalance = walletInnerService.getBalance(commonParam.getClientExternalKey(), commonParam.getUserName());
        Assert.isTrue(oldBalance >= entity.getBetMoney(), () -> new IllegalArgumentException("Insufficient fund"));
        long newBalance = betOrderInnerService.addBetOrder(entity);
        return DepositResponse.builder().transactionId(param.getTransactionId()).balance(BigDecimal.valueOf(newBalance).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)).build();
    }

    @Transactional(rollbackFor = Exception.class)
    public RollbackTransactionResponse rollbackTransaction(RollbackTransactionParam param) {
        boolean valid = JavaxValidationUtil.isValid(param);
        Assert.isTrue(valid, () -> new IllegalArgumentException("Invalid parameter"));
        CommonParam commonParam = this.analyzeCommonParam();
        long balance = betOrderInnerService.rollbackOrder(commonParam.getClientExternalKey(), param.getTransactionId());
        return RollbackTransactionResponse.builder().transactionId(param.getTransactionId()).balance(BigDecimal.valueOf(balance).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)).build();
    }

    public void invokeException(HttpServletResponse response, int code, String message) {
        response.addIntHeader("X-ErrorCode", code);
        response.addHeader("X-ErrorMessage", message);
    }

    private ThirdSeamlessBetOrderEntity buildDepositBetOrderEntity(String platformCode, String thirdUserId, DepositParam param) {
        ThirdSeamlessBetOrderEntity entity = new ThirdSeamlessBetOrderEntity();
        entity.setOrderNum(param.getTransactionId());
        entity.setParentOrderNum(param.getTransactionId());
        entity.setPlatformCode(platformCode);
        entity.setThirdUserName(thirdUserId);
        entity.setThirdGameId(ThirdPlatformLocalCacheUtil.getThirdGameByGameName(platformCode, StrUtil.trim(param.getTransactionInfo().getGameName())).getThirdGameId());
        long betMoney = param.getAmount().multiply(new BigDecimal(100)).longValue();
        entity.setBetMoney(betMoney);
        entity.setValidBet(betMoney);
        entity.setWinMoney(0L);
        entity.setOrderStatusEnum(ThirdBetOrderStatusEnum.PENDING);
        entity.setBetTime(LocalDateTime.now());
        entity.setWinTypeEnum(BetOrderWinTypeEnum.NORMAL);
        entity.setRefOrderNum(param.getTransactionInfo().getRoundId());
        log.debug("投注:{}", entity);
        return entity;
    }

    private ThirdSeamlessBetOrderEntity buildCloseRoundWithdrawBetOrderEntity(String platformCode, String thirdUserId, WithdrawParam param) {
        ThirdSeamlessBetOrderEntity entity = new ThirdSeamlessBetOrderEntity();
        entity.setPlatformCode(platformCode);
        entity.setThirdUserName(thirdUserId);
        entity.setRefOrderNum(param.getTransactionInfo().getRoundId());
        // 派彩金额
        long winMoney = param.getAmount().multiply(new BigDecimal(100)).longValue();
        entity.setWinMoney(winMoney);
        // 结算时间
        entity.setSettleTime(LocalDateTime.now());
        entity.setOrderStatusEnum(ThirdBetOrderStatusEnum.CANCELED);
        entity.setRefOrderNum(param.getTransactionInfo().getRoundId());
        return entity;
    }

    private ThirdSeamlessBetOrderEntity buildWithdrawBetOrderEntity(String platformCode, String thirdUserId, WithdrawParam param) {
        ThirdSeamlessBetOrderEntity entity = new ThirdSeamlessBetOrderEntity();
        entity.setPlatformCode(platformCode);
        // 结算订单号
        entity.setOrderNum(param.getTransactionInfo().getBetTransactionId());
        // 派奖金额
        long payoutMoney = param.getAmount().multiply(new BigDecimal(100)).longValue();
        entity.setWinMoney(payoutMoney);
        entity.setSettleTime(LocalDateTime.now());
        entity.setOrderStatusEnum(ThirdBetOrderStatusEnum.COMPLETED);
        entity.setWinTypeEnum(BetOrderWinTypeEnum.NORMAL);
        log.debug("结算:{}", entity);
        return entity;
    }

    private Map<String, String> analyzeHttpServletRequest() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        Map<String, String> headerMap = Maps.newHashMap();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headerMap.put(headerName, headerValue);
        }
        return headerMap;
    }

    public CommonParam analyzeCommonParam() {
        Map<String, String> headerMap = this.analyzeHttpServletRequest();
        log.info("请求头信息:{}", headerMap);
        CommonParam commonParam = new CommonParam();
        String[] arr = {"X-Signature", "X-SessionId", "X-UserName", "X-ClientExternalkey"};
        commonParam.setSignature(headerMap.get(arr[0]));
        commonParam.setSessionId(headerMap.get(arr[1]));
        commonParam.setUserName(headerMap.get(arr[2]));
        commonParam.setClientExternalKey(((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest().getHeader(arr[3]));
        log.info("CommonParam:{}", commonParam);
        return commonParam;
    }
}