package com.wd.lottery.module.third.controller.callback;

import com.wd.lottery.module.third.dock.zbridge.cmd.CMDVerifyTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Description: pg auth callback
 *
 * <p> Created on 2024/7/4.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@RestController
@RequestMapping("${callback-path}/${module-path.third}/cmd/{platformCode}")
public class CMDCallbackController {

    @Autowired
    private CMDVerifyTokenService cmdVerifyTokenService;

    /**
     * CMD TOKEN认证
     *
     * @param request  HttpServletRequest
     * @param response HttpServletResponse
     */
    @GetMapping(value = "verifySession")
    public void authentication(HttpServletRequest request, HttpServletResponse response) {
        String token = request.getParameter("token");
        String secretKey = request.getParameter("secret_key");
        log.debug("CMD auth callback, token:{}, key: {}", token, secretKey);
        try {
            cmdVerifyTokenService.authentication(token, secretKey, response);
        } catch (IOException e) {
            log.error("CMD authentication is fail", e);
        }
    }
}