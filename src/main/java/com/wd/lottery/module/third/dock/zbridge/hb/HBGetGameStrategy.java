package com.wd.lottery.module.third.dock.zbridge.hb;


import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.hb.common.HBApiEnum;
import com.wd.lottery.module.third.dock.zbridge.hb.common.HBGameType;
import com.wd.lottery.module.third.dock.zbridge.hb.common.HBHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.hb.common.HBRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.hb.res.HBResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;


@Component(BridgeConstant.HB_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
@Slf4j
public class HBGetGameStrategy extends AbstractGetGameStrategy {


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        HBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, HBRequestConfig.class);
        HBHttpRequestTemplate requestTemplate = new HBHttpRequestTemplate(requestConfig);
        GameList gameList = requestTemplate
                .api(HBApiEnum.GAME_LIST.getPath())
                .toPOST()
                .toCustomObject(GameList.class);
        log.debug("HB:下载代理游戏响应:{}", JSONUtil.toJsonStr(gameList));
        return toDockGameList(gameList.getGames(), platformCode);
    }

    private List<DockGame> toDockGameList(List<Game> games, String platformCode) {
        return games.stream().filter(game -> !Objects.equals(HBGameType.GAMBLE,game.getGameTypeId()))
                .map(game -> {
                    DockGame dockGame = new DockGame();
                    dockGame.setPlatformCode(platformCode);
                    dockGame.setThirdGameId(game.getBrandGameId());
                    dockGame.setGameName(game.getName());
                    dockGame.setGameCode(game.getBrandGameId());
                    // 游戏分类
                    String gameTypeId = game.getGameTypeId();
                    GameCategoryEnum categoryEnum = parseGameCategory(gameTypeId);
                    dockGame.setGameCategoryEnum(categoryEnum);

                    return dockGame;
                })
                .collect(Collectors.toList());
    }

    private GameCategoryEnum parseGameCategory(String gameTypeId) {
        switch (gameTypeId) {
            case HBGameType.BACCARAT:
            case HBGameType.BLACKJACK:
            case HBGameType.ROULETTE:
            case HBGameType.VIDEO_POKER:
            case HBGameType.CASINO_POKER:
            case HBGameType.SIC_BO:
            case HBGameType.WAR:
            case HBGameType.DRAGON_TIGER:
                return GameCategoryEnum.POKER;
            case HBGameType.VIDEO_SLOTS:
                return GameCategoryEnum.SLOT;
            default:
                // ignore
        }
        return GameCategoryEnum.SLOT;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class GameList extends HBResponse<Void> {
        @JsonProperty("Games")
        private List<Game> games;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Game extends HBResponse<Void> {
        /**
         * 游戏ID
         **/
        @JsonProperty("BrandGameId")
        private String brandGameId;

        /**
         * 游戏名
         **/
        @JsonProperty("Name")
        private String name;

        /**
         * 游戏code
         **/
        @JsonProperty("KeyName")
        private String keyName;

        /**
         * 游戏类型id
         **/
        @JsonProperty("GameTypeId")
        private String gameTypeId;

        /**
         * 游戏类型名
         **/
        @JsonProperty("GameTypeName")
        private String gameTypeName;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("HB getOpenGameUrl dto:{} ", dto);

        CurrencyEnum currencyEnum = dto.getCurrencyEnum();
        String platformCode = dto.getPlatformCode();
        HBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, HBRequestConfig.class);
        HBHttpRequestTemplate requestTemplate = new HBHttpRequestTemplate(requestConfig);

        // HomeUrl:回首页按钮的 URL（非必要），此参数结合游戏表现

        HBUserServiceStrategy.UserCreate response = requestTemplate
                .api(HBApiEnum.ACCOUNT_CREATE.getPath())
                .toPOST()
                .addParameter("Username", dto.getThirdUserName())
                .addParameter("Password", dto.getThirdUserPasswd())
                .addParameter("CurrencyCode", ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, currencyEnum))
                .addParameter("PlayerHostAddress", dto.getLobbyUrl())
                .addParameter("UserAgent", requestConfig.getAgentCode())
                .toBeanAndCall(HBUserServiceStrategy.UserCreate.class);
        String token = response.getToken();
        String link = requestConfig.getGameLink();
        final String defaultLang = "en";
        StringBuilder gameLink = new StringBuilder(link)
                .append("?brandid=").append(requestConfig.getBrandId())
                .append("&brandgameid=").append(dto.getThirdGameId())
                .append("&token=").append(token)
                .append("&mode=").append("real")
                .append("&locale=").append(ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang));
        log.debug("HB:getOpenGameUrl:{}", gameLink);

        return gameLink.toString();
    }


    @Override
    public String getDemoGameUrl(DockGetGameUrl param) {
        log.debug("HB get demo game url, param: {}", JSONUtil.toJsonStr(param));
        CurrencyEnum currencyEnum = param.getCurrencyEnum();
        String platformCode = param.getPlatformCode();
        HBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, HBRequestConfig.class);

        String link = requestConfig.getGameLink();
        StringBuilder gameLink = new StringBuilder(link)
                .append("?brandid=").append(requestConfig.getBrandId())
                .append("&brandgameid=").append(param.getThirdGameId())
                .append("&token=").append(UUID.randomUUID())
                .append("&mode=").append("fun")
                .append("&locale=").append(getThirdLang(param.getLang()));
        log.debug("HB demo game url:{}", gameLink);

        return gameLink.toString();
    }
}
