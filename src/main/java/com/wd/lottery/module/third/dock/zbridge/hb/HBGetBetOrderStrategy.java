package com.wd.lottery.module.third.dock.zbridge.hb;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.hb.common.HBApiEnum;
import com.wd.lottery.module.third.dock.zbridge.hb.common.HBHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.hb.common.HBRequestConfig;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import com.wd.lottery.module.third.util.Encrypt;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component(BridgeConstant.HB_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
@Slf4j
public class HBGetBetOrderStrategy extends AbstractGetBetOrderStrategy {


    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        log.debug("HB requestThirdOrder dto:{} ", flag);
        String platformCode = flag.getPlatformCode();
        CurrencyEnum currencyEnum = flag.getCurrencyEnum();
        HBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, HBRequestConfig.class);
        HBHttpRequestTemplate requestTemplate = new HBHttpRequestTemplate(requestConfig);
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);
        DateTimeFormatter paramPattern = DateTimeFormatter.ofPattern(platformConfig.getRequestTimePattern());
        String timeZone = platformConfig.getPlatformTimeZone();
        String startTime = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
        String endTime = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
        List<BetOrderData> betList = requestTemplate
                .api(HBApiEnum.GAME_FLOW.getPath())
                .toPOST()
                .addParameter("DTStartUTC", startTime)
                .addParameter("DTEndUTC", endTime)
                .toCustomObject(new TypeReference<List<BetOrderData>>() {
                });
        log.debug("HB requestThirdOrder resp:{} ", JSONUtil.toJsonStr(betList));
        List<DockBetOrder> orderList = toDockBetOrderList(betList, platformCode);
        flag.setFinished(true);
        return orderList;
    }

    @Data
    public static class BetOrderData {
        @JsonProperty("GameStateId")
        private Long gameStateId;
        @JsonProperty("FeatureCount")
        private Long featureCount;
        @JsonProperty("BuyFeatureId")
        private Long buyFeatureId;
        @JsonProperty("Provider")
        private String provider;
        @JsonProperty("GameTypeId")
        private Long gameTypeId;
        @JsonProperty("DtStarted")
        private String dtStarted;
        @JsonProperty("FriendlyGameInstanceId")
        private Long friendlyGameInstanceId;
        @JsonProperty("JackpotContribution")
        private BigDecimal jackpotContribution;
        @JsonProperty("CurrencyCode")
        private String currencyCode;
        @JsonProperty("ChannelTypeId")
        private Long channelTypeId;
        @JsonProperty("BalanceAfter")
        private BigDecimal balanceAfter;
        /**
         * 游戏ID
         **/
        @JsonProperty("BrandGameId")
        private String brandGameId;

        /**
         * 用户id
         **/
        @JsonProperty("PlayerId")
        private String playerId;

        /**
         * 代理商id
         **/
        @JsonProperty("BrandId")
        private String brandId;

        /**
         * 用户名
         **/
        @JsonProperty("Username")
        private String username;

        /**
         * 游戏code
         **/
        @JsonProperty("GameKeyName")
        private String gameKeyName;

        /**
         * 游戏code
         **/
        @JsonProperty("DtCompleted")
        private String dtCompleted;

        @JsonProperty("GameInstanceId")
        private String gameInstanceId;

        /**
         * 游戏code
         **/
        @JsonProperty("Stake")
        private BigDecimal stake;

        /**
         * 游戏code
         **/
        @JsonProperty("Payout")
        private BigDecimal payout;

        /**
         * 游戏code
         **/
        @JsonProperty("JackpotWin")
        private BigDecimal jackpotWin;


    }


    private List<DockBetOrder> toDockBetOrderList(List<BetOrderData> list, String platformCode) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<DockBetOrder> resultList = new ArrayList<>(list.size());
        list.stream().filter(Objects::nonNull).forEach(obj -> {
            DockBetOrder dockBetOrder = new DockBetOrder();
            dockBetOrder.setThirdUserName(obj.getUsername());
            dockBetOrder.setPlatformCode(platformCode);
            dockBetOrder.setOrderNum(obj.getGameInstanceId());
            dockBetOrder.setOrderNumParent(obj.getGameInstanceId());
            dockBetOrder.setGameId(obj.getBrandGameId());
            dockBetOrder.setBetMoney(toPlatformMoney(obj.getStake()));
            dockBetOrder.setWinMoney(toPlatformMoney(obj.getPayout()));
            dockBetOrder.setValidBetMoney(toPlatformMoney(obj.getStake()));
            LocalDateTime dateTimeWithZone = parseThirdOrderTime(obj.getDtCompleted(), platformCode);
            dockBetOrder.setOrderTime(dateTimeWithZone);
            resultList.add(dockBetOrder);
        });
        log.debug("HB requestThirdOrder resultList:{} ", JSONUtil.toJsonStr(resultList));
        return resultList;
    }

    private LocalDateTime parseThirdOrderTime(String bdate, String platformCode) {
        String replace = bdate.replace("T", " ");
        int dotIndex = replace.indexOf(".");
        String dateStr = dotIndex > 0 ? replace.substring(0, dotIndex) : replace;
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);
        DateTimeFormatter paramPattern = DateTimeFormatter.ofPattern(platformConfig.getOrderTimePattern());
        LocalDateTime dateTime = LocalDateTime.parse(dateStr, paramPattern);
        String timeZone = platformConfig.getPlatformTimeZone();
        return dateTime.atZone(ZoneId.of(timeZone))
                .withZoneSameInstant(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        HBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), HBRequestConfig.class);
        String link = requestConfig.getGameLink().replace("/play", "");
        String hash = Encrypt.sha256(dto.getOrderNo().toLowerCase() + requestConfig.getBrandId().toLowerCase() + requestConfig.getApiKey().toLowerCase());
        return link + HBApiEnum.ORDER_DETAIL_URL.getPath() +
                "?brandid=" + requestConfig.getBrandId() +
                "&gameinstanceid=" + dto.getOrderNo() +
                "&hash=" + hash +
                "&locale=en&viewtype=game";
    }

}
