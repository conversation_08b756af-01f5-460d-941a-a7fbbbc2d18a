package com.wd.lottery.module.third.dock.zbridge.hc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.hc.common.HCHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.hc.common.HCRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.hc.res.HCResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component(BridgeConstant.HC_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
@Slf4j
public class HCGetBetOrderStrategy extends AbstractGetBetOrderStrategy {

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        log.debug("HC requestThirdOrder dto:{} ", flag);
        String platformCode = flag.getPlatformCode();
        CurrencyEnum currencyEnum = flag.getCurrencyEnum();
        HCRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, HCRequestConfig.class);
        HCHttpRequestTemplate requestTemplate = new HCHttpRequestTemplate(requestConfig);
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(flag.getPlatformCode());
        ZoneId zoneId = ZoneId.of(platformConfig.getPlatformTimeZone());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(platformConfig.getRequestTimePattern());
        String orderTimePattern = platformConfig.getOrderTimePattern();
        String startTime = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId).format(formatter);
        String endTime = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId).format(formatter);
        int pageNo = flag.getIndex();
        // 订单异常时data字段返回[], 无法解析, 使用Object兼容处理
        HCResponse<Object> response = requestTemplate
                .addParameter("action", "17")
                .addParameter("starttime", startTime)
                .addParameter("endtime", endTime)
                .addParameter("page", String.valueOf(pageNo))
                .addParameter("pernumber", "2000")
                .toBeanAndCall(new TypeReference<HCResponse<Object>>() {
                });
        log.debug("HC:游戏注单响应:({})-({})-{}", startTime, endTime, JSONUtil.toJsonStr(response));
        String jsonStr = JSONUtil.toJsonStr(response.getData());
        OrderResponse orderResponse = JSONUtil.toBean(jsonStr, OrderResponse.class);
        List<OrderDto> betOrders = orderResponse.getList();
        Integer totalPage = orderResponse.getTotalPage();
        if (CollectionUtils.isEmpty(betOrders) || pageNo >= totalPage) {
            flag.setFinished(true);
        }
        flag.setIndex(pageNo + 1);
        return toDockBetOrderList(betOrders, platformCode, zoneId, orderTimePattern);
    }


    private List<DockBetOrder> toDockBetOrderList(List<OrderDto> list, String platformCode, ZoneId zoneId, String orderTimePattern) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<DockBetOrder> resultList = new ArrayList<>(list.size());
        list.stream().filter(Objects::nonNull).forEach(obj -> {
            DockBetOrder dockBetOrder = new DockBetOrder();
            dockBetOrder.setThirdUserName(obj.getUid());
            dockBetOrder.setPlatformCode(platformCode);
            dockBetOrder.setOrderNum(obj.getNo());
            dockBetOrder.setOrderNumParent(obj.getNo());
            dockBetOrder.setGameId(obj.getGameCode());
            dockBetOrder.setBetMoney(toPlatformMoney(obj.getBet()));
            dockBetOrder.setWinMoney(toPlatformMoney(obj.getWin()));
            dockBetOrder.setValidBetMoney(toPlatformMoney(obj.getValidbet()));
            dockBetOrder.setOrderTime(parseThirdOrderTime(obj.getGameDate(), zoneId, orderTimePattern));
            resultList.add(dockBetOrder);
        });
        log.debug("HC requestThirdOrder resultList:{} ", JSONUtil.toJsonStr(resultList));
        return resultList;
    }


    private LocalDateTime parseThirdOrderTime(String gameDate, ZoneId zoneId, String orderTimePattern) {
        DateTimeFormatter paramPattern = DateTimeFormatter.ofPattern(orderTimePattern);
        LocalDateTime dateTime = LocalDateTime.parse(gameDate, paramPattern);
        return dateTime.atZone(zoneId)
                .withZoneSameInstant(ZoneId.systemDefault())
                .toLocalDateTime();
    }


    @Data
    public static class OrderResponse {
        private List<OrderDto> list;
        private Integer totalPage;
        private Integer page;
        @JsonProperty("pernumber")
        private Integer pernumber;
        private Integer count;
    }

    @Data
    public static class OrderDto {
        private String uid;
        @JsonProperty("No")
        private String No;
        @JsonProperty("MainNo")
        private String MainNo;
        @JsonProperty("SubNo")
        private String SubNo;
        @JsonProperty("gameid")
        private String gameid;
        private String gameCode;
        private String gameName;
        private String gameType;
        private BigDecimal bet;
        private BigDecimal win;
        @JsonProperty("validbet")
        private BigDecimal validbet;
        private BigDecimal netWin;
        @JsonProperty("PreAmount")
        private BigDecimal PreAmount;
        @JsonProperty("AftAmount")
        private BigDecimal AftAmount;
        private String gameDate;
        private String awardTime;
        private String state;
        @JsonProperty("MoneyType")
        private Integer MoneyType;
        @JsonProperty("Feature")
        private Integer Feature;
        @JsonProperty("RoundEnd")
        private Integer RoundEnd;
    }


    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        return super.getOrderDetailUrl(dto);
    }
}
