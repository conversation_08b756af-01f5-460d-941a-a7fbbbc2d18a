package com.wd.lottery.module.third.dock.zbridge.cq9;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.currencyreport.DockCurrencyReport;
import com.wd.lottery.module.third.dock.currencyreport.DockGetCurrencyReportData;
import com.wd.lottery.module.third.dock.currencyreport.GetCurrencyReportDataStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.cq9.common.CQ9APIEnum;
import com.wd.lottery.module.third.dock.zbridge.cq9.common.CQ9GameType;
import com.wd.lottery.module.third.dock.zbridge.cq9.common.CQ9HttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.cq9.common.CQ9RequestConfig;
import com.wd.lottery.module.third.dock.zbridge.cq9.res.CQ9Response;
import com.wd.lottery.module.third.dto.ThirdPlatformBasicInfoDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 币种报表
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.CQ9_PLATFORM_CODE + GetCurrencyReportDataStrategy.BEAN_NAME_SUFFIX)
public class CQ9GetCurrencyReportDataStrategy implements GetCurrencyReportDataStrategy {

    @Override
    public List<DockCurrencyReport> getCurrencyReport(DockGetCurrencyReportData dto) {
        log.debug("CQ9:币种报表:{}", JSONUtil.toJsonStr(dto));
        ThirdPlatformBasicInfoDTO platform = ThirdPlatformLocalCacheUtil.getThirdPlatformBasicInfo(dto.getPlatformCode());
        // 时间
        String start = ThirdPlatformMappingConverter.parsePullOrderParamTime(dto.getBeginTime(), dto.getPlatformCode());
        String end = ThirdPlatformMappingConverter.parsePullOrderParamTime(dto.getEndTime(), dto.getPlatformCode());
        // 币种
        String currencies = platform.getCurrencies();
        List<CurrencyEnum> currencyEnums = CurrencyEnum.parseNamesToEnum(currencies);
        List<DockCurrencyReport> reports = Lists.newArrayList();
        //获取 所有的gameType
        List<String> gameTypes = getAllGameTypes(platform);
        for (CurrencyEnum currency : currencyEnums) {
            DockCurrencyReport report = new DockCurrencyReport();
            report.setPlatformCode(dto.getPlatformCode());
            report.setPlatformName(platform.getPlatformName());
            report.setCurrencyEnum(currency);
            long betCount = 0L;
            long betMoney = 0L;
            long winMoney = 0L;

            CQ9RequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), currency, CQ9RequestConfig.class);
            BigDecimal rate = ThirdPlatformMappingConverter.getCurrencyExchangeRate(requestConfig, currency);

            for (String gameType : gameTypes) {
                CQ9HttpRequestTemplate requestTemplate = new CQ9HttpRequestTemplate(requestConfig, currency);
                CQ9Response<PageRecord> response = requestTemplate.api(CQ9APIEnum.CURRENCY_REPORT.getPath())
                        .toGET()
                        .addParameter("starttime", start)
                        .addParameter("endtime", end)
                        .addParameter("groupby", "day")
                        .addParameter("page", String.valueOf(1))
                        .addParameter("pagesize", String.valueOf(500))
                        .addParameter("gametype",gameType)
                        .toBeanAndCall(new TypeReference<CQ9Response<PageRecord>>() {
                        });
                log.debug("CQ9:币种报表响应:start:{},end:{},币种:{},gameType:{},结果:{}",start,end,currency,gameType, JSONUtil.toJsonStr(response));
                if (Objects.isNull(response) || ObjectUtil.notEqual(response.getCode(),0)
                        || Objects.isNull(response.getData()) || CollUtil.isEmpty(response.getData().getBetOrders())) {
                    continue;
                }
                for (PageRecord.CurrencyReport betOrder : response.getData().getBetOrders()) {
                    betCount += betOrder.getRounds().longValue();
                    long bets = ThirdPlatformMappingConverter.toSysMoneyLong(betOrder.getBets(), rate);
                    betMoney += bets;
                    long winl = ThirdPlatformMappingConverter.toSysMoneyLong(betOrder.getWins(), rate);
                    if (StringUtils.equals(gameType,CQ9GameType.TABLE)) {
                        //table类型的游戏未中奖时返回的wins为wins + bets
                        BigDecimal wins = betOrder.getBets().add(betOrder.getWins());
                        winl = ThirdPlatformMappingConverter.toSysMoneyLong(wins, rate);
                    }
                    winMoney += winl;
                }

            }
            report.setBetCount(betCount);
            report.setBetMoney(betMoney);
            report.setWinMoney(winMoney - betMoney);
            reports.add(report);
        }
        return reports;
    }

    private List<String> getAllGameTypes(ThirdPlatformBasicInfoDTO platform) {
        String[] split = platform.getCategories().split(",");
        List<String> gameTypes = Lists.newArrayList();
        for (String gameType : split) {
            for (GameCategoryEnum category : GameCategoryEnum.values()) {
                if (gameType.equals(category.name())) {
                    String cq9GameType = getCQ9GameType(category);
                    if (StringUtils.isNotBlank(cq9GameType)) {
                        gameTypes.add(cq9GameType);
                    }
                    break;
                }
            }
        }
        return gameTypes;
    }

    private String getCQ9GameType(GameCategoryEnum category) {
        switch (category) {
            case FISH:
                return CQ9GameType.FISH;
            case SLOT:
                return CQ9GameType.SLOT;
            case POKER:
                return CQ9GameType.TABLE;
            case THREE:
                return CQ9GameType.ARCADE;
            default:
                return null;
        }
    }



    /**
     * 币种报表
     */
    @Data
    public static class PageRecord {
        @JsonProperty(value = "totalSize")
        private Integer totalSize;
        @JsonProperty(value = "data")
        private List<CurrencyReport> betOrders;

        /**
         * 币种报表详情
         */
        @Data
        public static class CurrencyReport {
            /**
             * 代理編號
             */
            private String parentid;

            /**
             * 玩家帳號
             */
            private String account;

            /**
             * 遊戲代碼
             */
            private String gamecode;

            /**
             * 遊戲商名稱
             */
            private String gamehall;

            /**
             * 玩家局數
             */
            private Integer rounds;

            /**
             * 總下注
             */
            private BigDecimal bets;

            /**
             * 有效下注額
             */
            private BigDecimal validbets;

            /**
             * 總贏分（已包含總彩池獎金及總共從PC贏得的金額）
             */
            private BigDecimal wins;

            /**
             * 總彩池獎金
             */
            private BigDecimal jackpots;

            /**
             * 幣別
             */
            private String currency;

            /**
             * 日期
             */
            private String datetime;

            /**
             * 總抽水金額
             */
            private BigDecimal rakes;

            /**
             * 開房費用
             */
            private BigDecimal roomfee;
        }
    }
}