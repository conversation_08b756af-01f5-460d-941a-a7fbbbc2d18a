package com.wd.lottery.module.third.dock.zbridge.zevo;


import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.zevo.common.ZEVOApiEnum;
import com.wd.lottery.module.third.dock.zbridge.zevo.common.ZEVOHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.zevo.common.ZEVORequestConfig;
import com.wd.lottery.module.third.dock.zbridge.zevo.req.ZEVOOpenGameParam;
import com.wd.lottery.module.third.dock.zbridge.zevo.res.ZEVOApiTokenRes;
import com.wd.lottery.module.third.dock.zbridge.zevo.res.ZEVOGameRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: evo game strategy
 *
 * <p> Created on 2024/5/13.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.ZEVO_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class ZEVOGetGameServiceStrategy extends AbstractGetGameStrategy {

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        ZEVORequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, ZEVORequestConfig.class);
        ZEVOHttpRequestTemplate requestTemplate = new ZEVOHttpRequestTemplate(requestConfig);

        ZEVOGameRes gameRes = requestTemplate
                .toGET()
                .api(ZEVOApiEnum.GAME_LIST.getPath())
                .addHeader("Authorization", requestConfig.getGameListAuthorization())
                .addUrlParameter("casinoKey", requestConfig.getCasinoKey())
                .toCustomObject(ZEVOGameRes.class);
        if(Objects.isNull(gameRes) || MapUtils.isEmpty(gameRes.getTables())){
            return Collections.emptyList();
        }

        Map<String, DockGame> collect = gameRes.getTables()
                .values()
                .stream()
                .map(i -> toDockGame(i, platformCode))
                .collect(Collectors.toMap(DockGame::getThirdGameId, Function.identity(), (v1, v2) -> v1));

        return new ArrayList<>(collect.values());
    }


    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        CurrencyEnum currencyEnum = dto.getCurrencyEnum();
        ZEVORequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), currencyEnum, ZEVORequestConfig.class);
        ZEVOHttpRequestTemplate requestTemplate = new ZEVOHttpRequestTemplate(requestConfig);

        final String defaultLang = "en";

        ZEVOOpenGameParam param = new ZEVOOpenGameParam();
        param.setUuid(UUID.randomUUID().toString());
        ZEVOOpenGameParam.PlayerParam playerParam = new ZEVOOpenGameParam.PlayerParam();
        playerParam.setId(dto.getThirdUserId());
        playerParam.setLanguage(ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang));
        playerParam.setCurrency(ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, dto.getCurrencyEnum()));
        playerParam.setUpdate(true);

        ZEVOOpenGameParam.PlayerSession playerSession = new ZEVOOpenGameParam.PlayerSession();

        playerSession.setId(UUID.randomUUID().toString());
        playerSession.setIp(dto.getIp());

        playerParam.setSession(playerSession);
        param.setPlayer(playerParam);

        ZEVOApiTokenRes res = requestTemplate
                .toPOST()
                .api(ZEVOApiEnum.API_TOKEN.getPath())
                .addHeader("Authorization", requestConfig.getLoginAuthorization())
                .addHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .addUrlParameter("casinoKey", requestConfig.getCasinoKey())
                .addUrlParameter("apiToken", requestConfig.getLoginApiToken())
                .body(JacksonUtil.toJSONString(param))
                .toBeanAndCall(ZEVOApiTokenRes.class,false);

        if (Objects.isNull(res) || StringUtils.isEmpty(res.getEntry())) {
            throw new ThirdPlatformException("zevo get open game url fail, response is null");
        }

        return requestConfig.getHost() + res.getEntry();
    }

    private DockGame toDockGame(ZEVOGameRes.GameItem thirdGame, String platformCode) {
        DockGame dockGame = new DockGame();
        dockGame.setPlatformCode(platformCode);

        dockGame.setThirdGameId(thirdGame.getTableId());
        dockGame.setGameCode(thirdGame.getTableId());
        dockGame.setGameName(thirdGame.getName());
        dockGame.setGameCategoryEnum(GameCategoryEnum.CASINO);
        dockGame.setRemark(thirdGame.getGameType());
        return dockGame;
    }

}
