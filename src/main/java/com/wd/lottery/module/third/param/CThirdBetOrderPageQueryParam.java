package com.wd.lottery.module.third.param;

import com.wd.lottery.module.common.constants.GameCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:
 *
 * <p> Created on 2024/7/10.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
public class CThirdBetOrderPageQueryParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "页码", defaultValue = "1")
    private Integer current;
    @Schema(description = "页大小", defaultValue = "20")
    private Integer size;

    @Schema(hidden = true)
    private Long merchantId;

    @Schema(hidden = true)
    private Long uid;

    @Schema(description = "游戏分类")
    private GameCategoryEnum gameCategoryEnum;

    @Schema(description = "平台编码")
    private String platformCode;

    @Schema(description = "游戏名")
    private String gameName;

    @Schema(description = "最小投注")
    private Long minBet;

    @Schema(description = "最大投注")
    private Long maxBet;

    @Schema(description = "最小派奖")
    private Long minWin;

    @Schema(description = "最大派奖")
    private Long maxWin;

    @Schema(description = "结算开始时间", pattern = "yyyy-MM-dd'T'HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime beginTime;
    @Schema(description = "结算结束时间", pattern = "yyyy-MM-dd'T'HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime endTime;

    public CThirdBetOrderPageQueryParam(){
        this.current = 1;
        this.size = 20;
    }
}
