package com.wd.lottery.module.third.dock.zbridge.joker;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.joker.common.JOKERHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.joker.common.JOKERRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.joker.common.JOKERResponse;
import com.wd.lottery.module.third.entity.ThirdGameEntity;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Component(BridgeConstant.JOKER_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class JOKERGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        String platformCode = flag.getPlatformCode();
        JOKERRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, flag.getCurrencyEnum(), JOKERRequestConfig.class);

        String startDate = ThirdPlatformMappingConverter.parsePullOrderParamTime(flag.getBegin(), platformCode);
        String endDate = ThirdPlatformMappingConverter.parsePullOrderParamTime(flag.getEnd(), platformCode);

        JOKERResponse<BetOrderRes> response = doRequestThirdOrder(requestConfig, startDate, endDate, flag.getLastFlag());
        String nextId = response.getNextId();
        if (StringUtils.isBlank(nextId)) {
            flag.setFinished(true);
        } else {
            flag.setLastFlag(nextId);
        }
        return parseThirdOrderData(response, requestConfig, flag);
    }

    private JOKERResponse<BetOrderRes> doRequestThirdOrder(JOKERRequestConfig requestConfig, String startDate, String endDate, String lastFlag) {
        return new JOKERHttpRequestTemplate(requestConfig)
                .addParameter("Method", "TSM")
                .addParameter("StartDate", startDate)
                .addParameter("EndDate", endDate)
                .addParameter("NextId", lastFlag)
                .addParameter("Delay", "0")
                .toBeanAndCall(new TypeReference<JOKERResponse<BetOrderRes>>() {
                });
    }

    private List<DockBetOrder> parseThirdOrderData(JOKERResponse<BetOrderRes> response, JOKERRequestConfig requestConfig, BetOrderPullFlag flag) {

        BetOrderRes betOrderRes = response.getData();
        List<Bet> betList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(betOrderRes.getGame())) {
            betList.addAll(betOrderRes.getGame());
        }
        if (CollectionUtils.isNotEmpty(betOrderRes.getJackpot())) {
            betList.addAll(betOrderRes.getJackpot());
        }
        if (CollectionUtils.isNotEmpty(betOrderRes.getCompetition())) {
            betList.addAll(betOrderRes.getCompetition());
        }
        if (CollectionUtils.isEmpty(betList)) {
            return Collections.emptyList();
        }
        BigDecimal rate = ThirdPlatformMappingConverter.getCurrencyExchangeRate(requestConfig, flag.getCurrencyEnum());
        return betList.stream()
                .map(bet -> toDockBetOrder(bet, flag.getPlatformCode(), rate))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public DockBetOrder toDockBetOrder(Bet bet, String platformCode, BigDecimal rate) {
        if (Objects.isNull(bet)) {
            return null;
        }
        ThirdGameEntity thirdGame = ThirdPlatformLocalCacheUtil.getThirdGame(platformCode, bet.getGameCode());
        String gameId = null == thirdGame ? "" : thirdGame.getThirdGameId();
        DockBetOrder dockBetOrder = new DockBetOrder();
        dockBetOrder.setThirdUserName(StringUtils.lowerCase(bet.getUsername()));
        dockBetOrder.setPlatformCode(platformCode);
        dockBetOrder.setOrderNum(bet.getOCode());
        dockBetOrder.setOrderNumParent(bet.getOCode());
        dockBetOrder.setGameId(gameId);
        dockBetOrder.setBetMoney(ThirdPlatformMappingConverter.toSysMoneyLong(bet.getAmount(), rate));
        dockBetOrder.setWinMoney(ThirdPlatformMappingConverter.toSysMoneyLong(bet.getResult(), rate));
        if (("Grand Jackpot".equals(bet.getType()) || ("Prize Drop".equals(bet.getType())))) {
            dockBetOrder.setJackpot(dockBetOrder.getWinMoney());
        } else {
            dockBetOrder.setJackpot(0L);
        }
        //有效投注 =  if (投注金额 == 派奖金额)  0 else 投注金额
        dockBetOrder.setValidBetMoney(dockBetOrder.getBetMoney() == dockBetOrder.getWinMoney() ? 0 : dockBetOrder.getBetMoney());
        String timeStr = bet.getTime().substring(0, 19);
        dockBetOrder.setOrderTime(ThirdPlatformMappingConverter.parseThirdOrderTime(timeStr, platformCode));
        return dockBetOrder;
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        JOKERRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), JOKERRequestConfig.class);
        JOKERHttpRequestTemplate requestTemplate = new JOKERHttpRequestTemplate(requestConfig);
        try {
            OrderDetailUrlRes response = requestTemplate
                    .addParameter("Method", "History")
                    .addParameter("Type", "Game")
                    .addParameter("OCode", dto.getOrderNo())
                    .toCustomObject(OrderDetailUrlRes.class);
            log.debug("http getOrderDetailUrl response:{} ", response);
            JSONObject jsonObj = JSONUtil.parseObj(response);
            if (ObjectUtils.isEmpty(jsonObj)) {
                throw new ThirdPlatformException("cannot get bet detail url, historyId = " + dto.getOrderNo());
            }
            String url = response.getUrl();
            log.debug("joker getGameDetailUrl:{}", url);
            return url.startsWith("//") ? String.format("https:%s", url) : url;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    @Data
    public static class BetOrderRes {
        @JsonProperty("Game")
        private List<Bet> game;
        @JsonProperty("Jackpot")
        private List<Bet> jackpot;
        @JsonProperty("Competition")
        private List<Bet> competition;
    }

    @Data
    public static class Bet {
        @JsonProperty("OCode")
        private String oCode;
        @JsonProperty("Username")
        private String username;
        @JsonProperty("GameCode")
        private String gameCode;
        @JsonProperty("Description")
        private String description;
        @JsonProperty("RoundID")
        private String roundId;
        @JsonProperty("Type")
        private String type;
        @JsonProperty("Amount")
        private BigDecimal amount;
        @JsonProperty("Result")
        private BigDecimal result;
        @JsonProperty("Time")
        private String time;
    }

    @Data
    static class OrderDetailUrlRes {
        @JsonProperty("Url")
        private String url;
    }
}
