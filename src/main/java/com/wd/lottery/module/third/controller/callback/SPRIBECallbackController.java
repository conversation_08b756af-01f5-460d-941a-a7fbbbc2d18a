package com.wd.lottery.module.third.controller.callback;

import com.wd.lottery.module.third.dock.zbridge.spribe.SPRIBECallbackService;
import com.wd.lottery.module.third.dock.zbridge.spribe.req.*;
import com.wd.lottery.module.third.dock.zbridge.spribe.req.*;
import com.wd.lottery.module.third.dock.zbridge.spribe.res.BalanceOptRes;
import com.wd.lottery.module.third.dock.zbridge.spribe.res.PlayerInfo;
import com.wd.lottery.module.third.dock.zbridge.spribe.res.SPRIBEResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * Description: spribe third platform callback
 *
 * <p> Created on 2024/7/3.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@RestController
@RequestMapping("${callback-path}/${module-path.third}/spribe/{platformCode}")
public class SPRIBECallbackController {

    private final SPRIBECallbackService spribeCallbackService;

    public SPRIBECallbackController(SPRIBECallbackService spribeCallbackService) {
        this.spribeCallbackService = spribeCallbackService;
    }

    /**
     * 登录验证回调
     *
     * @param param 登录验证回调参数
     * @return 验证结果
     */
    @PostMapping(value = "auth", produces = MediaType.APPLICATION_JSON_VALUE)
    public SPRIBEResponse<PlayerInfo> auth(@RequestBody AuthParam param, @PathVariable String platformCode){
        log.debug("user auth callback, param: {}", param);
        param.setPlatformCode(platformCode.toUpperCase());
        try {
            return SPRIBEResponse.success(spribeCallbackService.auth(param));
        } catch (Exception e) {
            log.error("spribe auth failed, param: {}", param, e);
            return SPRIBEResponse.failed(e);
        }
    }

    /**
     * 查询单一钱包余额
     */
    @PostMapping(value = "info", produces = MediaType.APPLICATION_JSON_VALUE)
    public SPRIBEResponse<PlayerInfo> getPlayInfo(PlayerInfoParam param, @PathVariable String platformCode){
        log.debug("get player info callback, param: {}", param);
        param.setPlatformCode(platformCode.toUpperCase());
        try {
            return SPRIBEResponse.success(spribeCallbackService.getPlayInfo(param));
        } catch (Exception e) {
            log.error("spribe get info failed, param: {}", param, e);
            return SPRIBEResponse.failed(e);
        }
    }

    /**
     * 投注
     */
    @PostMapping(value = "withdraw", produces = MediaType.APPLICATION_JSON_VALUE)
    public SPRIBEResponse<BalanceOptRes> withdraw(@RequestBody @Valid WithdrawParam param, @PathVariable String platformCode){
        log.debug("withdraw callback, param: {}", param);
        param.setPlatformCode(platformCode.toUpperCase());
        try {
            return SPRIBEResponse.success(spribeCallbackService.withdraw(param));
        } catch (Exception e) {
            log.error("spribe withdraw failed, param: {}", param, e);
            return SPRIBEResponse.failed(e);
        }
    }

    /**
     * 结算
     */
    @PostMapping(value = "deposit", produces = MediaType.APPLICATION_JSON_VALUE)
    public SPRIBEResponse<BalanceOptRes> deposit(@RequestBody @Valid DepositParam param, @PathVariable String platformCode){
        log.debug("deposit callback, param: {}", param);
        param.setPlatformCode(platformCode.toUpperCase());
        try {
            return SPRIBEResponse.success(spribeCallbackService.deposit(param));
        } catch (Exception e) {
            log.error("spribe deposit failed, param: {}", param, e);
            return SPRIBEResponse.failed(e);
        }
    }

    /**
     * 回滚
     */
    @PostMapping(value = "rollback", produces = MediaType.APPLICATION_JSON_VALUE)
    public SPRIBEResponse<BalanceOptRes> rollback(@RequestBody @Valid RollbackParam param, @PathVariable String platformCode){
        log.debug("rollback callback, param: {}", param);
        param.setPlatformCode(platformCode.toUpperCase());
        try {
            return SPRIBEResponse.success(spribeCallbackService.rollback(param));
        } catch (Exception e) {
            log.error("spribe rollback failed, param: {}", param, e);
            return SPRIBEResponse.failed(e);
        }
    }

}
