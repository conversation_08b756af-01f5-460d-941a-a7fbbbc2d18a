package com.wd.lottery.module.third.service.impl;


import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.common.constants.LogSubTypeConstants;
import com.wd.lottery.module.common.constants.LogTypeConstants;
import com.wd.lottery.module.common.dto.SelectOptionDTO;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.constants.ThirdConstants;
import com.wd.lottery.module.third.constants.ThirdLoginTypeEnum;
import com.wd.lottery.module.third.dock.DockPlatformServiceFaced;
import com.wd.lottery.module.third.dock.betorder.BetOrderPullService;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.user.PlatformConfigValidate;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dto.*;
import com.wd.lottery.module.third.entity.*;
import com.wd.lottery.module.third.param.*;
import com.wd.lottery.module.third.repo.*;
import com.wd.lottery.module.third.service.BThirdPlatformService;
import com.wd.lottery.module.third.service.ThirdSitePlatformInnerService;
import com.google.common.collect.Lists;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <p> Created on 2024/5/29.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class BThirdPlatformServiceImpl implements BThirdPlatformService {

    private final ThirdPlatformRepo thirdPlatformRepo;
    private final ThirdPlatformPropertyRepo thirdPlatformPropertyRepo;
    private final ThirdPlatformCategoryRepo thirdPlatformCategoryRepo;
    private final DockPlatformServiceFaced dockPlatformServiceFaced;
    private final ThirdGameRepo thirdGameRepo;
    private final ThirdSitePlatformRepo thirdSitePlatformRepo;
    private final ThirdSiteGameRepo thirdSiteGameRepo;
    private final ThirdSitePlatformInnerService thirdSitePlatformInnerService;
    private final BetOrderPullService betOrderPullService;
    private final ThirdPlatformStatusConfigRepo thirdPlatformStatusConfigRepo;

    @javax.annotation.Resource(name = Constants.DISCARD_THREAD_POOL)
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public BThirdPlatformServiceImpl(ThirdPlatformRepo thirdPlatformRepo,
                                     ThirdPlatformPropertyRepo thirdPlatformPropertyRepo,
                                     ThirdPlatformCategoryRepo thirdPlatformCategoryRepo,
                                     DockPlatformServiceFaced dockPlatformServiceFaced,
                                     ThirdGameRepo thirdGameRepo,
                                     ThirdSitePlatformRepo thirdSitePlatformRepo,
                                     ThirdSiteGameRepo thirdSiteGameRepo,
                                     ThirdSitePlatformInnerService thirdSitePlatformInnerService,
                                     BetOrderPullService betOrderPullService, ThirdPlatformStatusConfigRepo thirdPlatformStatusConfigRepo) {
        this.thirdPlatformRepo = thirdPlatformRepo;
        this.thirdPlatformPropertyRepo = thirdPlatformPropertyRepo;
        this.thirdPlatformCategoryRepo = thirdPlatformCategoryRepo;
        this.dockPlatformServiceFaced = dockPlatformServiceFaced;
        this.thirdGameRepo = thirdGameRepo;
        this.thirdSitePlatformRepo = thirdSitePlatformRepo;
        this.thirdSiteGameRepo = thirdSiteGameRepo;
        this.betOrderPullService = betOrderPullService;
        this.thirdSitePlatformInnerService = thirdSitePlatformInnerService;
        this.thirdPlatformStatusConfigRepo = thirdPlatformStatusConfigRepo;
    }


    @Override
    public List<SelectOptionDTO> getNewPlatformOptions() {
        // 解析 platform.json
        List<ThirdPlatformDTO> templates = parseThirdPlatformTemplate();
        Set<String> platformCodes = thirdPlatformRepo.lambdaQuery()
                .select(ThirdPlatformEntity::getCode)
                .list()
                .stream()
                .map(ThirdPlatformEntity::getCode)
                .collect(Collectors.toSet());
        // 过滤已经存在的
        return templates.stream()
                .filter(i -> !platformCodes.contains(i.getCode()))
                .map(i -> new SelectOptionDTO(i.getName(), i.getCode()))
                .sorted(Comparator.comparing(SelectOptionDTO::getLabel))
                .collect(Collectors.toList());
    }

    @Override
    public ThirdPlatformDTO getPlatformTemplate(String platformCode) {
        // 解析 platform.json
        Map<String, ThirdPlatformDTO> templates = parseThirdPlatformTemplate()
                .stream()
                .collect(Collectors.toMap(ThirdPlatformDTO::getCode, v -> v, (v1, v2) -> v1));

        ThirdPlatformDTO thirdPlatformDTO = templates.get(platformCode);
        if (Objects.isNull(thirdPlatformDTO)) {
            throw new ApiException(CommonCode.THIRD_PLATFORM_NOT_CONFIGURED);
        }
        String bridgePlatformCode = thirdPlatformDTO.getBridgePlatformCode();
        if (StringUtils.isBlank(bridgePlatformCode)) {
            thirdPlatformDTO.setBridgePlatformCode(thirdPlatformDTO.getCode());
        }

        return thirdPlatformDTO;
    }

    @Override
    public ThirdPlatformDTO getThirdPlatformDetail(String platformCode) {
        CurrencyEnum currencyEnum = AdminTokenInfoUtil.getRequestCurrencyEnumNotNull();
        // base info
        ThirdPlatformDTO platformDetail = thirdPlatformRepo.getThirdPlatformDetail(platformCode, currencyEnum);
        log.debug("platform detail base: {}", JacksonUtil.toJSONString(platformDetail));
        if (Objects.isNull(platformDetail)) {
            throw new ApiException(CommonCode.THIRD_PLATFORM_NOT_EXISTS);
        }
        // properties
        List<ThirdPlatformPropertyDTO> properties = thirdPlatformPropertyRepo.getThirdPlatformProperties(platformCode);
        platformDetail.setRequestProperties(properties);
        // categories
        List<ThirdPlatformCategoryDTO> categories = thirdPlatformCategoryRepo.getThirdPlatformCategories(platformCode, currencyEnum);

        platformDetail.setPlatformCategories(categories);

        return platformDetail;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveThirdPlatform(ThirdPlatformDTO param) {
        ThirdPlatformEntity platform = thirdPlatformRepo.getThirdPlatformByCode(param.getCode());
        if (Objects.nonNull(platform)) {
            thirdPlatformRepo.updateThirdPlatform(param);
            return;
        }
        thirdPlatformRepo.saveThirdPlatform(param);
    }

    @Override
    public Page<ThirdPlatformPageItemDTO> getPlatformPage(ThirdPlatformPageParam param) {

        Page<ThirdPlatformPageItemDTO> dtoPage = thirdPlatformRepo.getThirdPlatformPage(param);
        dtoPage.getRecords().forEach(i -> {
            i.setPullOrderType(getPullOrderType(i.getConfigJson()));
            // 隐藏非必要字段
            i.setConfigJson(null);
        });

        return dtoPage;
    }

    private Integer getPullOrderType(String configJson) {
        ThirdPlatformConfigDTO config = JacksonUtil.toJavaObject(configJson, ThirdPlatformConfigDTO.class);
        Integer pullOrderType = config.getPullOrderType();
        if (Objects.isNull(pullOrderType)) {
            pullOrderType = 0;
        }
        return pullOrderType;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncPlatformGame(String platformCode, CurrencyEnum currencyEnum) {
        List<DockGame> dockGames = dockPlatformServiceFaced.getGameList(platformCode, currencyEnum);
        log.debug("sync game count: {}, platform: {}, currency: {} ", dockGames.size(), platformCode, currencyEnum);
        thirdGameRepo.syncPlatformGame(dockGames, platformCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void assignPlatform(AssignPlatformParam param) {
        List<String> items = param.getPlatforms();
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        // 查询原平台
        List<ThirdSitePlatformEntity> list = thirdSitePlatformRepo.lambdaQuery()
                .eq(ThirdSitePlatformEntity::getMerchantId, param.getMerchantId())
                .eq(ThirdSitePlatformEntity::getIsDel, BooleanEnum.FALSE)
                .list();
        Map<String, ThirdSitePlatformEntity> map = list.stream().collect(Collectors.toMap(ThirdSitePlatformEntity::getPlatformCode, v -> v, (v1, v2) -> v1));

        // 新增
        List<ThirdSitePlatformEntity> addList = new ArrayList<>();
        for (String platformCode : items) {
            ThirdSitePlatformEntity entity = map.get(platformCode);
            if (Objects.isNull(entity)) {
                addList.add(buildThirdSitePlatform(platformCode, param.getMerchantId()));
            }
        }
        // 增加新平台
        if (!addList.isEmpty()) {
            log.debug("assign third platform to merchant: {}, count: {}", param.getMerchantId(), addList.size());
            thirdSitePlatformRepo.saveBatch(addList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void assignGame(AssignGameParam param) {
        log.debug("assign game to merchant, platform: {}, merchant: {}, gameCount:{}", param.getPlatformCode(), param.getMerchantId(), CollectionUtils.size(param.getGameIds()));

        // 删除已分配游戏
        thirdSiteGameRepo.remove(new LambdaQueryWrapper<ThirdSiteGameEntity>().eq(ThirdSiteGameEntity::getPlatformCode, param.getPlatformCode())
                .eq(ThirdSiteGameEntity::getMerchantId, param.getMerchantId()));
        List<Long> gameIds = param.getGameIds();
        if (CollectionUtils.isEmpty(gameIds)) {
            log.debug("assigned game param is empty");
            return;
        }
        // 重新保存已分配游戏
        final int batchSize = 100;
        Lists.partition(gameIds, batchSize)
                .forEach(ids -> {
                    List<ThirdSiteGameEntity> entities = ids.stream()
                            .map(i -> {
                                ThirdSiteGameEntity entity = new ThirdSiteGameEntity();
                                entity.setGameId(i);
                                entity.setMerchantId(param.getMerchantId());
                                entity.setPlatformCode(param.getPlatformCode());
                                entity.setShowGame(BooleanEnum.TRUE);
                                return entity;
                            }).collect(Collectors.toList());
                    thirdSiteGameRepo.saveBatch(entities);
                });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePlatformMaintainStatus(MaintainStatusParam param) {
        ThirdPlatformStatusConfigEntity statusConfig = thirdPlatformStatusConfigRepo.lambdaQuery()
                .eq(ThirdPlatformStatusConfigEntity::getPlatformCode, param.getPlatformCode())
                .eq(ThirdPlatformStatusConfigEntity::getCurrencyEnum, param.getCurrencyEnum())
                .one();
        if (Objects.isNull(statusConfig)) {
            statusConfig = ThirdPlatformStatusConfigEntity.getDefaultInstance();
            statusConfig.setId(IdWorker.getId());
            statusConfig.setPlatformCode(param.getPlatformCode());
            statusConfig.setCurrencyEnum(param.getCurrencyEnum());
            statusConfig.setIsMaintain(param.getIsMaintain());
            thirdPlatformStatusConfigRepo.save(statusConfig);
            return;
        }
        statusConfig.setIsMaintain(param.getIsMaintain());
        statusConfig.setUpdateTime(LocalDateTime.now());
        thirdPlatformStatusConfigRepo.updateById(statusConfig);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePlatformTransferStatus(TransferStatusParam param) {
        ThirdPlatformStatusConfigEntity statusConfig = thirdPlatformStatusConfigRepo.lambdaQuery()
                .eq(ThirdPlatformStatusConfigEntity::getPlatformCode, param.getPlatformCode())
                .eq(ThirdPlatformStatusConfigEntity::getCurrencyEnum, param.getCurrencyEnum())
                .one();
        if (Objects.isNull(statusConfig)) {
            statusConfig = ThirdPlatformStatusConfigEntity.getDefaultInstance();
            statusConfig.setId(IdWorker.getId());
            statusConfig.setPlatformCode(param.getPlatformCode());
            statusConfig.setCurrencyEnum(param.getCurrencyEnum());
            statusConfig.setEnableTransfer(param.getEnableTransfer());
            thirdPlatformStatusConfigRepo.save(statusConfig);
            return;
        }
        statusConfig.setEnableTransfer(param.getEnableTransfer());
        statusConfig.setUpdateTime(LocalDateTime.now());
        thirdPlatformStatusConfigRepo.updateById(statusConfig);
    }

    @Override
    public void pullBetOrderManually(String platformCode, String begin, String end) {
        String traceId = MDC.get(Constants.MDC_TRACE_ID_KEY);
        this.threadPoolTaskExecutor.execute(() -> {
            MDC.put(Constants.MDC_TRACE_ID_KEY, traceId);
            try {
                betOrderPullService.pullBetOrderManualByTime(platformCode,begin, end);
            } catch (Exception e) {
                log.error("get platform bet order failed, platformCode: {}, begin:{}, end: {}", platformCode, begin, end, e);
                throw new IllegalStateException(e);
            }
        });
    }

    @Override
    public void updatePlatformEnableStatus(EnableStatusParam param) {
        thirdPlatformRepo.lambdaUpdate()
                .set(ThirdPlatformEntity::getEnableEnum, param.getEnableStatus())
                .set(ThirdPlatformEntity::getUpdateTime, LocalDateTime.now())
                .eq(ThirdPlatformEntity::getCode, param.getPlatformCode())
                .update();
        // 禁用商户平台
        if (EnableEnum.FALSE == param.getEnableStatus()) {
            thirdSitePlatformInnerService.disablePlatformByCode(param.getPlatformCode());
        }
    }

    @Override
    public List<ThirdPlatformAssignDTO> findAllPlatform(Long merchantId) {
        return this.thirdPlatformRepo.selectAllPlatformInfo(merchantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @LogRecord(bizNo = "", type = LogTypeConstants.THIRD_PLATFORM_CONFIG, subType = LogSubTypeConstants.UPDATE,
            success = "{{#param.operateLog()}}这些平台被配置给了商户:{{#param.merchantId}}")
    public void assignMorePlatform(AssignMorePlatformParam param) {
        log.debug("批量分配三方平台到商户[入参]:{}", JSONUtil.toJsonStr(param));
        List<AssignMorePlatformParam.PlatformInformation> platforms = param.getPlatforms();
        if (CollUtil.isEmpty(platforms)) {
            return;
        }

        // 查询商品平台
        List<ThirdSitePlatformEntity> exists = this.thirdSitePlatformRepo.lambdaQuery().eq(ThirdSitePlatformEntity::getMerchantId, param.getMerchantId()).list();
        Map<String, ThirdSitePlatformEntity> existMap = CollStreamUtil.toMap(exists, ThirdSitePlatformEntity::getPlatformCode, Function.identity());
        // 归类新增更新
        List<ThirdSitePlatformEntity> insertList = Lists.newArrayList();
        List<ThirdSitePlatformEntity> assignToUnAssign = Lists.newArrayList(), assignToAssign = Lists.newArrayList(), unassignToAssign = Lists.newArrayList();
        CollUtil.forEach(platforms, (platform, index) -> {
            ThirdSitePlatformEntity tspEntity = existMap.get(platform.getPlatformCode());
            if (Objects.isNull(tspEntity)) {
                ThirdSitePlatformEntity entity = this.buildThirdSitePlatform(platform.getPlatformCode(), param.getMerchantId());
                entity.setEnableEnum(EnableEnum.getByCode(platform.getEnableEnum()));
                insertList.add(entity);
            } else {
                boolean isNotAssigned = platform.getAssigned() == 0;
                tspEntity.setEnableEnum(EnableEnum.getByCode(platform.getEnableEnum()));
                // 不分配
                if (isNotAssigned) {
                    // 从分配变更为不分配?
                    if (BooleanEnum.FALSE.equals(tspEntity.getIsDel())) {
                        tspEntity.setIsDel(BooleanEnum.TRUE);
                        assignToUnAssign.add(tspEntity);
                    }
                } else {
                    // 分配
                    // 从分配变更为分配?
                    if (BooleanEnum.FALSE.equals(tspEntity.getIsDel())) {
                        assignToAssign.add(tspEntity);
                    } else {
                        // 从不分配变更为分配?
                        tspEntity.setIsDel(BooleanEnum.FALSE);
                        unassignToAssign.add(tspEntity);
                    }
                }
            }
        });

        List<ThirdSitePlatformEntity> sitePlatforms = Lists.newArrayList();
        List<ThirdSiteGameEntity> sitePlatformGames = Lists.newArrayList();

        // 商户新增平台
        if (CollUtil.isNotEmpty(insertList)) {
            sitePlatforms.addAll(insertList);
            List<String> pCodes = CollStreamUtil.toList(insertList, ThirdSitePlatformEntity::getPlatformCode);
            this.refreshMerchantPlatformGame(param.getMerchantId(), pCodes, sitePlatformGames);
        }

        List<ThirdSitePlatformEntity> toAssignSitePlatforms = Lists.newArrayList();
        List<ThirdSitePlatformEntity> toUnAssignSitePlatforms = Lists.newArrayList();

        // 商户更新平台
        if (CollUtil.isNotEmpty(assignToUnAssign)) {
            toUnAssignSitePlatforms.addAll(assignToUnAssign);
        }
        if (CollUtil.isNotEmpty(assignToAssign)) {
            List<String> pCodes = CollStreamUtil.toList(assignToAssign, ThirdSitePlatformEntity::getPlatformCode);
            this.refreshMerchantPlatformGame(param.getMerchantId(), pCodes, sitePlatformGames);
        }
        if (CollUtil.isNotEmpty(unassignToAssign)) {
            toAssignSitePlatforms.addAll(unassignToAssign);
            List<String> pCodes = CollStreamUtil.toList(unassignToAssign, ThirdSitePlatformEntity::getPlatformCode);
            this.refreshMerchantPlatformGame(param.getMerchantId(), pCodes, sitePlatformGames);
        }

        // 新增商品平台，注意：这里没有使用批量保存或者更新，因有唯一索引缘故
        if (CollUtil.isNotEmpty(sitePlatforms)) {
            this.thirdSitePlatformRepo.saveBatch(sitePlatforms);
        }
        // 更新商户平台
        LambdaUpdateWrapper<ThirdSitePlatformEntity> updateWrapper;
        if (CollUtil.isNotEmpty(toAssignSitePlatforms)) {
            updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.in(ThirdSitePlatformEntity::getId, CollStreamUtil.toList(toAssignSitePlatforms, ThirdSitePlatformEntity::getId));
            updateWrapper.set(ThirdSitePlatformEntity::getIsDel, BooleanEnum.FALSE);
            this.thirdSitePlatformRepo.update(updateWrapper);
        }
        if (CollUtil.isNotEmpty(toUnAssignSitePlatforms)) {
            updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.in(ThirdSitePlatformEntity::getId, CollStreamUtil.toList(toUnAssignSitePlatforms, ThirdSitePlatformEntity::getId));
            updateWrapper.set(ThirdSitePlatformEntity::getIsDel, BooleanEnum.TRUE);
            this.thirdSitePlatformRepo.update(updateWrapper);
        }
        // 新增商户平台游戏
        if (CollUtil.isNotEmpty(sitePlatformGames)) {
            this.thirdSiteGameRepo.saveBatch(sitePlatformGames);
        }
    }

    @Override
    public void pullBetOrderByFlagManually(String platformCode, String flag) {
        log.debug("pull bet order manually, platform: {}, flag: {}", platformCode, flag);
        final String traceId = MDC.get(Constants.MDC_TRACE_ID_KEY);
        this.threadPoolTaskExecutor.execute(() -> {
            MDC.put(Constants.MDC_TRACE_ID_KEY, traceId);
            try {
                betOrderPullService.pullBetOrderManualByFlag(platformCode, flag);
            } catch (Exception e) {
                log.error("pull bet order by flag manually failed, platform: {}, flag: {}", platformCode, flag, e);
                throw new IllegalStateException(e);
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void newAgent(NewAgentParam param) {
        // check new code exists
        boolean exists = thirdPlatformRepo.exists(new LambdaQueryWrapper<ThirdPlatformEntity>().eq(ThirdPlatformEntity::getCode, param.getNewCode()));
        if (exists) {
            throw new ApiException(CommonCode.THIRD_PLATFORM_CODE_EXISTS);
        }
        ThirdPlatformEntity fromPlatform = thirdPlatformRepo.getThirdPlatformByCode(param.getFromCode());
        if (Objects.isNull(fromPlatform)) {
            throw new ApiException(CommonCode.THIRD_PLATFORM_NOT_EXISTS);
        }
        ThirdPlatformEntity newPlatform = buildNewAgentPlatform(fromPlatform, param);
        thirdPlatformRepo.save(newPlatform);

        List<ThirdPlatformCategoryEntity> fromCategories = thirdPlatformCategoryRepo.lambdaQuery().eq(ThirdPlatformCategoryEntity::getPlatformCode, param.getFromCode())
                .list();
        if (!fromCategories.isEmpty()) {
            thirdPlatformCategoryRepo.saveBatch(buildNewAgentPlatformCategories(fromCategories, param.getNewCode()));
        }

    }

    @Override
    public void validatePlatformConfig(String platformCode, CurrencyEnum currencyEnum) {
        PlatformConfigValidate validate = PlatformConfigValidate.builder().platformCode(platformCode).currencyEnum(currencyEnum).build();
        this.dockPlatformServiceFaced.validatePlatformConfig(validate);
    }

    private List<ThirdPlatformCategoryEntity> buildNewAgentPlatformCategories(List<ThirdPlatformCategoryEntity> fromCategories, String newPlatformCode) {
        final String updateBy = AdminTokenInfoUtil.getAdminName();
        final LocalDateTime now = LocalDateTime.now();

        return fromCategories.stream().map(i -> {
            ThirdPlatformCategoryEntity category = new ThirdPlatformCategoryEntity();
            BeanUtils.copyProperties(i, category, "id", "updateBy", "updateTime");
            category.setPlatformCode(newPlatformCode);
            category.setUpdateBy(updateBy);
            category.setUpdateTime(now);

            return category;
        }).collect(Collectors.toList());
    }

    private ThirdPlatformEntity buildNewAgentPlatform(ThirdPlatformEntity fromPlatform, NewAgentParam param) {
        ThirdPlatformEntity np = new ThirdPlatformEntity();
        BeanUtils.copyProperties(fromPlatform, np, "id", "createTime", "updateTime");

        np.setCode(param.getNewCode());
        np.setName(param.getNewName());

        LocalDateTime now = LocalDateTime.now();
        np.setCreateTime(now);
        np.setUpdateTime(now);

        return np;
    }

    private List<Long> getUnAssignedGameIds(Long merchantId, String platformCode, List<Long> ids) {
        List<Long> rowIds = thirdSiteGameRepo.lambdaQuery()
                .select(ThirdSiteGameEntity::getGameId)
                .eq(ThirdSiteGameEntity::getMerchantId, merchantId)
                .eq(ThirdSiteGameEntity::getPlatformCode, platformCode)
                .in(ThirdSiteGameEntity::getGameId, ids)
                .list()
                .stream()
                .map(ThirdSiteGameEntity::getGameId)
                .collect(Collectors.toList());
        if (rowIds.size() == ids.size()) {
            return new ArrayList<>();
        }

        return getDifference(ids, rowIds);
    }

    private List<Long> getDifference(List<Long> ids, List<Long> rowIds) {
        List<Long> difference = new ArrayList<>();

        for (Long id : ids) {
            if (!rowIds.contains(id)) {
                difference.add(id);
            }
        }
        return difference;
    }

    private ThirdSitePlatformEntity buildThirdSitePlatform(String platformCode, Long merchantId) {
        ThirdPlatformBasicInfoDTO thirdPlatform = ThirdPlatformLocalCacheUtil.getThirdPlatformBasicInfo(platformCode);
        if (Objects.isNull(thirdPlatform)) {
            throw new ApiException(CommonCode.THIRD_PLATFORM_NOT_EXISTS);
        }
        ThirdSitePlatformEntity entity = new ThirdSitePlatformEntity();
        entity.setMerchantId(merchantId);
        entity.setPlatformCode(thirdPlatform.getPlatformCode());
        entity.setPlatformName(thirdPlatform.getPlatformName());
        entity.setEnableEnum(EnableEnum.TRUE);

        entity.setCurrencies(thirdPlatform.getCurrencies());
        entity.setCategories(thirdPlatform.getCategories());
        return entity;
    }

    private List<ThirdPlatformDTO> parseThirdPlatformTemplate() {
        Resource resource = new ClassPathResource("third/platform.json");
        List<ThirdPlatformDTO> templates;

        try (InputStream inputStream = resource.getInputStream()) {
            templates = JacksonUtil.toJavaObject(inputStream, new TypeReference<List<ThirdPlatformDTO>>() {
            });
        } catch (IOException e) {
            log.error("parse platform.json error", e);
            throw new IllegalStateException(e);
        }

        templates.forEach(i -> {
            String requiredProperties = i.getRequiredProperties();
            List<ThirdPlatformPropertyDTO> properties = parseThirdPlatformTemplateProperties(requiredProperties);
            i.setRequestProperties(properties);

            i.setIsMaintain(BooleanEnum.FALSE);
            i.setEnableTransfer(EnableEnum.TRUE);
            i.setEnableEnum(EnableEnum.TRUE);

            // categories
            List<ThirdPlatformCategoryDTO> categories = parseThirdPlatformTemplateCategories(i.getCategories());
            i.setPlatformCategories(categories);
        });

        return templates;
    }


    private List<ThirdPlatformPropertyDTO> parseThirdPlatformTemplateProperties(String requiredProperties) {
        LinkedHashSet<ThirdPlatformPropertyDTO> linkedHashSet = Arrays.stream(requiredProperties.split(","))
                .map(i -> {
                    ThirdPlatformPropertyDTO property = new ThirdPlatformPropertyDTO();
                    property.setPropertyKey(i.trim());
                    property.setPropertyVal("");
                    property.setCurrencyEnum(CurrencyEnum.ALL);
                    return property;
                })
                .collect(Collectors.toCollection(LinkedHashSet::new));
        // KG-1655 add common properties
        // lang mapping property
        linkedHashSet.add(new ThirdPlatformPropertyDTO(ThirdConstants.THIRD_LANG_MAPPING_PROPERTY_KEY, "", CurrencyEnum.ALL));
        // currency mapping property
        linkedHashSet.add(new ThirdPlatformPropertyDTO(ThirdConstants.THIRD_CURR_MAPPING_PROPERTY_KEY, "", CurrencyEnum.ALL));
        // exchange rate property
        linkedHashSet.add(new ThirdPlatformPropertyDTO(ThirdConstants.THIRD_EXCHANGE_RATE_MAPPING_PROPERTY_KEY, "", CurrencyEnum.ALL));
        return new ArrayList<>(linkedHashSet);
    }

    private List<ThirdPlatformCategoryDTO> parseThirdPlatformTemplateCategories(String categories) {
        return Arrays.stream(categories.split(","))
                .map(i -> {
                    ThirdPlatformCategoryDTO categoryDTO = new ThirdPlatformCategoryDTO();
                    categoryDTO.setCategoryEnum(GameCategoryEnum.valueOf(i.trim()));
                    categoryDTO.setCategoryImg("{}");
                    categoryDTO.setThirdLoginTypeEnum(ThirdLoginTypeEnum.GAME);
                    return categoryDTO;
                })
                .collect(Collectors.toList());
    }

    private void refreshMerchantPlatformGame(Long merchantId, List<String> platformCodes, List<ThirdSiteGameEntity> sitePlatformGames) {
        // 查询平台游戏
        LambdaQueryWrapper<ThirdGameEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(ThirdGameEntity::getId, ThirdGameEntity::getPlatformCode);
        queryWrapper.in(ThirdGameEntity::getPlatformCode, platformCodes);
        List<ThirdGameEntity> platformGames = this.thirdGameRepo.list(queryWrapper);
        Map<String, List<ThirdGameEntity>> pgMap = CollStreamUtil.groupByKey(platformGames, ThirdGameEntity::getPlatformCode);
        // 过滤商户每个平台暂未添加的游戏
        CollUtil.forEach(pgMap, (platformCode, pGames, index) -> {
            List<Long> unAssignedSiteGameIds = this.getUnAssignedGameIds(merchantId, platformCode, CollStreamUtil.toList(pGames, ThirdGameEntity::getId));
            Opt.ofEmptyAble(unAssignedSiteGameIds).ifPresent(items -> items.forEach(item -> {
                ThirdSiteGameEntity entity = new ThirdSiteGameEntity();
                entity.setGameId(item);
                entity.setMerchantId(merchantId);
                entity.setPlatformCode(platformCode);
                sitePlatformGames.add(entity);
            }));
        });
    }
}
