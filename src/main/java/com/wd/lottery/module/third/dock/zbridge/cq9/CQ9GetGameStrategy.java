package com.wd.lottery.module.third.dock.zbridge.cq9;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.cq9.common.CQ9APIEnum;
import com.wd.lottery.module.third.dock.zbridge.cq9.common.CQ9GameType;
import com.wd.lottery.module.third.dock.zbridge.cq9.common.CQ9HttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.cq9.common.CQ9RequestConfig;
import com.wd.lottery.module.third.dock.zbridge.cq9.res.CQ9Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 游戏策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.CQ9_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class CQ9GetGameStrategy extends AbstractGetGameStrategy {

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        log.debug("CQ9:下载游戏:{}-{}", platformCode, currencyEnum);
        // 下载游戏
        CQ9RequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, CQ9RequestConfig.class);
        CQ9HttpRequestTemplate requestTemplate = new CQ9HttpRequestTemplate(requestConfig, currencyEnum);
        CQ9Response<List<Game>> response = requestTemplate.api(CQ9APIEnum.GET_GAME.getPath())
                .toGET()
                .toBeanAndCall(new TypeReference<CQ9Response<List<Game>>>() {
                });
        log.debug("CQ9:下载代理游戏响应:{}", JSONUtil.toJsonStr(response));
        List<Game> games = response.getData();
        if (CollUtil.isEmpty(games)) {
            return ListUtil.empty();
        }
        // 游戏转换
        return CollStreamUtil.toList(games, item -> {
            DockGame dockGame = new DockGame();
            dockGame.setPlatformCode(platformCode);
            // 游戏分类
            switch (item.getGametype()) {
                case CQ9GameType.FISH:
                    dockGame.setGameCategoryEnum(GameCategoryEnum.FISH);
                    break;
                case CQ9GameType.TABLE:
                    dockGame.setGameCategoryEnum(GameCategoryEnum.POKER);
                    break;
                case CQ9GameType.ARCADE:
                    dockGame.setGameCategoryEnum(GameCategoryEnum.THREE);
                    break;
                case CQ9GameType.SLOT:
                case CQ9GameType.LIVE:
                default:
                    dockGame.setGameCategoryEnum(GameCategoryEnum.SLOT);
            }
            dockGame.setThirdGameId(item.getGamecode());
            // 游戏名称
            Map<String, String> nameMap = CollStreamUtil.toMap(item.getNameset(), Game.NameSet::getLang, Game.NameSet::getName);
            dockGame.setGameName(nameMap.getOrDefault("en", item.getGamename()));
            dockGame.setGameCode(item.getGamecode());
            return dockGame;
        });
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dockGetGameUrl) {
        log.debug("CQ9:玩家打开游戏:{}", JSONUtil.toJsonStr(dockGetGameUrl));
        CQ9RequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dockGetGameUrl.getPlatformCode(), dockGetGameUrl.getCurrencyEnum(), CQ9RequestConfig.class);
        CQ9HttpRequestTemplate requestTemplate = new CQ9HttpRequestTemplate(requestConfig, dockGetGameUrl.getCurrencyEnum());
        // 玩家密码
        String password = dockGetGameUrl.getThirdUserPasswd();
        // 玩家登入
        String playerToken = this.getPlayerLoginToken(dockGetGameUrl.getPlatformCode(), dockGetGameUrl.getCurrencyEnum(), dockGetGameUrl.getThirdUserName(), password);
        final String defaultLang = "en";
        // 请求游戏
        CQ9Response<GameLink> response = requestTemplate.api(CQ9APIEnum.OPEN_GAME.getPath())
                .toPOST()
                .addParameter("usertoken", playerToken)
                .addParameter("gamehall", "cq9")
                .addParameter("gamecode", dockGetGameUrl.getGameCode())
                .addParameter("gameplat", dockGetGameUrl.getDeviceEnum().equals(DeviceEnum.PC) ? "web" : "mobile")
                .addParameter("lang", ThirdPlatformMappingConverter.toThirdLang(requestConfig, dockGetGameUrl.getLang(), defaultLang))
                .toBeanAndCall(new TypeReference<CQ9Response<GameLink>>() {
                });
        log.debug("CQ9:玩家打开游戏响应:{}", JSONUtil.toJsonStr(response));
        boolean isEmpty = ObjectUtil.isNull(response.getData()) && StrUtil.isEmpty(response.getData().getUrl());
        Assert.isFalse(isEmpty, () -> new RuntimeException("获取游戏路径失败"));
        return response.getData().getUrl();
    }

    /**
     * 玩家登入
     *
     * @param platformCode  平台码
     * @param currencyEnum  币种
     * @param thirdUserName 玩家账号
     * @param password      玩家账号密码
     * @return {@link String} 玩家登入后的token
     */
    private String getPlayerLoginToken(String platformCode, CurrencyEnum currencyEnum, String thirdUserName, String password) {
        log.debug("CQ9:玩家登入:{}-{}", thirdUserName, password);
        CQ9RequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, CQ9RequestConfig.class);
        CQ9HttpRequestTemplate requestTemplate = new CQ9HttpRequestTemplate(requestConfig, currencyEnum);
        CQ9Response<UserLoginToken> response = requestTemplate.api(CQ9APIEnum.PLAYER_LOGIN.getPath())
                .toPOST()
                .addParameter("account", thirdUserName)
                .addParameter("password", password)
                .toBeanAndCall(new TypeReference<CQ9Response<UserLoginToken>>() {
                });
        log.debug("CQ9:玩家登入响应:{}", JSONUtil.toJsonStr(response));
        return response.getData().getUsertoken();
    }

    /**
     * 游戏详情
     */
    @Data
    public static class Game {

        /**
         * 遊戲廠商
         */
        private String gamehall;

        /**
         * 遊戲種類
         */
        private String gametype;

        /**
         * 遊戲代碼
         */
        private String gamecode;

        /**
         * 遊戲名稱
         */
        private String gamename;

        /**
         * 遊玩平台
         */
        private String gameplat;

        /**
         * 遊戲技術
         */
        private String gametech;

        /**
         * 遊戲狀態
         */
        private Boolean status;

        /**
         * 維護狀態
         */
        private Boolean maintain;

        /**
         * 支援語系
         */
        private List<String> lang;

        /**
         * 遊戲名稱陣列
         */
        private List<NameSet> nameset;


        /**
         * 游戏对应语言名称
         */
        @Data
        public static class NameSet {
            /**
             * 遊戲名稱語系
             */
            private String lang;

            /**
             * 遊戲名稱
             */
            private String name;
        }

    }

    /**
     * 玩家登入token
     */
    @Data
    public static class UserLoginToken {
        /**
         * Token
         */
        private String usertoken;
    }

    /**
     * 打开游戏
     */
    @Data
    public static class GameLink {
        private String url;
        private String token;
    }
}