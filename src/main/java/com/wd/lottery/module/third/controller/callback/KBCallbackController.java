package com.wd.lottery.module.third.controller.callback;

import cn.hutool.json.JSONUtil;
import com.wd.lottery.module.third.dock.zbridge.kb.KBCallbackService;
import com.wd.lottery.module.third.dock.zbridge.kb.common.KBCallbackException;
import com.wd.lottery.module.third.dock.zbridge.kb.common.KBResCodeEnum;
import com.wd.lottery.module.third.dock.zbridge.kb.param.*;
import com.wd.lottery.module.third.dock.zbridge.kb.param.*;
import com.wd.lottery.module.third.dock.zbridge.kb.res.KBCallbackRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: KB callback processor
 *
 * <p> Created on 2024/12/18.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@RestController
@RequestMapping("${callback-path}/${module-path.third}/kb/{platformCode}")
public class KBCallbackController {

    private final KBCallbackService kbCallbackService;

    public KBCallbackController(KBCallbackService kbCallbackService) {
        this.kbCallbackService = kbCallbackService;
    }

    /**
     * 用户回调验证
     *
     * @param authParam 验证参数
     * @return 验证结果
     */
    @PostMapping("auth")
    public KBCallbackRes auth(@RequestBody KBCallbackParam authParam) {
        log.debug("kb auth callback: {}", JSONUtil.toJsonStr(authParam));
        try {
            return kbCallbackService.auth(authParam);
        } catch (KBCallbackException ke) {
            log.error("auth callback failed", ke);
            return KBCallbackRes.failed(ke.getResCode());
        } catch (Exception e) {
            log.error("auth unknown error", e);
            return KBCallbackRes.failed(KBResCodeEnum.OTHER_ERROR);
        }
    }

    /**
     * 下注并结算
     */
    @PostMapping("bet")
    public KBCallbackRes bet(@RequestBody KBBetAndSettledParam betParam) {
        log.debug("kb bet callback: {}", JSONUtil.toJsonStr(betParam));
        try {
            return kbCallbackService.bet(betParam);
        } catch (KBCallbackException ke) {
            log.error("bet callback failed", ke);
            return KBCallbackRes.failed(ke.getResCode());
        } catch (Exception e) {
            log.error("bet unknown error", e);
            return KBCallbackRes.failed(KBResCodeEnum.OTHER_ERROR);
        }
    }

    /**
     * 撤销 下注并结算
     */
    @PostMapping("cancelBet")
    public KBCallbackRes cancelBet(@RequestBody KBCancelBetParam cancelBetParam) {
        log.debug("kb cancelBet callback: {}", JSONUtil.toJsonStr(cancelBetParam));
        try {
            return kbCallbackService.cancelBet(cancelBetParam);
        } catch (KBCallbackException ke) {
            log.error("cancelBet callback failed", ke);
            return KBCallbackRes.failed(ke.getResCode());
        } catch (Exception e) {
            log.error("cancel bet unknown error", e);
            return KBCallbackRes.failed(KBResCodeEnum.OTHER_ERROR);
        }
    }

    /**
     * 下注/结算
     */
    @PostMapping("sessionBet")
    public KBCallbackRes sessionBet(@RequestBody KBSessionBetParam sessionBetParam) {
        log.debug("kb sessionBet callback: {}", JSONUtil.toJsonStr(sessionBetParam));
        try {
            return kbCallbackService.sessionBet(sessionBetParam);
        } catch (KBCallbackException ke) {
            log.error("sessionBet callback failed", ke);
            return KBCallbackRes.failed(ke.getResCode());
        } catch (Exception e) {
            log.error("session bet unknown error", e);
            return KBCallbackRes.failed(KBResCodeEnum.OTHER_ERROR);
        }
    }

    /**
     * 撤销下注
     */
    @PostMapping("cancelSessionBet")
    public KBCallbackRes cancelSessionBet(@RequestBody KBCancelSessionBetParam cancelSessionBetParam) {
        log.debug("kb cancelSessionBet callback: {}", JSONUtil.toJsonStr(cancelSessionBetParam));
        try {
            return kbCallbackService.cancelSessionBet(cancelSessionBetParam);
        } catch (KBCallbackException ke) {
            log.error("cancelSessionBet callback failed", ke);
            return KBCallbackRes.failed(ke.getResCode());
        } catch (Exception e) {
            log.error("cancel session bet unknown error", e);
            return KBCallbackRes.failed(KBResCodeEnum.OTHER_ERROR);
        }
    }

    /**
     * 获取余额
     */
    @PostMapping("balance")
    public KBCallbackRes balance(@RequestBody KBCallbackParam balanceParam) {
        log.debug("kb get balance callback: {}", JSONUtil.toJsonStr(balanceParam));
        try {
            return kbCallbackService.getBalance(balanceParam);
        } catch (KBCallbackException ke) {
            log.error("get balance callback failed", ke);
            return KBCallbackRes.failed(ke.getResCode());
        } catch (Exception e) {
            log.error("get balance unknown error", e);
            return KBCallbackRes.failed(KBResCodeEnum.OTHER_ERROR);
        }
    }

    /**
     * 派奖
     */
    @PostMapping("reward")
    public KBCallbackRes reward(@RequestBody KBRewardParam rewardParam) {
        log.debug("kb reward callback: {}", JSONUtil.toJsonStr(rewardParam));
        try {
            return kbCallbackService.reward(rewardParam);
        } catch (KBCallbackException ke) {
            log.error("reward callback failed", ke);
            return KBCallbackRes.failed(ke.getResCode());
        } catch (Exception e) {
            log.error("reward unknown error", e);
            return KBCallbackRes.failed(KBResCodeEnum.OTHER_ERROR);
        }
    }

}
