package com.wd.lottery.module.third.dock.zbridge.sbo;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.EncryptUtil;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.sbo.common.*;
import com.wd.lottery.module.third.dock.zbridge.sbo.res.SBOResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import com.wd.lottery.module.third.entity.ThirdGameEntity;
import com.wd.lottery.module.third.repo.ThirdGameRepo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 游戏注单
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.SBO_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class SBOGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {

    @Autowired
    private ThirdGameRepo thirdGameRepo;

    private final static Map<String, ThirdGameEntity> CACHE_MAP = Maps.newConcurrentMap();

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        log.debug("SBO:游戏注单:{}", JSONUtil.toJsonStr(flag));
        SBORequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), SBORequestConfig.class);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(flag.getPlatformCode());

        ZoneId system = ZoneId.systemDefault();
        ZoneId zoneId = ZoneId.of(configJson.getPlatformTimeZone());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(configJson.getRequestTimePattern());

        String startTime = flag.getBegin().atZone(system).withZoneSameInstant(zoneId).format(formatter);
        String endTime = flag.getEnd().atZone(system).withZoneSameInstant(zoneId).format(formatter);

        List<DockBetOrder> betOrderList = Lists.newArrayListWithExpectedSize(100);
        for (String portfolio : SBOGameType.THIRD_PRODUCT_LIST) {
            List<OrderList.OrderItem> betOrders = this.requestBetOrder(requestConfig, portfolio, startTime, endTime);
            if (CollUtil.isEmpty(betOrders)) {
                continue;
            }
            List<DockBetOrder> batch = this.toDockBetOrder(flag.getPlatformCode(), configJson, betOrders);
            betOrderList.addAll(batch);
        }
        flag.setFinished(true);
        this.analyseGame(betOrderList);
        return betOrderList;
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        log.debug("SBO:对局详情:{}", JSONUtil.toJsonStr(dto));
        SBORequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), SBORequestConfig.class);
        SBOHttpRequestTemplate requestTemplate = new SBOHttpRequestTemplate(requestConfig);

        String portfolio = SBOGameType.PRODUCT_SPORTS;
        String orderNo = dto.getOrderNo();
        if (!StringUtils.isNumeric(orderNo)) {
            portfolio = SBOGameType.PRODUCT_VIRTUAL;
        }

        try {
            GameDetail response = requestTemplate.toPOST()
                    .api(SBOApiEnum.GET_BET_ORDER_DETAIL.getPath())
                    .addParameter("Portfolio", portfolio)
                    .addParameter("Refno", orderNo)
                    .toBeanAndCall(GameDetail.class);
            log.debug("SBO:对局详情响应:{}", JSONUtil.toJsonStr(response));
            boolean isNotEmpty = ObjectUtil.isNotNull(response) && StrUtil.isNotEmpty(response.getUrl());
            return isNotEmpty ? response.getUrl() : StrUtil.EMPTY;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    /**
     * 请求游戏注单
     *
     * @param requestConfig SBORequestConfig
     * @param portfolio     portfolio
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return {@link List} 游戏注单
     */
    private List<OrderList.OrderItem> requestBetOrder(SBORequestConfig requestConfig, String portfolio, String startTime, String endTime) {
        SBOHttpRequestTemplate requestTemplate = new SBOHttpRequestTemplate(requestConfig);
        OrderList response = requestTemplate.toPOST()
                .api(SBOApiEnum.GET_BET_ORDER.getPath())
                .addParameter("portfolio", portfolio)
                .addParameter("startDate", startTime)
                .addParameter("endDate", endTime)
                .toBeanAndCall(OrderList.class);
        log.debug("SBO:游戏注单响应:({}至{})-{}-{}", startTime, endTime, portfolio, JSONUtil.toJsonStr(response));
        return response.getResult();
    }

    /**
     * TO DOCK ORDER
     *
     * @param platformCode platformCode
     * @param configJson   ThirdPlatformConfigDTO
     * @param betOrders    OrderList
     * @return {@link List<DockBetOrder>}
     */
    private List<DockBetOrder> toDockBetOrder(String platformCode, ThirdPlatformConfigDTO configJson, List<OrderList.OrderItem> betOrders) {
        List<DockBetOrder> resultList = Lists.newArrayListWithExpectedSize(100);
        for (OrderList.OrderItem item : betOrders) {
            if (SBOHelper.INVALID_STATUS_LIST.contains(item.getStatus())) {
                continue;
            }
            // 系统注单
            DockBetOrder dockBetOrder = this.toDockBetOrder(platformCode, configJson, item);
            // 游戏名称
            String gameName = item.getSportsType();
            // 虚拟体育
            gameName = StrUtil.isBlank(gameName) ? item.getProductType() : gameName;
            // 临时存储
            dockBetOrder.setGameId(gameName);
            resultList.add(dockBetOrder);
        }
        return resultList;
    }

    /**
     * TO DOCK ORDER
     *
     * @param platformCode platformCode
     * @param configJson   ThirdPlatformConfigDTO
     * @param item         OrderList.OrderItem
     * @return {@link DockBetOrder}
     */
    private DockBetOrder toDockBetOrder(String platformCode, ThirdPlatformConfigDTO configJson, OrderList.OrderItem item) {
        DockBetOrder dockBetOrder = new DockBetOrder();
        dockBetOrder.setPlatformCode(platformCode);
        dockBetOrder.setOrderNum(item.getRefNo());
        dockBetOrder.setOrderNumParent(item.getRefNo());
        // dockBetOrder.setGameId(null);
        dockBetOrder.setThirdUserName(item.getUsername());

        // BigDecimal moneyUnit = ObjectUtil.isNull(configJson.getMoneyUnit()) ? new BigDecimal(100) : new BigDecimal(configJson.getMoneyUnit());
        dockBetOrder.setBetMoney(super.toPlatformMoney(item.getActualStake()));
        // 中奖金额 = 盈亏 + 投注金额
        dockBetOrder.setWinMoney(super.toPlatformMoney(item.getWinLost().add(item.getActualStake())));
        // 有效投注 =  if (投注金额 == 派奖金额)  0 else 投注金额
        dockBetOrder.setValidBetMoney(dockBetOrder.getBetMoney() == dockBetOrder.getWinMoney() ? 0 : dockBetOrder.getBetMoney());

        String orderTime = this.filterMill(item.getOrderTime());
        LocalDateTime dateTime = LocalDateTime.parse(orderTime, DateTimeFormatter.ofPattern(configJson.getOrderTimePattern()));
        Date date = Date.from(dateTime.atZone(ZoneId.of(configJson.getPlatformTimeZone())).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime().toInstant(OffsetDateTime.now().getOffset()));
        dockBetOrder.setOrderTime(DateUtil.toLocalDateTime(date));

        try {
            List<OrderList.SubBet> subBet = item.getSubBet();
            String betOption = subBet.stream().map(sb -> String.format("%s: %s@%s", sb.getMarketType(), sb.getBetOption(), sb.getOdds())).collect(Collectors.joining("|"));
            String score = subBet.stream().map(sb -> String.format("%s: %s [%s][%s]", sb.getLeague(), sb.getMatch(), sb.getHtScore(), sb.getFtScore())).collect(Collectors.joining("|"));
            dockBetOrder.setBetDetail(String.format("%s__%s", score, betOption));
        } catch (Exception ex) {
            log.error("SBO:设置游戏注单详情失败", ex);
        }
        return dockBetOrder;
    }

    /**
     * 添加三方游戏
     *
     * @param dockBetOrders 游戏注单
     */
    private void analyseGame(List<DockBetOrder> dockBetOrders) {
        if (CollUtil.isEmpty(dockBetOrders)) {
            return;
        }
        // 注单游戏
        List<ThirdGameEntity> thirdGameList = CollStreamUtil.toList(dockBetOrders, item -> this.toThirdGame(item.getPlatformCode(), item.getGameId()));
        Collection<ThirdGameEntity> surplusGameList = CollUtil.filterNew(thirdGameList, item -> !CACHE_MAP.containsKey(item.getGameCode()));
        // 查找游戏
        Map<String, ThirdGameEntity> addGameMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(surplusGameList)) {
            String platformCode = CollUtil.getFirst(dockBetOrders).getPlatformCode();
            List<ThirdGameEntity> gameList = this.thirdGameRepo.lambdaQuery()
                    .eq(ThirdGameEntity::getPlatformCode, platformCode)
                    .eq(ThirdGameEntity::getIsDel, BooleanEnum.FALSE)
                    .in(ThirdGameEntity::getGameCode, CollStreamUtil.toList(surplusGameList, ThirdGameEntity::getGameCode))
                    .list();
            log.debug("SBO:Get Existing Game:{}", JSONUtil.toJsonStr(gameList));
            Map<String, ThirdGameEntity> existGameMap = CollStreamUtil.toMap(gameList, ThirdGameEntity::getGameCode, Function.identity());
            // 区分游戏
            CollUtil.forEach(surplusGameList, (surplusGame, index) -> {
                ThirdGameEntity temporary = existGameMap.get(surplusGame.getGameCode());
                if (ObjectUtil.isNull(temporary)) {
                    if (!addGameMap.containsKey(surplusGame.getGameCode())) {
                        addGameMap.put(surplusGame.getGameCode(), surplusGame);
                    }
                } else {
                    CACHE_MAP.put(surplusGame.getGameCode(), temporary);
                }
            });
        }
        // 设置三方游戏ID
        log.debug("SBO:Add Game:{}", JSONUtil.toJsonStr(addGameMap));
        CollUtil.forEach(dockBetOrders, (item, index) -> {
            String gameCode = this.getThirdGameCode(item.getGameId());
            if (CACHE_MAP.containsKey(gameCode)) {
                item.setGameId(CACHE_MAP.get(gameCode).getThirdGameId());
            } else {
                ThirdGameEntity tempGame = addGameMap.get(gameCode);
                item.setGameId(ObjectUtil.isNull(tempGame) ? null : tempGame.getThirdGameId());
            }
        });
        if (CollUtil.isNotEmpty(addGameMap)) {
            boolean isSaved = this.thirdGameRepo.saveBatch(addGameMap.values());
            if (isSaved) {
                CollUtil.forEach(addGameMap.values(), (addGame, index) -> CACHE_MAP.put(addGame.getGameCode(), addGame));
            }
        }
    }

    /**
     * 三方游戏
     *
     * @param platformCode 平台码
     * @param gameName     游戏名称
     * @return {@link ThirdGameEntity} 三方游戏
     */
    private ThirdGameEntity toThirdGame(String platformCode, String gameName) {
        ThirdGameEntity entity = new ThirdGameEntity();
        entity.setId(IdUtil.getSnowflakeNextId());
        entity.setPlatformCode(platformCode);
        entity.setThirdGameId(IdUtil.getSnowflakeNextIdStr());
        entity.setGameCode(this.getThirdGameCode(gameName));
        entity.setGameName(gameName);
        entity.setGameCategoryEnum(GameCategoryEnum.SPORT);
        entity.setGameCategoryName(GameCategoryEnum.SPORT.getDesc().toUpperCase());
        entity.setSupportedCurrency(CurrencyEnum.ALL.name());
        entity.setEnableEnum(EnableEnum.TRUE);
        return entity;
    }

    /**
     * 游戏编码
     *
     * @param gameName 游戏名称
     * @return {@link String} 游戏编码
     */
    private String getThirdGameCode(String gameName) {
        if (StrUtil.isBlank(gameName)) {
            return null;
        }
        String gameCode = StringUtils.deleteWhitespace(gameName);
        if (gameCode.length() >= 50) {
            gameCode = EncryptUtil.md5(gameCode);
        }
        return gameCode;
    }

    /**
     * 将时间中的毫秒去掉
     *
     * @param time 时间字符串
     * @return {@link String}
     */
    private String filterMill(String time) {
        int index = time.indexOf(".");
        if (index > 0) {
            return time.substring(0, index);
        }
        return time;
    }

    /**
     * 游戏注单
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class OrderList extends SBOResponse<Void> {
        private List<OrderItem> result;

        @Data
        public static class OrderItem {
            /**
             * 投注编号
             */
            private String refNo;

            /**
             * 下注的玩家名称
             */
            private String username;

            /**
             * 下注的体育类型
             */
            private String sportsType;

            /**
             * 玩家下注的时间
             */
            private String orderTime;

            /**
             * 注单归帐日。(格式为日期请忽略时间部分"00:00:00")(仅在注单结算后显示)
             */
            private String winLostDate;

            /**
             * 注单被结算的时间。若注单有被重新结算(won, lose, draw或void)，settleTime也会同步更新。
             */
            private String settleTime;

            /**
             * 修改日期
             */
            private String modifyDate;

            /**
             * 下注的赔率
             */
            private BigDecimal odds;

            /**
             * "下注的赔率类别 M : Malay odds H : HongKong odds E : Euro odds I : Indonesia odds"
             */
            private String oddsStyle;

            /**
             * 玩家的投注金
             */
            private BigDecimal stake;

            /**
             * 玩家的实际投注金。在特别的下注赔率会与注金(stake)不同
             */
            private BigDecimal actualStake;

            /**
             * 请参考币别
             */
            private String currency;

            /**
             * 玩家的注单状态
             */
            private String status;

            /**
             * 玩家的注单净赢
             */
            private BigDecimal winLost;

            /**
             * 玩家的流水
             */
            private BigDecimal turnover;

            /**
             * 依据投注额的总投注流水
             */
            private BigDecimal turnoverByStake;

            /**
             * 依据有效投注额的总投注流水
             */
            private BigDecimal turnoverByActualStake;

            /**
             * 依据投注额的淨投注流水
             */
            private BigDecimal netTurnoverByStake;

            /**
             * 依据有效投注额的淨投注流水
             */
            private BigDecimal netTurnoverByActualStake;

            /**
             * 是否为半场获胜或半场失败
             */
            private Boolean isHalfWonLose;

            /**
             * 是否为现场赛事
             */
            private Boolean isLive;

            /**
             * 注单未清算，将回传当前不包含注金(ActualStake)的最大净赢。 当注单清算时，将回传当前不包含注金(ActualStake)的预估净赢。
             */
            private BigDecimal maxWinWithoutActualStake;

            /**
             * 玩家下注的ip
             */
            private String ip;

            /**
             * 注单的子注单
             */
            private List<SubBet> subBet;

            // private String sportsType; // 運動類型

            /**
             * 注單是否被風控檢測到
             */
            @JsonProperty("IsSystemTagRisky")
            private Boolean isSystemTagRisky;

            /**
             * 注單退款/取消之原因
             */
            @JsonProperty("VoidReason")
            private String voidReason;

            /**
             * 玩家是否被風控檢測到
             */
            private Boolean isCustomerTagRisky;

            /**
             * "直屬下綫的用戶名/自己的用戶名 如果注單是下綫的，會顯示屬於你直屬下綫的用戶名 如果注單是自己的，會顯示自己的用戶名"
             */
            private String topDownline;

            /**
             * 虚拟体育专有字段， 产品类别
             */
            private String productType;
        }

        @Data
        public static class SubBet {
            /**
             * e-Bordeaux
             */
            private String betOption;

            /**
             * Handicap
             */
            private String marketType;

            /**
             * Football
             */
            private String sportType;

            /**
             * 0.5
             */
            private BigDecimal hdp;

            /**
             * 1.74
             */
            private BigDecimal odds;

            /**
             * e-Football F22 Elite Club Friendly
             */
            private String league;

            /**
             * e-Borussia Dortmund vs e-Bordeaux
             */
            private String match;

            /**
             * lose
             */
            private String status;

            /**
             * 2023-01-07T00:00:00
             */
            private String winlostDate;

            /**
             * 2:3
             */
            private String liveScore;

            /**
             * 2:1
             */
            private String htScore;

            /**
             * 3:3
             */
            private String ftScore;

            private String customeizedBetType;

            /**
             * 2023-01-07T00:00:00
             */
            private String kickOffTime;

            /**
             * false
             */
            private Boolean isHalfWonLose;
        }
    }

    /**
     * 对局详情
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class GameDetail extends SBOResponse<Void> {
        private String url;
    }
}