package com.wd.lottery.module.third.dock.zbridge.jdb;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.jdb.common.JDBHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.jdb.common.JDBRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.jdb.res.JDBResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Description: pp game service strategy
 *
 * <p> Created on 2024/5/23.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.JDB_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class JDBGetGameServiceStrategy extends AbstractGetGameStrategy {


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        JDBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, JDBRequestConfig.class);
        JDBHttpRequestTemplate requestTemplate = new JDBHttpRequestTemplate(requestConfig);

        JDBResponse<List<GameCategory>> response = requestTemplate
                .toPOST()
                .addParameter("action", "49")
                .addParameter("ts", System.currentTimeMillis() + "")
                .addParameter("lang", "en")
                .toBeanAndCall(new TypeReference<JDBResponse<List<GameCategory>>>() {
                });
        List<GameCategory> thirdCategories = response.getData();
        if (CollectionUtils.isEmpty(thirdCategories)) {
            return new ArrayList<>();
        }
        String categoryConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformBasicInfo(platformCode).getCategories();

        return parseAndFilterThirdGame(categoryConfig, thirdCategories, platformCode);

    }

    private List<DockGame> parseAndFilterThirdGame(String categoryConfig, List<GameCategory> thirdCategories, String platformCode) {
        List<DockGame> resultList = new ArrayList<>(100);
        for (GameCategory thirdCategory : thirdCategories) {
            Integer gType = thirdCategory.getGType();
            List<GameItem> list = thirdCategory.getList();
            if (Objects.isNull(gType) || CollectionUtils.isEmpty(list)) {
                continue;
            }
            switch (gType) {
                case 0:
                    if (StringUtils.contains(categoryConfig, GameCategoryEnum.SLOT.name())) {
                        resultList.addAll(toDockGame(list, GameCategoryEnum.SLOT, gType));
                    }
                    break;
                case 9:
                    if (StringUtils.contains(categoryConfig, GameCategoryEnum.SLOT.name())) {
                        resultList.addAll(toDockGame(list, GameCategoryEnum.MINI, gType));
                    }
                    break;
                case 7:
                    if (StringUtils.contains(categoryConfig, GameCategoryEnum.FISH.name())) {
                        resultList.addAll(toDockGame(list, GameCategoryEnum.FISH, gType));
                    }
                    break;
                case 12:
                    if (StringUtils.contains(categoryConfig, GameCategoryEnum.LOTTERY.name())) {
                        resultList.addAll(toDockGame(list, GameCategoryEnum.LOTTERY, gType));
                    }
                    break;
                case 18:
                    if (StringUtils.contains(categoryConfig, GameCategoryEnum.POKER.name())) {
                        resultList.addAll(toDockGame(list, GameCategoryEnum.POKER, gType));
                    }
                    break;
                default:
                    // ignore
            }
        }
        resultList.forEach(i -> i.setPlatformCode(platformCode));
        return resultList;
    }

    private List<DockGame> toDockGame(List<GameItem> gameItemList, GameCategoryEnum gameCategoryEnum, int gtype) {

        List<DockGame> list = new ArrayList<>(100);
        for (GameItem gameItem : gameItemList) {
            DockGame game = new DockGame();
            game.setGameCategoryEnum(gameCategoryEnum);
            String gameName = gameItem.getName();
            String gameCode = StringUtils.deleteWhitespace(gameName).toLowerCase();

            game.setThirdGameId(String.valueOf(gameItem.getGameId()));
            game.setGameCode(gameCode);
            game.setGameName(gameName);
            // JDB remark 存放 gType, 用于获取游戏链接
            game.setRemark(String.valueOf(gtype));

            if (Boolean.TRUE.equals(gameItem.getIsNew())) {
                game.setTag("new");
            }
            list.add(game);
        }

        return list;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        JDBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), JDBRequestConfig.class);
        JDBHttpRequestTemplate requestTemplate = new JDBHttpRequestTemplate(requestConfig);

        final String defaultLang = "en";
        JDBResponse<Object> resp = requestTemplate
                .toPOST()
                .addParameter("action", "11")
                .addParameter("ts", System.currentTimeMillis() + "")
                .addParameter("uid", dto.getThirdUserName())
                .addParameter("gType", getGType(dto))
                .addParameter("mType", dto.getThirdGameId())
                .addParameter("lang", ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang))
                // 不使用JDB游戏大厅
                .addParameter("windowMode", "2")
                // 增加 返回URL
                .addParameter("lobbyURL", dto.getLobbyUrl())
                .toBeanAndCall(new TypeReference<JDBResponse<Object>>() {
                });

        return resp.getPath();
    }


    @Override
    public String getDemoGameUrl(DockGetGameUrl dto) {
        log.debug("JDB get demo game url, param: {}", JSONUtil.toJsonStr(dto));
        JDBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), JDBRequestConfig.class);
        JDBHttpRequestTemplate requestTemplate = new JDBHttpRequestTemplate(requestConfig);

        final String defaultLang = "en";
        JDBResponse<Object> resp = requestTemplate
                .toPOST()
                .addParameter("action", "47")
                .addParameter("ts", System.currentTimeMillis() + "")
                .addParameter("gType", getGType(dto))
                .addParameter("mType", dto.getThirdGameId())
                .addParameter("lang", ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang))
                // 不使用JDB游戏大厅
                .addParameter("windowMode", "2")
                // 增加 返回URL
                .addParameter("lobbyURL", dto.getLobbyUrl())
                .toBeanAndCall(new TypeReference<JDBResponse<Object>>() {
                });

        return resp.getPath();
    }

    private String getGType(DockGetGameUrl dto) {
        return dto.getRemark();
    }

    @Data
    public static class GameCategory {
        /**
         * 游戏类型: 老虎机	0
         * 捕鱼机	7
         * 街机	9
         * 电子彩票	12
         * 棋牌	18
         */
        @JsonProperty("gType")
        private Integer gType;
        // 游戏列表
        private List<GameItem> list;
    }

    @Data
    public static class GameItem {
        // 机台类型
        @JsonProperty("mType")
        private Integer gameId;
        // 是否为新游戏
        private Boolean isNew;
        // 单一格式图档连结
        private String image;
        // 游戏名称
        private String name;
    }

}
