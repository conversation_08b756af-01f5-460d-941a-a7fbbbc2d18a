package com.wd.lottery.module.third.dock.zbridge.bg;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.bg.common.BGApiEnum;
import com.wd.lottery.module.third.dock.zbridge.bg.common.BGGameType;
import com.wd.lottery.module.third.dock.zbridge.bg.common.BGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.bg.common.BGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.bg.res.BGResponse;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Base64;
import java.util.List;

/**
 * 游戏策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.BG_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class BGGetGameStrategy extends AbstractGetGameStrategy {

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        log.debug("BG:下载游戏:{}-{}", platformCode, currencyEnum);
        ClassPathResource resource = new ClassPathResource("third/bg_game_list.xlsx");
        List<Game> games = Lists.newArrayList();
        try (InputStream inputStream = resource.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            games.addAll(reader.readAll(Game.class));
        } catch (Exception e) {
            log.error(StrUtil.format("BG:下载游戏:{}-{}", platformCode, currencyEnum), e);
        }
        return CollStreamUtil.toList(games, game -> this.toDockGame(platformCode, game));
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("BG:玩家打开游戏:{}", JSONUtil.toJsonStr(dto));
        BGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), BGRequestConfig.class);
        boolean isCasino = StrUtil.isEmpty(dto.getGameCode());
        final String defaultLang = "en_US";
        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(dto.getPlatformCode(), dto.getLang(), defaultLang);
        if (isCasino) {
            return this.getOpenCasinoGameUrl(dto, requestConfig, thirdLang);
        }
        return this.getOpenFishGameUrl(dto, requestConfig, thirdLang);
    }

    /**
     * 转DockGame
     *
     * @param platformCode 平台代码
     * @param game         游戏
     * @return {@link DockGame}
     */
    private DockGame toDockGame(String platformCode, Game game) {
        DockGame dockGame = new DockGame();
        dockGame.setPlatformCode(platformCode);
        GameCategoryEnum category = StrUtil.equals(BGGameType.CASINO, game.getGameType()) ? GameCategoryEnum.CASINO : GameCategoryEnum.FISH;
        dockGame.setGameCategoryEnum(category);
        dockGame.setThirdGameId(game.getGameCode());
        dockGame.setGameName(game.getGameName());
        dockGame.setGameCode(game.getGameCode());
        return dockGame;
    }

    /**
     * 打开真人游戏路径
     *
     * @param dto           DockGetGameUrl
     * @param requestConfig 平台配置
     * @param thirdLang     三方语言
     * @return {@link String} 游戏打开路径
     */
    private String getOpenCasinoGameUrl(DockGetGameUrl dto, BGRequestConfig requestConfig, String thirdLang) {
        BGHttpRequestTemplate requestTemplate = new BGHttpRequestTemplate(requestConfig);
        // 摘要
        String random = RandomUtil.randomString(8);
        String encoder = Base64.getEncoder().encodeToString(DigestUtil.sha1(requestConfig.getAgentPassword()));
        String digest = DigestUtil.md5Hex(random + requestConfig.getSn() + dto.getThirdUserName() + encoder);
        // 平台
        boolean isPc = ObjectUtil.isNull(dto.getDeviceEnum()) || dto.getDeviceEnum() == DeviceEnum.PC;
        // 请求游戏
        BGResponse<String> response = requestTemplate.toPOST()
                .api(BGApiEnum.OPEN_GAME.getPath())
                .addParameter("random", random)
                .addParameter("digest", digest)
                .addParameter("loginId", dto.getThirdUserName())
                .addParameter("locale", thirdLang)
                .addParameter("isMobileUrl", isPc ? "0" : "1")
                .addParameter("fromIp", dto.getIp())
                .addParameter("returnUrl", dto.getLobbyUrl())
                .toBeanAndCall(new TypeReference<BGResponse<String>>() {
                });
        log.debug("BG:玩家打开真人游戏响应:{}", JSONUtil.toJsonStr(response));
        boolean isEmpty = ObjectUtil.isNull(response) && StrUtil.isEmpty(response.getData());
        Assert.isFalse(isEmpty, () -> new RuntimeException("获取真人游戏路径失败"));
        return response.getData();
    }

    /**
     * 打开电子游戏路径
     *
     * @param dto           DockGetGameUrl
     * @param requestConfig 平台配置
     * @param thirdLang     三方语言
     * @return {@link String} 游戏打开路径
     */
    private String getOpenFishGameUrl(DockGetGameUrl dto, BGRequestConfig requestConfig, String thirdLang) {
        BGHttpRequestTemplate requestTemplate = new BGHttpRequestTemplate(requestConfig);
        // 摘要
        String random = RandomUtil.randomString(8);
        String sign = DigestUtil.md5Hex(random + requestConfig.getSn() + requestConfig.getApiSecret());
        // 平台
        boolean isPc = ObjectUtil.isNull(dto.getDeviceEnum()) || dto.getDeviceEnum() == DeviceEnum.PC;
        // 请求游戏
        BGResponse<String> response = requestTemplate.toPOST()
                .api(BGApiEnum.OPEN_GAME_FISH.getPath())
                .addParameter("random", random)
                .addParameter("sign", sign)
                .addParameter("gameType", dto.getGameCode())
                .addParameter("loginId", dto.getThirdUserName())
                .addParameter("lang", thirdLang)
                .addParameter("isMobileUrl", isPc ? "0" : "1")
                .addParameter("fromIp", dto.getIp())
                .addParameter("returnUrl", dto.getLobbyUrl())
                .toBeanAndCall(new TypeReference<BGResponse<String>>() {
                });
        log.debug("BG:玩家打开捕鱼游戏响应:{}", JSONUtil.toJsonStr(response));
        boolean isEmpty = ObjectUtil.isNull(response) && StrUtil.isEmpty(response.getData());
        Assert.isFalse(isEmpty, () -> new RuntimeException("获取捕鱼游戏路径失败"));
        return response.getData();
    }

    /**
     * 游戏
     */
    @Data
    public static class Game {
        private String gameType;
        private String gameCode;
        private String gameName;
    }
}