package com.wd.lottery.module.third.dock.zbridge.sexy;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.sexy.common.SEXYApiEnum;
import com.wd.lottery.module.third.dock.zbridge.sexy.common.SEXYHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.sexy.common.SEXYRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.sexy.res.SEXYResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component(BridgeConstant.SEXYBCRT_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class SEXYGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {
    private static final List<String> DragonTiger = Arrays.asList("MX-LIVE-006");
    private static final List<String> Baccarat = Arrays.asList("MX-LIVE-001", "MX-LIVE-002", "MX-LIVE-003");
    private static final List<String> AndarBahar = Arrays.asList("MX-LIVE-012");
    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        log.debug("SEXYBCRT requestThirdOrder dto:{} ", JSONUtil.toJsonStr(flag));
        CurrencyEnum currencyEnum = flag.getCurrencyEnum();
        String platformCode = flag.getPlatformCode();
        SEXYRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, SEXYRequestConfig.class);
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);
        SEXYHttpRequestTemplate requestTemplate = new SEXYHttpRequestTemplate(requestConfig);
        DateTimeFormatter paramPattern = DateTimeFormatter.ofPattern(platformConfig.getRequestTimePattern());
        String timeZone = platformConfig.getPlatformTimeZone();
        LocalDateTime sixDaysAgo = LocalDateTime.now().minusDays(6);
        String startTime = null;
        String endTime = null;
        if (sixDaysAgo.isBefore(flag.getBegin())) {
            startTime = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
            endTime = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
        } else {
            log.warn("SEXYBCRT:游戏注单时间超出了范围:{}", JSONUtil.toJsonStr(flag));
            startTime = sixDaysAgo.atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
            endTime = sixDaysAgo.plusMinutes(platformConfig.getMaxStep()).atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
            flag.setBegin(sixDaysAgo);
            flag.setEnd(sixDaysAgo.plusMinutes(platformConfig.getMaxStep()));
        }
        flag.setFinished(true);
        // 拉取注单
        // TODO 三方的测试和PRD不一样，PRD使用pullOrderApiUrl，已和三方确认过，合并代码到PRD时需要变更。
        TransactionResp resp = getTransactionResp(requestTemplate, startTime, platformCode, requestConfig.getDebtApiUrl(), endTime);
        log.debug("SEXYBCRT:游戏注单响应:{}", JSONUtil.toJsonStr(resp));
        List<TransactionResp.BetOrderData> dataList = resp.getTransactions();
        if (!CollectionUtils.isEmpty(dataList)) {
            List<DockBetOrder> orderList = dataList.stream().map(v -> this.toDockOrder(v, platformCode)).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            return orderList;
        } else {
            log.warn("SEXYBCRT:游戏注单响应异常:{}", JSONUtil.toJsonStr(resp));
        }
        return Collections.emptyList();
    }

    private DockBetOrder toDockOrder(TransactionResp.BetOrderData order, String platformCode) {
        DockBetOrder dockOrder = new DockBetOrder();
        dockOrder.setThirdUserName(order.getUserId());
        dockOrder.setPlatformCode(platformCode);
        dockOrder.setOrderNum(order.getPlatformTxId());
        dockOrder.setOrderNumParent(order.getPlatformTxId());
        String gameCode = order.getGameCode();
        dockOrder.setGameId(gameCode);
        dockOrder.setBetMoney(toPlatformMoney(order.getRealBetAmount()));
        dockOrder.setWinMoney(toPlatformMoney(order.getRealWinAmount()));
        dockOrder.setValidBetMoney(toPlatformMoney(order.getTurnover()));
        dockOrder.setOrderTime(parseThirdOrderTime(order.getUpdateTime(), platformCode));
        //视讯平台才进行注单详情操作
        if (BridgeConstant.SEXYBCRT_PLATFORM_CODE.equals(platformCode)) {
            try {
                JSONObject jsonObject = JSONUtil.parseObj(order.getGameInfo());
                List result = jsonObject.getJSONArray("result");
                List<String> detailList = Optional.of((List<Object>) result).orElse(Collections.emptyList())
                        .stream()
                        .filter(Objects::nonNull)
                        .map(Object::toString)
                        .collect(Collectors.toList());

                //判断游戏类型，如果是 龙虎 或 百家乐，需要新增对应数据
                String detail = "";
                if (DragonTiger.contains(gameCode)) { // 龙虎
                    if (detailList.size() == 6)
                        detail = String.format("Dragon: %s, Tiger: %s", detailList.get(0), detailList.get(3));
                    else
                        detail = detailList.stream()
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.joining(", "));
                } else if (Baccarat.contains(gameCode) && detailList.size() == 6) { //百家乐
                    detail = String.format("Player: %s %s %s, Banker: %s %s %s",
                            detailList.get(0),
                            detailList.get(1),
                            detailList.get(2),
                            detailList.get(3),
                            detailList.get(4),
                            detailList.get(5)
                    );
                } else if (AndarBahar.contains(gameCode)) { // 安达尔巴哈尔
                    JSONObject obj = JSONUtil.parseObj(detailList.get(0));
                    String andarCard = obj.getStr("lastAndarCard");
                    String baharCard = obj.getStr("lastBaharCard");
                    String mainCard = obj.getStr("mainCard");
                    detail = String.format("AndarCard: %s, MainCard: %s, BaharCard: %s", andarCard, mainCard, baharCard);
                } else {
                    detail = detailList.stream()
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(", "));
                }
                if (detail.length() > 200) {
                    detail = detail.substring(0, 200) + "...";
                }
                if (StringUtils.isNotBlank(detail)) {
                    dockOrder.setBetDetail(detail + "__" + order.getBetType());
                } else {
                    dockOrder.setBetDetail(detailList.stream()
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(", "))
                            + "__" + order.getBetType());
                }

            } catch (Exception e) {
                log.warn(String.format("AWC betDetail %s 处理失败", order), e);
            }
        }

        return dockOrder;
    }

    private LocalDateTime parseThirdOrderTime(String updateTime, String platformCode) {
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);
        DateTimeFormatter paramPattern = DateTimeFormatter.ofPattern(platformConfig.getOrderTimePattern());
        LocalDateTime dateTime = LocalDateTime.parse(updateTime, paramPattern);
        String timeZone = platformConfig.getPlatformTimeZone();
        return dateTime.atZone(ZoneId.of(timeZone))
                .withZoneSameInstant(ZoneId.systemDefault())
                .toLocalDateTime();
    }


    private TransactionResp getTransactionResp(SEXYHttpRequestTemplate requestTemplate, String startTime, String platformCode, String pullOrderApiUrl, String endTime) {
        return requestTemplate
                .host(pullOrderApiUrl)
                .api(SEXYApiEnum.BET_LIST.getPath())
                .toPOST()
                .addParameter("startTime", startTime)
                .addParameter("endTime", endTime)
                .addParameter("platform", platformCode)
                //-1: 取消投注，超时或系统错误时会发生。一般拉帐时并不会显示取消单，由于JDB老虎机/JDB捕鱼/YL捕鱼/FC/JILI/SG捕鱼下注方式不同，报表中 会针对他们特别显示此状态,
                .addParameter("status", "1")//0: 已下注, 1: 已结账, 2: 注单无效, 9: 无效的交易,不会再后台报表中呈现
                .toBeanAndCall(TransactionResp.class);
    }

    @Data
    public static class TransactionResp extends SEXYResponse<List<TransactionResp.BetOrderData>> {
        private List<BetOrderData> transactions;

        @Data
        public static class BetOrderData {
            /**
             * 玩家id
             **/
            private String userId;
            /**
             * 游戏商注单号
             **/
            private String platformTxId;
            /**
             * 平台名称
             **/
            private String platform;
            /**
             * 游戏编码
             **/
            private String gameCode;
            /**
             * 游戏类型
             **/
            private String gameType;
            /**
             * 游戏平台的下注项目
             **/
            private String betType;
            /**
             * 交易时间
             **/
            private String txTime;
            /**
             * 下注金额
             **/
            private BigDecimal betAmount;
            /**
             * 返还金额 (包含下注金额)
             **/
            private BigDecimal winAmount;
            /**
             * 游戏平台有效投注
             **/
            private BigDecimal turnover;
            /**
             * 该交易当前状况
             **/
            private String txStatus;
            /**
             * 真实下注金额
             **/
            private BigDecimal realBetAmount;
            /**
             * 真实返还金额
             **/
            private BigDecimal realWinAmount;
            /**
             * 累积奖金的下注金额
             **/
            private BigDecimal jackpotBetAmount;
            /**
             * 累积奖金的获胜金额
             **/
            private BigDecimal jackpotWinAmount;
            /**
             * 货币名称
             **/
            private String currency;
            /**
             * 更新时间(遵循ISO8601格式)
             **/
            private String updateTime;
            /**
             * 游戏商的回合识别码
             **/
            private String roundId;
            /**
             * 游戏讯息会由游戏商以 JSON 格式提供，依各游戏商回传资讯呈现的资料会有所不同
             * 请您不应针对 gameInfo 内的参数进行验证
             **/
            private String gameInfo;
            /****/
            private Integer settleStatus;
            /**
             * 下注时间
             **/
            private String betTime;
            /**
             * 游戏名称
             **/
            private String gameName;
        }
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        SEXYRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), SEXYRequestConfig.class);
        SEXYHttpRequestTemplate requestTemplate = new SEXYHttpRequestTemplate(requestConfig);
        try {
            BetOrderDetailResponse response = requestTemplate.toPOST()
                    .host(requestConfig.getApiUrl())
                    .api(SEXYApiEnum.BET_ORDER_DETAIL.getPath())
                    .addParameter("userId", dto.getThirdUserId())
                    .addParameter("platform", dto.getPlatformCode())
                    .addParameter("platformTxId", dto.getOrderNo())
                    .toBeanAndCall(BetOrderDetailResponse.class);
            log.debug("SEXYBCRT注单详情信息:{}", JacksonUtil.toJSONString(response));
            return Objects.isNull(response) ? StrUtil.EMPTY : response.getTxnUrl();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    @Data
    public static class BetOrderDetailResponse extends SEXYResponse<Void> {
        private String url;
        private String txnUrl;
        private String roundUrl;
    }
}
