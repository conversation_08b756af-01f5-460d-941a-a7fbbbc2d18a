package com.wd.lottery.module.third.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.RequestUtil;
import com.wd.lottery.config.SystemConfig;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.third.constants.ThirdConstants;
import com.wd.lottery.module.third.constants.ThirdRedisConstants;
import com.wd.lottery.module.third.dock.DockPlatformServiceFaced;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dto.ThirdSiteGameDTO;
import com.wd.lottery.module.third.dto.ThirdUserDTO;
import com.wd.lottery.module.third.entity.ThirdGameEntity;
import com.wd.lottery.module.third.repo.ThirdSiteGameRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 *
 * <p> Created on 2024/6/11.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class ThirdSiteGameInnerService {

    private final DockPlatformServiceFaced dockPlatformServiceFaced;
    private final ThirdSiteGameRepo thirdSiteGameRepo;
    private final ThirdSiteUserInnerService thirdSiteUserInnerService;
    private final StringRedisTemplate stringRedisTemplate;

    @Resource
    private SystemConfig systemConfig;

    public ThirdSiteGameInnerService(DockPlatformServiceFaced dockPlatformServiceFaced,
                                     ThirdSiteGameRepo thirdSiteGameRepo,
                                     ThirdSiteUserInnerService thirdSiteUserInnerService,
                                     StringRedisTemplate stringRedisTemplate) {
        this.dockPlatformServiceFaced = dockPlatformServiceFaced;
        this.thirdSiteGameRepo = thirdSiteGameRepo;
        this.thirdSiteUserInnerService = thirdSiteUserInnerService;
        this.stringRedisTemplate = stringRedisTemplate;
    }


    public List<ThirdSiteGameDTO> listGameByPlatform(String platformCode, CurrencyEnum currencyEnum, GameCategoryEnum categoryEnum, Long merchantId) {
        return thirdSiteGameRepo.listGameByPlatform(platformCode, currencyEnum, categoryEnum, merchantId);
    }


    public List<ThirdSiteGameDTO> getMerchantHotGameList(Long merchantId, CurrencyEnum currencyEnum) {
        return thirdSiteGameRepo.getMerchantHotGameList(merchantId, currencyEnum);
    }

    public String getGameLoginUrl(String platformCode, ThirdGameEntity gameEntity, DeviceEnum deviceEnum, ThirdUserDTO thirdUserDTO) {

        log.debug("get game url, platformCode: {}, gameEntity: {}, thirdUser: {}", platformCode, gameEntity, thirdUserDTO);

        DockGetGameUrl getGameUrlParam = buildGetGameUrlParam(thirdUserDTO, deviceEnum, gameEntity);
        String openGameUrl = dockPlatformServiceFaced.getOpenGameUrl(platformCode, getGameUrlParam);

        // 更新三方用户登录时间
        thirdSiteUserInnerService.updateThirdUserLoginTime(thirdUserDTO);

        return assembleUrl(openGameUrl, getGameUrlParam);
    }

    private DockGetGameUrl buildGetGameUrlParam(ThirdUserDTO thirdUserDTO, DeviceEnum deviceEnum, ThirdGameEntity gameEntity) {
        if (Objects.isNull(thirdUserDTO)) {
            throw new ApiException("get or create third user error");
        }
        if(Objects.isNull(gameEntity)) {
            throw new ApiException("game not found");
        }
        if (BooleanEnum.TRUE == gameEntity.getIsDel() || BooleanEnum.TRUE == gameEntity.getIsMaintain()) {
            throw new ApiException("game status illegal");
        }
        return toDockGetGameUrl(thirdUserDTO, deviceEnum, gameEntity);
    }

    private static DockGetGameUrl toDockGetGameUrl(ThirdUserDTO thirdUserDTO, DeviceEnum deviceEnum, ThirdGameEntity gameEntity) {
        DockGetGameUrl param = new DockGetGameUrl();
        // member
        param.setMerchantId(thirdUserDTO.getMerchantId());
        param.setMemberId(thirdUserDTO.getMemberId());
        param.setCurrencyEnum(thirdUserDTO.getCurrencyEnum());

        // game
        param.setThirdGameId(gameEntity.getThirdGameId());
        param.setGameCode(gameEntity.getGameCode());
        param.setCategoryEnum(gameEntity.getGameCategoryEnum());
        param.setRemark(gameEntity.getRemark());
        param.setPlatformCode(gameEntity.getPlatformCode());

        // third user
        param.setThirdUserId(thirdUserDTO.getThirdUserId());
        param.setThirdUserName(thirdUserDTO.getThirdUserName());
        param.setThirdUserPasswd(thirdUserDTO.getThirdPassword());

        // req info
        param.setIp(RequestUtil.getRequestIpFromRequest());
        param.setLang(RequestUtil.getLang());
        param.setLobbyUrl(RequestUtil.getEndpoint());
        param.setDeviceEnum(deviceEnum);

        return param;
    }


    private String assembleUrl(String content, DockGetGameUrl dockGetGameUrl) {
        if (StringUtils.isBlank(content)) {
            throw new IllegalStateException("third game url content is empty");
        }

        if (!StringUtils.containsIgnoreCase(content, ThirdConstants.HTML_PATTERN)) {
            return content;
        }

        String uuid = IdWorker.get32UUID();
        String url = dockGetGameUrl.getLobbyUrl()
                + systemConfig.getContextPath()
                + StringPool.SLASH + systemConfig.getConsumerPath()
                + StringPool.SLASH + systemConfig.getThirdModulePath()
                + StringPool.SLASH + ThirdConstants.THIRD_PLATFORM_PATH
                + ThirdConstants.THIRD_GAME_OPEN_HTML_PATH
                + ThirdConstants.THIRD_GAME_OPEN_HTML_TOKEN_PARAM + uuid;

        String cacheKey = ThirdRedisConstants.OPEN_GAME_HTML_TOKEN + uuid;
        log.debug("cache open game html content, key: {}, url: {}, content: {}", cacheKey, url, content);
        stringRedisTemplate.opsForValue().set(cacheKey, content, 30, TimeUnit.SECONDS);
        return url;
    }

    public List<ThirdSiteGameDTO> getMerchantGameList(Long merchantId, CurrencyEnum currencyEnum){
        return thirdSiteGameRepo.getMerchantGameList(merchantId, currencyEnum);
    }

    /**
     * 获取游戏试玩链接
     *
     * @param platformCode 三方平台编码
     * @param gameEntity   游戏信息
     * @param deviceEnum   设备类型
     * @param member       会员信息
     * @return 试玩链接
     */
    public String getDemoGameUrl(String platformCode, ThirdGameEntity gameEntity, DeviceEnum deviceEnum, Member member) {
        log.debug("get demo game url, platformCode: {}, gameEntity: {}, member: {}", platformCode, gameEntity, member);
        ThirdUserDTO thirdUser = new ThirdUserDTO();
        thirdUser.setMerchantId(member.getMerchantId());
        thirdUser.setMemberId(member.getId());
        thirdUser.setCurrencyEnum(member.getCurrencyEnum());

        DockGetGameUrl param = buildGetGameUrlParam(thirdUser, deviceEnum, gameEntity);
        return dockPlatformServiceFaced.getDemoGameUrl(platformCode, param);
    }
}
