package com.wd.lottery.module.third.param;

import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description: 三方游戏更新参数
 *
 * <p> Created on 2024/7/24.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
public class ThirdGameUpdateParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "参数币种", hidden = true)
    private CurrencyEnum currencyEnum;

    @NotBlank
    @Schema(description = "平台编码")
    private String platformCode;
    @NotBlank
    @Schema(description = "三方游戏ID")
    private String thirdGameId;

    @NotBlank
    @Schema(description = "游戏名称")
    private String gameName;

    @NotNull
    @Schema(description = "游戏分类")
    private GameCategoryEnum gameCategoryEnum;

    @Schema(description = "游戏图片")
    private String gameImg;

    /**
     * 排序字段，越大越靠前
     */
    @Schema(description = "排序字段，越大越靠前")
    private Integer sort;
    /**
     * 游戏是否维护
     */
    @Schema(description = "游戏是否维护， TRUE  维护， FALSE 未维护")
    private BooleanEnum isMaintain;

    /**
     * 游戏是否热门
     */
    @Schema(description = "游戏是否热门， TRUE  是， FALSE 否")
    private BooleanEnum enableHot;

    @Schema(description = "游戏是否启用")
    private EnableEnum enableEnum;

    @Schema(description = "游戏支持的币种")
    private String supportedCurrency;
}
