package com.wd.lottery.module.third.dock.zbridge.sexy;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.base.PeachLang;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.sexy.common.SEXYApiEnum;
import com.wd.lottery.module.third.dock.zbridge.sexy.common.SEXYHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.sexy.common.SEXYRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.sexy.res.SEXYExcelGameDto;
import com.wd.lottery.module.third.dock.zbridge.sexy.res.SEXYResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.wd.lottery.module.third.dock.zbridge.sexy.SEXYUserServiceStrategy.buildBetLimit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component(BridgeConstant.SEXYBCRT_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class SEXYGetGameServiceStrategy extends AbstractGetGameStrategy {

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        List<DockGame> gameList = new ArrayList<>(100);
        ClassPathResource resource = new ClassPathResource("third/sexybcrt_game_list.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            EasyExcel.read(inputStream, SEXYExcelGameDto.class, new AnalysisEventListener<SEXYExcelGameDto>() {
                @Override
                public void invoke(SEXYExcelGameDto gameItem, AnalysisContext analysisContext) {
                    DockGame dockGame = toDockGame(gameItem, platformCode);
                    gameList.add(dockGame);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet().doRead();

        } catch (Exception e) {
            log.error("getGameList error", e);
        }

        return gameList;
    }

    private DockGame toDockGame(SEXYExcelGameDto gameItem, String platformCode) {
        DockGame dockGame = new DockGame();
        dockGame.setPlatformCode(platformCode);
        dockGame.setThirdGameId(gameItem.getGameCode());
        dockGame.setGameCode(gameItem.getGameCode());
        dockGame.setGameName(gameItem.getGameName());
        dockGame.setGameCategoryEnum(GameCategoryEnum.CASINO);
        dockGame.setRemark(gameItem.getGameType());
        return dockGame;
    }


    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        CurrencyEnum currencyEnum = dto.getCurrencyEnum();
        String platformCode = dto.getPlatformCode();
        SEXYRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, SEXYRequestConfig.class);
        SEXYHttpRequestTemplate requestTemplate = new SEXYHttpRequestTemplate(requestConfig);

        // externalURL:用于导回您指定的网站
        String gameCode = dto.getGameCode();
        if (Objects.isNull(gameCode)) {
            gameCode = "MX-LIVE-001";
        }
        LaunchGameResp response = requestTemplate
                .host(requestConfig.getApiUrl())
                .api(SEXYApiEnum.LOGIN_AND_LAUNCH_GAME.getPath())
                .toPOST()
                .addParameter("userId", dto.getThirdUserId())
                .addParameter("platform", "SEXYBCRT")
                .addParameter("gameType", "LIVE")
                // 进入后需要隐藏其他的平台
                .addParameter("gameCode", gameCode)
                .addParameter("betLimit", buildBetLimit(requestConfig.getBetLimit()))
                .addParameter("language", this.getLanguageCode(dto.getLang()))
                .addParameter("externalURL", dto.getLobbyUrl())
                .toBeanAndCall(LaunchGameResp.class, false);

        log.debug("SEXYBCRT getOpenGameUrl:{}", JSONUtil.toJsonStr(response));
        if (!Objects.equals(response.getStatus(), 0)) {
            throw new ApiException("SEXYBCRT getOpenGameUrl 异常:" + JSONUtil.toJsonStr(response));
        }
        return response.getUrl();
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class LaunchGameResp extends SEXYResponse<Void> {
        private String url;
        private List<String> extension;
    }

    private String getLanguageCode(String language) {
        String languageCode = "en";

        if (PeachLang.VI_VN.equals(language)) {
            languageCode = "vn";
        } else if (PeachLang.TH_TH.equals(language)) {
            languageCode = "th";
        }
        return languageCode;
    }


}
