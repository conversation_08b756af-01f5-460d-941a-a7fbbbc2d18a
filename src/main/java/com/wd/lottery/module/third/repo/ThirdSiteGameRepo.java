package com.wd.lottery.module.third.repo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dto.ThirdSiteGameDTO;
import com.wd.lottery.module.third.entity.ThirdSiteGameEntity;
import com.wd.lottery.module.third.param.ThirdSiteGameQueryParam;

import java.util.List;

/**
 * Description: 商户三方游戏数据库操作服务
 *
 * <p> Created on 2024/5/28.
 *
 * <AUTHOR>
 * @version 0.1
 */
public interface ThirdSiteGameRepo extends IService<ThirdSiteGameEntity> {


    List<ThirdSiteGameDTO> listGameByPlatform(String platformCode, CurrencyEnum currencyEnum, GameCategoryEnum categoryEnum, Long merchantId);

    Page<ThirdSiteGameDTO> getThirdSiteGamePage(ThirdSiteGameQueryParam param);

    List<ThirdSiteGameDTO> getMerchantGameList(Long merchantId, CurrencyEnum currencyEnum);

    List<ThirdSiteGameDTO> getMerchantHotGameList(Long merchantId, CurrencyEnum currencyEnum);
}
