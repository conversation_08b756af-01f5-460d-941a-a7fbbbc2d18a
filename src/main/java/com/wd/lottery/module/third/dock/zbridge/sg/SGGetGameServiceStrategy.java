package com.wd.lottery.module.third.dock.zbridge.sg;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.sg.common.SGGameType;
import com.wd.lottery.module.third.dock.zbridge.sg.common.SGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.sg.common.SGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.sg.req.AcctInfo;
import com.wd.lottery.module.third.dock.zbridge.sg.res.GetGameResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: sg game strategy
 *
 * <p> Created on 2024/5/13.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.SG_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class SGGetGameServiceStrategy extends AbstractGetGameStrategy {


    private final SGTokenService sgTokenService;

    public SGGetGameServiceStrategy(SGTokenService sgTokenService) {
        this.sgTokenService = sgTokenService;
    }


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        SGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, SGRequestConfig.class);
        SGHttpRequestTemplate requestTemplate = new SGHttpRequestTemplate(requestConfig);

        GetGameResponse response = requestTemplate
                .addHeader("API", "getGames")
                .toBeanAndCall(GetGameResponse.class);

        List<GetGameResponse.GameResp> games = response.getGames();
        if (CollUtil.isEmpty(games)) {
            return new ArrayList<>();
        }
        return toDockGameList(games, platformCode);
    }

    private List<DockGame> toDockGameList(List<GetGameResponse.GameResp> games, String platformCode) {
        if (CollectionUtils.isEmpty(games)) {
            return new ArrayList<>();
        }
        return games.stream()
                .map(game -> {
                    DockGame dockGame = new DockGame();
                    dockGame.setPlatformCode(platformCode);
                    dockGame.setThirdGameId(game.getGameCode());
                    dockGame.setGameName(game.getGameName());
                    dockGame.setGameCode(game.getGameCode());
                    dockGame.setGameCategoryEnum(parseGameCategory(game.getGameCode()));
                    return dockGame;
                })
                .collect(Collectors.toList());
    }

    private GameCategoryEnum parseGameCategory(String gameCode) {
        if (SGGameType.FISH_GAME_CODES.contains(gameCode)) {
            return GameCategoryEnum.FISH;
        }
        if (SGGameType.POKER_GAME_CODES.contains(gameCode)) {
            return GameCategoryEnum.POKER;
        }
        return GameCategoryEnum.SLOT;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        SGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), SGRequestConfig.class);

        String domain = requestConfig.getOpenGameUrl();
        domain = StrUtil.endWithIgnoreCase(domain, "/") ? domain : domain + "/";

        boolean isMobile = DeviceEnum.PC != dto.getDeviceEnum();

        final String defaultLang = "en_US";
        final String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang);
        final String thirdCurr = ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, dto.getCurrencyEnum());
        String token = sgTokenService.genToken(getAcctInfo(dto.getThirdUserId(), dto.getThirdUserId(), thirdCurr));

        String url = "%s%s/auth/?acctId=%s&language=%s&token=%s&game=%s&mobile=%s&menumode=on";
        url = String.format(url, domain, requestConfig.getMerchantCode(), dto.getThirdUserId(), thirdLang, token, dto.getGameCode(), isMobile ? "true" : "false");

        if (isMobile) {
            String endpoint = dto.getLobbyUrl();
            url = url.concat("&exitUrl=").concat(endpoint);
        }
        log.debug("SG:打开游戏链接:{}", url);
        return url;
    }

    @Override
    public String getDemoGameUrl(DockGetGameUrl dto) {
        SGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), SGRequestConfig.class);
        final String defaultLang = "en_US";
        final String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang);
        final String thirdCurr = ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, dto.getCurrencyEnum());

        boolean isMobile = DeviceEnum.PC != dto.getDeviceEnum();

        String token = sgTokenService.genToken(getAcctInfo(null, "demo", thirdCurr));
        String gameUrl = requestConfig.getDemoGameUrl()
                + "/" + requestConfig.getMerchantCode()
                + "/auth"
                + "?accId="
                + "&token=" + token
                + "&fun=" + true
                + "&language=" + thirdLang
                + "&game=" + dto.getGameCode()
                + "&mobile=" + isMobile
                + "&menumode=on"
                + "&exitUrl=" + dto.getLobbyUrl();

        log.debug("demo game url: {}", gameUrl);

        return gameUrl;
    }

    private AcctInfo getAcctInfo(String accId, String userName, String thirdCurr) {
        AcctInfo info = new AcctInfo();
        info.setAcctId(accId);
        info.setCurrency(thirdCurr);
        info.setBalance(BigDecimal.ZERO);
        info.setSiteId("0");
        info.setUserName(userName);
        return info;
    }


}
