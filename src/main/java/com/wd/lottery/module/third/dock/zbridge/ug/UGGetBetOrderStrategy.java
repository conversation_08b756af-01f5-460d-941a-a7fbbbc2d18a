package com.wd.lottery.module.third.dock.zbridge.ug;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.ug.common.UGApiEnum;
import com.wd.lottery.module.third.dock.zbridge.ug.common.UGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.ug.common.UGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.ug.common.UGResponse;
import com.wd.lottery.module.third.dock.zbridge.ug.req.BetDetailReqParam;
import com.wd.lottery.module.third.dock.zbridge.ug.req.BetReqParam;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;


@Slf4j
@Component(value = BridgeConstant.UG_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class UGGetBetOrderStrategy extends AbstractGetBetOrderStrategy {

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        log.debug("UG:游戏注单:{}", JSONUtil.toJsonStr(flag));
        UGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), UGRequestConfig.class);
        UGHttpRequestTemplate requestTemplate = new UGHttpRequestTemplate(requestConfig);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(flag.getPlatformCode());
        // 拉单参数
        String timeZone = configJson.getPlatformTimeZone();
        DateTimeFormatter paramPattern = DateTimeFormatter.ofPattern(configJson.getRequestTimePattern());
        String start = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
        String end = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
        BetReqParam betReqParam = BetReqParam.builder().apiKey(requestConfig.getApiKey()).operatorId(requestConfig.getOperatorId())
                .updateTimeFrom(start).updateTimeTo(end).build();
        // 拉取注单
        UGResponse<List<BetOrderInfo>> response = requestTemplate.toPOST()
                .api(UGApiEnum.BET_ORDER.getPath())
                .body(JacksonUtil.toJSONString(betReqParam))
                .toBeanAndCall(new TypeReference<UGResponse<List<BetOrderInfo>>>() {
                }, true);
        log.debug("UG:游戏注单响应:{}",  JSONUtil.toJsonStr(response));
        if (CollUtil.isEmpty(response.getData())) {
            flag.setFinished(true);
            return ListUtil.empty();
        }
        flag.setFinished(true);
        return CollStreamUtil.toList(response.getData(), betOrder -> this.toDockBetOrder(flag.getPlatformCode(), betOrder,paramPattern , timeZone));
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        log.debug("UG:对局详情:{}", JSONUtil.toJsonStr(dto));
        UGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), UGRequestConfig.class);
        UGHttpRequestTemplate requestTemplate = new UGHttpRequestTemplate(requestConfig);

        BetDetailReqParam betDetailReqParam = BetDetailReqParam.builder().apiKey(requestConfig.getApiKey()).operatorId(requestConfig.getOperatorId())
                .ticketId(dto.getOrderNo()).userId(dto.getThirdUserName()).build();

        try {
            UGResponse<String> response = requestTemplate.toPOST()
                    .api(UGApiEnum.GAME_DETAIL_URL.getPath())
                    .body(JacksonUtil.toJSONString(betDetailReqParam))
                    .toBeanAndCall(new TypeReference<UGResponse<String>>() {
                    }, true);
            log.debug("UG:对局详情响应结果:{}", JSONUtil.toJsonStr(response));
            return ObjectUtil.isNull(response) ? StrUtil.EMPTY : response.getData();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }


    private DockBetOrder toDockBetOrder(String platformCode, BetOrderInfo betOrder , DateTimeFormatter paramPattern,String timeZone ) {
        DockBetOrder dockBetOrder = new DockBetOrder();
        dockBetOrder.setPlatformCode(platformCode);
        dockBetOrder.setOrderNum(betOrder.getTicketId());
        dockBetOrder.setOrderNumParent(betOrder.getTicketId());
        String gameId =  betOrder.getBetOrderDetailList().get(0).getSportId().toString();
        dockBetOrder.setGameId(gameId);
        dockBetOrder.setThirdUserName(betOrder.getUserId());
        dockBetOrder.setBetMoney(super.toPlatformMoney(BigDecimal.valueOf(betOrder.getNetStake())));
        dockBetOrder.setWinMoney(super.toPlatformMoney(new BigDecimal(betOrder.getPayout())));
        dockBetOrder.setValidBetMoney(super.toPlatformMoney(BigDecimal.valueOf(betOrder.getTurnover())));
        if(CollUtil.isNotEmpty(betOrder.getBetOrderDetailList())){
            StringBuilder teamNameBuffer = new StringBuilder();
            StringBuilder resultBuffer = new StringBuilder();
            String betDetail = getBetDetail(betOrder.getBetOrderDetailList(), teamNameBuffer, resultBuffer);
            dockBetOrder.setBetDetail(betDetail);
        }
        LocalDateTime thirdBet = LocalDateTime.parse(betOrder.getBetTime(),paramPattern);
        dockBetOrder.setOrderTime(thirdBet.atZone(ZoneId.of(timeZone)).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime());
        return dockBetOrder;
    }

    private String getBetDetail(List<BetOrderDetail> betOrderDetailList , StringBuilder teamNameBuffer , StringBuilder resultBuffer){
        for (int i = 0; i < betOrderDetailList.size(); i++){
            if(i!=0){
                teamNameBuffer.append("|");
                resultBuffer.append("|");
            }
            BetOrderDetail datail = betOrderDetailList.get(0);
            teamNameBuffer.append(datail.getHomeTeamName());
            teamNameBuffer.append(" v ");
            teamNameBuffer.append(datail.getAwayTeamName());
            resultBuffer.append(datail.getDetailResult());
        }
        return teamNameBuffer.append("__").append(resultBuffer).toString();
    }



    @Data
    static class BetOrderDetail{
        @JsonProperty("sportId")
        private Integer sportId;
        @JsonProperty("homeTeamName")
        private String homeTeamName;
        @JsonProperty("awayTeamName")
        private String awayTeamName;
        @JsonProperty("detailResult")
        private String detailResult;
    }

    @Data
    static class BetOrderInfo{
        @JsonProperty("ticketId")
        private String ticketId;
        @JsonProperty("ticketGroupId")
        private String ticketGroupId;
        @JsonProperty("currencyId")
        private Integer currencyId;
        @JsonProperty("userId")
        private String userId;
        @JsonProperty("status")
        private Integer status;
        @JsonProperty("payout")
        private String payout;
        @JsonProperty("stake")
        private Double stake;
        @JsonProperty("netStake")
        private Double netStake;
        @JsonProperty("turnover")
        private Double turnover;
        @JsonProperty("winLose")
        private Double winLose;
        @JsonProperty("oddsExpression")
        private String oddsExpression;
        @JsonProperty("oddsExpressionShortName")
        private String oddsExpressionShortName;
        @JsonProperty("betTime")
        private String betTime;
        @JsonProperty("updateTime")
        private String updateTime;
        @JsonProperty("settleTime")
        private String settleTime;
        @JsonProperty("detail")
        private List<BetOrderDetail> betOrderDetailList;

    }

}