package com.wd.lottery.module.third.dock.zbridge.pg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.pg.component.PGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.pg.component.PGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.pg.component.PGResultRoot;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Description: bet order faced
 *
 * <p> Created on 2024/5/13.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.PG_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class PGGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {


    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        PGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), PGRequestConfig.class);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(flag.getPlatformCode());

        // 自动拉单
        if (!flag.isManual()) {
            return this.requestBetOrderAuto(flag, requestConfig);
        }

        // 手动拉单
        return this.requestBetOrderManual(flag, requestConfig, configJson);
    }

    private List<DockBetOrder> requestBetOrderAuto(BetOrderPullFlag flag, PGRequestConfig requestConfig) {
        PGHttpRequestTemplate requestTemplate = new PGHttpRequestTemplate(requestConfig);
        final int count = 5_000;
        BetOrderDataRoot dataRoot = requestTemplate
                .host("$dataGrabAPIDomain")
                .toPOST()
                .api("/Bet/v4/GetHistory")
                .addParameter("bet_type", "1")
                .addParameter("count", String.valueOf(count))
                .addParameter("row_version", getPullOrderFlag(flag))
                .toBeanAndCall(BetOrderDataRoot.class, false);
        if (!dataRoot.isSuccess()) {
            final Integer updateFlagCode = 1034;
            if (Objects.equals(updateFlagCode, dataRoot.getCode())) {
                flag.setLastFlag(parseLastFlagFromErrorMsg(dataRoot.getMsg()));
                flag.setFinished(true);
                return Collections.emptyList();
            }
            throw new ThirdPlatformException(dataRoot.getErrorMsg());
        }
        List<BetOrderData> data = dataRoot.getData();
        if (CollectionUtils.isEmpty(data)) {
            flag.setFinished(true);
            return Collections.emptyList();
        }
        String lastFlag = String.valueOf(data.get(data.size() - 1).getRowVersion());
        flag.setLastFlag(lastFlag);
        // data size less than 5000, finished
        flag.setFinished(data.size() < count);
        return data.stream().map(i -> this.toDockBetOrder(i, flag.getPlatformCode())).collect(Collectors.toList());
    }

    private List<DockBetOrder> requestBetOrderManual(BetOrderPullFlag flag, PGRequestConfig requestConfig, ThirdPlatformConfigDTO configJson) {
        JSONConfig jsonConfig = JSONConfig.create().setDateFormat(DatePattern.NORM_DATETIME_PATTERN);
        log.debug("PG:游戏注单[手动]:{}", JSONUtil.toJsonStr(flag, jsonConfig));

        // 三方时区
        ZoneId zoneId = ZoneId.of(configJson.getPlatformTimeZone());
        long startFlag = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId).toInstant().toEpochMilli();
        long endFlag = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId).toInstant().toEpochMilli();
        log.debug("PG:游戏注单[手动]:开始结束FLAG:{}-{}", startFlag, endFlag);

        final int count = 5_000;
        Integer pullFrequency = configJson.getPullFrequency();

        List<BetOrderData> data = Lists.newArrayList();
        PGHttpRequestTemplate requestTemplate = new PGHttpRequestTemplate(requestConfig);
        do {
            BetOrderDataRoot dataRoot = requestTemplate
                    .host("$dataGrabAPIDomain")
                    .toPOST()
                    .api("/Bet/v4/GetHistory")
                    .addParameter("bet_type", "1")
                    .addParameter("count", String.valueOf(count))
                    .addParameter("row_version", Convert.toStr(startFlag))
                    .toBeanAndCall(BetOrderDataRoot.class, false);
            log.debug("PG:游戏注单响应[手动]:{}-{}", startFlag, JSONUtil.toJsonStr(dataRoot));
            List<BetOrderData> betOrders = dataRoot.getData();
            if (CollUtil.isNotEmpty(betOrders)) {
                startFlag = betOrders.get(betOrders.size() - 1).getRowVersion();
                data.addAll(betOrders);
                if (CollUtil.size(betOrders) < count) {
                    break;
                }
                this.controlFrequency(pullFrequency);
            } else {
                break;
            }
        } while (startFlag < endFlag);
        flag.setFinished(true);
        return data.stream().map(i -> this.toDockBetOrder(i, flag.getPlatformCode())).collect(Collectors.toList());
    }

    private void controlFrequency(Integer pullFrequency) {
        if (Objects.isNull(pullFrequency) || pullFrequency <= 0) {
            return;
        }
        try {
            Thread.sleep(60000 / pullFrequency);
        } catch (InterruptedException ex) {
            log.warn("PG:游戏注单[手动]:Sleep InterruptedException", ex);
        }
    }

    private String parseLastFlagFromErrorMsg(String errorMsg) {
        final String splitChar = ":";
        try {
            String lastFlag = StringUtils.substringAfterLast(errorMsg, splitChar);
            return lastFlag.trim();
        } catch (Exception e) {
            throw new IllegalStateException("parse pull order flag failed, src: " + errorMsg);
        }
    }

    private String getPullOrderFlag(BetOrderPullFlag flag) {
        String lastFlag = flag.getLastFlag();
        // pull order auto
        if (StringUtils.isNotBlank(lastFlag)) {
            return lastFlag;
        }
        // pull order manual
        LocalDateTime begin = flag.getBegin();
        if (Objects.isNull(begin)) {
            throw new IllegalStateException("parse pull bet order flag failed");
        }
        return String.valueOf(begin.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }


    public DockBetOrder toDockBetOrder(BetOrderData betOrderData, String platformCode) {
        if (Objects.isNull(betOrderData)) {
            return null;
        }
        DockBetOrder betOrder = new DockBetOrder();
        betOrder.setPlatformCode(platformCode);
        betOrder.setThirdUserName(betOrderData.getPlayerName());
        betOrder.setOrderNum(betOrderData.getBetId() + "");
        betOrder.setOrderNumParent(betOrderData.getParentBetId() + "");
        betOrder.setGameId(betOrderData.getGameId() + "");


        betOrder.setBetMoney(toPlatformMoney(betOrderData.getBetAmount()));
        betOrder.setWinMoney(toPlatformMoney(betOrderData.getWinAmount()));
        betOrder.setValidBetMoney(toPlatformMoney(betOrderData.getBetAmount()));

        LocalDateTime dt = parseThirdOrderTime(betOrderData.getBetEndTime());

        betOrder.setOrderTime(dt);
        return betOrder;
    }

    private LocalDateTime parseThirdOrderTime(long epochMilliSeconds) {
        return Instant.ofEpochMilli(epochMilliSeconds)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

    }


    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        PGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), PGRequestConfig.class);
        PGHttpRequestTemplate requestTemplate = new PGHttpRequestTemplate(requestConfig);
        try {
            GetProxySessionResultRoot resultRoot = requestTemplate
                    .toPOST()
                    .api("/Login/v1/LoginProxy")
                    .toBeanAndCall(GetProxySessionResultRoot.class);

            String operatorSession = resultRoot.getData().getOperator_session();

            String pgSoftPublicDomain = requestConfig.getPgSoftPublicDomain();
            return pgSoftPublicDomain.replaceAll("//m.", "//public.") + "/history/redirect.html?" +
                    "trace_id=" + UUID.randomUUID() +
                    "&t=" + operatorSession +
                    "&psid=" + dto.getParentOrderNo() +
                    "&sid=" + dto.getOrderNo() +
                    "&gid=" + dto.getThirdGameId() +
                    "&type=" + "operator";
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    public static final class BetOrderDataRoot extends PGResultRoot<List<BetOrderData>> {
    }

    @Data
    @ToString
    public static final class BetOrderData {

        private long parentBetId;
        private long betId; //orderId
        private long total_agent_uid; //总代理id
        private String playerName; //玩家名称
        private int gameId;
        private int platform; //设备类型

        private long rowVersion; //Updated time of data
        private long betTime; //Unix time stamp in milliseconds
        private long betEndTime;

        private BigDecimal betAmount;
        private BigDecimal winAmount;

        private long jackpotContributionAmount;
        private long jackpotWinAmount;

        /**
         * Status of hand:
         * 1: Non-last hand
         * 2: Last hand
         * 3: Adjusted
         */
        private long handsStatus;

        private String currency;
    }

    private static class GetProxySessionResultRoot extends PGResultRoot<GetProxySessionResult> {
    }

    @Data
    @ToString
    static class GetProxySessionResult {
        private String operator_session;
    }
}
