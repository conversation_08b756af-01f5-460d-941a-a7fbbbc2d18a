package com.wd.lottery.module.third.dock.zbridge.dg;


import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.dg.common.DGApiEnum;
import com.wd.lottery.module.third.dock.zbridge.dg.common.DGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.dg.common.DGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.dg.res.DGExcelGameItem;
import com.wd.lottery.module.third.dock.zbridge.dg.res.DGResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: dg game strategy
 *
 * <p> Created on 2024/5/13.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.DG_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class DGGetGameServiceStrategy extends AbstractGetGameStrategy {


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        List<DockGame> gameList = new ArrayList<>(100);
        ClassPathResource resource = new ClassPathResource("third/dg_game.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            EasyExcel.read(inputStream, DGExcelGameItem.class, new AnalysisEventListener<DGExcelGameItem>() {
                @Override
                public void invoke(DGExcelGameItem gameItem, AnalysisContext analysisContext) {
                    DockGame dockGame = toDockGame(gameItem, platformCode);
                    gameList.add(dockGame);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet().doRead();

        } catch (Exception e) {
            log.error("getGameList error", e);
        }

        return gameList;
    }

    private DockGame toDockGame(DGExcelGameItem gameItem, String platformCode) {
        DockGame dockGame = new DockGame();

        dockGame.setPlatformCode(platformCode);
        dockGame.setThirdGameId(gameItem.getGameId());
        dockGame.setGameCode(gameItem.getGameId());
        dockGame.setGameName(gameItem.getGameName() + " " + gameItem.getTableName());
        dockGame.setGameCategoryEnum(GameCategoryEnum.CASINO);

        dockGame.setRemark(gameItem.getGameType());
        return dockGame;
    }


    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        CurrencyEnum currencyEnum = dto.getCurrencyEnum();
        DGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), currencyEnum, DGRequestConfig.class);
        DGHttpRequestTemplate requestTemplate = new DGHttpRequestTemplate(requestConfig);

        Map<String, String> thirdUserParam = new HashMap<>();
        thirdUserParam.put("username", dto.getThirdUserName());
        String passwd = DigestUtil.md5Hex(dto.getThirdUserName() + requestConfig.getAgentName());
        thirdUserParam.put("passwd", passwd);

        final String defaultLang = "en";

        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang);

        DGResponse<String> res = requestTemplate
                .api(DGApiEnum.USER_LOGIN.getPath())
                .addParameter("lang", thirdLang)
                .addParameter("domains", "1")
                .addParameter("member", JSONUtil.toJsonStr(thirdUserParam))
                .toBeanAndCall(new TypeReference<DGResponse<String>>() {
                });

        return res.getList().get(0) + res.getToken() + "&language=" + thirdLang + "&backUrl=" + dto.getLobbyUrl();
    }

}
