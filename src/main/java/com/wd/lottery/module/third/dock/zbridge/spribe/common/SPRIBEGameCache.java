package com.wd.lottery.module.third.dock.zbridge.spribe.common;

import com.wd.lottery.module.common.constants.GameCategoryEnum;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: spribe game cache
 *
 * <p> Created on 2024/7/3.
 *
 * <AUTHOR>
 * @version 0.1
 */
public class SPRIBEGameCache {

    private static final Map<String, String> miniGames = new HashMap<>();
    public static final Map<GameCategoryEnum, Map<String, String>> gameMap = Collections.singletonMap(GameCategoryEnum.MINI, miniGames);

    static{
        miniGames.put("aviator", "Aviator");
        miniGames.put("dice", "Dice");
        miniGames.put("goal", "Goal");
        miniGames.put("plinko", "Plinko");
        miniGames.put("mines", "Mines");
        miniGames.put("hi-lo", "Hi Lo");
        miniGames.put("keno", "Keno");
        miniGames.put("mini-roulette", "Mini Roulette");
        miniGames.put("hotline", "Hotline");
        miniGames.put("balloon", "Balloon");
        miniGames.put("multikeno", "Keno 80");
    }


    private SPRIBEGameCache() {
    }
}
