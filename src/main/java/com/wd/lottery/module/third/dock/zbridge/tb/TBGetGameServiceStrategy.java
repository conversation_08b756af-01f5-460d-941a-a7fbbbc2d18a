package com.wd.lottery.module.third.dock.zbridge.tb;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.tb.common.*;
import com.wd.lottery.module.third.dock.zbridge.tb.res.TBResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.TreeMap;


@Slf4j
@Component(BridgeConstant.TB_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class TBGetGameServiceStrategy extends AbstractGetGameStrategy {

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        List<DockGame> gameList = new ArrayList<>(100);
        ClassPathResource resource = new ClassPathResource("third/tb_list.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            EasyExcel.read(inputStream, TBExcelGameItem.class, new AnalysisEventListener<TBExcelGameItem>() {
                @Override
                public void invoke(TBExcelGameItem gameItem, AnalysisContext analysisContext) {
                    DockGame dockGame = toDockGame(gameItem, platformCode);
                    gameList.add(dockGame);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    //由于所有数据都是交于上层实现,故不需要实现
                }
            }).sheet().doRead();

        } catch (Exception e) {
            log.error("getGameList error", e);
        }
        return gameList;
    }

    private DockGame toDockGame(TBExcelGameItem gameItem, String platformCode) {
        DockGame dockGame = new DockGame();
        dockGame.setGameCategoryEnum(GameCategoryEnum.MINI);
        dockGame.setPlatformCode(platformCode);
        dockGame.setThirdGameId(gameItem.getGameId());
        dockGame.setGameCode(gameItem.getGameId());
        dockGame.setGameName(gameItem.getGameName());
        return dockGame;
    }


    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        TBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), TBRequestConfig.class);
        TBHttpRequestTemplate requestTemplate = new TBHttpRequestTemplate(requestConfig,dto.getCurrencyEnum());

        final String defaultLang = "en";
        // 请求参数
        TreeMap<String, Object> dataMap = new TreeMap<>();
        dataMap.put("username", dto.getThirdUserName());
        dataMap.put("app_id", Integer.parseInt(dto.getThirdGameId()));
        dataMap.put("ip", dto.getIp());
        dataMap.put("lang", ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang));
        TBAESUtil instance = new TBAESUtil(dataMap, TBApiEnum.LOGIN.getPath(), requestConfig.getPid(), requestConfig.getApiSecret());

        OpenGameRes openGameRes = requestTemplate
                .toPOST()
                .body(instance.getParamStr())
                .toBeanAndCall(OpenGameRes.class);
        log.debug("tb getOpenGameUrl:{}",openGameRes);
        return openGameRes.getUrl();
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    static class OpenGameRes extends TBResponse<Void> {

        private String username;

        private String url;

    }


}
