package com.wd.lottery.module.third.dock.zbridge.mgp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.mgp.common.MGPHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.mgp.common.MGPRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.mgp.res.MGPBetListResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component(BridgeConstant.MGP_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
@Slf4j
public class MGPGetBetOrderStrategy extends AbstractGetBetOrderStrategy {
    @Autowired
    protected StringRedisTemplate redisTemplate;

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        log.debug("MGP requestThirdOrder dto:{} ", flag);
        String platformCode = flag.getPlatformCode();
        CurrencyEnum currencyEnum = flag.getCurrencyEnum();
        MGPRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, MGPRequestConfig.class);
        MGPHttpRequestTemplate requestTemplate = new MGPHttpRequestTemplate(requestConfig, redisTemplate);
        final int pageSize = 20_000;
        String api = "/agents/{agentCode}/bets";
        requestTemplate
                .api(api)
                .toGET()
                .addParameter("limit", String.valueOf(pageSize));
        if (!StringUtils.equals("0", flag.getLastFlag())) {
            requestTemplate.addParameter("startingAfter", flag.getLastFlag());
        }
        String execute = requestTemplate.toCustomObject(String.class);
        // 拉取成功, 结果是以[开头, 所有以{开头的都是失败的
        if (StrUtil.startWith(execute, "{")) {
            throw new ThirdPlatformException("拉取注单失败");
        }
        List<MGPBetListResponse.BetOrder> betList = JSONUtil.toBean(execute, new TypeReference<List<MGPBetListResponse.BetOrder>>() {
        }, true);
        log.debug("MGP:游戏注单响应:{}", JSONUtil.toJsonStr(betList));
        if (CollUtil.isEmpty(betList)) {
            flag.setFinished(true);
            return Collections.emptyList();
        }
        String betUID = CollUtil.getLast(betList).getBetUID();
        log.debug("MGP:lastFlag:{}", betUID);
        flag.setLastFlag(betUID);

        List<DockBetOrder> list = betList.stream()
                .map(i -> this.toDockBetOrder(i, platformCode, currencyEnum, requestConfig))
                .collect(Collectors.toList());
        return filterHisBetOrders(list, requestConfig.getMaxHisDays());
    }

    private List<DockBetOrder> filterHisBetOrders(List<DockBetOrder> list, Integer maxHisDays) {
        log.debug("filter mgp bet orders, before size: {}", CollUtil.size(list));
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        LocalDateTime maxHis = LocalDateTime.now().minusDays(maxHisDays);
        List<DockBetOrder> validBetOrders = list.stream()
                .filter(i -> i.getOrderTime().isAfter(maxHis))
                .collect(Collectors.toList());
        log.debug("filter mgp bet orders, after size: {}", CollUtil.size(validBetOrders));
        return validBetOrders;
    }

    private DockBetOrder toDockBetOrder(MGPBetListResponse.BetOrder betOrder, String platformCode, CurrencyEnum currencyEnum, MGPRequestConfig requestConfig) {
        DockBetOrder dockOrder = new DockBetOrder();
        dockOrder.setThirdUserName(betOrder.getPlayerId());
        dockOrder.setPlatformCode(platformCode);
        dockOrder.setOrderNum(betOrder.getBetUID());
        dockOrder.setOrderNumParent(betOrder.getBetUID());
        dockOrder.setOrderTime(ThirdPlatformMappingConverter.parseThirdOrderTime(betOrder.getGameEndTimeUTC(), platformCode));

        // 获取币种兑换比例
        BigDecimal rate = ThirdPlatformMappingConverter.getCurrencyExchangeRate(requestConfig, currencyEnum);

        long betMoney = ThirdPlatformMappingConverter.toSysMoneyLong(betOrder.getBetAmount(), rate);
        // 有效投注
        dockOrder.setValidBetMoney(betMoney);
        dockOrder.setBetMoney(betMoney);
        long total = ThirdPlatformMappingConverter.toSysMoneyLong(betOrder.getPayoutAmount(), rate);
        dockOrder.setWinMoney(total);
        dockOrder.setGameId(betOrder.getGameCode());
        return dockOrder;
    }


    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        MGPRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), MGPRequestConfig.class);
        MGPHttpRequestTemplate requestTemplate = new MGPHttpRequestTemplate(requestConfig, redisTemplate);
        try {
            List<BetOrderDetailResponse> response = requestTemplate.toPOST()
                    .api(String.format("/agents/{agentCode}/players/%s/betVisualizers", dto.getThirdUserId()))
                    .addParameter("utcOffset", "0")
                    .addParameter("betUid", dto.getOrderNo())
                    .addParameter("langCode", "en")
                    .toCustomObject(new com.fasterxml.jackson.core.type.TypeReference<List<BetOrderDetailResponse>>() {
                    });
            log.debug("MGP注单详情信息:{}", JacksonUtil.toJSONString(response));
            return CollUtil.isEmpty(response) ? StrUtil.EMPTY : CollUtil.getFirst(response).getUrl();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    @Data
    public static class BetOrderDetailResponse {
        private String product;
        private String url;
    }
}
