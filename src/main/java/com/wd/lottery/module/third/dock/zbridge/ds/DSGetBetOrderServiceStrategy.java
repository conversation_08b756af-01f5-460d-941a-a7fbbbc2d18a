package com.wd.lottery.module.third.dock.zbridge.ds;


import cn.hutool.core.collection.CollUtil;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.ds.common.DSApiEnum;
import com.wd.lottery.module.third.dock.zbridge.ds.common.DSBetOrderStatusEnum;
import com.wd.lottery.module.third.dock.zbridge.ds.common.DSHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.ds.common.DSRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.ds.req.DSBetDetailReq;
import com.wd.lottery.module.third.dock.zbridge.ds.req.DSPullOrderReq;
import com.wd.lottery.module.third.dock.zbridge.ds.res.DSBetDetailRes;
import com.wd.lottery.module.third.dock.zbridge.ds.res.DSBetOrderRes;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import com.wd.lottery.module.third.entity.ThirdBetOrderPullConfEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: bet order faced
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.DS_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class DSGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {

    // 每次拉取2000条, 防止一次拉取数据过多超时
    private static final int limit = 2000;

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        log.debug("ds pull bet order, flag:{}", JacksonUtil.toJSONString(flag));
        String platformCode = flag.getPlatformCode();
        DSRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, flag.getCurrencyEnum(), DSRequestConfig.class);
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);

        String reqTimePattern = platformConfig.getRequestTimePattern();
        String orderTimePattern = platformConfig.getOrderTimePattern();

        DateTimeFormatter reqFormatter = DateTimeFormatter.ofPattern(reqTimePattern);
        String begin = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(platformConfig.getPlatformTimeZone())).format(reqFormatter);
        String end = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(platformConfig.getPlatformTimeZone())).format(reqFormatter);

        DSBetOrderRes res = doRequest(requestConfig, begin, end, flag.getIndex());

        List<DSBetOrderRes.BetOrderRecord> rows = res.getRows();
        if(CollUtil.isEmpty(rows)){
            flag.setFinished(true);
            return Collections.emptyList();
        }
        int currentSize = rows.size();
        if (currentSize >= limit) {
            flag.setIndex(flag.getIndex() + 1);
            flag.setFinished(false);
        }else{
            flag.setFinished(true);
        }

        return toDockBetOrderList(rows, platformCode, orderTimePattern);
    }

    @Override
    protected ThirdBetOrderPullConfEntity getFirstPullBetOrderFlag(String platformCode, CurrencyEnum currencyEnum) {
        ThirdBetOrderPullConfEntity betOrderFlag = super.getFirstPullBetOrderFlag(platformCode, currencyEnum);
        betOrderFlag.setStartTime(LocalDateTime.now().minusMinutes(30));
        betOrderFlag.setEndTime(LocalDateTime.now().minusMinutes(30));

        return betOrderFlag;
    }

    private DSBetOrderRes doRequest(DSRequestConfig requestConfig, String begin, String end, int page) {
        DSPullOrderReq param = new DSPullOrderReq();
        DSPullOrderReq.TimeRange timeRange = new DSPullOrderReq.TimeRange(begin, end);
        param.setFinishTime(timeRange);
        param.setIndex(page-1);

        param.setLimit(limit);

        return new DSHttpRequestTemplate(requestConfig)
                .api(DSApiEnum.BET_ORDER.getPath())
                .toPOST()
                .body(JacksonUtil.toJSONString(param))
                .toBeanAndCall(DSBetOrderRes.class);
    }

    private List<DockBetOrder> toDockBetOrderList(List<DSBetOrderRes.BetOrderRecord> rows, String platformCode, String orderTimePattern) {
        return rows.stream()
                .filter(this::isValidOrder)
                .map(data -> {
                    DockBetOrder dockBetOrder = new DockBetOrder();
                    dockBetOrder.setThirdUserName(data.getMember());
                    dockBetOrder.setPlatformCode(platformCode);
                    dockBetOrder.setOrderNum(data.getGameSerial());
                    dockBetOrder.setOrderNumParent(data.getId());
                    dockBetOrder.setGameId(data.getGameId());
                    long betMoney = toPlatformMoney(data.getBetAmount());
                    dockBetOrder.setBetMoney(betMoney);
                    long winMoney = toPlatformMoney(data.getPayoutAmount());
                    dockBetOrder.setWinMoney(winMoney);
                    long validAmount = toPlatformMoney(data.getValidAmount());
                    dockBetOrder.setValidBetMoney(validAmount);

                    String orderTimeStr = data.getFinishAt().substring(0, 19);
                    LocalDateTime orderTime = LocalDateTime.parse(orderTimeStr, DateTimeFormatter.ofPattern(orderTimePattern))
                            .atZone(ZoneId.of("UTC"))
                            .withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();

                    dockBetOrder.setOrderTime(orderTime);
                    return dockBetOrder;
                })
                .collect(Collectors.toList());
    }

    private boolean isValidOrder(DSBetOrderRes.BetOrderRecord row) {
        if (Objects.isNull(row) || Objects.isNull(row.getStatus())) {
            return false;
        }

        DSBetOrderStatusEnum status = DSBetOrderStatusEnum.getByCode(row.getStatus());
        if (Objects.isNull(status)) {
            return false;
        }

        return status == DSBetOrderStatusEnum.normal;
    }



    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        DSRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), DSRequestConfig.class);
        DSHttpRequestTemplate requestTemplate = new DSHttpRequestTemplate(requestConfig);
        DSBetDetailReq param = new DSBetDetailReq();
        param.setBetId(dto.getParentOrderNo());
        param.setGameId(dto.getThirdGameId());
        param.setSerial(dto.getOrderNo());
        param.setLang("en_us");
        try {
        DSBetDetailRes res = requestTemplate.api(DSApiEnum.BET_DETAIL.getPath())
                .toPOST()
                .body(JacksonUtil.toJSONString(param))
                .toBeanAndCall(DSBetDetailRes.class);

            return res.getUrl();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

}
