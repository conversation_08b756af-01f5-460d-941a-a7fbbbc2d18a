package com.wd.lottery.module.third.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.module.third.constants.GameSourceEnum;
import com.wd.lottery.module.third.dto.ThirdGameDTO;
import com.wd.lottery.module.third.dto.ThirdSiteGameDTO;
import com.wd.lottery.module.third.entity.ThirdGameEntity;
import com.wd.lottery.module.third.param.*;
import com.wd.lottery.module.third.param.*;
import com.wd.lottery.module.third.repo.ThirdGameRepo;
import com.wd.lottery.module.third.repo.ThirdGameStatusConfigRepo;
import com.wd.lottery.module.third.repo.ThirdSiteGameRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 三方游戏B端服务
 *
 * <p> Created on 2024/7/22.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class BThirdGameService {

    private final ThirdGameRepo thirdGameRepo;
    private final ThirdSiteGameRepo thirdSiteGameRepo;
    private final ThirdGameStatusConfigRepo thirdGameStatusConfigRepo;

    @Value("${third.rtp.platformCodes:OMGPG,OMGJILI}")
    private String rtpPlatformCodes;

    public BThirdGameService(ThirdGameRepo thirdGameRepo,
                             ThirdSiteGameRepo thirdSiteGameRepo,
                             ThirdGameStatusConfigRepo thirdGameStatusConfigRepo) {
        this.thirdGameRepo = thirdGameRepo;
        this.thirdSiteGameRepo = thirdSiteGameRepo;
        this.thirdGameStatusConfigRepo = thirdGameStatusConfigRepo;
    }

    /**
     * 分页查询游戏
     *
     * @param param 分页查询参数
     * @return 游戏分页
     */
    public Page<ThirdGameDTO> getPage(ThirdGamePageParam param) {
        Page<ThirdGameDTO> dtoPage = thirdGameRepo.getThirdGamePage(param);
        List<String> platformCodeList = Arrays.stream(rtpPlatformCodes.split(",")).map(String::trim).collect(Collectors.toList());
        // fill isRTP
        dtoPage.getRecords().forEach(i ->{
            if(CollectionUtils.isNotEmpty(platformCodeList)){
                i.setIsRTP(platformCodeList.contains(i.getPlatformCode())? BooleanEnum.TRUE:BooleanEnum.FALSE);
            }
        });
        return dtoPage;
    }

    /**
     * 更新游戏
     *
     * @param param 更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateGame(ThirdGameUpdateParam param) {
        // 查询游戏
        ThirdGameEntity gameEntity = thirdGameRepo.lambdaQuery()
                .eq(ThirdGameEntity::getThirdGameId, param.getThirdGameId())
                .eq(ThirdGameEntity::getPlatformCode, param.getPlatformCode())
                .one();
        if (Objects.isNull(gameEntity)) {
            log.warn("third game not found, platform: {}, thirdGameId: {}", param.getPlatformCode(), param.getThirdGameId());
            throw new ApiException(CommonCode.PARAM_INVALID);
        }
        // gameName
        if (Objects.nonNull(param.getGameName())) {
            gameEntity.setGameName(param.getGameName());
        }
        // gameImg
        if (Objects.nonNull(param.getGameImg())) {
            gameEntity.setGameImg(param.getGameImg());
        }
        // gameCategory
        if (Objects.nonNull(param.getGameCategoryEnum())) {
            gameEntity.setGameCategoryEnum(param.getGameCategoryEnum());
        }
        // enableEnum
        if (Objects.nonNull(param.getEnableEnum())) {
            gameEntity.setEnableEnum(param.getEnableEnum());
        }
        // isMaintain
        if (Objects.nonNull(param.getIsMaintain())) {
            gameEntity.setIsMaintain(param.getIsMaintain());
        }
        // supportedCurrency
        if (Objects.nonNull(param.getSupportedCurrency())) {
            gameEntity.setSupportedCurrency(param.getSupportedCurrency());
        }
        gameEntity.setUpdateTime(LocalDateTime.now());
        thirdGameRepo.updateById(gameEntity);

        // 更新游戏状态（排序、热门）
        ThirdGameUpdateStatusParam statusParam = new ThirdGameUpdateStatusParam(param.getPlatformCode(), gameEntity.getId(), param.getCurrencyEnum());
        statusParam.setSort(param.getSort());
        statusParam.setEnableHot(param.getEnableHot());

        thirdGameStatusConfigRepo.saveOrUpdateGameStatusConfig(statusParam);
    }

    /**
     * 更新游戏图片
     *
     * @param param 更新图片参数
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGameImg(ThirdGameImgUpdateParam param) {
        return thirdGameRepo.lambdaUpdate()
                .eq(ThirdGameEntity::getThirdGameId, param.getThirdGameId())
                .eq(ThirdGameEntity::getPlatformCode, param.getPlatformCode())
                .set(ThirdGameEntity::getGameImg, param.getGameImg())
                .update();
    }

    /**
     * 更新游戏维护状态
     *
     * @param param 更新维护参数
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGameMaintainStatus(ThirdGameMaintainUpdateParam param) {
        return thirdGameRepo.lambdaUpdate()
                .eq(ThirdGameEntity::getThirdGameId, param.getThirdGameId())
                .eq(ThirdGameEntity::getPlatformCode, param.getPlatformCode())
                .set(ThirdGameEntity::getIsMaintain, param.getIsMaintain())
                .update();
    }

    /**
     * 更新游戏启用状态
     *
     * @param param 更新启用参数
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGameEnableStatus(ThirdGameEnableUpdateParam param) {
        return thirdGameRepo.lambdaUpdate()
                .eq(ThirdGameEntity::getThirdGameId, param.getThirdGameId())
                .eq(ThirdGameEntity::getPlatformCode, param.getPlatformCode())
                .set(ThirdGameEntity::getEnableEnum, param.getEnableEnum())
                .update();
    }

    /**
     * 更新游戏维护状态
     *
     * @param param 添加游戏参数
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addGame(ThirdGameAddParam param) {
        ThirdGameEntity entity = new ThirdGameEntity();
        entity.setThirdGameId(param.getThirdGameId());
        entity.setPlatformCode(param.getPlatformCode());
        entity.setGameCode(param.getGameCode());
        entity.setGameName(param.getGameName());
        entity.setGameCategoryEnum(param.getGameCategoryEnum());
        entity.setGameSourceEnum(GameSourceEnum.MANUAL);
        entity.setEnableEnum(EnableEnum.TRUE);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        return thirdGameRepo.save(entity);
    }

    public List<ThirdSiteGameDTO> queryAssignedGame(QueryAssignedGameParam param) {
        CurrencyEnum currencyEnum = AdminTokenInfoUtil.getRequestCurrencyEnumNotNull();
        return thirdSiteGameRepo.listGameByPlatform(param.getPlatformCode(), currencyEnum,null, param.getMerchantId());
    }

    /**
     * 批量上传游戏图片
     * @param param 批量上传游戏图片参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpload(ThirdGameImgBatchUploadParam param) {
        String [] pathArray = param.getPath().split("\\|");
        Map<String, String> map = new HashMap<>();
        for (String path : pathArray) {
            String[] split = path.split(":");
            if (split.length != 2) {
                continue;
            }
            map.put(split[0], split[1]);
        }
        List<ThirdGameEntity> list = thirdGameRepo.lambdaQuery()
                .eq(ThirdGameEntity::getPlatformCode, param.getPlatformCode())
                .eq(ThirdGameEntity::getIsDel, 0)
                .list();
        if(CollUtil.isEmpty(list)) {
            throw new ThirdPlatformException("该平台下没有游戏");
        }
        Map<String, List<ThirdGameEntity>> gameMap = list.stream().collect(Collectors.groupingBy(ThirdGameEntity::getGameName));
        List<ThirdGameEntity> updateList = new ArrayList<>();
        StringBuilder logSb = new StringBuilder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            List<ThirdGameEntity> thirdGameEntities = gameMap.get(entry.getKey());
            if(CollUtil.isEmpty(thirdGameEntities)) {
                logSb.append(entry.getKey()).append(",");
                continue;
            }
            for (ThirdGameEntity thirdGame : thirdGameEntities) {
                thirdGame.setGameImg(entry.getValue());
                updateList.add(thirdGame);
            }
        }
        if(CollUtil.isNotEmpty(updateList)) {
            thirdGameRepo.saveOrUpdateBatch(updateList);
        }
        if(logSb.length() > 0) {
            throw new ThirdPlatformException("失败的图片："+logSb.substring(0, logSb.length()-1));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateGameCurrency(ThirdGameUpdateCurrencyParam param) {

        String supportedCurrencies = parseAndValidateSupportedCurrency(param.getCurrency());
        thirdGameRepo.lambdaUpdate().eq(ThirdGameEntity::getId, param.getGameId())
                .set(ThirdGameEntity::getSupportedCurrency, supportedCurrencies)
                .update();
    }


    private String parseAndValidateSupportedCurrency(String currencyStr){
        Assert.notBlank(currencyStr);
        List<CurrencyEnum> currencyList;
        String splitChar = ",";
        try {
            currencyList = Arrays.stream(currencyStr.split(splitChar)).map(s -> CurrencyEnum.valueOf(s.trim())).collect(Collectors.toList());
        } catch (Exception e) {
            throw new ApiException(CommonCode.PARAM_INVALID);
        }
        return currencyList.stream().map(Enum::name).collect(Collectors.joining(splitChar));
    }
}
