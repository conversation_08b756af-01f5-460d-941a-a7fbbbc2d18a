package com.wd.lottery.module.third.dock.zbridge.tada;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.tada.common.TADAHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.tada.common.TADARequestConfig;
import com.wd.lottery.module.third.dock.zbridge.tada.res.TADAOpenGameResponse;
import com.wd.lottery.module.third.dock.zbridge.tada.res.TADAResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.TADA_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class TADAGetBetOrderStrategy extends AbstractGetBetOrderStrategy {

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        TADARequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), TADARequestConfig.class);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(flag.getPlatformCode());
        TADAHttpRequestTemplate requestTemplate = new TADAHttpRequestTemplate(requestConfig);
        List<DockBetOrder> dockBetOrders = this.requestOrder(flag, requestTemplate, configJson);
        flag.setFinished(true);
        return dockBetOrders;
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        TADARequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), TADARequestConfig.class);
        TADAHttpRequestTemplate requestTemplate = new TADAHttpRequestTemplate(requestConfig);
        try {
            TADAResponse<TADAOpenGameResponse> response = requestTemplate.toPOST()
                    .api("/GetGameDetailUrl")
                    .addParameter("WagersId", dto.getOrderNo())
                    .toBeanAndCall(new TypeReference<TADAResponse<TADAOpenGameResponse>>() {
                    });
            log.debug("TADA游戏详情响应: {}", JSONUtil.toJsonStr(response));
            return response.getData().getUrl();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    private List<DockBetOrder> requestOrder(BetOrderPullFlag flag, TADAHttpRequestTemplate requestTemplate, ThirdPlatformConfigDTO configJson) {
        int index = 1;
        int totalIndex;
        List<DockBetOrder> dockBetOrders = Lists.newArrayList();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(configJson.getRequestTimePattern());
        LocalDateTime start = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(configJson.getPlatformTimeZone())).toLocalDateTime();
        LocalDateTime end = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(configJson.getPlatformTimeZone())).toLocalDateTime();
        do {
            TADAResponse<ResultData> response = requestTemplate.toGET()
                    .api("/GetBetRecordByTime")
                    .addParameter("StartTime", formatter.format(start))
                    .addParameter("EndTime", formatter.format(end))
                    .addParameter("FilterAgent", "1")
                    .addParameter("Page", String.valueOf(index))
                    .addParameter("PageLimit", "20000")
                    .toBeanAndCall(new TypeReference<TADAResponse<ResultData>>() {
                    }, false);
            log.debug("TADA拉取游戏注单: {}-{}-{}", start, end, JSONUtil.toJsonStr(response));
            // 拉单为空
            if (response.getCode() == 996) {
                break;
            }
            ResultData data = response.getData();
            if (CollUtil.isNotEmpty(data.getResult())) {
                dockBetOrders.addAll(convert(data.getResult(), flag.getPlatformCode(), configJson.getOrderTimePattern(), configJson.getPlatformTimeZone()));
            }
            index++;
            totalIndex = data.getPagination().getTotalPages();
            this.controlFrequency(index, totalIndex);
        } while (index <= totalIndex);
        return dockBetOrders;
    }

    private List<DockBetOrder> convert(List<BetOrderData> result, String platformCode, String orderTimePattern, String platformTimeZone) {
        return result.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BetOrderData::getWagersId))), ArrayList::new))
                .stream()
                .map(betOrder -> toDockBetOrder(betOrder, platformCode, orderTimePattern, platformTimeZone))
                .collect(Collectors.toList());
    }

    private DockBetOrder toDockBetOrder(BetOrderData betOrderData, String platformCode, String orderTimePattern, String platformTimeZone) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(orderTimePattern);
        DockBetOrder dockOrder = new DockBetOrder();
        dockOrder.setThirdUserName(betOrderData.getAccount());
        dockOrder.setPlatformCode(platformCode);
        dockOrder.setOrderNum(betOrderData.getWagersId());
        dockOrder.setGameId(betOrderData.getGameId());

        dockOrder.setBetMoney(toPlatformMoney(betOrderData.getBetAmount().abs()));
        dockOrder.setWinMoney(toPlatformMoney(betOrderData.getPayoffAmount().abs()));
        dockOrder.setValidBetMoney(toPlatformMoney(betOrderData.getTurnover().abs()));

        LocalDateTime thirdBet = LocalDateTime.parse(betOrderData.getWagersTime(), formatter);
        dockOrder.setOrderTime(thirdBet.atZone(ZoneId.of(platformTimeZone)).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime());

        return dockOrder;
    }

    private void controlFrequency(int index, int totalIndex) {
        try {
            if (index <= totalIndex) {
                // call frequency 20/m
                TimeUnit.SECONDS.sleep(3);
            }
        } catch (InterruptedException e) {
            log.warn("requestOrder sleep InterruptedException", e);
        }
    }

    @Data
    public static class ResultData {
        @JsonProperty("Pagination")
        private Pagination pagination;

        @JsonProperty("Result")
        private List<BetOrderData> result;
    }

    @Data
    public static class Pagination {

        // 当前页数
        @JsonProperty("CurrentPage")
        private Integer currentPage;

        // 总页数
        @JsonProperty("TotalPages")
        private Integer totalPages;

        // 每页笔数
        @JsonProperty("PageLimit")
        private Integer pageLimit;

        // 总笔数
        @JsonProperty("TotalNumber")
        private Integer totalNumber;
    }

    @Data
    public static class BetOrderData {
        // 会员唯一识别值
        @JsonProperty("Account")
        private String account;

        // 在游戏内注单唯一值 注单ID
        @JsonProperty("WagersId")
        private String wagersId;

        // 游戏的唯一识别值
        @JsonProperty("GameId")
        private String gameId;

        // 投注时间
        @JsonProperty("WagersTime")
        private String wagersTime;

        // 投注金额
        @JsonProperty("BetAmount")
        private BigDecimal betAmount;

        /**
         * 有效投注金额
         */
        @JsonProperty("Turnover")
        private BigDecimal turnover;

        // PayoffTime
        @JsonProperty("PayoffTime")
        private String payoffTime;

        // 派彩金额
        @JsonProperty("PayoffAmount")
        private BigDecimal payoffAmount;

        // 注单状态：1: 赢 2: 输
        @JsonProperty("Status")
        private Integer status;

        // 对帐时间
        @JsonProperty("SettlementTime")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
        private Date settlementTime;

        // 游戏类型：
        @JsonProperty("GameCategoryId")
        private String gameCategoryId;

        // 注单类型：1: main game 9: free game 11: 道具卡 12: 游戏内购
        @JsonProperty("Type")
        private Integer type;

        // 会员所属站长唯一识别值
        @JsonProperty("AgentId")
        private String agentId;
    }
}