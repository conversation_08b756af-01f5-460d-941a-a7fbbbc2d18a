package com.wd.lottery.module.third.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.constants.ThirdLoginTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description: 三方平台分类配置表
 *
 * <p> Created on 2024/5/22.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
@TableName("third_platform_category")
public class ThirdPlatformCategoryEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 平台code
     */
    private String platformCode;

    /**
     * 平台游戏分类
     */
    private GameCategoryEnum categoryEnum;

    /**
     * 分类图片配置
     */
    private String categoryImg;

    /**
     * 游戏登入方式
     */
    private ThirdLoginTypeEnum thirdLoginTypeEnum;

    /**
     * 是否支持试玩
     */
    private BooleanEnum isSupportDemo;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
