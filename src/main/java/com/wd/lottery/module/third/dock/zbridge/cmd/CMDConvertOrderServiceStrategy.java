package com.wd.lottery.module.third.dock.zbridge.cmd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.module.third.constants.ConvertOrderStatusEnum;
import com.wd.lottery.module.third.constants.ConvertOrderTransferEnum;
import com.wd.lottery.module.third.dock.convertorder.ConvertOrderServiceStrategy;
import com.wd.lottery.module.third.dock.convertorder.DockConvertOrder;
import com.wd.lottery.module.third.dock.convertorder.DockGetConvertOrderStatus;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.cmd.common.CMDAPIEnum;
import com.wd.lottery.module.third.dock.zbridge.cmd.common.CMDHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.cmd.common.CMDRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.cmd.res.CMDResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 钱包转账
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.CMD_PLATFORM_CODE + ConvertOrderServiceStrategy.BEAN_NAME_SUFFIX)
public class CMDConvertOrderServiceStrategy implements ConvertOrderServiceStrategy {

    @Override
    public void addOrder(DockConvertOrder dockConvertOrder) throws Exception {
        log.debug("CMD:钱包转账:{}", JSONUtil.toJsonStr(dockConvertOrder));
        CMDRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dockConvertOrder.getPlatformCode(), dockConvertOrder.getCurrencyEnum(), CMDRequestConfig.class);
        CMDHttpRequestTemplate requestTemplate = new CMDHttpRequestTemplate(requestConfig);
        // 转出OR转入
        Integer paymentType = dockConvertOrder.getTransferEnum() == ConvertOrderTransferEnum.IN ? 1 : 0;
        // 币种比例
        BigDecimal rate = ThirdPlatformMappingConverter.getCurrencyExchangeRate(requestConfig, dockConvertOrder.getCurrencyEnum());
        BigDecimal convertMoney = dockConvertOrder.balance2BigDecimal().multiply(rate);
        CMDResponse<ConvertOrder> response = requestTemplate.toGET()
                .addParameter("method", CMDAPIEnum.CONVERT_TO_THIRD.getPath())
                .addParameter("paymentType", Convert.toStr(paymentType))
                .addParameter("userName", dockConvertOrder.getThirdUserName())
                .addParameter("money", convertMoney.toPlainString())
                .addParameter("ticketNo", dockConvertOrder.getOrderNo())
                .toBeanAndCall(new TypeReference<CMDResponse<ConvertOrder>>() {
                });
        log.debug("CMD:钱包转账响应:{}", JSONUtil.toJsonStr(response));
        dockConvertOrder.setThirdOrderNo(dockConvertOrder.getOrderNo());
    }

    @Override
    public ConvertOrderStatusEnum getOrderStatus(DockGetConvertOrderStatus dockGetConvertOrderStatus) {
        log.debug("CMD:钱包转账状态:{}", JSONUtil.toJsonStr(dockGetConvertOrderStatus));
        CMDRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dockGetConvertOrderStatus.getPlatformCode(), dockGetConvertOrderStatus.getCurrencyEnum(), CMDRequestConfig.class);
        CMDHttpRequestTemplate requestTemplate = new CMDHttpRequestTemplate(requestConfig);
        try {
            CMDResponse<List<OrderStatus>> response = requestTemplate.toGET()
                    .addParameter("method", CMDAPIEnum.CONVERT_ORDER_STATUS.getPath())
                    .addParameter("userName", dockGetConvertOrderStatus.getThirdUserName())
                    .addParameter("ticketNo", dockGetConvertOrderStatus.getOrderNo())
                    .toBeanAndCall(new TypeReference<CMDResponse<List<OrderStatus>>>() {
                    }, false);
            log.debug("CMD:钱包转账状态响应:{}", JSONUtil.toJsonStr(response));
            if (ObjectUtil.isNull(response)) {
                return ConvertOrderStatusEnum.VERIFY;
            }
            if (response.isSuccess()) {
                if (CollUtil.isEmpty(response.getData())) {
                    return ConvertOrderStatusEnum.FAIL;
                }
                OrderStatus orderStatus = CollUtil.getFirst(response.getData());
                return orderStatus.getStatus() == 1 ? ConvertOrderStatusEnum.SUCCESS : ConvertOrderStatusEnum.FAIL;
            }
        } catch (Exception ex) {
            log.warn(StrUtil.format("CQ9:钱包转账状态失败:{}", JSONUtil.toJsonStr(dockGetConvertOrderStatus)), ex);
            return ConvertOrderStatusEnum.VERIFY;
        }
        return ConvertOrderStatusEnum.VERIFY;
    }

    /**
     * 钱包转账
     */
    @Data
    public static class ConvertOrder {

        //会员交易的唯一单号，由合作商传入
        @JsonProperty("BetAmount")
        private String betAmount;

        //用户未结算余额
        @JsonProperty("Outstanding")
        private String outstanding;

        //球网系统转账流水号
        @JsonProperty("PaymentId")
        private String paymentId;
    }

    /**
     * 订单状态
     */
    @Data
    public static class OrderStatus {

        /**
         * 会员交易的唯一单号，由合作商传入
         */
        @JsonProperty(value = "TicketNo")
        private String ticketNo;

        /**
         * CMD 交易流水号
         */
        @JsonProperty(value = "PaymentId")
        private Integer paymentId;

        /**
         * 合作商标识
         */
        @JsonProperty(value = "PartnerName")
        private String partnerName;

        /**
         * 会员在合作商平台的标识
         */
        @JsonProperty(value = "SourceName")
        private String sourceName;

        /**
         * 交易状态标识，0 申请，1 成功，2 失败
         */
        @JsonProperty("Status")
        private Integer status;

        /**
         * 操作金额
         */
        @JsonProperty(value = "Amount")
        private BigDecimal amount;

        /**
         * 操作后金额
         */
        @JsonProperty(value = "Balance")
        private BigDecimal balance;

        /**
         * 订单创建时间
         */
        @JsonProperty(value = "CreateTs")
        private Long createTs;

        /**
         * 更新日期
         */
        @JsonProperty(value = "UpdateTs")
        private Long updateTs;
    }
}