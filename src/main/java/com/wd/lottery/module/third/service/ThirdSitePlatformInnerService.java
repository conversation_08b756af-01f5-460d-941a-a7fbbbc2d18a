package com.wd.lottery.module.third.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.merchant.entity.Merchant;
import com.wd.lottery.module.merchant.service.MerchantService;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dto.*;
import com.wd.lottery.module.third.entity.ThirdPlatformCategoryEntity;
import com.wd.lottery.module.third.entity.ThirdPlatformCategoryStatusConfigEntity;
import com.wd.lottery.module.third.entity.ThirdSitePlatformCategoryEntity;
import com.wd.lottery.module.third.entity.ThirdSitePlatformEntity;
import com.wd.lottery.module.third.param.ThirdSitePlatformQueryParam;
import com.wd.lottery.module.third.repo.ThirdPlatformCategoryRepo;
import com.wd.lottery.module.third.repo.ThirdPlatformCategoryStatusConfigRepo;
import com.wd.lottery.module.third.repo.ThirdSitePlatformCategoryRepo;
import com.wd.lottery.module.third.repo.ThirdSitePlatformRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 三方平台内部服务
 *
 * <p> Created on 2024/5/23.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class ThirdSitePlatformInnerService {

    private final ThirdSitePlatformRepo thirdSitePlatformRepo;
    private final ThirdSitePlatformCategoryRepo thirdSitePlatformCategoryRepo;
    private final ThirdPlatformCategoryRepo thirdPlatformCategoryRepo;
    private final MerchantService merchantService;
    private final ThirdPlatformCategoryStatusConfigRepo platformCategoryStatusConfigRepo;

    public ThirdSitePlatformInnerService(ThirdSitePlatformRepo thirdSitePlatformRepo,
                                         ThirdSitePlatformCategoryRepo thirdSitePlatformCategoryRepo,
                                         ThirdPlatformCategoryRepo thirdPlatformCategoryRepo,
                                         MerchantService merchantService,
                                         ThirdPlatformCategoryStatusConfigRepo platformCategoryStatusConfigRepo) {
        this.thirdSitePlatformRepo = thirdSitePlatformRepo;
        this.thirdSitePlatformCategoryRepo = thirdSitePlatformCategoryRepo;
        this.thirdPlatformCategoryRepo = thirdPlatformCategoryRepo;
        this.merchantService = merchantService;
        this.platformCategoryStatusConfigRepo = platformCategoryStatusConfigRepo;
    }


    /**
     * 获取商户三方平台
     *
     * @param merchantId 商户ID
     * @return 三方平台列表
     */
    @Cacheable(cacheNames = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<ThirdSitePlatformDTO> listMerchantPlatform(Long merchantId, CurrencyEnum currencyEnum) {
        ThirdSitePlatformQueryParam param = new ThirdSitePlatformQueryParam();
        param.setMerchantId(merchantId);
        param.setCurrencyEnum(currencyEnum);
        param.setEnableEnum(EnableEnum.TRUE);

        return this.thirdSitePlatformRepo.getMerchantPlatformList(param);
    }

    @Cacheable(cacheNames = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<ThirdSitePlatformItemDTO> listMerchantCategoryPlatform(Long merchantId, CurrencyEnum currencyEnum) {
        // 查询商户所有平台
        List<ThirdSitePlatformDTO> thirdSitePlatformList = this.listMerchantPlatform(merchantId, currencyEnum);
        final String currency = currencyEnum.name();

        // 过滤币种不支持
        thirdSitePlatformList.removeIf(i -> !i.getCurrencies().contains(currency));

        if (CollectionUtils.isEmpty(thirdSitePlatformList)) {
            return new ArrayList<>();
        }
        // 拆分平台分类
        List<ThirdSitePlatformItemDTO> collect = thirdSitePlatformList.stream()
                .map(i -> {
                    String categories = i.getCategories();
                    return Arrays.stream(categories.split(","))
                            .map(c -> {
                                ThirdSitePlatformItemDTO item = new ThirdSitePlatformItemDTO();
                                BeanUtils.copyProperties(i, item);
                                GameCategoryEnum categoryEnum = GameCategoryEnum.valueOf(c.trim());
                                item.setCategoryEnum(categoryEnum);
                                return item;
                            })
                            .collect(Collectors.toSet());
                }).flatMap(Collection::stream)
                .collect(Collectors.toList());

        fillCategoryConfig(collect, merchantId);
        fillPlatformImg(collect, merchantId);

        // 填充维护状态
        fillPlatformMaintainStatusBridgeCode(collect, currencyEnum);
        // 填充分类排序热门配置
        fillCategoryStatus(collect, currencyEnum);

        return collect;
    }

    private void fillCategoryStatus(List<ThirdSitePlatformItemDTO> platformItems, CurrencyEnum currencyEnum) {
        if (CollectionUtils.isEmpty(platformItems)) {
            return;
        }
        Set<String> platformCodes = platformItems.stream().map(ThirdSitePlatformItemDTO::getPlatformCode).collect(Collectors.toSet());
        Map<String, ThirdPlatformCategoryStatusConfigEntity> collect = platformCategoryStatusConfigRepo.lambdaQuery().in(ThirdPlatformCategoryStatusConfigEntity::getPlatformCode, platformCodes)
                .eq(ThirdPlatformCategoryStatusConfigEntity::getCurrencyEnum, currencyEnum)
                .list()
                .stream()
                .collect(Collectors.toMap(k -> k.getPlatformCode() + "_" + k.getCategoryEnum().name(), v -> v));
        platformItems.forEach(i -> {
            String key = i.getPlatformCode() + "_" + i.getCategoryEnum().name();
            ThirdPlatformCategoryStatusConfigEntity entity = collect.get(key);
            if (Objects.nonNull(entity)) {
                i.setEnableHot(entity.getEnableHot());
                i.setSort(entity.getSort());
            } else {
                i.setEnableHot(BooleanEnum.FALSE);
                i.setSort(0);
            }
        });
    }

    private void fillPlatformMaintainStatusBridgeCode(List<ThirdSitePlatformItemDTO> dtoList, CurrencyEnum currencyEnum) {
        for (ThirdSitePlatformItemDTO sitePlatform : dtoList) {
            ThirdPlatformBasicInfoDTO thirdPlatform = ThirdPlatformLocalCacheUtil.getThirdPlatformBasicInfo(sitePlatform.getPlatformCode());
            ThirdPlatformStatusDTO status = ThirdPlatformLocalCacheUtil.getThirdPlatformStatusByCurrency(sitePlatform.getPlatformCode(), currencyEnum);
            sitePlatform.setIsMaintain(status.getIsMaintain());
            String bridgeCode = thirdPlatform.getBridgePlatformCode();
            if (StringUtils.isBlank(bridgeCode)) {
                bridgeCode = sitePlatform.getPlatformCode();
            }
            sitePlatform.setBridgePlatformCode(bridgeCode);
        }
    }

    private void fillPlatformImg(List<ThirdSitePlatformItemDTO> collect, Long merchantId) {
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }

        // 查询商户信息
        Merchant merchant = this.merchantService.getByIdCache(merchantId);
        String layout = merchant.getLayout();

        for (ThirdSitePlatformItemDTO itemDTO : collect) {
            String platformImg = this.getPlatformImgByLayout(layout, itemDTO.getPlatformImg());
            if (StringUtils.isNotBlank(platformImg)) {
                continue;
            }
            // fill default platform img
            ThirdPlatformBasicInfoDTO thirdPlatform = ThirdPlatformLocalCacheUtil.getThirdPlatformBasicInfo(itemDTO.getPlatformCode());
            platformImg = this.getPlatformImgByLayout(layout, thirdPlatform.getPlatformImg());
            itemDTO.setPlatformImg(platformImg);
        }
    }

    /**
     * 根据版面获取平台logo
     * @param layout 商户平台版面
     * @param platformImg 平台logo，来源：平台配置/商户平台配置
     * @return {@link String} 版面logo
     */
    private String getPlatformImgByLayout(String layout, String platformImg) {
        if (StrUtil.isEmpty(platformImg)) {
            return StrUtil.EMPTY;
        }
        boolean isJson = JSONUtil.isTypeJSON(platformImg);
        if (!isJson) {
            return platformImg;
        }
        if (StrUtil.isEmpty(layout)) {
            return StrUtil.EMPTY;
        }
        JSONObject jsonObject = JSONUtil.parseObj(platformImg);
        JSONObject result = new JSONObject();
        CollUtil.forEach(jsonObject, (key, value, index) -> {
            if (StrUtil.startWith(key, layout) && ObjectUtil.isNotEmpty(value)) {
                result.set(key, value);
            }
        });
        return result.isEmpty() ? StrUtil.EMPTY : JSONUtil.toJsonStr(result);
    }

    private void fillCategoryConfig(List<ThirdSitePlatformItemDTO> collect, Long merchantId) {
        // 查询商户信息
        Merchant merchant = merchantService.getByIdCache(merchantId);
        // 查询商户分类配置
        Map<String, ThirdSitePlatformCategoryEntity> siteCategoryMap = thirdSitePlatformCategoryRepo.getPlatformCategoryMapByMerchantId(merchantId);
        // 查询三方分类配置
        Map<String, ThirdPlatformCategoryEntity> platformCategoryMap = thirdPlatformCategoryRepo.getPlatformCategoryMap();

        for (ThirdSitePlatformItemDTO dto : collect) {
            String key = dto.getPlatformCode() + "_" + dto.getCategoryEnum().name();

            ThirdPlatformCategoryEntity platformCategoryConfig = platformCategoryMap.get(key);
            if (Objects.isNull(platformCategoryConfig)) {
                log.warn("no platform category config found, platformCode: {}", key);
                continue;
            }
            // fill login type
            dto.setLoginTypeEnum(platformCategoryConfig.getThirdLoginTypeEnum());
            // fill category img
            String categoryImg = parsePlatformCategoryImgByMerchantLayout(platformCategoryConfig.getCategoryImg(), merchant.getLayout());
            dto.setCategoryImg(categoryImg);
            // fill is support demo
            dto.setIsSupportDemo(platformCategoryConfig.getIsSupportDemo());

            ThirdSitePlatformCategoryEntity siteCategoryConfig = siteCategoryMap.get(key);
            if (Objects.nonNull(siteCategoryConfig) && StringUtils.isNotBlank(siteCategoryConfig.getCategoryImg())) {
                // override category img
                dto.setCategoryImg(siteCategoryConfig.getCategoryImg());
            }
        }
    }

    private String parsePlatformCategoryImgByMerchantLayout(String categoryImgJson, String layout) {
        if (StringUtils.isBlank(categoryImgJson)) {
            return "";
        }
        final String defaultKey = "default";
        Map<String, String> map;
        if (JacksonUtil.isJsonObjectString(categoryImgJson)) {
            map = JacksonUtil.toJavaObject(categoryImgJson, new TypeReference<Map<String, String>>() {
            });
        } else {
            map = new HashMap<>();
            map.put(defaultKey, categoryImgJson);
        }
        String layoutCategoryImg = map.get(layout);
        if (StringUtils.isNotBlank(layoutCategoryImg)) {
            return layoutCategoryImg;
        }
        return map.get(defaultKey);
    }

    /**
     * 三方平台更新，同步更新已分配到商户的三方游戏
     *
     * @param param 平台更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateThirdSitePlatform(ThirdPlatformDTO param) {
        LambdaUpdateWrapper<ThirdSitePlatformEntity> updateWrapper = new LambdaUpdateWrapper<>();
        //更新币种及分类
        updateWrapper.set(ThirdSitePlatformEntity::getCategories, param.getCategories())
                .set(ThirdSitePlatformEntity::getCurrencies, param.getCurrencies())
                .eq(ThirdSitePlatformEntity::getPlatformCode, param.getCode())
                .eq(ThirdSitePlatformEntity::getIsDel, BooleanEnum.FALSE);

        EnableEnum enableEnum = param.getEnableEnum();
        if (enableEnum == EnableEnum.FALSE) {
            updateWrapper.set(ThirdSitePlatformEntity::getEnableEnum, enableEnum);
        }
        this.thirdSitePlatformRepo.update(updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void disablePlatformByCode(String platformCode) {
        this.thirdSitePlatformRepo.lambdaUpdate()
                .eq(ThirdSitePlatformEntity::getPlatformCode, platformCode)
                .eq(ThirdSitePlatformEntity::getIsDel, BooleanEnum.FALSE)
                .set(ThirdSitePlatformEntity::getEnableEnum, EnableEnum.FALSE)
                .update();
    }

    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public ThirdSitePlatformEntity getByPlatformCode(String platformCode, Long merchantId) {
        return this.thirdSitePlatformRepo.lambdaQuery()
                .eq(ThirdSitePlatformEntity::getPlatformCode, platformCode)
                .eq(ThirdSitePlatformEntity::getMerchantId, merchantId)
                .eq(ThirdSitePlatformEntity::getIsDel, BooleanEnum.FALSE)
                .one();
    }
}
