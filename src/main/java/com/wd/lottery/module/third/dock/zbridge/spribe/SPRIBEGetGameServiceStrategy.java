package com.wd.lottery.module.third.dock.zbridge.spribe;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.spribe.common.SPRIBEGameCache;
import com.wd.lottery.module.third.dock.zbridge.spribe.common.SPRIBERequestConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Description: spribe game strategy
 *
 * <p> Created on 2024/7/3.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component(BridgeConstant.SPRIBE_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class SPRIBEGetGameServiceStrategy extends AbstractGetGameStrategy {

    private final SPRIBETokenService tokenService;

    public SPRIBEGetGameServiceStrategy(SPRIBETokenService tokenService) {
        this.tokenService = tokenService;
    }

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {

        List<DockGame> list = new ArrayList<>(100);

        Map<GameCategoryEnum, Map<String, String>> gameMap = SPRIBEGameCache.gameMap;
        for (Map.Entry<GameCategoryEnum, Map<String, String>> categoryEntry : gameMap.entrySet()) {
            for (Map.Entry<String, String> gameEntry : categoryEntry.getValue().entrySet()) {
                DockGame game = new DockGame();
                game.setPlatformCode(platformCode);
                game.setGameCategoryEnum(categoryEntry.getKey());
                game.setThirdGameId(gameEntry.getKey());
                game.setGameCode(gameEntry.getKey());
                game.setGameName(gameEntry.getValue());

                list.add(game);
            }
        }
        return list;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        SPRIBERequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), SPRIBERequestConfig.class);
        String lang = getThirdLang(dto.getLang());
        String returnUrl = dto.getLobbyUrl();
        String currency = getThirdCurrency(dto.getCurrencyEnum());
        String token = tokenService.generateUserLoginToken(dto, requestConfig);
        // https://{launch-url}/{game}?user={user}&token={token}&lang={lang}&currency={currency}&operator={operator}&return_url={return_url}
        return requestConfig.getLaunchGameUrl() + "/" + dto.getThirdGameId()
                + "?user=" + dto.getThirdUserId()
                + "&token=" + token
                + "&lang=" + lang
                + "&currency=" + currency
                + "&operator=" + requestConfig.getOperator()
                + "&return_url=" + returnUrl;
    }

}
