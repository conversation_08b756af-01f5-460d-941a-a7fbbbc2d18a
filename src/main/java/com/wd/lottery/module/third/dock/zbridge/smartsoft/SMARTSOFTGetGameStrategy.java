package com.wd.lottery.module.third.dock.zbridge.smartsoft;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.smartsoft.common.SMARTSOFTHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.smartsoft.common.SMARTSOFTRequestConfig;
import com.google.common.base.Splitter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.SMARTSOFT_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class SMARTSOFTGetGameStrategy extends AbstractGetGameStrategy {

    @Autowired
    private SMARTSOFTTokenService smartsoftTokenService;

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        SMARTSOFTRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, SMARTSOFTRequestConfig.class);
        SMARTSOFTHttpRequestTemplate requestTemplate = new SMARTSOFTHttpRequestTemplate(requestConfig);
        List<Game> response = requestTemplate.toGET()
                .api("/GamblingService/api/Games/DetailedList")
                .toCustomObject(new TypeReference<List<Game>>() {
                });
        log.debug("smart soft get game list response: {}", JacksonUtil.toJSONString(response));
        boolean bln = Objects.nonNull(response);
        Assert.isTrue(bln, () -> new ThirdPlatformException("synchronize smart soft get game list failed"));
        return Optional.ofNullable(response).map(items -> this.toDockGameList(platformCode, items)).orElseGet(Collections::emptyList);
    }

    private List<DockGame> toDockGameList(String platformCode, List<Game> games) {
        List<DockGame> dockGames = new ArrayList<>(games.size());
        games.forEach(game -> {
            DockGame dockGame = new DockGame();
            dockGame.setPlatformCode(platformCode);
            GameCategoryEnum gameCategoryEnum = "Slots".equals(game.getGameCategory()) ? GameCategoryEnum.SLOT : GameCategoryEnum.MINI;
            dockGame.setGameCategoryEnum(gameCategoryEnum);
            dockGame.setThirdGameId(game.getGameId().toString());
            dockGame.setGameName(game.getGameName());
            dockGame.setGameCode(game.getGameId().toString());
            dockGame.setRemark(String.join(StringPool.COLON, game.getGameCategory(), game.getGameName()));
            dockGames.add(dockGame);
        });
        return dockGames;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        SMARTSOFTRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), SMARTSOFTRequestConfig.class);
        String token = this.smartsoftTokenService.generateToken(dto.getPlatformCode(), dto.getThirdGameId(), dto.getThirdUserId(), dto.getCurrencyEnum().name(), requestConfig.getPortalName());
        List<String> splitter = Splitter.on(StringPool.COLON).trimResults().splitToList(dto.getRemark());
        String endpoint = dto.getLobbyUrl();
        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), "en");
        String launcher = String.format("%s/GameLauncher/Loader.aspx?Token=%s&GameCategory=%s&GameName=%s&ReturnUrl=%s&Lang=%s&PortalName=%s", requestConfig.getHost(), token, splitter.get(0), splitter.get(1), endpoint, thirdLang, requestConfig.getPortalName());
        log.debug("smart soft launcher game url: {}", launcher);
        return launcher;
    }

    @Data
    public static class Game {
        private String uuid;
        private String title;
        private String image;
        private String provider;
        private String productType;
        private String gameCategory;
        private String gameName;
        private String hasDemo;
        private Integer gameId;
        private String similarGameCategory;
    }

    @Data
    public static class SupportGame {
        private String gameName;
        private String gameCategory;
        private String support;
    }
}