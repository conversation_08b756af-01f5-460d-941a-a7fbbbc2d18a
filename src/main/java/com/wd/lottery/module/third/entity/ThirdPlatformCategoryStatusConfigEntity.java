package com.wd.lottery.module.third.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description: 三方平台分类状态配置（关联币种）
 *
 * <p> Created on 2025/5/14.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
@TableName("third_platform_category_status_config")
public class ThirdPlatformCategoryStatusConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.NONE)
    private Long id;

    /**
     * 平台编码
     */
    private String platformCode;
    /**
     * 分类编码
     */
    private GameCategoryEnum categoryEnum;

    /**
     * 币种编码
     */
    private CurrencyEnum currencyEnum;

    /**
     * 是否维护
     */
    private BooleanEnum enableHot;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public static ThirdPlatformCategoryStatusConfigEntity getDefaultInstance(){
        ThirdPlatformCategoryStatusConfigEntity statusConfig = new ThirdPlatformCategoryStatusConfigEntity();
        statusConfig.setId(IdWorker.getId());
        statusConfig.setEnableHot(BooleanEnum.FALSE);
        statusConfig.setSort(0);
        statusConfig.setCreateTime(LocalDateTime.now());
        statusConfig.setUpdateTime(LocalDateTime.now());

        return statusConfig;
    }
}
