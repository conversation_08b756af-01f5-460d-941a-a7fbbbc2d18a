package com.wd.lottery.module.third.dock.zbridge.tada;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.tada.common.TADAHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.tada.common.TADARequestConfig;
import com.wd.lottery.module.third.dock.zbridge.tada.res.TADAGameResponse;
import com.wd.lottery.module.third.dock.zbridge.tada.res.TADAResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.TADA_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class TADAGetGameStrategy extends AbstractGetGameStrategy {

    public static final List<String> CRASH_GAME_IDS = Collections.unmodifiableList(Arrays.asList("241", "254", "235", "236", "224", "229", "232", "233"));

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        TADARequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, TADARequestConfig.class);
        TADAHttpRequestTemplate requestTemplate = new TADAHttpRequestTemplate(requestConfig);
        TADAResponse<List<TADAGameResponse>> response = requestTemplate.toPOST()
                .api("/GetGameList")
                .toBeanAndCall(new TypeReference<TADAResponse<List<TADAGameResponse>>>() {
                });
        log.debug("TADA获取游戏响应: {}", JSONUtil.toJsonStr(response));
        List<TADAGameResponse> gameList = response.getData();
        return CollStreamUtil.toList(gameList, item -> {
            DockGame dockGame = new DockGame();
            dockGame.setPlatformCode(platformCode);
            dockGame.setThirdGameId(item.getGameId());
            dockGame.setGameName(item.getName().get("en-US"));
            dockGame.setGameCode(item.getGameId());
            // 处理小游戏
            if (CRASH_GAME_IDS.contains(item.getGameId())) {
                dockGame.setGameCategoryEnum(GameCategoryEnum.MINI);
            } else {
                dockGame.setGameCategoryEnum(GameCategoryEnum.SLOT);
            }
            return dockGame;
        });
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        TADARequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), TADARequestConfig.class);
        TADAHttpRequestTemplate requestTemplate = new TADAHttpRequestTemplate(requestConfig);
        TADAResponse<String> response = requestTemplate.toPOST()
                .api("/LoginWithoutRedirect")
                .addParameter("Account", dto.getThirdUserName())
                .addParameter("GameId", dto.getThirdGameId())
                .addParameter("Lang", ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), "en"))
                .toBeanAndCall(new TypeReference<TADAResponse<String>>() {
                });
        log.debug("TADA打开游戏响应: {}", JSONUtil.toJsonStr(response));
        return response.getData();
    }
}