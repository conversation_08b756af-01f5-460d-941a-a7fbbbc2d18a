package com.wd.lottery.module.third.dock.betorder;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.dock.DockPlatformServiceFaced;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dto.ThirdPlatformBasicInfoDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@JobHandler(value = "betOrderPullHandler")
public class BetOrderPullHandler extends IJobHandler {

    private final DockPlatformServiceFaced dockPlatformServiceFaced;
    private final BetOrderSendToMQService sendDataToMQService;

    public BetOrderPullHandler(DockPlatformServiceFaced dockPlatformServiceFaced, BetOrderSendToMQService sendDataToMQService) {
        this.dockPlatformServiceFaced = dockPlatformServiceFaced;
        this.sendDataToMQService = sendDataToMQService;
    }

    @Override
    public ReturnT<String> execute(String platformCode) throws Exception {
        String traceId = MDC.get(Constants.MDC_TRACE_ID_KEY);
        if (StringUtils.isBlank(traceId)) {
            traceId = IdWorker.getIdStr();
            MDC.put(Constants.MDC_TRACE_ID_KEY, traceId);
        }
        XxlJobLogger.log("execute pull bet order, param: {}, traceId: {}", platformCode, traceId);

        ThirdPlatformBasicInfoDTO thirdPlatform = ThirdPlatformLocalCacheUtil.getThirdPlatformBasicInfo(platformCode);

        if (thirdPlatform == null) {
            log.warn("查询游戏平台异常");
            return new ReturnT<>(ReturnT.FAIL_CODE, "platform not found");
        }
        if (thirdPlatform.getEnableEnum() == EnableEnum.FALSE) {
            log.info("platform {} is disabled, ignore", platformCode);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "platform is  disabled");
        }
        try {
            List<PullBetOrderResult> resultList = dockPlatformServiceFaced.pullBetOrderAuto(platformCode, sendDataToMQService::sendDataToMQ);
            log.info("pull bet order task finished， platform: {}", platformCode);
            return parsePullBetOrderResult(resultList, platformCode);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("pull bet order task failed with exception, platformCode: {}, message: {}", platformCode, e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "task failed with exception, message: " + e.getMessage());
        }
    }

    private ReturnT<String> parsePullBetOrderResult(List<PullBetOrderResult> resultList, String param) {
        log.debug("parse pull bet order result, param: {}, resultList: {}", param, JacksonUtil.toJSONString(resultList));
        boolean hasError = false;
        int index = 1;
        for (PullBetOrderResult result : resultList) {
            XxlJobLogger.log("Pulled bet order batch {}, status: {}, count: {}, param: {}",
                    index,
                    result.isSuccess(),
                    result.getPullCount(),
                    JacksonUtil.toJSONString(result.getParam()));

            if(!result.isSuccess()){
                hasError = true;
            }
        }
        if(hasError){
            return new ReturnT<>(ReturnT.FAIL_CODE, "pull bet order task failed");
        }
        return ReturnT.SUCCESS;
    }

}
