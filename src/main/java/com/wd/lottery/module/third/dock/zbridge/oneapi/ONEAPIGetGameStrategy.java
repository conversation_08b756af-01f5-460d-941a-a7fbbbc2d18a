package com.wd.lottery.module.third.dock.zbridge.oneapi;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.oneapi.common.*;
import com.wd.lottery.module.third.dock.zbridge.oneapi.req.GameParam;
import com.wd.lottery.module.third.dock.zbridge.oneapi.req.OpenGameParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;


@Slf4j
@Component(value = BridgeConstant.ONEAPI_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class ONEAPIGetGameStrategy extends AbstractGetGameStrategy {
    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        log.debug("ONEAPI:下载游戏:{}-{}", platformCode, currencyEnum);
        List<DockGame> dockGameList = new ArrayList<>();
        ONEAPIRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, ONEAPIRequestConfig.class);
        ONEAPIHttpRequestTemplate requestTemplate = new ONEAPIHttpRequestTemplate(requestConfig);

        final String vendorCode = requestConfig.getVendorCode();

        int pageNo = 1;
        int totalPages = 1;
        while (pageNo<=totalPages){
            GameParam gameParam = GameParam.builder().traceId(UUID.randomUUID().toString())
                    .vendorCode(vendorCode)
                    .pageNo(pageNo)
                    .pageSize(500)
                    .displayLanguage("EN").build();

            ONEPAIResponse<GameRoot> response =  requestTemplate
                    .toPOST()
                    .api(ONEAPIConstant.GAMELIST)
                    .body(JacksonUtil.toJSONString(gameParam))
                    .toBeanAndCall(new TypeReference<ONEPAIResponse<GameRoot>>() {
                    }, true);
            log.debug("ONEAPI:getGame.resp: {}", response);

            //聚合游戏
            List<DockGame> games = convertToDockGame(response.getData() , platformCode);
            if (!games.isEmpty()) {
                dockGameList.addAll(games);
            }

            //如果当前页不是最后一页,继续请求
            pageNo+=1;
            totalPages = response.getData().getTotalPages();
        }
        return dockGameList;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("ONEAPI:玩家打开游戏:{}", JSONUtil.toJsonStr(dto));
        ONEAPIRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), ONEAPIRequestConfig.class);
        ONEAPIHttpRequestTemplate requestTemplate = new ONEAPIHttpRequestTemplate(requestConfig);
        final String thirdCurr = ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, dto.getCurrencyEnum());
        final String defaultLang = "en";
        OpenGameParam openGameParam = OpenGameParam.builder().traceId(UUID.randomUUID().toString())
                .username(dto.getThirdUserName())
                .gameCode(dto.getGameCode())
                .language(ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang))
                .platform(DeviceEnum.PC.name().equals(dto.getDeviceEnum().name()) ? "web" : "H5")
                .currency(thirdCurr)
                .lobbyUrl(StringUtils.isNotBlank(dto.getLobbyUrl()) ? dto.getLobbyUrl() : "")
                .ipAddress(dto.getIp()).build();

        ONEPAIResponse<GameLink> response =  requestTemplate
                .toPOST()
                .api(ONEAPIConstant.GAME_LINK)
                .body(JacksonUtil.toJSONString(openGameParam))
                .toBeanAndCall(new TypeReference<ONEPAIResponse<GameLink>>() {
                }, true);
        log.debug("ONEAPI:getOpenGameUrl.resp: {}", response);
        return response.getData().getGameUrl();
    }

    private List<DockGame> convertToDockGame(GameRoot gameRoot, String platformCode) {
        HeadField headers = gameRoot.getHeaders();
        List<List<String>> games = gameRoot.getGames();
        if (CollectionUtils.isEmpty(games)) {
            return Collections.emptyList();
        }
        return games.stream().map(item -> {
            DockGame dockGame = new DockGame();
            dockGame.setPlatformCode(platformCode);
            dockGame.setThirdGameId(item.get(headers.getGameCode()));
            dockGame.setGameName(item.get(headers.getGameName()));
            dockGame.setGameCode(item.get(headers.getGameCode()));
            GameCategoryEnum gameCategoryEnum = parseGameCategoryEnum(item.get(headers.getCategoryCode()));
            dockGame.setGameCategoryEnum(gameCategoryEnum);

            return dockGame;
        }).collect(Collectors.toList());

    }


    private GameCategoryEnum parseGameCategoryEnum(String thirdCategory) {
        ONEAPIGameTypeEnum typeEnum = ONEAPIGameTypeEnum.findByType(thirdCategory);
        GameCategoryEnum sysCategory = GameCategoryEnum.SLOT;
        if(typeEnum == null){
            return sysCategory;
        }
        switch (typeEnum) {
            case CASINO:
                sysCategory = GameCategoryEnum.CASINO;
                break;
            case MINI:
                sysCategory = GameCategoryEnum.MINI;
                break;
            case FISH:
                sysCategory = GameCategoryEnum.FISH;
                break;
            case POKER:
                sysCategory = GameCategoryEnum.POKER;
                break;
            case SPORT:
                sysCategory = GameCategoryEnum.SPORT;
                break;
            default:
                // ignore
        }
        return sysCategory;
    }

    @Data
    public static class GameRoot{
        private HeadField headers;
        private List<List<String>> games;
        private Integer  currentPage;
        private Integer  totalItems;
        private Integer  totalPages;

    }

    @Data
    @AllArgsConstructor
    public static class GameItem {
        private String gameCode;
        private String gameName;
        private String categoryCode;
        private String imageSquare;
        private String imageLandscape;
        private String languageCode;
        private String platformCode;
        private String currencyCode;
    }

    @Data
    public static class HeadField{
        private Integer gameCode;
        private Integer gameName;
        private Integer categoryCode;
        private Integer imageSquare;
        private Integer imageLandscape;
        private Integer languageCode;
        private Integer platformCode;
        private Integer currencyCode;
    }

    @Data
    public static class GameLink{
        private String gameUrl;
        private String token;
    }

    @Data
    public static class GameVendor{
        private String name;
        private String currencyCode;
        private String code;
        private String categoryCode;
    }
}