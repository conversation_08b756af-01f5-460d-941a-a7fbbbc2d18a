package com.wd.lottery.module.third.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.module.common.dto.SelectOptionDTO;
import com.wd.lottery.module.third.dto.ThirdPlatformAssignDTO;
import com.wd.lottery.module.third.dto.ThirdPlatformDTO;
import com.wd.lottery.module.third.dto.ThirdPlatformPageItemDTO;
import com.wd.lottery.module.third.param.*;
import com.wd.lottery.module.third.param.*;
import com.wd.lottery.module.third.service.BThirdPlatformService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * Description: 三方平台B端controller
 *
 * <p> Created on 2024/5/29.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Tag(name = "三方平台管理")
@RestController
@RequestMapping("${business-path}/${module-path.third}/platform")
public class BThirdPlatformController {

    private final BThirdPlatformService bThirdPlatformService;

    public BThirdPlatformController(BThirdPlatformService bThirdPlatformService) {
        this.bThirdPlatformService = bThirdPlatformService;
    }

    @Operation(summary = "查询可新增三方平台下拉选项")
    @GetMapping("getNewPlatformOptions")
    public ApiResult<List<SelectOptionDTO>> getNewPlatformOptions() {
        List<SelectOptionDTO> options = bThirdPlatformService.getNewPlatformOptions();
        return ApiResult.success(options);
    }

    @Operation(summary = "查询三方平台模板")
    @GetMapping("getTemplate")
    public ApiResult<ThirdPlatformDTO> getTemplate(@RequestParam String platformCode) {
        ThirdPlatformDTO templateDTO = bThirdPlatformService.getPlatformTemplate(platformCode);
        return ApiResult.success(templateDTO);
    }

    @Operation(summary = "查询平台详情")
    @GetMapping("getDetail")
    public ApiResult<ThirdPlatformDTO> getDetail(@RequestParam String platformCode) {
        ThirdPlatformDTO detail = bThirdPlatformService.getThirdPlatformDetail(platformCode);
        return ApiResult.success(detail);
    }

    @Operation(summary = "保存三方平台(新增、更新)")
    @PostMapping("saveThirdPlatform")
    public ApiResult<Boolean> saveThirdPlatform(@RequestBody @Valid ThirdPlatformDTO param) {
        bThirdPlatformService.saveThirdPlatform(param);
        return ApiResult.success(true);
    }

    @Operation(summary = "分页查询三方平台列表")
    @GetMapping("getPlatformPage")
    public ApiResult<Page<ThirdPlatformPageItemDTO>> getPlatformPage(ThirdPlatformPageParam param) {
        param.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        Page<ThirdPlatformPageItemDTO> page = bThirdPlatformService.getPlatformPage(param);
        return ApiResult.success(page);
    }


    @Operation(summary = " 同步三方平台游戏")
    @PostMapping("syncGame")
    public ApiResult<Boolean> syncPlatformGame(@RequestParam String platformCode) {
        CurrencyEnum currencyEnum = AdminTokenInfoUtil.getRequestCurrencyEnumNotNull();
        bThirdPlatformService.syncPlatformGame(platformCode, currencyEnum);
        return ApiResult.success(true);
    }

    // 分配三方平台到商户
    @Operation(summary = "分配三方平台到商户")
    @PostMapping("assignPlatform")
    public ApiResult<Boolean> assignPlatform(@RequestBody @Valid AssignPlatformParam param) {
        bThirdPlatformService.assignPlatform(param);
        return ApiResult.success(true);
    }


    // 分配三方游戏到商户
    @Operation(summary = "分配三方游戏到商户")
    @PostMapping("assignGame")
    public ApiResult<Boolean> assignGame(@RequestBody @Valid AssignGameParam param) {
        bThirdPlatformService.assignGame(param);
        return ApiResult.success(true);
    }

    @Operation(summary = "更新三方平台维护开关")
    @PostMapping("updateMaintainStatus")
    public ApiResult<Boolean> updateMaintainStatus(@RequestBody @Valid MaintainStatusParam param) {
        param.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        bThirdPlatformService.updatePlatformMaintainStatus(param);
        return ApiResult.success(true);
    }

    @Operation(summary = "更新三方平台额度转换开关")
    @PostMapping("updateTransferStatus")
    public ApiResult<Boolean> updateTransferStatus(@RequestBody @Valid TransferStatusParam param) {
        param.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        bThirdPlatformService.updatePlatformTransferStatus(param);
        return ApiResult.success(true);
    }

    @Operation(summary = "更新平台启用状态")
    @PostMapping("updateEnableStatus")
    public ApiResult<Boolean> updateEnableStatus(@RequestBody @Valid EnableStatusParam param){
        bThirdPlatformService.updatePlatformEnableStatus(param);
        return ApiResult.success(true);
    }

    @Operation(summary = "手动拉取注单")
    @Parameters({
            @Parameter(name = "platformCode", description = "三方平台编码", example = "PG"),
            @Parameter(name = "begin", description = "开始时间， 格式：yyyy-MM-dd HH:mm:ss", example = "2024-06-30 12:00:00"),
            @Parameter(name = "end", description = "结束时间， 格式：yyyy-MM-dd HH:mm:ss", example = "2024-06-30 12:30:00")
    })
    @GetMapping("pullBetOrderManually")
    public ApiResult<Boolean> pullBetOrderManually(@RequestParam String platformCode, @RequestParam String begin, @RequestParam String end) {
        bThirdPlatformService.pullBetOrderManually(platformCode, begin, end);
        return ApiResult.success(true);
    }

    @Operation(summary = "手动拉单（根据标记）")
    @GetMapping("pullBetOrderByFlagManually")
    public ApiResult<Boolean> pullBetOrderByFlagManually(@RequestParam String platformCode, @RequestParam String flag) {
        bThirdPlatformService.pullBetOrderByFlagManually(platformCode, flag);
        return ApiResult.success(true);
    }

    @Operation(summary = "查询商户分配和未分配平台")
    @Parameter(name = "merchantId", description = "商户id", example = "1")
    @GetMapping("getAllPlatform")
    public ApiResult<List<ThirdPlatformAssignDTO>> getAllPlatform(@RequestParam @NotNull(message = "商户id不能为空") Long merchantId) {
        return ApiResult.success(this.bThirdPlatformService.findAllPlatform(merchantId));
    }

    @Operation(summary = "批量给商户分配平台")
    @PostMapping("assignMorePlatform")
    public ApiResult<Boolean> assignMorePlatform(@RequestBody @Valid AssignMorePlatformParam param) {
        this.bThirdPlatformService.assignMorePlatform(param);
        return ApiResult.success();
    }

    @Operation(summary = "新建代理线路")
    @PostMapping("newAgent")
    public ApiResult<Boolean> newAgent(@RequestBody @Valid NewAgentParam param){
        bThirdPlatformService.newAgent(param);
        return ApiResult.success(true);
    }

    @Operation(
            method = "POST",
            summary = "校验平台不同币种配置",
            parameters = @Parameter(hidden = true),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "平台代码", required = true, content = @Content(schema = @Schema(implementation = PlatformConfigParam.class))),
            responses = @ApiResponse(description = "", content = @Content(schema = @Schema(implementation = Boolean.class)))
    )
    @PostMapping(value = "validatePlatformConfig")
    public ApiResult<Boolean> validatePlatformConfig(@RequestBody PlatformConfigParam param) {
        this.bThirdPlatformService.validatePlatformConfig(param.getPlatformCode(), param.getCurrencyEnum());
        return ApiResult.success();
    }
}