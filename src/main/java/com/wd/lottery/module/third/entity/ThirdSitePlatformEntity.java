package com.wd.lottery.module.third.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.EnableEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:
 *
 * <p> Created on 2024/5/29.
 *
 * <AUTHOR>
 * @version 0.1
 */
@TableName("third_site_platform")
@Data
public class ThirdSitePlatformEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private Long merchantId;

    private String platformCode;

    private String platformName;

    private String currencies;

    private String categories;

    private String platformImg;

    private EnableEnum enableEnum;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private BooleanEnum isDel;
}
