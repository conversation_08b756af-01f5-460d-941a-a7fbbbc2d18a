package com.wd.lottery.module.third.param;

import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Description:
 *
 * <p> Created on 2024/6/19.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
public class ThirdGamePageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(hidden = true)
    private CurrencyEnum currencyEnum;

    @Schema(description = "当前页码")
    private Integer current;
    @Schema(description = "分页大小")
    private Integer size;

    @NotBlank
    @Schema(description = "平台编码")
    private String platformCode;

    @Schema(description = "游戏分类")
    private GameCategoryEnum gameCategoryEnum;

    @Schema(description = "游戏编码")
    private String gameCode;

    @Schema(description = "游戏名称")
    private String gameName;

    @Schema(description = "游戏标签")
    private String tag;

    @Schema(description = "游戏是否维护")
    private BooleanEnum isMaintain;
}
