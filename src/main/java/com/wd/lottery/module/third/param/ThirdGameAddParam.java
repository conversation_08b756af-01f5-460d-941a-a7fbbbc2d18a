package com.wd.lottery.module.third.param;

import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description: 手动增加游戏参数
 *
 * <p> Created on 2024/7/24.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
public class ThirdGameAddParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank
    @Schema(description = "平台编码")
    private String platformCode;
    @NotBlank
    @Schema(description = "三方游戏ID")
    private String thirdGameId;

    @NotBlank
    @Schema(description = "游戏编码")
    private String gameCode;

    @NotBlank
    @Schema(description = "游戏名称")
    private String gameName;

    @NotNull
    @Schema(description = "游戏分类")
    private GameCategoryEnum gameCategoryEnum;

    private BooleanEnum isMaintain;

    private BooleanEnum isHot;
}
