package com.wd.lottery.module.third.dock.zbridge.ppzr;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.ppzr.common.PPCASINOApiEnum;
import com.wd.lottery.module.third.dock.zbridge.ppzr.common.PPCASINOHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.ppzr.common.PPCASINORequestConfig;
import com.wd.lottery.module.third.dock.zbridge.ppzr.res.PPCASINOResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 三方游戏
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.PP_CASINO_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class PPCASINOGetGameServiceStrategy extends AbstractGetGameStrategy {

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        log.debug("PP:CASINO:下载游戏:{}-{}", platformCode, currencyEnum);
        PPCASINORequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, PPCASINORequestConfig.class);
        PPCASINOHttpRequestTemplate requestTemplate = new PPCASINOHttpRequestTemplate(requestConfig);
        GameResponse response = requestTemplate.toPOST()
                .api(PPCASINOApiEnum.GET_GAME.getPath())
                .toBeanAndCall(GameResponse.class);
        log.debug("PP:CASINO:下载代理游戏响应:{}", JSONUtil.toJsonStr(response));
        // 游戏转换
        return CollStreamUtil.toList(response.getGameList(), item -> {
            DockGame dockGame = new DockGame();
            dockGame.setPlatformCode(platformCode);
            dockGame.setGameCategoryEnum(GameCategoryEnum.CASINO);
            dockGame.setThirdGameId(Convert.toStr(item.getGameIdNumeric()));
            dockGame.setGameName(item.getGameName());
            dockGame.setGameCode(item.getGameId());
            return dockGame;
        });
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("PP:CASINO:玩家打开游戏:{}", JSONUtil.toJsonStr(dto));
        PPCASINORequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), PPCASINORequestConfig.class);
        PPCASINOHttpRequestTemplate requestTemplate = new PPCASINOHttpRequestTemplate(requestConfig);
        // 请求游戏
        dto.setGameCode("101");

        final String defaultLang = "en";
        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang);
        OpenGameResponse response = requestTemplate.toPOST()
                .api(PPCASINOApiEnum.OPEN_GAME.getPath())
                .addParameter("externalPlayerId", dto.getThirdUserName())
                .addParameter("gameId", dto.getGameCode())
                .addParameter("language", thirdLang)
                .addParameter("lobbyURL", dto.getLobbyUrl())
                .toBeanAndCall(OpenGameResponse.class);
        log.debug("PP:CASINO:玩家打开游戏响应:{}", JSONUtil.toJsonStr(response));
        boolean isEmpty =StrUtil.isEmpty(response.getGameUrl());
        Assert.isFalse(isEmpty, () -> new RuntimeException("获取游戏路径失败"));
        return response.getGameUrl();
    }

    /**
     * 游戏列表
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class GameResponse extends PPCASINOResponse<Void> {

        private List<GameItem> gameList;

        @Data
        public static class GameItem {

            /**
             * 由 Pragmatic Play 提供的游戏唯一符号标识符
             **/
            @JsonProperty(value = "gameID")
            private String gameId;

            /**
             * 游戏名称
             **/
            private String gameName;

            /**
             * 游戏的唯一符号标识符
             **/
            @JsonProperty(value = "gameTypeID")
            private String gameTypeId;

            /**
             * 游戏类型的简要说明
             */
            private String typeDescription;

            /**
             * 游戏所采用技术的简要说明，以逗号分隔。可能的值包括：html5 - HTML5 游戏
             */
            private String technology;

            /**
             * 游戏所采用的技术，以逗号分隔。可能的值包括：H5 - HTML5 游戏
             */
            @JsonProperty(value = "technologyID")
            private String technologyId;

            /**
             * 可用于打开游戏的平台。可能的值（以逗号分隔）：
             * <p>MOBILE – 如果游戏应在移动设备上打开</p>
             * <p>WEB – 如果游戏应在桌面设备上打开</p>
             * <p>DOWNLOAD – 如果游戏可在下载的游戏客户端中打开</p>
             */
            private String platform;

            /**
             * 若为真，则表示游戏提供演示版本。
             */
            private Boolean demoGameAvailable;

            /**
             * 描述游戏宽度和高度之间的比例关系。
             */
            private String aspectRatio;

            /**
             * gameID 的数值
             */
            private Long gameIdNumeric;

            /**
             * 若为真，则游戏提供免费回合奖励
             * <p>该字段为可选，并会作为响应在请求包含 options 列表中的 GetFrbDetails 时出现</p>
             */
            private Boolean frbAvailable;

            /**
             * 若为真，则游戏提供可变的免费回合奖励
             * <p>该字段为可选，并会作为响应在请求包含 options 列表中的 GetFrbDetails 时出现</p>
             */
            private Boolean variableFrbAvailable;

            /**
             * 游戏中的可选赔付线数
             * <p>该字段为可选，并会作为响应在请求包含 options 列表中的 GetLines 时出现</p>
             */
            private Integer lines;

            /**
             * 支持的游戏内功能列表
             * <p>该字段是可选的，在请求包含的情况下将出现在响应中</p>
             * <p>选项列表中的 GetFeatures</p>
             * <p>可能的值：</p>
             * <p>BUY - 游戏内购买功能</p>
             * <p>ANTE - 底注支持</p>
             * <p>SUPER_SPIN - 超级旋转支持</p>
             */
            private List<String> features;

            /**
             * 游戏产品组合类型
             * <p>可用选项：</p>
             * <p>RNG - 主产品组合游戏（视频老虎机、经典老虎机等）</p>
             * <p>LC – 真人娱乐场产品组合</p>
             * <p>VSB - 虚拟体育博彩产品组合</p>
             * <p>该字段是可选的，在请求包含的情况下将出现在响应中</p>
             */
            private String dataType;
        }
    }

    /**
     * 游戏路径
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class OpenGameResponse extends PPCASINOResponse<Void> {
        @JsonProperty(value = "gameURL")
        private String gameUrl;
    }
}