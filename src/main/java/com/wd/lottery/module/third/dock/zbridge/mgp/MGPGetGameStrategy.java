package com.wd.lottery.module.third.dock.zbridge.mgp;


import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.mgp.common.MGPHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.mgp.common.MGPRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.mgp.res.MGPGameListResponse;
import com.wd.lottery.module.third.dock.zbridge.mgp.res.MGPGameResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Component(BridgeConstant.MGP_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
@Slf4j
public class MGPGetGameStrategy extends AbstractGetGameStrategy {
    @Autowired
    protected StringRedisTemplate redisTemplate;

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        MGPRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, MGPRequestConfig.class);
        MGPHttpRequestTemplate requestTemplate = new MGPHttpRequestTemplate(requestConfig, redisTemplate);
        String api = "/agents/{agentCode}/games";
        String execute = requestTemplate
                .api(api)
                .toGET()
                .toCustomObject(String.class);
        // 获取游戏列表, 非[开头即获取失败
        if (!execute.startsWith("[")) {
            throw new ThirdPlatformException("获取游戏失败");
        }
        List<MGPGameListResponse.Game> list = JSONUtil.toBean(execute, new TypeReference<List<MGPGameListResponse.Game>>() {
        }, true);
        List<DockGame> gameList = new ArrayList<>();
        for (MGPGameListResponse.Game game : list) {
            DockGame dockGame = toDockGame(game, platformCode);
            gameList.add(dockGame);
        }
        return gameList;
    }

    private DockGame toDockGame(MGPGameListResponse.Game g, String platformCode) {
        DockGame game = new DockGame();
        game.setPlatformCode(platformCode);
        game.setThirdGameId(g.getGameCode());
        game.setGameName(g.getGameName());
        game.setGameCode(g.getGameCode());
        switch (g.getChannelCode().toLowerCase()) {
            case "livedealer":
            case "microgaminglive":
                game.setGameCategoryEnum(GameCategoryEnum.CASINO);
                break;
            case "slots":
                game.setGameCategoryEnum(GameCategoryEnum.SLOT);
                break;
            case "fishing":
                game.setGameCategoryEnum(GameCategoryEnum.FISH);
                break;
            case "arcade":
                game.setGameCategoryEnum(GameCategoryEnum.THREE);
                break;
            case "qipai":
                game.setGameCategoryEnum(GameCategoryEnum.POKER);
                break;
            case "videobingo":
            case "videopokers":
                game.setGameCategoryEnum(GameCategoryEnum.MINI);
                break;
            default:
                game.setGameCategoryEnum(GameCategoryEnum.SLOT);
        }
        return game;
    }


    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("MG getOpenGameUrl dto:{} ", dto);
        CurrencyEnum currencyEnum = dto.getCurrencyEnum();
        String platformCode = dto.getPlatformCode();
        MGPRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, MGPRequestConfig.class);
        MGPHttpRequestTemplate requestTemplate = new MGPHttpRequestTemplate(requestConfig, redisTemplate);
        String api = "/agents/{agentCode}/players/" + dto.getThirdUserName() + "/sessions";
        requestTemplate.api(api)
                .toPOST();
        String gameCode = dto.getGameCode();
        if (StringUtils.isBlank(gameCode)) {
            // 真人视讯
            requestTemplate.addParameter("contentType", "Lobby")
                    .addParameter("contentCode", "MGL_GRAND_LobbyAll");
        } else {
            requestTemplate.addParameter("contentCode", gameCode)
                    .addParameter("homeUrl", dto.getLobbyUrl());
        }
        MGPGameResponse execute = requestTemplate.addParameter("platform", dto.getDeviceEnum() == DeviceEnum.PC ? "Desktop" : "Mobile")
                // use common converter to convert third lang
                .addParameter("langCode", ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), dto.getLang()))
                .toBeanAndCall(MGPGameResponse.class);
        if (StringUtils.isBlank(execute.getUrl())) {
            throw new ThirdPlatformException("游戏维护中");
        }
        return execute.getUrl();
    }

    @Override
    public String getDemoGameUrl(DockGetGameUrl param) {
        log.debug("get mgp demo game url, param: {}", param);
        MGPRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(param.getPlatformCode(), param.getCurrencyEnum(), MGPRequestConfig.class);
        MGPHttpRequestTemplate requestTemplate = new MGPHttpRequestTemplate(requestConfig, redisTemplate);

        final String api = "/agents/{agentCode}/demoSessions";

        requestTemplate.api(api)
                .toPOST();
        String gameCode = param.getGameCode();
        if (StringUtils.isBlank(gameCode)) {
            // 真人视讯
            requestTemplate.addParameter("contentType", "Lobby")
                    .addParameter("contentCode", "MGL_GRAND_LobbyAll");
        } else {
            requestTemplate.addParameter("contentCode", gameCode);
        }

        MGPGameResponse execute = requestTemplate.addParameter("platform", param.getDeviceEnum() == DeviceEnum.PC ? "Desktop" : "Mobile")
                // use common converter to convert third lang
                .addParameter("langCode", ThirdPlatformMappingConverter.toThirdLang(requestConfig, param.getLang(), param.getLang()))
                .toBeanAndCall(MGPGameResponse.class);
        if (StringUtils.isBlank(execute.getUrl())) {
            throw new ThirdPlatformException("游戏维护中");
        }
        return execute.getUrl();
    }
}
