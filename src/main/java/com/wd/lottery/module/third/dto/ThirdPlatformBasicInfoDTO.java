package com.wd.lottery.module.third.dto;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: 三方平台基础信息
 *
 * <p> Created on 2025/4/25.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
public class ThirdPlatformBasicInfoDTO {

    /**
     * 平台code
     */
    private String platformCode;
    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 桥接平台编码
     */
    private String bridgePlatformCode;

    /**
     * 平台logo
     */
    private String platformImg;

    /**
     * 开通币种
     */
    private String currencies;

    /**
     * 游戏种类, 多个逗号间隔
     * @see GameCategoryEnum
     */
    private String categories;

    /**
     * 平台配置
     */
    private String configJson;

    /**
     * 平台状态, 0 禁用 1 启用
     */
    private EnableEnum enableEnum;
    /**
     * 是否删除 0 未删除 1 已删除
     */
    private BooleanEnum isDel;

    public List<CurrencyEnum> getSupportedCurrencies() {
        return CurrencyEnum.parseNamesToEnum(currencies);
    }

    public List<GameCategoryEnum> getSupportedCategories() {

        List<GameCategoryEnum> list = new ArrayList<>();
        if (StrUtil.isBlank(categories)) {
            return list;
        }
        for (String categoryStr : categories.split(StrPool.COMMA)) {
            try {
                GameCategoryEnum gameCategoryEnum = GameCategoryEnum.valueOf(categoryStr);
                list.add(gameCategoryEnum);
            } catch (Exception e) {
                // ignore
            }
        }
        return list;
    }
}
