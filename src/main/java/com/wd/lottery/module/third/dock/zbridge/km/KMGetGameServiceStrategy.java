package com.wd.lottery.module.third.dock.zbridge.km;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.base.PeachLang;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.km.common.KMExcelGameItem;
import com.wd.lottery.module.third.dock.zbridge.km.common.KMHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.km.common.KMRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.km.common.KMResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Component(BridgeConstant.KM_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class KMGetGameServiceStrategy extends AbstractGetGameStrategy {


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        List<DockGame> gameList = new ArrayList<>(100);
        ClassPathResource resource = new ClassPathResource("third/KM_game_list.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            EasyExcel.read(inputStream, KMExcelGameItem.class, new AnalysisEventListener<KMExcelGameItem>() {
                @Override
                public void invoke(KMExcelGameItem gameItem, AnalysisContext analysisContext) {
                    DockGame dockGame = toDockGame(gameItem, platformCode);
                    gameList.add(dockGame);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    //由于所有数据都是交于上层实现,故不需要实现
                }
            }).sheet().doRead();

        } catch (Exception e) {
            log.error("getGameList error", e);
        }

        return gameList;
    }

    private DockGame toDockGame(KMExcelGameItem gameItem, String platformCode) {
        DockGame dockGame = new DockGame();
        if("POKER".equals(gameItem.getGameType())){
            dockGame.setGameCategoryEnum(GameCategoryEnum.POKER);
        }
        if("SLOT".equals(gameItem.getGameType())){
            dockGame.setGameCategoryEnum(GameCategoryEnum.SLOT);
        }
        dockGame.setPlatformCode(platformCode);
        dockGame.setThirdGameId(gameItem.getGameCode());
        dockGame.setGameCode(gameItem.getGameCode());
        dockGame.setGameName(gameItem.getGameName());

        return dockGame;
    }


    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        KMRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), KMRequestConfig.class);
        KMHttpRequestTemplate requestTemplate = new KMHttpRequestTemplate(requestConfig);

        TokenRes tokenRes = requestTemplate
                .api("/api/player/authorize")
                .toPOST()
                .addParameter("ipaddress", dto.getIp())
                .addParameter("username", dto.getThirdUserName())
                .addParameter("userid", dto.getThirdUserId())
                .addParameter("lang", ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), PeachLang.EN_US))
                .addParameter("cur", ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, dto.getCurrencyEnum()))
                .addParameter("betlimitid", "1")
                .addParameter("istestplayer", "true")
                .addParameter("platformtype", DeviceEnum.PC.equals(dto.getDeviceEnum())?"0":"1")
                .toCustomObject(TokenRes.class);

        log.debug("http getOpenGameUrl response:{} ",tokenRes);
//        https://staging-lobby.queenmakergames.co/gamelauncher?gpcode=KMQM&gcode=thai_hi_lo_2&token=TPvYxzEysPgHs81Edhj71TTQrnETLWvzZwcU9jkCK0wChhObZyftcPGHyNZCzbwFFp6t2Mc380pCbHi11FgrAEpLSFHukNd8BwubpEBCc5Pnrrmicf21iCJhzjmKIEMkEU
        String openGameUrlTemplate = "%s/gamelauncher?gpcode=KMQM&gcode=%s&token=%s";

        String gameUrl = String.format(openGameUrlTemplate,requestConfig.getLobbyURL(),dto.getGameCode(),tokenRes.getAuthtoken()) ;
        log.debug("km gameUrl:{}",gameUrl);
        return gameUrl;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class TokenRes extends KMResponse<Object> {

        private String authtoken;

        private Boolean isnew;

    }


}
