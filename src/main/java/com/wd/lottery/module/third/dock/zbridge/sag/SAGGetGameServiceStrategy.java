package com.wd.lottery.module.third.dock.zbridge.sag;


import cn.hutool.core.collection.CollUtil;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.sag.common.SAGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.sag.common.SAGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.sag.res.GetGameRes;
import com.wd.lottery.module.third.dock.zbridge.sag.res.OpenGameRes;
import com.wd.lottery.module.third.dock.zbridge.sag.res.SAGGameItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: sg game strategy
 *
 * <p> Created on 2024/5/13.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.SAG_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class SAGGetGameServiceStrategy extends AbstractGetGameStrategy {


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        SAGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, SAGRequestConfig.class);
        SAGHttpRequestTemplate requestTemplate = new SAGHttpRequestTemplate(requestConfig);

        GetGameRes response = requestTemplate
                .addParameter("method", "GetActiveHostList")
                .xmlToBeanAndCall(GetGameRes.class);

        List<SAGGameItem> games = response.getGameList();
        if (CollUtil.isEmpty(games)) {
            return new ArrayList<>();
        }
        return toDockGameList(games, platformCode);
    }

    private List<DockGame> toDockGameList(List<SAGGameItem> games, String platformCode) {

        return games.stream()
                .map(game -> {
                    DockGame dockGame = new DockGame();
                    dockGame.setPlatformCode(platformCode);
                    dockGame.setThirdGameId(game.getHostID());
                    dockGame.setGameCode(game.getHostID());
                    dockGame.setGameName(game.getHostName());
                    dockGame.setGameCategoryEnum(GameCategoryEnum.CASINO);
                    return dockGame;
                })
                .collect(Collectors.toList());
    }


    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        SAGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), SAGRequestConfig.class);
        SAGHttpRequestTemplate requestTemplate = new SAGHttpRequestTemplate(requestConfig);

        String openGameUrlTemplate = "%s?username=%s&token=%s&lang=%s&lobby=%s";
        final String defaultLang = "en-us";

        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang);
        String thirdCurr = ThirdPlatformMappingConverter.toThirdCurrency(requestConfig, dto.getCurrencyEnum());
        OpenGameRes res = requestTemplate
                .addParameter("method", "LoginRequest")
                .addParameter("Username", dto.getThirdUserName())
                .addParameter("CurrencyType", thirdCurr)
                .xmlToBeanAndCall(OpenGameRes.class);


        String gameUrl =  String.format(openGameUrlTemplate,
                requestConfig.getGameUrl(), dto.getThirdUserName(),
                res.getToken(),
                thirdLang,
                requestConfig.getLobbyCode());

        gameUrl += "&returnurl=" + dto.getLobbyUrl();
        log.debug("sag getOpenGameUrl: {}",  gameUrl);
        return gameUrl;
    }


}
