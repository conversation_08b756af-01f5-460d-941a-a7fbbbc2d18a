package com.wd.lottery.module.third.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description: 商户三方平台分类配置表
 *
 * <p> Created on 2024/5/29.
 *
 * <AUTHOR>
 * @version 0.1
 */
@TableName("third_site_platform_category")
@Data
public class ThirdSitePlatformCategoryEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private Long merchantId;

    private String platformCode;

    private GameCategoryEnum categoryEnum;

    private String categoryImg;

    private String updateBy;

    private LocalDateTime updateTime;

}
