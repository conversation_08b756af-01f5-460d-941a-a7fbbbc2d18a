package com.wd.lottery.module.third.dock.zbridge.kg;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.kg.common.KINGGAMEApiEnum;
import com.wd.lottery.module.third.dock.zbridge.kg.common.KINGGAMEHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.kg.common.KINGGAMERequestConfig;
import com.wd.lottery.module.third.dock.zbridge.kg.common.KINGGAMEType;
import com.wd.lottery.module.third.dock.zbridge.kg.res.KINGGAMEResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 游戏策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.KINGGAME_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class KINGGAMEGetGameStrategy extends AbstractGetGameStrategy {

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        log.debug("KingGame:下载游戏:{}-{}", platformCode, currencyEnum);
        KINGGAMERequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, KINGGAMERequestConfig.class);
        KINGGAMEHttpRequestTemplate requestTemplate = new KINGGAMEHttpRequestTemplate(requestConfig);

        // 请求游戏
        KINGGAMEResponse<List<Game>> response = requestTemplate.api(KINGGAMEApiEnum.GET_GAME.getPath()).toBeanAndCall(new TypeReference<KINGGAMEResponse<List<Game>>>() {
        });
        log.debug("KingGame:下载代理游戏响应:{}", JSONUtil.toJsonStr(response));

        List<Game> games = response.getData();
        return CollStreamUtil.toList(games, game -> this.toDockGame(platformCode, game));
    }

    /**
     * 转系统游戏
     *
     * @param platformCode 平台代码
     * @param game         三方游戏
     * @return {@link DockGame} 系统游戏
     */
    private DockGame toDockGame(String platformCode, Game game) {
        DockGame dockGame = new DockGame();
        dockGame.setPlatformCode(platformCode);
        // 游戏分类
        switch (game.getGameCategoryId()) {
            case KINGGAMEType.SLOT:
                dockGame.setGameCategoryEnum(GameCategoryEnum.SLOT);
                break;
            case KINGGAMEType.FISH:
                dockGame.setGameCategoryEnum(GameCategoryEnum.FISH);
                break;
            case KINGGAMEType.CARD:
                dockGame.setGameCategoryEnum(GameCategoryEnum.POKER);
                break;
            default:
                dockGame.setGameCategoryEnum(GameCategoryEnum.MINI);
        }
        dockGame.setThirdGameId(game.getGameId());
        dockGame.setGameName(game.getName());
        dockGame.setGameCode(game.getGameId());
        return dockGame;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        log.debug("KingGame:玩家打开游戏:{}", JSONUtil.toJsonStr(dto));
        KINGGAMERequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), KINGGAMERequestConfig.class);
        KINGGAMEHttpRequestTemplate requestTemplate = new KINGGAMEHttpRequestTemplate(requestConfig);

        // 打开平台
        DeviceEnum deviceEnum = dto.getDeviceEnum();
        String platform = deviceEnum == DeviceEnum.PC ? "web" : "app";

        final String defaultLang = "en-US";

        // 请求游戏
        KINGGAMEResponse<String> response = requestTemplate.api(KINGGAMEApiEnum.OPEN_GAME.getPath())
                .addParameter("Account", dto.getThirdUserName())
                .addParameter("GameId", dto.getThirdGameId())
                .addParameter("Lang", ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang))
                .addParameter("HomeUrl", dto.getLobbyUrl())
                .addParameter("platform", platform)
                .toBeanAndCall(new TypeReference<KINGGAMEResponse<String>>() {
                });
        log.debug("KingGame:玩家打开游戏响应:{}", JSONUtil.toJsonStr(response));
        return response.getData();
    }

    /**
     * 游戏
     */
    @Data
    public static class Game {

        /**
         * 游戏唯一ID
         */
        @JsonProperty("game_id")
        private String gameId;

        /**
         * 游戏名 key值分别为zh-CN,zh-TW,en-US
         */
        @JsonProperty("name")
        private String name;

        /**
         * 游戏唯一code 游戏类型
         */
        @JsonProperty("GameCategoryId")
        private String gameCategoryId;
    }
}