package com.wd.lottery.module.third.dock.zbridge.ds;


import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.ds.common.DSApiEnum;
import com.wd.lottery.module.third.dock.zbridge.ds.common.DSHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.ds.common.DSRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.ds.res.DSExcelGameItem;
import com.wd.lottery.module.third.dock.zbridge.ds.res.DSGameRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: dragoon soft game strategy
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.DS_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class DSGetGameServiceStrategy extends AbstractGetGameStrategy {


    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {
        List<DockGame> gameList = new ArrayList<>(100);
        ClassPathResource resource = new ClassPathResource("third/ds_games.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            EasyExcel.read(inputStream, DSExcelGameItem.class, new AnalysisEventListener<DSExcelGameItem>() {
                @Override
                public void invoke(DSExcelGameItem gameItem, AnalysisContext analysisContext) {
                    DockGame dockGame = toDockGame(gameItem, platformCode);
                    gameList.add(dockGame);
                }
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet().doRead();

        } catch (Exception e) {
            log.error("parse game excel error", e);
        }

        return gameList;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        CurrencyEnum currencyEnum = dto.getCurrencyEnum();
        DSRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), currencyEnum, DSRequestConfig.class);
        DSHttpRequestTemplate requestTemplate = new DSHttpRequestTemplate(requestConfig);

        final String defaultLang = "en_us";

        String thirdLang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang);


        JSONObject paramJson = new JSONObject();
        paramJson.set("game_id",dto.getGameCode());
        paramJson.set("agent",requestConfig.getAgent());
        paramJson.set("account",dto.getThirdUserName());
        paramJson.set("lang", thirdLang);


        DSGameRes res = requestTemplate
                .toPOST()
                .api(DSApiEnum.GAME_LINK.getPath())
                .body(paramJson.toString())
                .toBeanAndCall(DSGameRes.class);
        return res.getUrl();
    }


    private DockGame toDockGame(DSExcelGameItem gameItem, String platformCode) {
        DockGame dockGame = new DockGame();

        dockGame.setPlatformCode(platformCode);
        dockGame.setThirdGameId(gameItem.getGameId());
        dockGame.setGameCode(gameItem.getGameId());
        dockGame.setGameName(gameItem.getGameNameEn());

        GameCategoryEnum categoryEnum = convertGameCategory(gameItem.getGameType());
        dockGame.setGameCategoryEnum(categoryEnum);

        dockGame.setRemark(gameItem.getRtp());
        return dockGame;
    }

    private GameCategoryEnum convertGameCategory(String gameType) {
        if (StringUtils.isBlank(gameType)) {
            return GameCategoryEnum.SLOT;
        }
        GameCategoryEnum categoryEnum = GameCategoryEnum.SLOT;
        switch (gameType) {
            case "Fishing":
                categoryEnum = GameCategoryEnum.FISH;
                break;
            case "Arcade":
            case "Slot":
                break;
            default:
                // ignore
        }
        return categoryEnum;
    }

}
