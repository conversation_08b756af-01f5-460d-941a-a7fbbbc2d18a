package com.wd.lottery.module.third.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.GrapeApplication;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.common.util.MemberTokenInfoUtil;
import com.wd.lottery.module.common.dto.IndexDataDTO;
import com.wd.lottery.module.common.vo.InitDataVO;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.member.constants.MemberStateEnum;
import com.wd.lottery.module.member.constants.MemberTypeEnum;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.third.constants.ThirdLoginTypeEnum;
import com.wd.lottery.module.third.constants.ThirdRedisConstants;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dto.*;
import com.wd.lottery.module.third.entity.ThirdGameEntity;
import com.wd.lottery.module.third.entity.ThirdPlatformConvertRecord;
import com.wd.lottery.module.third.entity.ThirdSitePlatformEntity;
import com.wd.lottery.module.third.repo.ThirdGameRepo;
import com.wd.lottery.module.third.service.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: C 端三方模块service 实现
 *
 * <p> Created on 2024/5/31.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class CThirdPlatformServiceImpl implements CThirdPlatformService {

    private final ThirdSitePlatformInnerService thirdSitePlatformInnerService;
    private final ThirdSiteGameInnerService thirdSiteGameInnerService;
    private final ThirdConvertOrderInnerService thirdConvertOrderInnerService;
    private final ThirdSiteUserInnerService thirdSiteUserInnerService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final MemberService memberService;
    private final ThirdPlatformConvertRecordInnerService thirdPlatformConvertRecordInnerService;

    private final ThirdGameRepo thirdGameRepo;
    private final RedisService redisService;

    public CThirdPlatformServiceImpl(ThirdSitePlatformInnerService thirdSitePlatformInnerService,
                                     ThirdSiteGameInnerService thirdSiteGameInnerService,
                                     ThirdConvertOrderInnerService thirdConvertOrderInnerService,
                                     ThirdSiteUserInnerService thirdSiteUserInnerService,
                                     @Qualifier(Constants.THIRD_PLATFORM_THREAD_POOL) ThreadPoolTaskExecutor threadPoolTaskExecutor,
                                     MemberService memberService,
                                     ThirdGameRepo thirdGameRepo,
                                     ThirdPlatformConvertRecordInnerService thirdPlatformConvertRecordInnerService,
                                     RedisService redisService) {

        this.thirdSitePlatformInnerService = thirdSitePlatformInnerService;
        this.thirdSiteGameInnerService = thirdSiteGameInnerService;
        this.thirdConvertOrderInnerService = thirdConvertOrderInnerService;
        this.thirdSiteUserInnerService = thirdSiteUserInnerService;
        this.threadPoolTaskExecutor = threadPoolTaskExecutor;
        this.memberService = memberService;
        this.thirdGameRepo = thirdGameRepo;
        this.thirdPlatformConvertRecordInnerService = thirdPlatformConvertRecordInnerService;
        this.redisService = redisService;
    }

    /**
     * 查询三方支持的平台
     *
     * @param currencyEnum 币种
     * @param merchantId   商户ID
     * @return 平台列表
     */
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    @Override
    public List<ThirdSitePlatformItemDTO> listPlatform(CurrencyEnum currencyEnum, Long merchantId) {
        return thirdSitePlatformInnerService.listMerchantCategoryPlatform(merchantId, currencyEnum)
                .stream()
                // 先按热门排序
                .sorted(Comparator.comparing((ThirdSitePlatformItemDTO o)  -> o.getEnableHot().getCode()).reversed()
                        // 再按 sort 排序
                        .thenComparing(Comparator.comparingInt(ThirdSitePlatformItemDTO::getSort).reversed()
                                // 最后按平台名称排序
                                .thenComparing(ThirdSitePlatformItemDTO::getPlatformName)))
                .collect(Collectors.toList());
    }

    @Override
    public void getThirdSitePlatformList(IndexDataDTO indexDataDTO, InitDataVO initDataVO){
        Assert.notNull(indexDataDTO);
        Assert.notNull(indexDataDTO.getMerchantId());
        Assert.notNull(indexDataDTO.getCurrencyEnum());
        CThirdPlatformServiceImpl platformService = GrapeApplication.getBean(this.getClass());
        List<ThirdSitePlatformItemDTO> thirdSitePlatformItemDTOList = platformService.listPlatform(indexDataDTO.getCurrencyEnum(), indexDataDTO.getMerchantId());
        initDataVO.setThirdSitePlatformItemList(thirdSitePlatformItemDTOList);
    }

    @Override
    public void getHotThirdSiteGameList(IndexDataDTO indexDataDTO, InitDataVO initDataVO) {
        Assert.notNull(indexDataDTO);
        Assert.notNull(indexDataDTO.getMerchantId());
        Assert.notNull(indexDataDTO.getCurrencyEnum());

        CThirdPlatformServiceImpl platformService = GrapeApplication.getBean(this.getClass());
        List<ThirdSitePlatformItemDTO> thirdSitePlatformItemDTOList = platformService.listPlatform(indexDataDTO.getCurrencyEnum(), indexDataDTO.getMerchantId());

        Map<String, ThirdSitePlatformItemDTO> platformCategoryMap = thirdSitePlatformItemDTOList.stream()
                .filter(thirdSitePlatformItemDTO -> ThirdLoginTypeEnum.GAME.equals(thirdSitePlatformItemDTO.getLoginTypeEnum()))
                .collect(Collectors.toMap(k -> k.getPlatformCode() + "_" + k.getCategoryEnum().name(), Function.identity(), (v1, v2) -> v1));

        List<ThirdSiteGameDTO> games = thirdSiteGameInnerService.getMerchantHotGameList(indexDataDTO.getMerchantId(), indexDataDTO.getCurrencyEnum());

        Set<String> keySet = platformCategoryMap.keySet();
        log.debug("GetHotThirdSiteGameList:{}-{}-{}", JSONUtil.toJsonStr(indexDataDTO), JSONUtil.toJsonStr(games), StrUtil.join(StrUtil.COMMA, keySet));
        List<ThirdSiteGameDTO> hotThirdSiteGameDTOList = games.stream()
                .filter(i -> keySet.contains(i.getPlatformCode() + "_" + i.getGameCategoryName()))
                // filter supported currency
                .filter(i -> isSupportedCurrency(i.getSupportedCurrency(), indexDataDTO.getCurrencyEnum()))
                .sorted(Comparator.comparing((ThirdSiteGameDTO game) -> {
                            ThirdSitePlatformItemDTO platformItem = platformCategoryMap.get(game.getPlatformCode() + "_" + game.getGameCategoryName());
                            return platformItem.getSort();
                        }).reversed()
                        .thenComparing(ThirdSiteGameDTO::getSort).reversed())
                .collect(Collectors.toList());
        initDataVO.setHotThirdSiteGameList(hotThirdSiteGameDTOList);
    }


    /**
     * 查询平台游戏
     *
     * @param platformCode 平台编码
     * @param categoryEnum 游戏分类
     * @param currencyEnum 币种
     * @param merchantId   商户ID
     * @return 游戏列表
     */
    @Cacheable(cacheNames = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    @Override
    public List<ThirdSiteGameDTO> listGameByPlatform(String platformCode, GameCategoryEnum categoryEnum, CurrencyEnum currencyEnum, Long merchantId) {
        ThirdSitePlatformEntity sitePlatform = thirdSitePlatformInnerService.getByPlatformCode(platformCode, merchantId);
        if(Objects.isNull(sitePlatform) || EnableEnum.FALSE == sitePlatform.getEnableEnum()){
            return new ArrayList<>();
        }
        List<ThirdSiteGameDTO> games = thirdSiteGameInnerService.listGameByPlatform(platformCode, currencyEnum, categoryEnum, merchantId);
        games = games.stream().filter(it -> Objects.equals(it.getShowGame(), EnableEnum.TRUE)).collect(Collectors.toList());
        if (CurrencyEnum.ALL == currencyEnum) {
            return games;
        }
        // filter games by currency
        return games.stream().filter(i -> isSupportedCurrency(i.getSupportedCurrency(), currencyEnum))
                .sorted(Comparator.comparingInt((ThirdSiteGameDTO game) -> game.getEnableHot().getCode()).reversed()
                        .thenComparing(Comparator.comparing(ThirdSiteGameDTO::getSort).reversed()))
                .collect(Collectors.toList());
    }

    @Cacheable(cacheNames = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    @Override
    public List<ThirdSiteGameDTO> getAllGameList(Long merchantId, CurrencyEnum currencyEnum) {
        Assert.notNull(merchantId);
        Assert.notNull(currencyEnum);

        List<ThirdSitePlatformItemDTO> thirdSitePlatformItemDTOList = this.listPlatform(currencyEnum, merchantId);
        if(CollUtil.isEmpty(thirdSitePlatformItemDTOList)){
            return new ArrayList<>();
        }

        Map<String, ThirdSitePlatformItemDTO> platformCategoryMap = thirdSitePlatformItemDTOList.stream()
                .filter(thirdSitePlatformItemDTO -> ThirdLoginTypeEnum.GAME.equals(thirdSitePlatformItemDTO.getLoginTypeEnum()))
                .collect(Collectors.toMap(k -> k.getPlatformCode() + "_" + k.getCategoryEnum().name(), Function.identity(), (v1, v2) -> v1));

        Set<String> keySet = platformCategoryMap.keySet();
        List<ThirdSiteGameDTO> thirdGames = thirdSiteGameInnerService.getMerchantGameList(merchantId, currencyEnum);
        // filter supported currency
        return thirdGames.stream()
                .filter(i -> isSupportedCurrency(i.getSupportedCurrency(), currencyEnum))
                .filter(i -> keySet.contains(i.getPlatformCode() + "_" + i.getGameCategoryName()))
                .sorted(Comparator.comparingInt((ThirdSiteGameDTO game) -> game.getEnableHot().getCode()).reversed()
                        .thenComparing(Comparator.comparing((ThirdSiteGameDTO game) -> {
                            ThirdSitePlatformItemDTO platformItem = platformCategoryMap.get(game.getPlatformCode() + "_" + game.getGameCategoryName());
                            return platformItem.getSort();
                        }).reversed()
                        .thenComparing(ThirdSiteGameDTO::getSort).reversed()))
                .collect(Collectors.toList());
    }

    private boolean isSupportedCurrency(String supportedCurrency, CurrencyEnum currencyEnum) {
        List<String> currencies = Arrays.asList(supportedCurrency.split(","));

        return CollectionUtils.containsAny(currencies, CurrencyEnum.ALL.name(), currencyEnum.name());
    }

    /**
     * 获取游戏登录地址
     *
     * @param platformCode 平台编码
     * @param gameId       游戏ID
     * @param deviceEnum   设备类型
     * @param merchantId   商户ID
     * @return 游戏登录地址
     */
    @Override
    public String getGameLoginUrl(String platformCode, Long gameId, DeviceEnum deviceEnum, Long merchantId) {
        Long memberId = MemberTokenInfoUtil.getMemberId();
        Member member = memberService.getMemberById(memberId, merchantId);
        if (member.getMemberStateEnum() == MemberStateEnum.DISABLE) {
            throw new ApiException(CommonCode.MEMBER_DISABLED);
        }
        // check third platform
        checkThirdPlatformStatus(platformCode, member.getCurrencyEnum());
        // check third site platform
        checkThirdSitePlatformStatus(platformCode, merchantId);
        // check third game
        ThirdGameEntity gameEntity = checkThirdGame(gameId);
        // lobby game fill platform code
        final Long defaultLobbyGameId = -1L;
        if(defaultLobbyGameId.equals(gameId)){
            gameEntity.setPlatformCode(platformCode);
        }
        // 测试会玩直接返回试玩链接
        if(member.getMemberTypeEnum() == MemberTypeEnum.TEST_MEMBER){
            return thirdSiteGameInnerService.getDemoGameUrl(platformCode, gameEntity, deviceEnum, member);
        }
        // 查询/创建三方会员
        ThirdUserDTO thirdUserDTO = thirdSiteUserInnerService.queryThirdPlatformUser(member.getMerchantId(), platformCode, member.getId());
        if (Objects.isNull(thirdUserDTO)) {
            thirdUserDTO = thirdSiteUserInnerService.createThirdPlatformUser(member, platformCode);
        }

        // 单一钱包平台同步转账
        if (BridgeConstant.SEAMLESS_PLATFORMS.contains(platformCode)) {
            log.debug("post money block, platform: {}, merchantId: {}, memberId: {}", platformCode, merchantId, memberId);
            postMoney(platformCode, merchantId, member, true);
        }else{
            log.debug("post money async, platform: {}, merchantId: {}, memberId: {}", platformCode, merchantId, memberId);
            postMoney(platformCode, merchantId, member, false);
        }

        String gameUrl = thirdSiteGameInnerService.getGameLoginUrl(platformCode, gameEntity, deviceEnum, thirdUserDTO);
        // 保存商户玩家最近最多30个游戏
        this.cachePlayerLastGameRecord(merchantId, memberId, gameId);

        return gameUrl;
    }

    private void checkThirdSitePlatformStatus(String platformCode, Long merchantId) {
        ThirdSitePlatformEntity sitePlatformEntity = thirdSitePlatformInnerService.getByPlatformCode(platformCode, merchantId);
        if (Objects.isNull(sitePlatformEntity)) {
            throw new ApiException(CommonCode.THIRD_PLATFORM_NOT_EXISTS);
        }
        // 平台状态校验
        if (BooleanEnum.TRUE == sitePlatformEntity.getIsDel()
                || EnableEnum.FALSE == sitePlatformEntity.getEnableEnum()) {
            throw new ApiException(CommonCode.THIRD_SITE_PLATFORM_DISABLED);
        }
    }


    private ThirdGameEntity checkThirdGame(Long gameId) {
        final Long lobbyLogin = -1L;
        if (lobbyLogin.equals(gameId)) {
            return new ThirdGameEntity();
        }
        ThirdGameEntity game = thirdGameRepo.getByIdCache(gameId);
        if (Objects.isNull(game) || BooleanEnum.TRUE == game.getIsDel()) {
            throw new ApiException(CommonCode.THIRD_GAME_NOT_EXISTS);
        }
        if (BooleanEnum.TRUE == game.getIsMaintain()) {
            throw new ApiException(CommonCode.THIRD_GAME_IS_MAINTAIN);
        }
        return game;
    }

    /**
     * 资金归集
     *
     * @param memberId   会员ID
     * @param merchantId 商户ID
     */
    @Override
    public void collectMoney(Long memberId, Long merchantId) {
        String frequencyControlKey = ThirdRedisConstants.COLLECT_MONEY_LOCK + merchantId + ":" + memberId;
        redisService.frequencyControl(frequencyControlKey, ThirdRedisConstants.COLLECT_MONEY_LOCK_LOCK_SECONDS);

        List<ThirdUserDTO> thirdUsers = thirdSiteUserInnerService.listActiveThirdUser(memberId, merchantId);
        // 过滤出需要资金归集的三方平台用户
        List<ThirdUserDTO> collectUsers = filterNeedCollectThirdUser(thirdUsers);
        if(CollUtil.isEmpty(collectUsers)){
            log.debug("collectMoney stopped, third platform user is empty, memberId:{}, merchantId: {}", memberId, merchantId);
            return;
        }
        try {
            transferLock(memberId, "collectMoney");

            final String traceId = MDC.get(Constants.MDC_TRACE_ID_KEY);
            List<CompletableFuture<Void>> steps = new ArrayList<>();
            for (ThirdUserDTO thirdUser : collectUsers) {
                CompletableFuture<Void> step = supplyThirdToMainTask(thirdUser, traceId);
                steps.add(step);
            }
            CompletableFuture<Void> allStep = CompletableFuture.allOf(steps.toArray(new CompletableFuture[0]));
            allStep.join();
            log.info("collect money finished, member: {}, merchant: {}", memberId, merchantId);
        } finally {
            unlockTransfer(memberId, "collectMoney");
        }
    }

    private void collectMoneyAndAutoConvert(String platformCode, Member member, boolean block) {

        List<ThirdPlatformConvertRecord> convertRecords = thirdPlatformConvertRecordInnerService.listConvertRecords(member.getMerchantId(), member.getId())
                .stream()
                .filter(i -> !StringUtils.equals(platformCode, i.getPlatformCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(convertRecords)) {
            thirdConvertOrderInnerService.transferMainWalletToThird(platformCode, member);
            return;
        }
        final String traceId = MDC.get(Constants.MDC_TRACE_ID_KEY);
        List<CompletableFuture<Void>> steps = new ArrayList<>();
        List<String> platformCodeList = convertRecords.stream().map(ThirdPlatformConvertRecord::getPlatformCode).collect(Collectors.toList());
        List<ThirdUserDTO> thirdUsers = thirdSiteUserInnerService.listActiveThirdUser(member.getId(), member.getMerchantId(), platformCodeList);
        List<ThirdUserDTO> collectThirdUser = filterNeedCollectThirdUser(thirdUsers);
        if (CollUtil.isEmpty(collectThirdUser)) {
            thirdConvertOrderInnerService.transferMainWalletToThird(platformCode, member);
            log.debug("collectThirdUser is empty, platformCode: {}, memberId: {}", platformCode, member.getId());
            return;
        }
        for (ThirdUserDTO thirdUserDTO : collectThirdUser) {
            CompletableFuture<Void> step = supplyThirdToMainTask(thirdUserDTO, traceId);
            steps.add(step);
        }
        CompletableFuture<Void> completableFuture = CompletableFuture.allOf(steps.toArray(new CompletableFuture[0]))
                .thenAccept(v -> {
                    MDC.put(Constants.MDC_TRACE_ID_KEY, traceId);
                    try {
                        thirdConvertOrderInnerService.transferMainWalletToThird(platformCode, member);
                    } catch (Exception e) {
                        log.error("transfer main to third failed, platformCode: {}, member: {}, merchant: {}", platformCode, member.getId(), member.getMerchantId(), e);
                    } finally {
                        MDC.clear();
                    }
                });
        if (block) {
            log.debug("wait form post money, platformCode: {}, memberId: {}", platformCode, member.getId());
            completableFuture.join();
        }
    }

    private CompletableFuture<Void> supplyThirdToMainTask(ThirdUserDTO thirdUserDTO, String traceId) {
        final String platformTraceId = traceId + "_" + thirdUserDTO.getPlatformCode();
        return CompletableFuture.supplyAsync(() -> {
            try {
                MDC.put(Constants.MDC_TRACE_ID_KEY, platformTraceId);
                thirdConvertOrderInnerService.transferThirdToMainWallet(thirdUserDTO);
            } catch (Exception e) {
                log.error("transfer third to main failed, thirdUserDTO:{}", thirdUserDTO, e);
            } finally {
                MDC.clear();
            }
            return null;
        }, threadPoolTaskExecutor);
    }

    private List<ThirdUserDTO> filterNeedCollectThirdUser(List<ThirdUserDTO> thirdUsers) {
        log.debug("filter need collect user, list: {}", JacksonUtil.toJSONString(thirdUsers));
        if (CollUtil.isEmpty(thirdUsers)) {
            return thirdUsers;
        }
        return thirdUsers.stream().filter(o -> EnableEnum.TRUE.equals(o.getEnableTransfer())).collect(Collectors.toList());
    }

    private void postMoney(String platformCode, Long merchantId, Member member, boolean block) {
        try {
            // 再次加锁
            transferLock(member.getId(), "postMoney");
            // collect money for auto convert
            collectMoneyAndAutoConvert(platformCode, member, block);
        } catch (Exception e) {
            log.error("exception occurred while transfer main to third, platform: {}, merchant: {}, member: {}", platformCode, merchantId, member.getId(), e);
            throw new ApiException(CommonCode.FAILED);
        } finally {
            unlockTransfer(member.getId(), "postMoney");
        }
    }


    private void transferLock(Long memberId, String msg) {
        String lockKey = ThirdRedisConstants.CONVERT_MEMBER_LOCK + memberId;
        log.debug("add transfer lock, key: {}, msg: {}", lockKey, msg);
        if (!redisService.setIfAbsent(lockKey, LocalDateTime.now(), ThirdRedisConstants.CONVERT_MEMBER_EXPIRE_LOCK_SECONDS)) {
            log.error("add transfer lock failed, because the member is transferring, key: {}, msg: {}", lockKey, msg);
            throw new ApiException(CommonCode.CONVERT_MEMBER_TRANSFERRING);
        }
    }

    private void unlockTransfer(Long memberId, String msg) {
        String lockKey = ThirdRedisConstants.CONVERT_MEMBER_LOCK + memberId;
        log.debug("unlock transfer, lockKey: {}, msg: {}", lockKey, msg);
        redisService.del(lockKey);
    }

    private void  checkThirdPlatformStatus(String platformCode, CurrencyEnum currencyEnum) {
        ThirdPlatformBasicInfoDTO platform = ThirdPlatformLocalCacheUtil.getThirdPlatformBasicInfo(platformCode);
        if (Objects.isNull(platform)) {
            throw new ApiException(CommonCode.THIRD_PLATFORM_NOT_EXISTS);
        }
        // 平台状态校验
        if (BooleanEnum.TRUE == platform.getIsDel()
                || EnableEnum.FALSE == platform.getEnableEnum()) {
            throw new ApiException(CommonCode.THIRD_PLATFORM_ILLEGAL_STATUS);
        }
        // 获取平台币种维护状态
        ThirdPlatformStatusDTO status = ThirdPlatformLocalCacheUtil.getThirdPlatformStatusByCurrency(platformCode, currencyEnum);
        if (BooleanEnum.TRUE == status.getIsMaintain()) {
            throw new ApiException(CommonCode.THIRD_PLATFORM_IS_MAINTAIN);
        }
        if (EnableEnum.FALSE.equals(status.getEnableTransfer())){
            throw new ApiException(CommonCode.THIRD_PLATFORM_CANNOT_TRANSFER);
        }
    }

    @Override
    public List<PlayerLastRecordGameDTO> getPlayerLastGameRecord(Long merchantId) {
        if (ObjectUtil.isNull(merchantId)) {
            return Collections.emptyList();
        }
        Long memberId = MemberTokenInfoUtil.getMemberId();
        String key = StrUtil.join(StrPool.COLON, ThirdRedisConstants.COLLECT_PLAYER_RECENT_GAME, Convert.toStr(merchantId), Convert.toStr(memberId));
        Boolean exist = this.redisService.hasKey(key);
        if (!exist) {
            return Collections.emptyList();
        }
        String value = Convert.toStr(this.redisService.get(key));
        List<String> gameIdList = StrUtil.split(value, StrPool.COLON);
        log.debug("获取玩家最近的游戏记录:{}-{}-{}", key, this.redisService.getExpire(key), value);

        List<Long> gameIds = CollStreamUtil.toList(gameIdList, Convert::toLong);
        List<ThirdGameEntity> gameList = this.thirdGameRepo.getLastGameRecord(gameIds, merchantId);
        Map<Long, ThirdGameEntity> gameMap = CollStreamUtil.toMap(gameList, ThirdGameEntity::getId, Function.identity());

        int size = gameIds.size();
        List<PlayerLastRecordGameDTO> result = Lists.newArrayListWithExpectedSize(size);
        for (int index = size - 1; index >= 0 ; index--) {
            ThirdGameEntity thirdGameEntity = gameMap.get(gameIds.get(index));
            if (ObjectUtil.isNull(thirdGameEntity)) {
                continue;
            }
            PlayerLastRecordGameDTO record = BeanUtil.copyProperties(thirdGameEntity, PlayerLastRecordGameDTO.class);
            result.add(record);
        }
        return result;
    }

    @Override
    public void transOutThirdMoney(String platformCode, Long memberId, Long merchantId) {
        log.debug("trans out third platform balance, platform: {}, member: {}", platformCode, memberId);
        if (StringUtils.isBlank(platformCode)) {
            quickCollectMoney(memberId, merchantId);
            return;
        }
        Member member = memberService.getMemberById(memberId, merchantId);
        try {
            // 加锁失败
            final int retryCount = 10;
            if (!tryAddTransferLock(memberId, retryCount)) {
                log.warn("try add transfer lock failed, member: {}, retryCount: {}", memberId, retryCount);
                return;
            }
            List<ThirdUserDTO> thirdUsers = thirdSiteUserInnerService.listActiveThirdUser(member.getId(), member.getMerchantId(), Collections.singletonList(platformCode));
            List<ThirdUserDTO> collectThirdUser = filterNeedCollectThirdUser(thirdUsers);
            if (CollUtil.isEmpty(collectThirdUser)) {
                log.debug("collectThirdUser is empty, platformCode: {}, memberId: {}", platformCode, member.getId());
                return;
            }
            final String traceId = MDC.get(Constants.MDC_TRACE_ID_KEY);
            supplyThirdToMainTask(collectThirdUser.get(0), traceId);
        } catch (Exception e) {
            log.error("trans out third platform balance error, member: {}, platform: {}", member, platformCode, e);
        } finally {
            unlockTransfer(memberId, "transOutThirdMoney");
        }
    }

    @Override
    public void quickCollectMoney(Long memberId, Long merchantId) {
        log.debug("quick collect third platform balance, member: {}, merchant: {}", memberId, merchantId);
        Member member = memberService.getMemberById(memberId, merchantId);
        try {
            // 加锁失败
            final int retryCount = 10;
            if (!tryAddTransferLock(memberId, retryCount)) {
                log.warn("try add transfer lock failed, member: {}, retryCount: {}", memberId, retryCount);
                return;
            }
            List<String> convertPlatforms = thirdPlatformConvertRecordInnerService.listConvertRecords(member.getMerchantId(), member.getId())
                    .stream()
                    .map(ThirdPlatformConvertRecord::getPlatformCode)
                    .collect(Collectors.toList());
            List<ThirdUserDTO> thirdUsers = thirdSiteUserInnerService.listActiveThirdUser(member.getId(), member.getMerchantId(), convertPlatforms);
            List<ThirdUserDTO> collectThirdUser = filterNeedCollectThirdUser(thirdUsers);
            if (CollUtil.isEmpty(collectThirdUser)) {
                log.debug("collectThirdUser is empty,  memberId: {}", member.getId());
                return;
            }
            final String traceId = MDC.get(Constants.MDC_TRACE_ID_KEY);
            List<CompletableFuture<Void>> steps = new ArrayList<>();
            for (ThirdUserDTO thirdUserDTO : collectThirdUser) {
                CompletableFuture<Void> step = supplyThirdToMainTask(thirdUserDTO, traceId);
                steps.add(step);
            }
            CompletableFuture.allOf(steps.toArray(new CompletableFuture[0]))
                    .thenAccept(v -> {
                        MDC.put(Constants.MDC_TRACE_ID_KEY, traceId);
                        log.info("quick collect money success, memberId: {}", member.getId());
                    });
        } catch (Exception e) {
            log.error("quick collect third platform balance error, member: {}", member, e);
        } finally {
            unlockTransfer(memberId, "quickCollectMoney");
        }
    }

    private boolean tryAddTransferLock(Long memberId, int retryCount) throws Exception {
        String lockKey = ThirdRedisConstants.CONVERT_MEMBER_LOCK + memberId;
        do {
            if (redisService.setIfAbsent(lockKey, LocalDateTime.now(), ThirdRedisConstants.CONVERT_MEMBER_EXPIRE_LOCK_SECONDS)) {
                return true;
            }
            TimeUnit.MILLISECONDS.sleep(200);
            retryCount--;
        }
        while (retryCount > 0);

        return false;
    }

    /**
     * 缓存商户玩家最近游戏记录(最多30条)
     *
     * @param merchantId 商户id
     * @param memberId 商户id
     * @param gameId 游戏id
     */
    private void cachePlayerLastGameRecord(Long merchantId, Long memberId, Long gameId) {
        try {
            log.debug("缓存商户玩家最近游戏记录:入参:{}-{}-{}", merchantId, memberId, gameId);
            // 只缓存SLOT
            Long lobbyLogin = -1L;
            if (lobbyLogin.equals(gameId)) {
                return;
            }
            ThirdGameEntity game = this.thirdGameRepo.getByIdCache(gameId);
            boolean isRight = GameCategoryEnum.SLOT == game.getGameCategoryEnum()
                    || GameCategoryEnum.MINI == game.getGameCategoryEnum()
                    || GameCategoryEnum.FISH == game.getGameCategoryEnum();
            if (!isRight) {
                return;
            }
            boolean isNotEmpty = ObjectUtil.isAllNotEmpty(merchantId, memberId, gameId);
            if (!isNotEmpty) {
                return;
            }
            // 存在?
            int maxGameSize = 30, expiredDays = 7;
            String key = StrUtil.join(StrPool.COLON, ThirdRedisConstants.COLLECT_PLAYER_RECENT_GAME, Convert.toStr(merchantId), Convert.toStr(memberId));
            Boolean exist = this.redisService.hasKey(key);
            if (!exist) {
                this.redisService.set(key, Convert.toStr(gameId), expiredDays, TimeUnit.DAYS);
                return;
            }
            // 取出
            String value = Convert.toStr(this.redisService.get(key));
            List<String> gameIdList = StrUtil.split(value, StrPool.COLON);
            // 重复?
            String gameIdString = Convert.toStr(gameId);
            if (gameIdList.contains(gameIdString)) {
                CollUtil.removeAny(gameIdList, gameIdString);
            }
            gameIdList.add(gameIdString);
            // 超过30条记录?
            int size = CollUtil.size(gameIdList);
            if (size < maxGameSize) {
                value = StrUtil.join(StrPool.COLON, gameIdList);
                this.redisService.set(key, value, expiredDays, TimeUnit.DAYS);
                return;
            }
            // 拿到最新30条进行复写
            List<String> nowGameIdList = CollUtil.sub(CollUtil.reverseNew(gameIdList), 0, maxGameSize);
            value = StrUtil.join(StrPool.COLON, CollUtil.reverseNew(nowGameIdList));
            this.redisService.set(key, value, expiredDays, TimeUnit.DAYS);
            log.debug("缓存商户玩家最近游戏记录:数据:{}-{}", key, value);
        } catch (Exception ex) {
            // 处理异常，避免影响主流程
            log.warn(StrUtil.format("缓存商户玩家最近游戏记录:{}-{}-{}", memberId, memberId, gameId), ex);
        }
    }
}
