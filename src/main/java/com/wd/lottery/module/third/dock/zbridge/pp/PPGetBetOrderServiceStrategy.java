package com.wd.lottery.module.third.dock.zbridge.pp;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.StrUtil;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.pp.common.PPApiEnum;
import com.wd.lottery.module.third.dock.zbridge.pp.common.PPHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.pp.common.PPRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.pp.res.BetOrderData;
import com.wd.lottery.module.third.dock.zbridge.pp.res.PPGetGameDetailRes;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Description: pp game service strategy
 *
 * <p> Created on 2024/5/23.
 *
 * <AUTHOR>
 * @version 0.2
 */
@Slf4j
@Component(BridgeConstant.PP_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class PPGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {


    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        PPRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), PPRequestConfig.class);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(flag.getPlatformCode());
        PPHttpRequestTemplate requestTemplate = new PPHttpRequestTemplate(requestConfig);

        // 自动拉单
        if(!flag.isManual()) {
            String lastFlag = flag.getLastFlag();
            if ("0".equals(lastFlag)) {
                lastFlag = flag.getBegin().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() + "";
                flag.setLastFlag(lastFlag);
            }
            String response = getBetOrderResponse(requestTemplate, requestConfig, lastFlag);
            log.debug("PP:游戏注单响应[自动]:[{}]-{}", lastFlag, response);
            return this.analyzeBetOrder(true, flag, response);
        }

        // 手动拉单
        boolean isFirst = StrUtil.isEmpty(flag.getRemark());
        Long timePoint = isFirst ? flag.getBegin().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : Convert.toLong(flag.getRemark());
        String response = this.getBetOrderResponse(requestTemplate, requestConfig, Convert.toStr(timePoint));
        log.debug("PP:游戏注单响应[手动]:[{}]-{}", timePoint, response);
        return this.analyzeBetOrder(true, flag, response);
    }

    private List<DockBetOrder> analyzeBetOrder(boolean isAuto, BetOrderPullFlag flag, String response) {
        try (BufferedReader br = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(response.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8))) {
            Long nextTimePoint = parseNextTimePoint(br.readLine());

            if (isAuto) {
                // update next timePoint
                flag.setLastFlag(nextTimePoint + "");
            } else {
                flag.setRemark(nextTimePoint + "");
            }

            // update finish flag
            long endPoint = flag.getEnd().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            flag.setFinished(nextTimePoint >= endPoint);

            List<BetOrderData> betOrderData = parseResponseOrderData(br);

            return toDockBetOrderList(betOrderData, flag.getPlatformCode());

        } catch (Exception e) {
            log.error("get response reader error, response: {}", response, e);
            throw new IllegalStateException(e);
        }
    }


    private List<DockBetOrder> toDockBetOrderList(List<BetOrderData> betOrderData, String platformCode) {
        if (CollectionUtils.isEmpty(betOrderData)) {
            return Collections.emptyList();
        }
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);

        String thirdZone = platformConfig.getPlatformTimeZone();

        String orderTimePattern = platformConfig.getOrderTimePattern();
        if (StringUtils.isBlank(orderTimePattern)) {
            orderTimePattern = "yyyy-MM-dd HH:mm:ss";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(orderTimePattern);

        List<DockBetOrder> resultList = new ArrayList<>(betOrderData.size());

        for (BetOrderData order : betOrderData) {
            DockBetOrder dockOrder = new DockBetOrder();
            dockOrder.setThirdUserName(order.getExtPlayerID());
            dockOrder.setPlatformCode(platformCode);
            dockOrder.setOrderNum(order.getPlaySessionID());
            dockOrder.setOrderNumParent(order.getParentSessionID());
            dockOrder.setGameId(order.getGameID());

            dockOrder.setBetMoney(toPlatformMoney(order.getBet()));
            dockOrder.setWinMoney(toPlatformMoney(order.getWin()));
            dockOrder.setValidBetMoney(toPlatformMoney(order.getBet()));

            LocalDateTime thirdBet = LocalDateTime.parse(order.getStartDate(), formatter);

            dockOrder.setOrderTime(thirdBet.atZone(ZoneId.of(thirdZone)).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime());

            BigDecimal jackpot = order.getJackpot();
            if (Objects.nonNull(jackpot)) {
                dockOrder.setJackpot(toPlatformMoney(jackpot));
            }

            resultList.add(dockOrder);
        }

        return resultList;

    }


    private List<BetOrderData> parseResponseOrderData(BufferedReader br) {

        List<BetOrderData> list = new ArrayList<>(100);

        CsvReadConfig csvReadConfig = CsvReadConfig.defaultConfig();
        csvReadConfig.setContainsHeader(true);
        CsvData csvData = CsvUtil.getReader(csvReadConfig).read(br);
        List<String> header = csvData.getHeader();
        List<CsvRow> rows = csvData.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return list;
        }

        rows.forEach(row -> {
            Map<String, String> map = new HashMap<>();
            for (int i = 0; i < header.size(); i++) {
                map.put(header.get(i), row.get(i));
            }
            // 只需要已完成的订单
            if (Objects.equals(map.get("status"), "C")) {
                list.add(BeanUtil.toBeanIgnoreError(map, BetOrderData.class));
            }
        });
        return list;
    }

    private Long parseNextTimePoint(String line) {

        try {
            if (StringUtils.isEmpty(line) || !line.startsWith("timepoint=")) {
                throw new IllegalStateException();
            }
            String tp = line.split("=")[1];
            return Long.parseLong(tp);
        } catch (Exception e) {
            log.error("parse response timePoint error", e);
            throw new IllegalStateException(e);
        }
    }

    private String getBetOrderResponse(PPHttpRequestTemplate requestTemplate, PPRequestConfig platformConfig, String timePoint) {
        return requestTemplate
                .toGET()
                // 拉取注单的apiUrl单独配置
                .api(PPApiEnum.PULL_BET_ORDER.getPath())
                .addParameter("login", platformConfig.getUsername())
                .addParameter("password", platformConfig.getSecret())
                .addParameter("timepoint", timePoint)
                .addParameter("dataType", "RNG")
                .toCustomObject(String.class);
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        PPRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), PPRequestConfig.class);
        PPHttpRequestTemplate requestTemplate = new PPHttpRequestTemplate(requestConfig);
        try {
            PPGetGameDetailRes res = requestTemplate.toPOST()
                    .api(PPApiEnum.GET_ORDER_DETAIL.getPath())
                    .addParameter("playerId", dto.getThirdUserName())
                    .addParameter("gameId", dto.getThirdGameId())
                    .addParameter("roundId", dto.getOrderNo())
                    .toBeanAndCall(PPGetGameDetailRes.class);

            return res.getUrl();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }
}
