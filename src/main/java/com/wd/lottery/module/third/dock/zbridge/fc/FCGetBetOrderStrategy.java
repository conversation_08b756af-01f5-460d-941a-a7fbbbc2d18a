package com.wd.lottery.module.third.dock.zbridge.fc;

import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.fc.common.FCApiEnum;
import com.wd.lottery.module.third.dock.zbridge.fc.common.FCHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.fc.common.FCRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.fc.res.FCBetOrderRes;
import com.wd.lottery.module.third.dock.zbridge.fc.res.FCGameDetailUrlRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component(BridgeConstant.FC_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
@Slf4j
public class FCGetBetOrderStrategy extends AbstractGetBetOrderStrategy {


    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        log.debug("FC requestThirdOrder dto:{} ", flag);
        String platformCode = flag.getPlatformCode();
        CurrencyEnum currencyEnum = flag.getCurrencyEnum();
        FCRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, currencyEnum, FCRequestConfig.class);

        LocalDateTime towHoursAgo = LocalDateTime.now().withSecond(0).plusHours(-2);
        LocalDateTime begin = flag.getBegin().withSecond(0);
        LocalDateTime end = flag.getEnd().withSecond(0);
        if (begin.isAfter(towHoursAgo)) {
            return getLatestOrder(requestConfig, flag);
        }
        if (end.isBefore(towHoursAgo)) {
            return getHistoryOrder(requestConfig, flag);
        }
        return getMixOrder(requestConfig, flag);
    }

    private List<DockBetOrder> getMixOrder(FCRequestConfig requestConfig, BetOrderPullFlag flag) {
        List<FCBetOrderRes.Record> list = new ArrayList<>(100);
        LocalDateTime towHoursAgo = LocalDateTime.now().withSecond(0).plusHours(-2);

        FCHttpRequestTemplate requestTemplate = new FCHttpRequestTemplate(requestConfig, flag.getCurrencyEnum());
        // his
        List<FCBetOrderRes.Record> hisList = getThirdOrder(requestTemplate, flag.getBegin(), towHoursAgo, flag.getPlatformCode(), FCApiEnum.BET_ORDER_2_HOUR.getPath());
        // latest
        List<FCBetOrderRes.Record> latestList = getThirdOrder(requestTemplate, towHoursAgo, flag.getEnd(), flag.getPlatformCode(), FCApiEnum.BET_ORDER.getPath());
        if (CollectionUtils.isNotEmpty(hisList)) {
            list.addAll(hisList);
        }
        if (CollectionUtils.isNotEmpty(latestList)) {
            list.addAll(latestList);
        }
        flag.setFinished(true);
        return toDockBetOrderList(list, flag, requestConfig);
    }

    private List<DockBetOrder> getHistoryOrder(FCRequestConfig requestConfig, BetOrderPullFlag flag) {
        FCHttpRequestTemplate requestTemplate = new FCHttpRequestTemplate(requestConfig, flag.getCurrencyEnum());
        List<FCBetOrderRes.Record> list = getThirdOrder(requestTemplate, flag.getBegin(), flag.getEnd(), flag.getPlatformCode(), FCApiEnum.BET_ORDER_2_HOUR.getPath());
        flag.setFinished(true);
        return toDockBetOrderList(list, flag, requestConfig);
    }

    private List<DockBetOrder> getLatestOrder(FCRequestConfig requestConfig, BetOrderPullFlag flag) {
        FCHttpRequestTemplate requestTemplate = new FCHttpRequestTemplate(requestConfig, flag.getCurrencyEnum());
        List<FCBetOrderRes.Record> list = getThirdOrder(requestTemplate, flag.getBegin(), flag.getEnd(), flag.getPlatformCode(), FCApiEnum.BET_ORDER.getPath());
        flag.setFinished(true);
        return toDockBetOrderList(list, flag, requestConfig);
    }

    private List<DockBetOrder> toDockBetOrderList(List<FCBetOrderRes.Record> list, BetOrderPullFlag flag, FCRequestConfig requestConfig) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        final String platformCode = flag.getPlatformCode();
        // 币种兑换比例
        BigDecimal rate = ThirdPlatformMappingConverter.getCurrencyExchangeRate(requestConfig, flag.getCurrencyEnum());

        List<DockBetOrder> resultList = new ArrayList<>(list.size());
        list.stream().filter(Objects::nonNull).forEach(obj -> {
            DockBetOrder dockBetOrder = new DockBetOrder();
            dockBetOrder.setThirdUserName(obj.getAccount());
            dockBetOrder.setPlatformCode(platformCode);
            dockBetOrder.setOrderNum(obj.getRecordID());
            dockBetOrder.setOrderNumParent(obj.getRecordID());
            dockBetOrder.setGameId(String.valueOf(obj.getGameID()));
            dockBetOrder.setBetMoney(ThirdPlatformMappingConverter.toSysMoneyLong(obj.getBet(), rate));
            dockBetOrder.setWinMoney(ThirdPlatformMappingConverter.toSysMoneyLong(obj.getPrize(), rate));

            LocalDateTime dateTimeWithZone = ThirdPlatformMappingConverter.parseThirdOrderTime(obj.getBdate(), platformCode);
            dockBetOrder.setOrderTime(dateTimeWithZone);

            //有效投注 =  if (投注金额 == 派奖金额)  0 else 投注金额
            dockBetOrder.setValidBetMoney(dockBetOrder.getBetMoney() == dockBetOrder.getWinMoney() ? 0 : dockBetOrder.getBetMoney());
            resultList.add(dockBetOrder);
        });
        return resultList;
    }

    private List<FCBetOrderRes.Record> getThirdOrder(FCHttpRequestTemplate requestTemplate, LocalDateTime begin, LocalDateTime end, String platformCode, String apiPath) {

        String startTime = ThirdPlatformMappingConverter.parsePullOrderParamTime(begin.withSecond(0), platformCode);
        String endTime = ThirdPlatformMappingConverter.parsePullOrderParamTime(end.withSecond(0), platformCode);

        log.debug("request FC bet order, begin: {}, end: {}", startTime, endTime);
        FCBetOrderRes res = requestTemplate.api(apiPath)
                .toPOST()
                .addParameter("StartDate", startTime)
                .addParameter("EndDate", endTime)
                .toBeanAndCall(FCBetOrderRes.class, false);
        return res.getRecords();
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        FCRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), FCRequestConfig.class);
        FCHttpRequestTemplate requestTemplate = new FCHttpRequestTemplate(requestConfig, dto.getCurrencyEnum());
        try {
            FCGameDetailUrlRes res = requestTemplate.api(FCApiEnum.GET_PLAYER_REPORT.getPath())
                    .toPOST()
                    .addParameter("MemberAccount", dto.getThirdUserName())
                    .addParameter("RecordID", dto.getOrderNo())
                    .toBeanAndCall(FCGameDetailUrlRes.class);
            return res.getUrl();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }
}
