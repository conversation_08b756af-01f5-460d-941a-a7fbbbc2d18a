package com.wd.lottery.module.third.repo.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dto.ThirdSiteGameDTO;
import com.wd.lottery.module.third.entity.ThirdSiteGameEntity;
import com.wd.lottery.module.third.mapper.ThirdSiteGameMapper;
import com.wd.lottery.module.third.param.ThirdSiteGameQueryParam;
import com.wd.lottery.module.third.repo.ThirdSiteGameRepo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <p> Created on 2024/5/29.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Component
public class ThirdSiteGameRepoImpl extends ServiceImpl<ThirdSiteGameMapper, ThirdSiteGameEntity> implements ThirdSiteGameRepo {

    @Value("${third.rtp.platformCodes:OMGPG,OMGJILI}")
    private String platformCodes;

    @Override
    public List<ThirdSiteGameDTO> listGameByPlatform(String platformCode, CurrencyEnum currencyEnum, GameCategoryEnum categoryEnum, Long merchantId) {
        ThirdSiteGameQueryParam param = new ThirdSiteGameQueryParam();
        param.setPlatformCode(platformCode);
        param.setCategory(categoryEnum);
        param.setMerchantId(merchantId);

        return this.getBaseMapper().getGameByPlatform(param);
    }

    @Override
    public Page<ThirdSiteGameDTO> getThirdSiteGamePage(ThirdSiteGameQueryParam param){

        Page<ThirdSiteGameDTO> page = new Page<>(param.getCurrent(), param.getSize());

        this.getBaseMapper().getThirdSiteGamePage(page, param);
        List<String> platformCodeList = Arrays.stream(platformCodes.split(",")).map(String::trim).collect(Collectors.toList());
        Optional.ofNullable(page.getRecords())
                .ifPresent(rows -> rows.forEach(i -> i.setIsRTP(platformCodeList.contains(i.getPlatformCode())? BooleanEnum.TRUE:BooleanEnum.FALSE)));

        return page;
    }

    @Override
    public List<ThirdSiteGameDTO> getMerchantGameList(Long merchantId, CurrencyEnum currencyEnum) {
        return this.getBaseMapper().getMerchantGameList(merchantId, currencyEnum);
    }

    @Override
    public List<ThirdSiteGameDTO> getMerchantHotGameList(Long merchantId, CurrencyEnum currencyEnum) {
        return this.getBaseMapper().getMerchantHotGameList(merchantId, currencyEnum);
    }
}
