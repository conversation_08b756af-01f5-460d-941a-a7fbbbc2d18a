package com.wd.lottery.module.third.dock.zbridge.ns;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.util.EncryptUtil;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.ns.common.NSAPIEnum;
import com.wd.lottery.module.third.dock.zbridge.ns.common.NSHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.ns.common.NSRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.ns.res.NSResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 游戏注单
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.NS_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class NSGetBetOrderStrategy extends AbstractGetBetOrderStrategy {

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag betOrderPullFlag) throws Exception {
        log.debug("NS:游戏注单:{}", JSONUtil.toJsonStr(betOrderPullFlag));
        NSRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(betOrderPullFlag.getPlatformCode(), betOrderPullFlag.getCurrencyEnum(), NSRequestConfig.class);
        NSHttpRequestTemplate requestTemplate = new NSHttpRequestTemplate(requestConfig, NSAPIEnum.GET_BET_ORDER.getPath(), true);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(betOrderPullFlag.getPlatformCode());
        // 拉单时间
        String timeZone = configJson.getPlatformTimeZone();
        DateTimeFormatter paramPattern = DateTimeFormatter.ofPattern(configJson.getRequestTimePattern());
        String start = betOrderPullFlag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
        String end = betOrderPullFlag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of(timeZone)).format(paramPattern);
        // 拉取注单
        int pageNo = betOrderPullFlag.getIndex();
        BetHistory response = requestTemplate.toPOST()
                .addParameter("beginDate", start)
                .addParameter("endDate", end)
                .addParameter("pageIndex", Convert.toStr(pageNo))
                .toBeanAndCall(BetHistory.class);
        log.debug("NS:游戏注单响应:{}", JSONUtil.toJsonStr(response));
        Integer totalIndex = response.getPageCount();
        List<BetHistory.BetInfo> betOrders = response.getList();
        if (CollectionUtils.isEmpty(betOrders) || pageNo >= totalIndex) {
            betOrderPullFlag.setFinished(true);
        }
        List<DockBetOrder> dockBetOrders = CollStreamUtil.toList(betOrders, betOrder -> this.toDockerOrder(betOrderPullFlag.getPlatformCode(), configJson, betOrder));
        betOrderPullFlag.setIndex(pageNo + 1);
        return dockBetOrders;
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dockGetBetOrderDetail) {
        log.debug("NS:游戏注单详情:{}", JSONUtil.toJsonStr(dockGetBetOrderDetail));
        NSRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dockGetBetOrderDetail.getPlatformCode(), dockGetBetOrderDetail.getCurrencyEnum(), NSRequestConfig.class);
        if (StrUtil.isEmpty(requestConfig.getSecretKey())) {
            return StrUtil.EMPTY;
        }
        String betOrderDetailUrl = requestConfig.getBetOrderDetailUrl();
        boolean isSatisfied = StrUtil.endWithIgnoreCase(betOrderDetailUrl, "/");
        if (!isSatisfied) {
            betOrderDetailUrl = betOrderDetailUrl + "/";
        }
        String orderDetailUrl = betOrderDetailUrl + NSAPIEnum.GET_BET_ORDER_DETAIL.getPath();
        String token = EncryptUtil.md5(dockGetBetOrderDetail.getOrderNo() + "|" + requestConfig.getSecretKey());
        String gameUrl = String.format(orderDetailUrl, requestConfig.getMerchantCode(), token, dockGetBetOrderDetail.getOrderNo());
        log.debug("NS:游戏注单详情URL:{}", gameUrl);
        return gameUrl;
    }

    private DockBetOrder toDockerOrder(String platformCode, ThirdPlatformConfigDTO configJson, BetHistory.BetInfo betOrder) {
        DockBetOrder dockBetOrder = new DockBetOrder();
        dockBetOrder.setPlatformCode(platformCode);
        dockBetOrder.setOrderNum(betOrder.getTicketId());
        dockBetOrder.setOrderNumParent(betOrder.getTicketId());
        dockBetOrder.setGameId(betOrder.getGameCode());
        dockBetOrder.setThirdUserName(betOrder.getAcctId().toLowerCase());

        // BigDecimal moneyUnit = ObjectUtil.isNull(configJson.getMoneyUnit()) ? new BigDecimal(100) : new BigDecimal(configJson.getMoneyUnit());
        dockBetOrder.setBetMoney(super.toPlatformMoney(betOrder.getBetAmount()));
        // 中奖金额 = 投注金额 + 盈亏金额
        dockBetOrder.setWinMoney(super.toPlatformMoney(betOrder.getWinLoss().add(betOrder.getBetAmount())));
        dockBetOrder.setValidBetMoney(super.toPlatformMoney(betOrder.getBetAmount()));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(configJson.getOrderTimePattern());
        if (ObjectUtil.isNotNull(betOrder.getTicketTime())) {
            DateTime dateTime = DateUtil.parse(betOrder.getTicketTime(), formatter);
            LocalDateTime orderTime = dateTime.toLocalDateTime().atZone(ZoneId.of(configJson.getPlatformTimeZone())).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
            dockBetOrder.setOrderTime(orderTime);
        }
        return dockBetOrder;
    }

    /**
     * 游戏注单
     *
     * <AUTHOR>
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class BetHistory extends NSResponse<Void> {

        /**
         * 总共的记录数
         **/
        private Integer resultCount;

        /**
         * 总共的页数
         **/
        private Integer pageCount;

        /**
         * 游戏注单
         */
        private List<BetInfo> list;

        @Data
        public static class BetInfo {

            /**
             * 下注单号
             **/
            private String ticketId;//	Varchar(20)	是	641482277	下注单号

            /**
             * ID
             **/
            private String acctId;//	Varchar(50)	是	TESTPLAYER1	用户标识 ID

            /**
             * 下注时间
             **/
            private String ticketTime;//	Char(15)	是	20120722T225417	下注时间

            /**
             * 游戏种类
             **/
            private String categoryId;//	Varchar(10)	是	SM or TB or AD or BN	游戏种类

            /**
             * 游戏代码
             **/
            private String gameCode;//	Varchar(10)	是	sLongX3	游戏代码

            /**
             * 代码
             **/
            private String currency;//	Char(3)	是	CNY	货币 ISO 代码

            /**
             * 下注金额
             **/
            private BigDecimal betAmount;//	Decimal(18, 6)	是	20	下注金额

            /**
             * 结果
             **/
            private String result;//	Varchar(1024)	否	23415	结果

            /**
             * 用户输赢
             **/
            private BigDecimal winLoss;//	Decimal(18, 6)	是	-20	用户输赢

            /****/
            private BigDecimal jackpotAmount;//	Decimal(18, 6)	是	0

            /**
             * 用户下注 IP
             **/
            private String betIp;//	Varchar(20)	是	*******	用户下注 IP

            /**
             * luckyDrawId
             */
            private Long luckyDrawId;//	Long	否	10

            /**
             * 是否已结束
             **/
            private String completed;//	boolean	是	True	是否已结束

            /**
             * 游戏 log ID
             **/
            private Long roundId;//	Integer	是	1823	游戏 log ID

            /**
             * 0 =没赢 jackpot
             **/
            private Integer sequence;//	Integer	是	0, 1, 2, 3	0 =没赢 jackpot

            /**
             * 注单来自手机或网
             **/
            private String channel;//	Varchar(10)	是	Mobile/Web	注单来自手机或网

            /**
             * 上轮余额
             **/
            private BigDecimal balance;//	Decimal(18, 6)	是	234.000000	上轮余额

            /**
             * 积宝赢额
             **/
            private BigDecimal jpWin;//	Decimal(18, 6)	是	-100.000000	积宝赢额
        }
    }
}