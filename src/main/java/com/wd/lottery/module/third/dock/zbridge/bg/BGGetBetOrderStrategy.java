package com.wd.lottery.module.third.dock.zbridge.bg;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.bg.common.BGApiEnum;
import com.wd.lottery.module.third.dock.zbridge.bg.common.BGHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.bg.common.BGRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.bg.res.BGResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import com.wd.lottery.module.third.entity.ThirdGameEntity;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;

/**
 * 游戏注单
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.BG_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class BGGetBetOrderStrategy extends AbstractGetBetOrderStrategy {

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        log.debug("BG:游戏注单:{}", JSONUtil.toJsonStr(flag));
        BGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), BGRequestConfig.class);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(flag.getPlatformCode());
        // 拉单时间
        ZoneId zoneId = ZoneId.of(configJson.getPlatformTimeZone());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(configJson.getRequestTimePattern());
        String startTime = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId).format(formatter);
        String endTime = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId).format(formatter);
        log.debug("BG:拉单时间:{}-{}", startTime, endTime);
        return this.requestMixtureThirdOrder(flag, requestConfig, startTime, endTime, configJson.getOrderTimePattern(), zoneId);
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        log.debug("BG:对局详情:{}", JSONUtil.toJsonStr(dto));
        BGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), BGRequestConfig.class);
        BGHttpRequestTemplate requestTemplate = new BGHttpRequestTemplate(requestConfig);
        // 摘要
        String random = RandomUtil.randomString(8);
        String digest = DigestUtil.md5Hex(random + requestConfig.getSn() + dto.getOrderNo() + requestConfig.getApiSecret());
        // 真人OR捕鱼? unique (platform_code, third_game_id)
        ThirdGameEntity thirdGame = ThirdPlatformLocalCacheUtil.getThirdGame(dto.getPlatformCode(), dto.getThirdGameId());
        log.debug("BG:ThirdGameEntity:{}", JSONUtil.toJsonStr(thirdGame));
        boolean isCasino = GameCategoryEnum.CASINO == thirdGame.getGameCategoryEnum();
        try {
            // 请求详情
            BGResponse<String> response = requestTemplate.toPOST()
                    .api(isCasino ? BGApiEnum.GET_BET_ORDER_DETAIL.getPath() : BGApiEnum.GET_BET_ORDER_DETAIL_FISH.getPath())
                    .addParameter("random", random)
                    .addParameter("sign", digest)
                    .addParameter("orderId", dto.getOrderNo())
                    .addParameter("lang", "zh")
                    .toBeanAndCall(new TypeReference<BGResponse<String>>() {
                    });
            log.debug("BG:对局详情响应:{}", JSONUtil.toJsonStr(response));
            boolean isEmpty = ObjectUtil.isNull(response) && StrUtil.isEmpty(response.getData());
            Assert.isFalse(isEmpty, () -> new RuntimeException("获取游戏详情失败"));
            return UrlBuilder.of(response.getData()).toURL().toString();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    /**
     * 混合拉单
     *
     * @param flag             BetOrderPullFlag
     * @param requestConfig    BGRequestConfig
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param orderTimePattern 注单时间格式
     * @param zoneId           ZoneId
     * @return {@link List<DockBetOrder>} 注单
     */
    private List<DockBetOrder> requestMixtureThirdOrder(BetOrderPullFlag flag, BGRequestConfig requestConfig, String startTime, String endTime, String orderTimePattern, ZoneId zoneId) {
        List<DockBetOrder> betOrders = Lists.newArrayList();
        List<DockBetOrder> casinoDockBetOrders = this.requestThirdCasinoOrder(flag, requestConfig, startTime, endTime, orderTimePattern, zoneId);
        if (CollUtil.isNotEmpty(casinoDockBetOrders)) {
            betOrders.addAll(casinoDockBetOrders);
        }
        List<DockBetOrder> fishDockBetOrders = this.requestThirdFishOrder(flag, requestConfig, startTime, endTime, orderTimePattern, zoneId);
        if (CollUtil.isNotEmpty(fishDockBetOrders)) {
            betOrders.addAll(fishDockBetOrders);
        }
        flag.setFinished(true);
        return betOrders;
    }

    /**
     * 真人游戏注单
     *
     * @param flag             BetOrderPullFlag
     * @param requestConfig    BGRequestConfig
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param orderTimePattern 注单时间格式
     * @param zoneId           ZoneId
     * @return {@link List<DockBetOrder>} 注单
     */
    private List<DockBetOrder> requestThirdCasinoOrder(BetOrderPullFlag flag, BGRequestConfig requestConfig, String startTime, String endTime, String orderTimePattern, ZoneId zoneId) {
        List<DockBetOrder> dockBetOrders = Lists.newArrayList();
        BGHttpRequestTemplate requestTemplate = new BGHttpRequestTemplate(requestConfig);
        // 摘要
        String random = RandomUtil.randomString(8);
        String encoder = Base64.getEncoder().encodeToString(DigestUtil.sha1(requestConfig.getAgentPassword()));
        String digest = DigestUtil.md5Hex(random + requestConfig.getSn() + encoder);
        // 请求注单
        int pageNo = 1;
        long fetchTotal = 0, allTotal = 0;
        do {
            BGResponse<CasinoPageRecord> response = requestTemplate.toPOST()
                    .api(BGApiEnum.GET_BET_ORDER.getPath())
                    .addParameter("random", random)
                    .addParameter("digest", digest)
                    .addParameter("agentLoginId", requestConfig.getAgentLoginId())
                    .addParameter("startTime", startTime)
                    .addParameter("endTime", endTime)
                    .addParameter("pageIndex", Convert.toStr(pageNo++))
                    .addParameter("pageSize", "500")
                    .toBeanAndCall(new TypeReference<BGResponse<CasinoPageRecord>>() {
                    });
            log.debug("BG:真人游戏注单响应:{}", JSONUtil.toJsonStr(response));
            CasinoPageRecord data = response.getData();
            allTotal = data.getTotal();
            if (CollUtil.isNotEmpty(data.getItems())) {
                fetchTotal += CollUtil.size(data.getItems());
                List<DockBetOrder> fetch = CollStreamUtil.toList(data.getItems(), item -> this.toDockBetOrder(flag.getPlatformCode(), orderTimePattern, zoneId, item));
                dockBetOrders.addAll(fetch);
            }
        } while (fetchTotal < allTotal);
        return dockBetOrders;
    }

    /**
     * 捕鱼游戏注单
     *
     * @param flag             BetOrderPullFlag
     * @param requestConfig    BGRequestConfig
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param orderTimePattern 注单时间格式
     * @param zoneId           ZoneId
     * @return {@link List<DockBetOrder>} 注单
     */
    private List<DockBetOrder> requestThirdFishOrder(BetOrderPullFlag flag, BGRequestConfig requestConfig, String startTime, String endTime, String orderTimePattern, ZoneId zoneId) {
        List<DockBetOrder> dockBetOrders = Lists.newArrayList();
        BGHttpRequestTemplate requestTemplate = new BGHttpRequestTemplate(requestConfig);
        // 摘要
        String random = RandomUtil.randomString(8);
        String digest = DigestUtil.md5Hex(random + requestConfig.getSn() + requestConfig.getApiSecret());
        // 请求注单
        int pageNo = 1;
        long fetchTotal = 0, allTotal = 0;
        do {
            BGResponse<FishPageRecord> response = requestTemplate.toPOST()
                    .api(BGApiEnum.GET_BET_ORDER_FISH.getPath())
                    .addParameter("random", random)
                    .addParameter("sign", digest)
                    .addParameter("startTime", startTime)
                    .addParameter("endTime", endTime)
                    .addParameter("agentLoginId", requestConfig.getAgentLoginId())
                    .addParameter("gameType", "1")
                    .addParameter("pageIndex", Convert.toStr(pageNo++))
                    .addParameter("pageSize", "500")
                    .addParameter("timeZone", "2")
                    .toBeanAndCall(new TypeReference<BGResponse<FishPageRecord>>() {
                    });
            log.debug("BG:捕鱼游戏注单响应:{}", JSONUtil.toJsonStr(response));
            FishPageRecord data = response.getData();
            allTotal = data.getTotal();
            if (CollUtil.isNotEmpty(data.getItems())) {
                fetchTotal += CollUtil.size(data.getItems());
                List<DockBetOrder> fetch = CollStreamUtil.toList(data.getItems(), item -> this.toDockBetOrder(flag.getPlatformCode(), orderTimePattern, zoneId, item));
                dockBetOrders.addAll(fetch);
            }
        } while (fetchTotal < allTotal);
        return dockBetOrders;
    }

    /**
     * 真人游戏注单转换
     *
     * @param platformCode     平台代码
     * @param orderTimePattern 注单时间格式
     * @param zoneId           ZoneId
     * @param item             注单
     * @return {@link DockBetOrder} 注单
     */
    private DockBetOrder toDockBetOrder(String platformCode, String orderTimePattern, ZoneId zoneId, CasinoPageRecord.OrderItem item) {
        DockBetOrder dockBetOrder = new DockBetOrder();
        dockBetOrder.setPlatformCode(platformCode);
        dockBetOrder.setOrderNum(item.getOrderId());
        dockBetOrder.setOrderNumParent(item.getOrderId());
        dockBetOrder.setGameId(item.getGameId());
        dockBetOrder.setThirdUserName(item.getLoginId());

        dockBetOrder.setValidBetMoney(super.toPlatformMoney(item.getValidBet()));
        dockBetOrder.setBetMoney(super.toPlatformMoney(item.getBAmount().abs()));
        dockBetOrder.setWinMoney(super.toPlatformMoney(item.getPayment()) + dockBetOrder.getBetMoney());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(orderTimePattern);
        LocalDateTime orderTime = LocalDateTime.parse(this.filterMill(item.getOrderTime()), formatter);
        orderTime = orderTime.atZone(zoneId).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
        dockBetOrder.setOrderTime(orderTime);
        return dockBetOrder;
    }

    /**
     * 捕鱼游戏注单转换
     *
     * @param platformCode     平台代码
     * @param orderTimePattern 注单时间格式
     * @param zoneId           ZoneId
     * @param item             注单
     * @return {@link DockBetOrder} 注单
     */
    private DockBetOrder toDockBetOrder(String platformCode, String orderTimePattern, ZoneId zoneId, FishPageRecord.OrderItem item) {
        DockBetOrder dockBetOrder = new DockBetOrder();
        dockBetOrder.setPlatformCode(platformCode);
        dockBetOrder.setOrderNum(item.getBetId());
        dockBetOrder.setOrderNumParent(item.getBetId());
        dockBetOrder.setGameId(item.getGameType());
        dockBetOrder.setThirdUserName(item.getLoginId());

        dockBetOrder.setValidBetMoney(super.toPlatformMoney(item.getValidAmount()));
        dockBetOrder.setBetMoney(super.toPlatformMoney(item.getBetAmount()));
        dockBetOrder.setWinMoney(super.toPlatformMoney(item.getPayout()) + dockBetOrder.getBetMoney());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(orderTimePattern);
        LocalDateTime orderTime = LocalDateTime.parse(this.filterMill(item.getOrderTime()), formatter);
        orderTime = orderTime.atZone(zoneId).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
        dockBetOrder.setOrderTime(orderTime);
        try {
            dockBetOrder.setJackpot(super.toPlatformMoney(Convert.toBigDecimal(item.getJackpot())));
        } catch (Exception ignore) {

        }
        dockBetOrder.setProgressive(super.toPlatformMoney(item.getExtend()));
        return dockBetOrder;
    }

    private String filterMill(String time) {
        int index = time.indexOf(".");
        if (index > 0) {
            return time.substring(0, index);
        }
        return time;
    }

    /**
     * 真人游戏注单列表
     */
    @Data
    public static class CasinoPageRecord {
        private List<OrderItem> items;
        private long total;
        private long pageIndex;
        private long pageSize;

        @Data
        public static class OrderItem {
            private String orderId;
            private String tranId;
            private String sn;
            private String uid;
            private String loginId;
            private String moduleId;
            private String moduleName;
            private String gameId;
            private String gameName;
            private String orderStatus;
            @JsonProperty("bAmount")
            private BigDecimal bAmount;
            @JsonProperty("aAmount")
            private BigDecimal aAmount;
            private String orderFrom;
            private String orderTime;
            private String lastUpdateTime;
            private String fromIp;
            private String issueId;
            private String playId;
            private String playName;
            private String playNameEn;
            private BigDecimal validBet;
            private BigDecimal payment;
        }
    }

    /**
     * 捕鱼游戏注单列表
     */
    @Data
    public static class FishPageRecord {
        private List<OrderItem> items;
        private long total;
        private String lastVersion;
        private long pageIndex;
        private long pageSize;
        private String etag;

        @Data
        public static class OrderItem {
            private String sn;
            private String userId;
            private String loginId;
            private String gameType;
            private String gameId;
            private String issueId;
            private String betId;
            private String fireCount;
            private BigDecimal betAmount;
            private BigDecimal validAmount;
            private BigDecimal calcAmount;
            private BigDecimal payout;
            private String orderTime;
            private String jackpot;
            private String jackpotType;
            private BigDecimal extend;
            private String orderFrom;
            private BigDecimal gameBalance;
        }
    }
}