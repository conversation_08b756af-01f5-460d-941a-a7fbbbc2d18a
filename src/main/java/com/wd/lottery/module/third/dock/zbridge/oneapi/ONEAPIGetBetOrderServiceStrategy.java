package com.wd.lottery.module.third.dock.zbridge.oneapi;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.exception.ThirdPlatformException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.oneapi.common.ONEAPIConstant;
import com.wd.lottery.module.third.dock.zbridge.oneapi.common.ONEAPIHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.oneapi.common.ONEAPIRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.oneapi.common.ONEPAIResponse;
import com.wd.lottery.module.third.dock.zbridge.oneapi.req.BetDetailParam;
import com.wd.lottery.module.third.dock.zbridge.oneapi.req.BetParam;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;


@Slf4j
@Component(BridgeConstant.ONEAPI_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class ONEAPIGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {


    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) {
        log.info("ONEAPI拉单初始请求参数:{}", JSONUtil.toJsonStr(flag));
        String platformCode = flag.getPlatformCode();
        ONEAPIRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(platformCode, flag.getCurrencyEnum(), ONEAPIRequestConfig.class);
        ThirdPlatformConfigDTO platformConfig = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(platformCode);
        //TimeStamp
        long startTime = Timestamp.valueOf(flag.getBegin()).getTime();
        long endTime = Timestamp.valueOf(flag.getEnd()).getTime();
        int index = flag.getIndex();
        ONEPAIResponse<TransactData> response = getBetOrderList(requestConfig, startTime, endTime, index);
        log.debug("ONEAPI拉单返回:{}", JSONUtil.toJsonStr(response));

        Integer totalPage = response.getData().getTotalPages();
        if (Objects.nonNull(totalPage) && index < totalPage) {
            flag.setIndex(index + 1);
        } else {
            flag.setFinished(true);
        }

        BigDecimal rate = ThirdPlatformMappingConverter.getCurrencyExchangeRate(platformCode, flag.getCurrencyEnum());

        return parseDockBetOrder(response.getData(), platformCode, rate, platformConfig.getPlatformTimeZone());
    }


    private ONEPAIResponse<TransactData> getBetOrderList(ONEAPIRequestConfig requestConfig, Long startDate, Long endDate, int pageNo) {
        log.info("pullOrder, startDate:{} , endDate:{}, page: {}", startDate, endDate, pageNo);
        BetParam betParam = BetParam.builder()
                .traceId(UUID.randomUUID().toString())
                .fromTime(startDate)
                .toTime(endDate)
                .pageNo(pageNo).build();

        log.info("ONEAPI拉单请求参数:{}", JSONUtil.toJsonStr(betParam));
        return new ONEAPIHttpRequestTemplate(requestConfig)
                .host(Objects.nonNull(requestConfig.getBetApiUrl()) ? requestConfig.getBetApiUrl() : requestConfig.getApiUrl())
                .toPOST()
                .api(ONEAPIConstant.BET_ORDER)
                .body(JacksonUtil.toJSONString(betParam))
                .toBeanAndCall(new TypeReference<ONEPAIResponse<TransactData>>() {
                });
    }

    private List<DockBetOrder> parseDockBetOrder(TransactData data, String platformCode, BigDecimal rate, String platformTimeZone) {
        if (Objects.isNull(data)
                || CollectionUtils.isEmpty(data.getTransactions())
                || Objects.isNull(data.getHeaders())) {
            return Collections.emptyList();
        }

        HeadField headers = data.getHeaders();
        List<List<String>> transactions = data.getTransactions();
        return transactions.stream().map(item -> toDockBetOrder(headers, item, platformCode, rate, platformTimeZone))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    private DockBetOrder toDockBetOrder(HeadField headers, List<String> item, String platformCode, BigDecimal rate, String platformTimeZone) {
        if (CollectionUtils.isEmpty(item)
                || ONEAPIConstant.BETSTATUS != Integer.parseInt(item.get(headers.getStatus()))) {
            return null;
        }
        DockBetOrder dockBetOrder = new DockBetOrder();
        dockBetOrder.setPlatformCode(platformCode);
        dockBetOrder.setOrderNum(item.get(headers.getBetId()));
        dockBetOrder.setOrderNumParent(item.get(headers.getRoundId()));
        dockBetOrder.setThirdUserName(item.get(headers.getUsername()));
        dockBetOrder.setGameId(item.get(headers.getGameCode()));
        BigDecimal bet = new BigDecimal(item.get(headers.getBetAmount()));
        dockBetOrder.setBetMoney(ThirdPlatformMappingConverter.toSysMoneyLong(bet, rate));
        BigDecimal win = new BigDecimal(item.get(headers.getWinAmount()));
        dockBetOrder.setWinMoney(ThirdPlatformMappingConverter.toSysMoneyLong(win, rate));
        BigDecimal validWin = new BigDecimal(item.get(headers.getEffectiveTurnover()));
        dockBetOrder.setValidBetMoney(ThirdPlatformMappingConverter.toSysMoneyLong(validWin, rate));
        long betTime = Long.parseLong(item.get(headers.getVendorSettleTime()));
        dockBetOrder.setOrderTime(parseThirdOrderTime(betTime,platformTimeZone));
        return dockBetOrder;
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        ONEAPIRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), ONEAPIRequestConfig.class);
        ONEAPIHttpRequestTemplate requestTemplate = new ONEAPIHttpRequestTemplate(requestConfig);
        LocalDateTime now = LocalDateTime.now().atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        LocalDateTime sevenDayBefore = now.minusDays(7);
        long fromTime = Timestamp.valueOf(sevenDayBefore).getTime();
        long toTime = Timestamp.valueOf(now).getTime();
        BetDetailParam betDetailParam = BetDetailParam.builder().traceId(UUID.randomUUID().toString())
                .betId(dto.getOrderNo()).fromTime(fromTime).toTime(toTime)
                .displayLanguage("EN").build();

        try {
            ONEPAIResponse<OrderDetailRes> orderDetailRes = requestTemplate
                    .toPOST()
                    .api(ONEAPIConstant.BET_DETAIL)
                    .body(JacksonUtil.toJSONString(betDetailParam))
                    .toBeanAndCall(new TypeReference<ONEPAIResponse<OrderDetailRes>>() {
                    });

            log.debug("http getOrderDetailUrl response:{} ", orderDetailRes);
            if (ObjectUtils.isEmpty(orderDetailRes)) {
                throw new ThirdPlatformException("cannot get bet detail url, historyId = " + dto.getOrderNo());
            }
            String url = orderDetailRes.getData().getDetailUrl();
            if (StrUtil.isEmpty(url)){
                return super.getOrderDetailUrl(dto);
            }
            return url.startsWith("//") ? String.format("https:%s", url) : url;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    private LocalDateTime parseThirdOrderTime(long epochMilliSeconds, String platformTimeZone) {
        return Instant.ofEpochMilli(epochMilliSeconds)
                .atZone(ZoneId.of(platformTimeZone))
                .withZoneSameInstant(ZoneId.systemDefault())
                .toLocalDateTime();

    }


    @Data
    static class TransactData {
        private HeadField headers;
        private List<List<String>> transactions;
        private Integer currentPage;
        private Integer totalPages;
    }

    @Data
    static class HeadField {
        private Integer betId;
        private Integer roundId;
        private Integer externalTransactionId;
        private Integer username;
        private Integer currencyCode;
        private Integer gameCode;
        private Integer vendorCode;
        private Integer gameCategoryCode;
        private Integer betAmount;
        private Integer winAmount;
        private Integer winLoss;
        private Integer effectiveTurnover;
        private Integer jackpotAmount;
        private Integer refundAmount;
        private Integer status;
        private Integer vendorBetTime;
        private Integer vendorSettleTime;
        private Integer isFreeSpin;

    }

    @Data
   public static class OrderDetailRes {
        private String detailUrl;
    }
}
