package com.wd.lottery.module.third.dock.zbridge.ug;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.module.third.constants.ConvertOrderStatusEnum;
import com.wd.lottery.module.third.constants.ConvertOrderTransferEnum;
import com.wd.lottery.module.third.dock.convertorder.ConvertOrderServiceStrategy;
import com.wd.lottery.module.third.dock.convertorder.DockConvertOrder;
import com.wd.lottery.module.third.dock.convertorder.DockGetConvertOrderStatus;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.ug.common.*;
import com.wd.lottery.module.third.dock.zbridge.ug.common.*;
import com.wd.lottery.module.third.dock.zbridge.ug.req.ConvertOrderReqParam;
import com.wd.lottery.module.third.dock.zbridge.ug.req.OrderStatusReqParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;


@Slf4j
@Component(value = BridgeConstant.UG_PLATFORM_CODE + ConvertOrderServiceStrategy.BEAN_NAME_SUFFIX)
public class UGConvertOrderServiceStrategy implements ConvertOrderServiceStrategy {

    @Override
    public void addOrder(DockConvertOrder dto) throws Exception {
        log.debug("UG:钱包转账:{}", JSONUtil.toJsonStr(dto));
        UGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), UGRequestConfig.class);
        UGHttpRequestTemplate requestTemplate = new UGHttpRequestTemplate(requestConfig);
        String apiKey = requestConfig.getApiKey();
        String operatorId = requestConfig.getOperatorId();
        long timestamp = LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond();
        String produceApiPassword = UGUtils.produceApiPassword(apiKey, operatorId, timestamp);
        BigDecimal convertMoney = dto.balance2BigDecimal();
        String key = UGUtils.produceTransferKey(produceApiPassword ,dto.getThirdUserName(),convertMoney.setScale(4, RoundingMode.DOWN));
        ConvertOrderReqParam convertOrderReqParam = ConvertOrderReqParam.builder().apiKey(apiKey).operatorId(operatorId).serialNumber(dto.getOrderNo())
                .userId(dto.getThirdUserName()).apiPassword(produceApiPassword).amount(convertMoney).key(key).build();

        String api = ConvertOrderTransferEnum.IN  == dto.getTransferEnum()? UGApiEnum.TRANSACTION_IN.getPath():UGApiEnum.TRANSACTION_OUT.getPath();
        requestTemplate
                .toPOST()
                .api(api)
                .body(JacksonUtil.toJSONString(convertOrderReqParam))
                .toBeanAndCall(UGResponse.class);


    }

    @Override
    public ConvertOrderStatusEnum getOrderStatus(DockGetConvertOrderStatus dto) {
        log.debug("UG:钱包转账状态:{}", JSONUtil.toJsonStr(dto));
        UGRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), UGRequestConfig.class);
        UGHttpRequestTemplate requestTemplate = new UGHttpRequestTemplate(requestConfig);
        OrderStatusReqParam orderStatusReqParam = OrderStatusReqParam.builder().apiKey(requestConfig.getApiKey()).operatorId(requestConfig.getOperatorId())
                .serialNumber(dto.getOrderNo()).build();

        UGResponse<List<OrderStatus>> response = requestTemplate
                .toPOST()
                .api(UGApiEnum.TRANSACTION_CHECK.getPath())
                .body(JacksonUtil.toJSONString(orderStatusReqParam))
                .toBeanAndCall(new TypeReference<UGResponse<List<OrderStatus>>>() {
                }, false);
        log.debug("UG:钱包转账状态响应:{}", JSONUtil.toJsonStr(response));

        // complete
        if(response.getCode() == 0){
            if(CollUtil.isNotEmpty(response.getData()) && response.getData().get(0).getStatus() == 1){
                return ConvertOrderStatusEnum.SUCCESS;
            }else{
                return ConvertOrderStatusEnum.FAIL;
            }
        }

        // not exist
        if(response.getCode() == 400001){
            return ConvertOrderStatusEnum.FAIL;
        }
        return ConvertOrderStatusEnum.VERIFY;
    }

    @Data
    static class OrderStatus{
        @JsonProperty("status")
        private Integer status;
    }

}