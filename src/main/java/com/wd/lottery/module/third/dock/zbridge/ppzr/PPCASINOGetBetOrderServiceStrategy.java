package com.wd.lottery.module.third.dock.zbridge.ppzr;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.csv.*;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.api.IErrorCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.betorder.*;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.ppzr.common.PPCASINOApiEnum;
import com.wd.lottery.module.third.dock.zbridge.ppzr.common.PPCASINOHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.ppzr.common.PPCASINORequestConfig;
import com.wd.lottery.module.third.dock.zbridge.ppzr.res.PPCASINOResponse;
import com.wd.lottery.module.third.dto.ThirdPlatformConfigDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 游戏注单
 *
 * <AUTHOR>
 */
@Slf4j
@Component(value = BridgeConstant.PP_CASINO_PLATFORM_CODE + GetBetOrderStrategy.BEAN_NAME_SUFFIX)
public class PPCASINOGetBetOrderServiceStrategy extends AbstractGetBetOrderStrategy {

    @Override
    protected List<DockBetOrder> requestThirdOrder(BetOrderPullFlag flag) throws Exception {
        PPCASINORequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(flag.getPlatformCode(), flag.getCurrencyEnum(), PPCASINORequestConfig.class);
        ThirdPlatformConfigDTO configJson = ThirdPlatformLocalCacheUtil.getThirdPlatformConfig(flag.getPlatformCode());
        PPCASINOHttpRequestTemplate requestTemplate = new PPCASINOHttpRequestTemplate(requestConfig);

        // 自动拉单
        ZoneId zoneId = ZoneId.of(configJson.getPlatformTimeZone());
        if (!flag.isManual()) {
            if (StrUtil.equals("0", flag.getLastFlag())) {
                Long epochMilli = flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId).toInstant().toEpochMilli();
                flag.setLastFlag(Convert.toStr(epochMilli));
            }
            String startFlag = flag.getLastFlag();
            String response = this.getBetOrderResponse(requestTemplate, requestConfig, flag.getLastFlag());
            List<DockBetOrder> dockBetOrders = this.analyzeBetOrder(true, flag, configJson, response);
            log.debug("PP:CASINO:拉取游戏注单响应[自动]:开始标记和时间({}和{}), 三方响应的结束标记和时间({}和{}), 响应结果:{}",
                    startFlag, Instant.ofEpochMilli(Convert.toLong(startFlag)).atZone(zoneId),
                    flag.getLastFlag(), Instant.ofEpochMilli(Convert.toLong(flag.getLastFlag())).atZone(zoneId),
                    response);
            return dockBetOrders;
        }

        // 手动拉单
        boolean isFirst = StrUtil.isEmpty(flag.getRemark());
        Long timePoint = isFirst ? flag.getBegin().atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId).toInstant().toEpochMilli() : Convert.toLong(flag.getRemark());
        String response = this.getBetOrderResponse(requestTemplate, requestConfig, Convert.toStr(timePoint));
        List<DockBetOrder> dockBetOrders = this.analyzeBetOrder(false, flag, configJson, response);
        log.debug("PP:CASINO:拉取游戏注单响应[手动]:开始标记和时间({}和{}), 三方响应的结束标记和时间({}和{}), 响应结果:{}",
                timePoint, Instant.ofEpochMilli(Convert.toLong(timePoint)).atZone(zoneId),
                flag.getRemark(), Instant.ofEpochMilli(Convert.toLong(flag.getRemark())).atZone(zoneId),
                response);
        return dockBetOrders;
    }

    @Override
    public String getOrderDetailUrl(DockGetBetOrderDetail dto) {
        log.debug("PP:CASINO:游戏对局详情:{}", JSONUtil.toJsonStr(dto));
        PPCASINORequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), PPCASINORequestConfig.class);
        PPCASINOHttpRequestTemplate requestTemplate = new PPCASINOHttpRequestTemplate(requestConfig);
        try {
            GameDetailResponse response = requestTemplate.toPOST()
                    .api(PPCASINOApiEnum.GET_BET_ORDER_DETAIL.getPath())
                    .addParameter("playerId", dto.getThirdUserId())
                    .addParameter("gameId", dto.getThirdGameId())
                    .addParameter("roundId", dto.getOrderNo())
                    .toBeanAndCall(GameDetailResponse.class);
            log.debug("PP:CASINO:游戏对局详情:{}", JSONUtil.toJsonStr(response));
            return response.getUrl();
        } catch (Exception e) {
            if (e instanceof ApiException) {
                IErrorCode errorCode = ((ApiException) e).getErrorCode();
                throw new ApiException(errorCode);
            }
            throw new ApiException(CommonCode.BET_ORDER_DETAIL_FAILED);
        }
    }

    /**
     * 获取游戏注单
     *
     * @param requestTemplate 请求模版
     * @param requestConfig   请求配置
     * @param lastFlag        拉单标识
     * @return {@link String} 游戏注单
     */
    private String getBetOrderResponse(PPCASINOHttpRequestTemplate requestTemplate, PPCASINORequestConfig requestConfig, String lastFlag) {
        return requestTemplate.toGET()
                .api(PPCASINOApiEnum.GET_BET_ORDER.getPath())
                .addParameter("login", requestConfig.getUsername())
                .addParameter("password", requestConfig.getSecret())
                .addParameter("timepoint", lastFlag)
                .addParameter("dataType", "LC")
                .addParameter("options", "addRoundDetails")
                .toCustomObject(String.class);
    }

    /**
     * 分析游戏注单CSV数据
     *
     * @param isAuto     自动拉单
     * @param flag       拉单标记
     * @param configJson 平台配置
     * @param response   游戏注单数据
     * @return {@link List<DockBetOrder>}
     */
    private List<DockBetOrder> analyzeBetOrder(boolean isAuto, BetOrderPullFlag flag, ThirdPlatformConfigDTO configJson, String response) {
        try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(response.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8))) {
            String everyLine = bufferedReader.readLine();
            // 解析TimePoint
            boolean isRight = StrUtil.isEmpty(everyLine) || !StrUtil.startWith(everyLine, "timepoint=");
            Assert.isFalse(isRight, () -> new IllegalArgumentException("无效行"));
            List<String> temporary = StrUtil.splitTrim(everyLine, "=");
            Long nextTimePoint = Convert.toLong(temporary.get(1));
            // 拉单标记
            if (isAuto) {
                flag.setLastFlag(Convert.toStr(nextTimePoint));
            } else {
                flag.setRemark(Convert.toStr(nextTimePoint));
            }
            // 本次拉单结束时间
            ZoneId zoneId = ZoneId.of(configJson.getPlatformTimeZone());
            long endPoint = flag.getEnd().atZone(ZoneId.systemDefault()).withZoneSameInstant(zoneId).toInstant().toEpochMilli();
            // 本次拉单结束?
            flag.setFinished(nextTimePoint >= endPoint);
            // 解析注单实例
            List<BetOrderDetail> betOrderData = this.parseResponseOrderData(bufferedReader);
            return this.toDockBetOrderList(flag.getPlatformCode(), configJson, betOrderData);
        } catch (Exception ex) {
            log.warn(StrUtil.format("PP:CASINO:解析注单CSV文件出错:{}", response), ex);
            throw new IllegalArgumentException(ex);
        }
    }

    /**
     * 解析数据行
     *
     * @param bufferedReader 读取缓存流
     * @return {@link List<BetOrderDetail>}
     */
    private List<BetOrderDetail> parseResponseOrderData(BufferedReader bufferedReader) {
        // 读取CSV数据
        CsvReadConfig csvReadConfig = CsvReadConfig.defaultConfig();
        csvReadConfig.setContainsHeader(true);
        CsvReader reader = CsvUtil.getReader(csvReadConfig);
        CsvData csvData = reader.read(bufferedReader);
        // 标题头
        List<String> header = csvData.getHeader();
        // 数据行
        List<CsvRow> rows = csvData.getRows();
        if (CollUtil.isEmpty(rows)) {
            return ListUtil.empty();
        }
        // 转换到注单Bean
        List<BetOrderDetail> betOrderDetails = Lists.newArrayList();
        int size = CollUtil.size(header);
        CollUtil.forEach(rows, (row, item) -> {
            Map<String, String> everyMap = Maps.newHashMapWithExpectedSize(size);
            for (int i = 0; i < header.size(); i++) {
                everyMap.put(header.get(i), row.get(i));
            }
            // 只需要已完成的订单
            boolean isFinished = Objects.equals(everyMap.get("status"), "C");
            if (isFinished) {
                betOrderDetails.add(BeanUtil.toBeanIgnoreError(everyMap, BetOrderDetail.class));
            }
        });
        return betOrderDetails;
    }

    /**
     * 转系统注单
     *
     * @param platformCode    平台码
     * @param configJson      平台配置
     * @param betOrderDetails 游戏注单
     * @return {@link List<DockBetOrder>}
     */
    private List<DockBetOrder> toDockBetOrderList(String platformCode, ThirdPlatformConfigDTO configJson, List<BetOrderDetail> betOrderDetails) {
        if (CollUtil.isEmpty(betOrderDetails)) {
            return Collections.emptyList();
        }
        String orderTimePattern = StrUtil.isBlank(configJson.getOrderTimePattern()) ? DatePattern.NORM_DATETIME_PATTERN : configJson.getOrderTimePattern();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(orderTimePattern);
        return CollStreamUtil.toList(betOrderDetails, betOrderDetail -> {
            DockBetOrder dockOrder = new DockBetOrder();
            dockOrder.setThirdUserName(betOrderDetail.getExtPlayerID());
            dockOrder.setPlatformCode(platformCode);
            dockOrder.setOrderNum(betOrderDetail.getPlaySessionID());
            dockOrder.setGameId(betOrderDetail.getGameID());

            dockOrder.setBetMoney(toPlatformMoney(betOrderDetail.getBet()));
            dockOrder.setWinMoney(toPlatformMoney(betOrderDetail.getWin()));
            dockOrder.setValidBetMoney(toPlatformMoney(betOrderDetail.getBet()));

            LocalDateTime thirdBet = LocalDateTime.parse(betOrderDetail.getStartDate(), formatter);
            dockOrder.setOrderTime(thirdBet.atZone(ZoneId.of(configJson.getPlatformTimeZone())).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime());

            BigDecimal jackpot = betOrderDetail.getJackpot();
            if (Objects.nonNull(jackpot)) {
                dockOrder.setJackpot(toPlatformMoney(jackpot));
            }
            return dockOrder;
        });
    }

    /**
     * 游戏注单
     */
    @Data
    public static class BetOrderDetail {

        /**
         * 玩家在Pragmatic Play 系统中的唯一标识符
         **/
        private Long playerID;

        /**
         * 娱乐场运营商系统中唯一的玩家标识符
         **/
        private String extPlayerID;

        /**
         * 由Pragmatic Play 提供的游戏唯一符号标识符
         **/
        private String gameID;

        /**
         * 玩家的特定游戏会话的ID（游戏回合的唯一编号）
         **/
        private String playSessionID;

        /**
         * 游戏开始的日期和时间 yyyy-MM-dd HH:mm:ss
         **/
        private String startDate;

        /**
         * 游戏结束的日期和时间。如果游戏回合尚未完成，将为null yyyy-MM-dd HH:mm:ss
         **/
        private String endDate;

        /**
         * 游戏状态：I -正在进行中（尚未完成）C -已完成
         **/
        private String status;

        /**
         * 投注金额
         **/
        private BigDecimal bet;

        /**
         * 赢得金额
         **/
        private BigDecimal win;

        /**
         * 交易货币，3个字母的ISO代码
         **/
        private String currency;

        /**
         * 累积奖金赢得的数量
         **/
        private BigDecimal jackpot;
    }

    /**
     * 对局详情
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class GameDetailResponse extends PPCASINOResponse<Void> {
        private String url;
    }
}