package com.wd.lottery.module.third.dock.zbridge.kb;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.third.dock.base.PeachLang;
import com.wd.lottery.module.third.dock.convorter.ThirdPlatformMappingConverter;
import com.wd.lottery.module.third.dock.game.AbstractGetGameStrategy;
import com.wd.lottery.module.third.dock.game.DockGame;
import com.wd.lottery.module.third.dock.game.DockGetGameUrl;
import com.wd.lottery.module.third.dock.game.GetGameStrategy;
import com.wd.lottery.module.third.dock.utils.ThirdPlatformLocalCacheUtil;
import com.wd.lottery.module.third.dock.zbridge.BridgeConstant;
import com.wd.lottery.module.third.dock.zbridge.kb.common.KBGameCache;
import com.wd.lottery.module.third.dock.zbridge.kb.common.KBHttpRequestTemplate;
import com.wd.lottery.module.third.dock.zbridge.kb.common.KBRequestConfig;
import com.wd.lottery.module.third.dock.zbridge.kb.common.KBSignatureUtil;
import com.wd.lottery.module.third.dock.zbridge.kb.res.KBApiRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: KB game strategy
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component(BridgeConstant.KB_PLATFORM_CODE + GetGameStrategy.BEAN_NAME_SUFFIX)
public class KBGetGameServiceStrategy extends AbstractGetGameStrategy {

    private final KBTokenService tokenService;

    public KBGetGameServiceStrategy(KBTokenService tokenService) {
        this.tokenService = tokenService;
    }

    @Override
    public List<DockGame> getGameList(String platformCode, CurrencyEnum currencyEnum) {

        List<DockGame> list = new ArrayList<>(100);

        Map<GameCategoryEnum, Map<String, String>> gameMap = KBGameCache.gameMap;
        for (Map.Entry<GameCategoryEnum, Map<String, String>> categoryEntry : gameMap.entrySet()) {
            for (Map.Entry<String, String> gameEntry : categoryEntry.getValue().entrySet()) {
                DockGame game = new DockGame();
                game.setPlatformCode(platformCode);
                game.setGameCategoryEnum(categoryEntry.getKey());
                game.setThirdGameId(gameEntry.getKey());
                game.setGameCode(gameEntry.getKey());
                game.setGameName(gameEntry.getValue());

                list.add(game);
            }
        }
        return list;
    }

    @Override
    public String getOpenGameUrl(DockGetGameUrl dto) {
        KBRequestConfig requestConfig = ThirdPlatformLocalCacheUtil.getRequestConfig(dto.getPlatformCode(), dto.getCurrencyEnum(), KBRequestConfig.class);
        KBHttpRequestTemplate requestTemplate = new KBHttpRequestTemplate(requestConfig);
        final String defaultLang = PeachLang.EN_US;
        String lang = ThirdPlatformMappingConverter.toThirdLang(requestConfig, dto.getLang(), defaultLang);

        String token = tokenService.generateToken(dto.getPlatformCode(), dto.getThirdGameId(), dto.getThirdUserId(), dto.getCurrencyEnum());
        LinkedHashMap<String, String> signParam = new LinkedHashMap<>();
        signParam.put("Token", token);
        signParam.put("GameId", dto.getThirdGameId());
        signParam.put("Lang", lang);
        signParam.put("AgentId", requestConfig.getAgentId());

        String sign = KBSignatureUtil.sign(signParam, requestConfig.getAgentId(), requestConfig.getAgentKey());
        log.debug("kb game hash: {}", sign);

        KBApiRes<String> res = requestTemplate
                .host(requestConfig.getBaseUrl())
                .api("/singleWallet/LoginWithoutRedirect")
                .toGET()
                .addParameter("Token", token)
                .addParameter("GameId", dto.getThirdGameId())
                .addParameter("Lang", lang)
                .addParameter("HomeUrl", dto.getLobbyUrl())
                .addParameter("AgentId", requestConfig.getAgentId())
                .addParameter("Key", sign)
                .toBeanAndCall(new TypeReference<KBApiRes<String>>() {
                });

        return res.getData();
    }
}
