package com.wd.lottery.module.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.dto.DateRangeDTO;
import com.wd.lottery.module.cash.service.CashDepositOrderService;
import com.wd.lottery.module.common.service.CommonLoginLogService;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.merchant.entity.Merchant;
import com.wd.lottery.module.merchant.service.MerchantService;
import com.wd.lottery.module.report.constans.ReportMemberRetentionDateEnum;
import com.wd.lottery.module.report.constans.ReportMemberRetentionTypeEnum;
import com.wd.lottery.module.report.dto.ReportMemberRetentionDTO;
import com.wd.lottery.module.report.entity.ReportMemberRetention;
import com.wd.lottery.module.report.mapper.ReportMemberRetentionMapper;
import com.wd.lottery.module.report.param.ReportMemberRetentionQueryParam;
import com.wd.lottery.module.report.service.ReportFirstDepositHisService;
import com.wd.lottery.module.report.service.ReportMemberDateService;
import com.wd.lottery.module.report.service.ReportMemberRetentionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.wd.lottery.common.constans.Constants.TEN_THOUSAND;


@Slf4j
@Service
@RequiredArgsConstructor
public class ReportMemberRetentionServiceImpl extends ServiceImpl<ReportMemberRetentionMapper, ReportMemberRetention> implements ReportMemberRetentionService {

    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    private MerchantService merchantService;

    @Resource
    private MemberService memberService;

    @Resource
    private CommonLoginLogService commonLoginLogService;

    @Resource
    private ReportMemberDateService reportMemberDateService;

    @Resource
    private ReportFirstDepositHisService reportFirstDepositHisService;

    @Resource
    private CashDepositOrderService cashDepositOrderService;

    @Override
    public void settleReportMemberRetention(LocalDate yesterdayDate) {
        List<Merchant> allMerchant = merchantService.getEnableMerchantList();

        for (Merchant merchant : allMerchant) {
            DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
            transactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
            TransactionStatus status = transactionManager.getTransaction(transactionDefinition);
            try {
                settleReportMemberRetentionImpl(merchant, yesterdayDate);
                transactionManager.commit(status);
            } catch (Exception e) {
                log.error(String.format("settleReportMemberRetention error, merchantId:{}", merchant.getCode()), e);
                transactionManager.rollback(status);
            }
        }
    }

    @Override
    public List<ReportMemberRetentionDTO> getList(ReportMemberRetentionQueryParam param) {
        Assert.notNull(param);
        Assert.notNull(param.getStartDate());
        Assert.notNull(param.getEndDate());
        Assert.notNull(param.getRetentionTypeEnum());

        return this.lambdaQuery()
                .eq(ReportMemberRetention::getMerchantId, param.getMerchantId())
                .eq(ReportMemberRetention::getCurrencyEnum, param.getCurrencyEnum())
                .eq(ReportMemberRetention::getRetentionTypeEnum, param.getRetentionTypeEnum())
                .between(ReportMemberRetention::getCreateDate, param.getStartDate(), param.getEndDate())
                .orderByDesc(ReportMemberRetention::getCreateDate)
                .list().stream().map(it -> {
                    ReportMemberRetentionDTO dto = new ReportMemberRetentionDTO();
                    BeanUtil.copyProperties(it, dto);
                    return dto;
                }).collect(Collectors.toList());
    }

    private void settleReportMemberRetentionImpl(Merchant merchant, LocalDate yesterdayDate) {
        Long merchantId = merchant.getId();

        List<CurrencyEnum> currencyEnumList = merchant.getCurrencyEnumList();
        //新增当前日期数据
        List<ReportMemberRetention> needSaveList = new ArrayList<>();
        for (CurrencyEnum currencyEnum : currencyEnumList) {
            for (ReportMemberRetentionTypeEnum retentionTypeEnum : ReportMemberRetentionTypeEnum.values()) {
                ReportMemberRetention memberRetention = new ReportMemberRetention();
                memberRetention.setMerchantId(merchantId);
                memberRetention.setCreateDate(yesterdayDate);
                memberRetention.setCurrencyEnum(currencyEnum);
                memberRetention.setRetentionTypeEnum(retentionTypeEnum);
                memberRetention.setTotalMemberCount(getRetentionMemberCount(merchantId, currencyEnum, yesterdayDate, retentionTypeEnum));
                needSaveList.add(memberRetention);
            }
        }
        this.saveBatch(needSaveList);

        //查询待更新数据
        List<ReportMemberRetention> needUpdateList = this.lambdaQuery()
                .eq(ReportMemberRetention::getMerchantId, merchantId)
                .between(ReportMemberRetention::getCreateDate, yesterdayDate.minusDays(31), yesterdayDate)
                .list();
        //过滤出符合 ReportMemberRetentionDateEnum 的所有数据
        needUpdateList = needUpdateList.stream()
                .map(it -> filterAndSetDateEnum(it, yesterdayDate))
                .filter(Objects::nonNull).collect(Collectors.toList());

        //根据 ReportMemberRetentionDateEnum 日期处理类型进行实际处理
        for (ReportMemberRetention memberRetention : needUpdateList) {
            ReportMemberRetentionDateEnum retentionDateEnum = memberRetention.getRetentionDateEnum();
            Long rate = getRetentionMemberRate(merchantId, memberRetention.getCurrencyEnum(), memberRetention.getCreateDate(),
                    yesterdayDate, memberRetention.getRetentionTypeEnum());
            //根据类型设置不同字段
            setRateByDateEnum(memberRetention, rate, retentionDateEnum);
        }

        this.updateBatchById(needUpdateList);
    }

    /**
     * 根据日期和留存类型获取总会员人数
     * @param date
     * @param typeEnum
     * @return
     */
    private Long getRetentionMemberCount(Long merchantId, CurrencyEnum currencyEnum, LocalDate date, ReportMemberRetentionTypeEnum typeEnum) {
        switch (typeEnum) {
            case NEW:
                return memberService.countByCreateTime(merchantId, currencyEnum, LocalDateTimeUtil.beginOfDay(date), LocalDateTimeUtil.endOfDay(date));
            case ACTIVE:
                return reportMemberDateService.countByCreateDateAndDeposit(merchantId, currencyEnum, null,false, new DateRangeDTO(date, date));
            case DAY_DEPOSIT:
                return cashDepositOrderService.countDepositMemberByCreateTime(merchantId, currencyEnum, null, LocalDateTimeUtil.beginOfDay(date), LocalDateTimeUtil.endOfDay(date));
            case FIRST_DEPOSIT:
                return reportFirstDepositHisService.countFirstDepositByPayTime(merchantId, currencyEnum, false, LocalDateTimeUtil.beginOfDay(date), LocalDateTimeUtil.endOfDay(date));
            case NEW_MEMBER_DEPOSIT:
                return reportFirstDepositHisService.countFirstDepositByPayTime(merchantId, currencyEnum, true, LocalDateTimeUtil.beginOfDay(date), LocalDateTimeUtil.endOfDay(date));
            case OLD_MEMBER_DEPOSIT:
                return cashDepositOrderService.countGt30dayDepositMemberByCreateTime(merchantId, currencyEnum, LocalDateTimeUtil.beginOfDay(date), LocalDateTimeUtil.endOfDay(date));
            default:
                throw new IllegalArgumentException();
        }
    }

    /**
     * 根据日期、留存类型、留存日期类型获取留存比率
     * @param date
     * @param typeEnum
     * @return
     */
    private Long getRetentionMemberRate(Long merchantId, CurrencyEnum currencyEnum, LocalDate retentionDate, LocalDate date, ReportMemberRetentionTypeEnum typeEnum) {
        List<Long> memberIdList;
        //根据数据时间查询数据时间时的会员ID列表
        switch (typeEnum) {
            case NEW:
                memberIdList = memberService.getMemberIdListByCreateTime(merchantId, currencyEnum, LocalDateTimeUtil.beginOfDay(retentionDate), LocalDateTimeUtil.endOfDay(retentionDate));
                break;
            case ACTIVE:
                memberIdList = reportMemberDateService.getMemberIdListByCreateDateAndDeposit(merchantId, currencyEnum, false, new DateRangeDTO(retentionDate, retentionDate));
                break;
            case DAY_DEPOSIT:
                memberIdList = cashDepositOrderService.getDepositMemberIdListByCreateTime(merchantId, currencyEnum, LocalDateTimeUtil.beginOfDay(retentionDate), LocalDateTimeUtil.endOfDay(retentionDate));
                break;
            case FIRST_DEPOSIT:
                memberIdList = reportFirstDepositHisService.getMemberIdListFirstDepositByPayTime(merchantId, currencyEnum, false, LocalDateTimeUtil.beginOfDay(retentionDate), LocalDateTimeUtil.endOfDay(retentionDate));
                break;
            case NEW_MEMBER_DEPOSIT:
                memberIdList = reportFirstDepositHisService.getMemberIdListFirstDepositByPayTime(merchantId, currencyEnum, true, LocalDateTimeUtil.beginOfDay(retentionDate), LocalDateTimeUtil.endOfDay(retentionDate));
                break;
            case OLD_MEMBER_DEPOSIT:
                memberIdList = cashDepositOrderService.selectGt30dayDepositMemberIdListByCreateTime(merchantId, currencyEnum, LocalDateTimeUtil.beginOfDay(retentionDate), LocalDateTimeUtil.endOfDay(retentionDate));
                break;
            default:
                throw new IllegalArgumentException();
        }
        if (CollectionUtil.isEmpty(memberIdList))
            return 0L;
        //使用上面的会员ID列表去查询，在指定日期登录过的数量
        Long activeMemberCount = commonLoginLogService.countMemberIdListByLastLoginTime(merchantId, memberIdList, LocalDateTimeUtil.beginOfDay(date), LocalDateTimeUtil.endOfDay(LocalDate.now()));
        Long memberIdCount = (long) memberIdList.size();

        return BigDecimal.valueOf(activeMemberCount)
                .divide(BigDecimal.valueOf(memberIdCount), 4, RoundingMode.HALF_DOWN)
                .multiply(BigDecimal.valueOf(TEN_THOUSAND))
                .longValue();
    }

    private void setRateByDateEnum(ReportMemberRetention retention, Long rate, ReportMemberRetentionDateEnum dateEnum) {
        switch (dateEnum) {
            case DAY_1:
                retention.setOneDayRetentionRate(rate);
                break;
            case DAY_3:
                retention.setThreeDayRetentionRate(rate);
                break;
            case DAY_5:
                retention.setFiveDayRetentionRate(rate);
                break;
            case DAY_7:
                retention.setSevenDayRetentionRate(rate);
                break;
            case DAY_15:
                retention.setFifteenDayRetentionRate(rate);
                break;
            case DAY_30:
                retention.setThirtyDayRetentionRate(rate);
                break;
        }
    }

    private ReportMemberRetention filterAndSetDateEnum(ReportMemberRetention retention, LocalDate date) {
        ReportMemberRetentionDateEnum dateEnum = ReportMemberRetentionDateEnum.computeDateEnum(retention.getCreateDate(), date);
        retention.setRetentionDateEnum(dateEnum);
        if (dateEnum == null)
            return null;
        else
            return retention;
    }

}


