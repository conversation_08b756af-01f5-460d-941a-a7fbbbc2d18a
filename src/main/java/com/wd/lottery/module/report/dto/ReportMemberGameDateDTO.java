package com.wd.lottery.module.report.dto;

import com.wd.lottery.module.common.constants.GameCategoryEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

@Data
public class ReportMemberGameDateDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long merchantId;

    private Long memberId;

    private List<Long> memberIdList;

    private LocalDate startDate;

    private LocalDate endDate;

    private String gameCode;

    private String platformCode;

    private GameCategoryEnum gameCategoryEnum;

    private Collection<GameCategoryEnum> gameCategoryEnumList;
}
