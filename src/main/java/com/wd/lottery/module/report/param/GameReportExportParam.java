package com.wd.lottery.module.report.param;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class GameReportExportParam {
    private Long merchantId;
    private LocalDate startDate;
    private LocalDate endDate;
    private CurrencyEnum currencyEnum;
    private Long memberId;
    private GameCategoryEnum gameCategoryEnum;
    private Long limit;
    private Integer id;


}
