package com.wd.lottery.module.report.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.InviteTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Getter
@Setter
@TableName("report_member_game_date")
@Schema(name = "ReportMemberGameDate", description = "")
public class ReportMemberGameDate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @Schema(description = "商户id")
    private Long merchantId;

    @Schema(description = "会员id")
    private Long memberId;

    @Schema(description = "会员名称")
    private String memberName;

    @Schema(description = "打码订单数量")
    private Long betCount = 0L;

    @Schema(description = "打码金额")
    private Long betAmount = 0L;

    @Schema(description = "游戏手续费：1、系统游戏手续费2、可以存三方额外收费游戏手续费3、可以存三方jackpot预付费")
    private Long serviceFee = 0L;

    @Schema(description = "有效打码金额")
    private Long validBetAmount = 0L;

    @Schema(description = "中奖金额")
    private Long winAmount = 0L;

    @Schema(description = "盈亏金额")
    private Long winLossAmount = 0L;

    private Long jackpot = 0L;

    @Schema(description = "锦标赛奖金")
    private Long tournamentReward = 0L;

    @Schema(description = "游戏代码")
    private String gameCode;

    @Schema(description = "游戏名称")
    private String gameName;

    @Schema(description = "游戏大类")
    private GameCategoryEnum gameCategoryEnum;

    @Schema(description = "平台代码")
    private String platformCode;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "数据归属日期")
    private LocalDate createDate;

    @Schema(description = "币种")
    private CurrencyEnum currencyEnum;

    @Schema(description = "邀请类型", example = "OFFICIAL")
    private InviteTypeEnum inviteTypeEnum;

    @Schema(description = "是否是直屬邀請", example = "TRUE")
    private BooleanEnum isDirect;

    @Schema(description = "渠道id", example = "123")
    private Long channelId;

    @TableField(exist = false)
    private BigDecimal winRate = BigDecimal.ZERO;

    @TableField(exist = false)
    private Long betPopulation = 0L;
}
