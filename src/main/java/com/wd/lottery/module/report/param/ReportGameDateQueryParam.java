package com.wd.lottery.module.report.param;

import com.wd.lottery.common.api.BaseParam;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

@Data
public class ReportGameDateQueryParam extends BaseParam implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "開始日期", example = "2024-06-01")
    @NotNull
    private LocalDate startDate;

    @Schema(description = "結束日期", example = "2024-06-30")
    @NotNull
    private LocalDate endDate;

    private GameCategoryEnum gameCategoryEnum;

    @Schema(description = "UID", example = "1000000")
    private Long memberId;

    @Schema(hidden = true)
    private Long merchantId;

    @Schema(hidden = true)
    private CurrencyEnum currencyEnum;

    @Schema(description = "語言", example = "zh-CN")
    private String language;

    @Schema(description = "是否导出全部 不傳就是true")
    private Boolean isAll = Boolean.TRUE;

    @Schema(description = "操作者", hidden = true)
    private String createBy;
}
