package com.wd.lottery.module.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.util.CommonUtil;
import com.wd.lottery.common.util.SqlUtil;
import com.wd.lottery.module.common.config.ExportExcelConfig;
import com.wd.lottery.module.common.constants.ExportTypeEnum;
import com.wd.lottery.module.common.export.GameReportExportExcelStrategy;
import com.wd.lottery.module.common.param.AsyncExportParam;
import com.wd.lottery.module.common.service.ExportExcelService;
import com.wd.lottery.module.merchant.entity.Merchant;
import com.wd.lottery.module.merchant.service.MerchantService;
import com.wd.lottery.module.report.dto.ListGroupGameCategoryByDateDTO;
import com.wd.lottery.module.report.dto.ReportMemberGameDateDTO;
import com.wd.lottery.module.report.entity.ReportMemberGameDate;
import com.wd.lottery.module.report.mapper.ReportMemberGameDateMapper;
import com.wd.lottery.module.report.param.*;
import com.wd.lottery.module.report.param.ReportGameDateQueryParam;
import com.wd.lottery.module.report.param.ReportGamePlatformDateMemberQueryParam;
import com.wd.lottery.module.report.param.ReportGamePlatformDateQueryParam;
import com.wd.lottery.module.report.param.ReportGameStatisticsQueryParam;
import com.wd.lottery.module.report.service.ReportMemberGameDateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.wd.lottery.common.constans.Constants.DEFAULT_SYSTEM_MERCHANT_ID;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Slf4j
@AllArgsConstructor
@Service
public class ReportMemberGameDateServiceImpl extends ServiceImpl<ReportMemberGameDateMapper, ReportMemberGameDate> implements ReportMemberGameDateService {
    private final ExportExcelService exportExcelService;
    private final MerchantService merchantService;

    @Override
    public ReportMemberGameDate getOneGroupByMemberIdOrElseNew(ReportMemberGameDateDTO reportMemberGameDateDTO) {
        Assert.notNull(reportMemberGameDateDTO.getMerchantId());
        Assert.notNull(reportMemberGameDateDTO.getMemberId());
        Assert.notNull(reportMemberGameDateDTO.getStartDate());
        Assert.notNull(reportMemberGameDateDTO.getEndDate());

        LambdaQueryWrapper<ReportMemberGameDate> lambdaQueryWrapper = getSelectSumWrapper(reportMemberGameDateDTO.getMerchantId(), reportMemberGameDateDTO.getStartDate(), reportMemberGameDateDTO.getEndDate());

        lambdaQueryWrapper
                .eq(ReportMemberGameDate::getMemberId, reportMemberGameDateDTO.getMemberId())
                .eq(StrUtil.isNotBlank(reportMemberGameDateDTO.getGameCode()), ReportMemberGameDate::getGameCode, reportMemberGameDateDTO.getGameCode())
                .eq(StrUtil.isNotBlank(reportMemberGameDateDTO.getPlatformCode()), ReportMemberGameDate::getPlatformCode, reportMemberGameDateDTO.getPlatformCode())
                .eq(reportMemberGameDateDTO.getGameCategoryEnum() != null, ReportMemberGameDate::getGameCategoryEnum, reportMemberGameDateDTO.getGameCategoryEnum())
                .in(CollUtil.isNotEmpty(reportMemberGameDateDTO.getGameCategoryEnumList()), ReportMemberGameDate::getGameCategoryEnum, reportMemberGameDateDTO.getGameCategoryEnumList())
                .groupBy(ReportMemberGameDate::getMemberId)
                .last(Constants.SQL_LIMIT_1);
        ReportMemberGameDate reportMemberGameDate = getOne(lambdaQueryWrapper);
        return Optional.ofNullable(reportMemberGameDate).orElse(new ReportMemberGameDate());
    }

    @Override
    public List<ReportMemberGameDate> getListGroupGameCategoryByDate(ListGroupGameCategoryByDateDTO listGroupGameCategoryByDateDTO) {
        Assert.notNull(listGroupGameCategoryByDateDTO.getMerchantId());
        Assert.notNull(listGroupGameCategoryByDateDTO.getStartDate());
        Assert.notNull(listGroupGameCategoryByDateDTO.getEndDate());

        Long merchantId = listGroupGameCategoryByDateDTO.getMerchantId();
        LocalDate startDate = listGroupGameCategoryByDateDTO.getStartDate();
        LocalDate endDate = listGroupGameCategoryByDateDTO.getEndDate();

        LambdaQueryWrapper<ReportMemberGameDate> lambdaQueryWrapper = getSelectSumWrapper(listGroupGameCategoryByDateDTO.getMerchantId(), startDate, endDate);

        lambdaQueryWrapper
                .groupBy(ReportMemberGameDate::getMemberId, ReportMemberGameDate::getGameCategoryEnum);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<ReportMemberGameDate> getListGroupByMemberId(ReportMemberGameDateDTO reportMemberGameDateDTO) {
        Assert.notNull(reportMemberGameDateDTO.getMerchantId());
        Assert.notEmpty(reportMemberGameDateDTO.getMemberIdList());
        Assert.notNull(reportMemberGameDateDTO.getStartDate());
        Assert.notNull(reportMemberGameDateDTO.getEndDate());

        LambdaQueryWrapper<ReportMemberGameDate> lambdaQueryWrapper = getSelectSumWrapper(reportMemberGameDateDTO.getMerchantId(), reportMemberGameDateDTO.getStartDate(), reportMemberGameDateDTO.getEndDate());

        lambdaQueryWrapper
                .in(ReportMemberGameDate::getMemberId, reportMemberGameDateDTO.getMemberIdList())
                .groupBy(ReportMemberGameDate::getMemberId);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<ReportMemberGameDate> getListGroupByPlatformCode(ReportGameDateQueryParam reportGameDateQueryParam) {
        Assert.notNull(reportGameDateQueryParam.getMerchantId());
        Assert.notNull(reportGameDateQueryParam.getCurrencyEnum());
        Assert.notNull(reportGameDateQueryParam.getStartDate());
        Assert.notNull(reportGameDateQueryParam.getEndDate());

        LambdaQueryWrapper<ReportMemberGameDate> lambdaQueryWrapper = getSelectSumWrapper(reportGameDateQueryParam.getMerchantId(), reportGameDateQueryParam.getStartDate(), reportGameDateQueryParam.getEndDate());

        lambdaQueryWrapper
                .eq(ReportMemberGameDate::getCurrencyEnum, reportGameDateQueryParam.getCurrencyEnum())
                .eq(Objects.nonNull(reportGameDateQueryParam.getMemberId()), ReportMemberGameDate::getMemberId, reportGameDateQueryParam.getMemberId())
                .eq(Objects.nonNull(reportGameDateQueryParam.getGameCategoryEnum()), ReportMemberGameDate::getGameCategoryEnum, reportGameDateQueryParam.getGameCategoryEnum())
                .groupBy(ReportMemberGameDate::getPlatformCode);
        List<ReportMemberGameDate> list = list(lambdaQueryWrapper);
        trySetWinRate(list);
        return list;
    }

    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    @Override
    public List<ReportMemberGameDate> getListGroupByPlatformCodeAllMerchant(ReportGameDateQueryParam reportGameDateQueryParam) {
        Assert.notNull(reportGameDateQueryParam.getCurrencyEnum());
        Assert.notNull(reportGameDateQueryParam.getStartDate());
        Assert.notNull(reportGameDateQueryParam.getEndDate());

        List<Long> merchantIds = merchantService.getEnableMerchantList()
                .stream()
                .filter(it -> it.getCurrencyEnumList().contains(reportGameDateQueryParam.getCurrencyEnum()))
                .map(Merchant::getId)
                .filter(id -> !Objects.equals(id, DEFAULT_SYSTEM_MERCHANT_ID)).collect(Collectors.toList());

        if (merchantIds.isEmpty()) {
            return Collections.emptyList();
        }

        List<ReportMemberGameDate> allResults = new ArrayList<>();
        for (Long merchantId : merchantIds) {
            reportGameDateQueryParam.setMerchantId(merchantId);
            List<ReportMemberGameDate> gameDates = getListGroupByPlatformCode(reportGameDateQueryParam);
            allResults.addAll(gameDates);
        }

        Map<String, ReportMemberGameDate> platformResults = new HashMap<>();
        for (ReportMemberGameDate result : allResults) {
            String platformCode = result.getPlatformCode();
            if (platformResults.containsKey(platformCode)) {
                ReportMemberGameDate existing = platformResults.get(platformCode);
                // 合并数据
                existing.setBetPopulation(existing.getBetPopulation() + result.getBetPopulation());
                existing.setBetCount(existing.getBetCount() + result.getBetCount());
                existing.setBetAmount(existing.getBetAmount() + result.getBetAmount());
                existing.setValidBetAmount(existing.getValidBetAmount() + result.getValidBetAmount());
                existing.setWinAmount(existing.getWinAmount() + result.getWinAmount());
                existing.setWinLossAmount(existing.getWinLossAmount() + result.getWinLossAmount());
                existing.setServiceFee(existing.getServiceFee() + result.getServiceFee());
                existing.setJackpot(existing.getJackpot() + result.getJackpot());
                existing.setTournamentReward(existing.getTournamentReward() + result.getTournamentReward());
            } else {
                platformResults.put(platformCode, result);
            }
        }

        return new ArrayList<>(platformResults.values());
    }

    @Override
    public List<ReportMemberGameDate> getListGroupByGameCode(ReportGamePlatformDateQueryParam reportGamePlatformDateQueryParam) {
        Assert.notNull(reportGamePlatformDateQueryParam.getMerchantId());
        Assert.notNull(reportGamePlatformDateQueryParam.getCurrencyEnum());
        Assert.notNull(reportGamePlatformDateQueryParam.getStartDate());
        Assert.notNull(reportGamePlatformDateQueryParam.getEndDate());
        Assert.notNull(reportGamePlatformDateQueryParam.getPlatformCode());

        LambdaQueryWrapper<ReportMemberGameDate> lambdaQueryWrapper = getSelectSumWrapper(reportGamePlatformDateQueryParam.getMerchantId(), reportGamePlatformDateQueryParam.getStartDate(), reportGamePlatformDateQueryParam.getEndDate());

        lambdaQueryWrapper
                .eq(ReportMemberGameDate::getPlatformCode, reportGamePlatformDateQueryParam.getPlatformCode())
                .eq(ReportMemberGameDate::getCurrencyEnum, reportGamePlatformDateQueryParam.getCurrencyEnum())
                .eq(Objects.nonNull(reportGamePlatformDateQueryParam.getMemberId()), ReportMemberGameDate::getMemberId, reportGamePlatformDateQueryParam.getMemberId())
                .eq(Objects.nonNull(reportGamePlatformDateQueryParam.getGameCategoryEnum()), ReportMemberGameDate::getGameCategoryEnum, reportGamePlatformDateQueryParam.getGameCategoryEnum())
                .groupBy(ReportMemberGameDate::getGameCode);

        lambdaQueryWrapper.orderBy(true,
                reportGamePlatformDateQueryParam.getSortEnum().getIsAsc(),
                reportGamePlatformDateQueryParam.getReportGamePlatformDateSortEnum().getFunc());

        List<ReportMemberGameDate> list = list(lambdaQueryWrapper);
        trySetWinRate(list);
        return list;
    }

    @Override
    public List<ReportMemberGameDate> getListGroupByMember(ReportGamePlatformDateMemberQueryParam reportGamePlatformDateQueryParam) {
        Assert.notNull(reportGamePlatformDateQueryParam.getMerchantId());
        Assert.notNull(reportGamePlatformDateQueryParam.getCurrencyEnum());
        Assert.notNull(reportGamePlatformDateQueryParam.getStartDate());
        Assert.notNull(reportGamePlatformDateQueryParam.getEndDate());
        Assert.notNull(reportGamePlatformDateQueryParam.getPlatformCode());

        LambdaQueryWrapper<ReportMemberGameDate> lambdaQueryWrapper = getSelectSumWrapper(reportGamePlatformDateQueryParam.getMerchantId(), reportGamePlatformDateQueryParam.getStartDate(), reportGamePlatformDateQueryParam.getEndDate());

        lambdaQueryWrapper
                .eq(ReportMemberGameDate::getPlatformCode, reportGamePlatformDateQueryParam.getPlatformCode())
                .eq(ReportMemberGameDate::getCurrencyEnum, reportGamePlatformDateQueryParam.getCurrencyEnum())
                .eq(ReportMemberGameDate::getGameCode, reportGamePlatformDateQueryParam.getGameCode())
                .eq(Objects.nonNull(reportGamePlatformDateQueryParam.getMemberId()), ReportMemberGameDate::getMemberId, reportGamePlatformDateQueryParam.getMemberId())
                .eq(Objects.nonNull(reportGamePlatformDateQueryParam.getGameCategoryEnum()), ReportMemberGameDate::getGameCategoryEnum, reportGamePlatformDateQueryParam.getGameCategoryEnum())
                .groupBy(ReportMemberGameDate::getMemberId);

        lambdaQueryWrapper.orderBy(true,
                reportGamePlatformDateQueryParam.getSortEnum().getIsAsc(),
                reportGamePlatformDateQueryParam.getReportGamePlatformDateSortEnum().getFunc());

        List<ReportMemberGameDate> list = list(lambdaQueryWrapper);
        trySetWinRate(list);
        return list;
    }

    @Override
    public List<ReportMemberGameDate> getListGroupByGameCategoryEnum(ReportGameStatisticsQueryParam reportGameStatisticsQueryParam) {
        Assert.notNull(reportGameStatisticsQueryParam.getMerchantId());
        Assert.notNull(reportGameStatisticsQueryParam.getStartDate());
        Assert.notNull(reportGameStatisticsQueryParam.getEndDate());
        Assert.notNull(reportGameStatisticsQueryParam.getMemberId());

        LambdaQueryWrapper<ReportMemberGameDate> lambdaQueryWrapper = getSelectSumWrapperForGameStatistic(reportGameStatisticsQueryParam.getMerchantId(), reportGameStatisticsQueryParam.getStartDate(), reportGameStatisticsQueryParam.getEndDate());

        lambdaQueryWrapper
                .eq(Objects.nonNull(reportGameStatisticsQueryParam.getMemberId()), ReportMemberGameDate::getMemberId, reportGameStatisticsQueryParam.getMemberId())
                .groupBy(ReportMemberGameDate::getGameCategoryEnum)
                .orderByAsc(ReportMemberGameDate::getGameCategoryEnum);
        return list(lambdaQueryWrapper);
    }

    private static void trySetWinRate(List<ReportMemberGameDate> list) {
        if(CollUtil.isNotEmpty(list)){
            for(ReportMemberGameDate reportMemberGameDate: list){
                reportMemberGameDate.setWinRate(CommonUtil.calculateRate(reportMemberGameDate.getWinLossAmount(), reportMemberGameDate.getBetAmount()));
            }
        }
    }

    @NotNull
    private static LambdaQueryWrapper<ReportMemberGameDate> getSelectSumWrapper(Long merchantId, LocalDate startDate, LocalDate endDate) {
        Assert.notNull(merchantId);
        Assert.notNull(startDate);
        Assert.notNull(endDate);
        QueryWrapper<ReportMemberGameDate> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(
                SqlUtil.selectCountDistinct(ReportMemberGameDate::getMemberId, ReportMemberGameDate::getBetPopulation),
                SqlUtil.selectMax(ReportMemberGameDate::getMemberId),
                SqlUtil.selectMax(ReportMemberGameDate::getGameCode),
                SqlUtil.selectMax(ReportMemberGameDate::getGameName),
                SqlUtil.selectMax(ReportMemberGameDate::getGameCategoryEnum),
                SqlUtil.selectMax(ReportMemberGameDate::getCurrencyEnum),
                SqlUtil.selectMax(ReportMemberGameDate::getPlatformCode),
                SqlUtil.selectMax(ReportMemberGameDate::getPlatformName),
                SqlUtil.selectSum(ReportMemberGameDate::getBetCount),
                SqlUtil.selectSum(ReportMemberGameDate::getBetAmount),
                SqlUtil.selectSum(ReportMemberGameDate::getValidBetAmount),
                SqlUtil.selectSum(ReportMemberGameDate::getWinAmount),
                SqlUtil.selectSum(ReportMemberGameDate::getWinLossAmount),
                SqlUtil.selectSum(ReportMemberGameDate::getServiceFee),
                SqlUtil.selectSum(ReportMemberGameDate::getJackpot),
                SqlUtil.selectSum(ReportMemberGameDate::getTournamentReward)
        );
        return queryWrapper.lambda()
                .eq(ReportMemberGameDate::getMerchantId, merchantId)
                .between(ReportMemberGameDate::getCreateDate, startDate, endDate);
    }

    @NotNull
    private static LambdaQueryWrapper<ReportMemberGameDate> getSelectSumWrapperForGameStatistic(Long merchantId, LocalDate startDate, LocalDate endDate) {
        Assert.notNull(merchantId);
        Assert.notNull(startDate);
        Assert.notNull(endDate);
        QueryWrapper<ReportMemberGameDate> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(
                SqlUtil.selectMax(ReportMemberGameDate::getGameCategoryEnum),
                SqlUtil.selectSum(ReportMemberGameDate::getBetCount),
                SqlUtil.selectSum(ReportMemberGameDate::getBetAmount),
                SqlUtil.selectSum(ReportMemberGameDate::getWinAmount)
        );
        return queryWrapper.lambda()
                .eq(ReportMemberGameDate::getMerchantId, merchantId)
                .between(ReportMemberGameDate::getCreateDate, startDate, endDate);
    }

    @Override
    public ApiResult<?> exportGameReport(ReportGameDateQueryParam param, List<ReportMemberGameDate> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return ApiResult.failed(CommonCode.NO_DATA);
        }

        AsyncExportParam asyncExportParam = new AsyncExportParam();
        asyncExportParam.setMerchantId(param.getMerchantId());
        asyncExportParam.setLanguage(param.getLanguage());
        asyncExportParam.setCreateBy(param.getCreateBy());
        asyncExportParam.setExportType(ExportTypeEnum.GAME_REPORT);
        asyncExportParam.setFileName(ExportTypeEnum.GAME_REPORT.getDesc() + "_" + DateUtil.today() + ".xlsx");

        log.info("exportParam..{},AsyncExportParam...{}", param, asyncExportParam);
        GameReportExportExcelStrategy strategy =
                new GameReportExportExcelStrategy(dataList);
        exportExcelService.asyncExportExcel(asyncExportParam, strategy, false);
        return ApiResult.success();
    }

}
