package com.wd.lottery.module.report.mq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.RabbitMQConstants;
import com.wd.lottery.common.util.CommonUtil;
import com.wd.lottery.module.cash.dto.CashDataStatisticsDTO;
import com.wd.lottery.module.member.constants.MemberTypeEnum;
import com.wd.lottery.module.report.strategy.reportcomsumer.*;
import com.rabbitmq.client.Channel;
import com.wd.lottery.module.report.strategy.reportcomsumer.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
@AllArgsConstructor
public class ReportConsumer {
    private final ReportMemberDateConsumerStrategy reportMemberDateConsumerStrategy;

    private final ReportMemberTotalConsumerStrategy reportMemberTotalConsumerStrategy;

    private final ReportMemberGameDateConsumerStrategy reportMemberGameDateConsumerStrategy;

    private final ReportMemberActivityTotalConsumerStrategy reportMemberActivityTotalConsumerStrategy;

    //@RabbitListener(queues = RabbitMQConstants.REPORT_QUEUE, containerFactory = RabbitMQConstants.BATCH_SIMPLE_CONTAINER_FACTORY)
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = RabbitMQConstants.REPORT_MEMBER_DATE_QUEUE),
            exchange = @Exchange(value = RabbitMQConstants.CASH_DATA_STATISTICS_AGGREGATE_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = RabbitMQConstants.CASH_DATA_STATISTICS_AGGREGATE_CASH_ALL_ROUTE_KEY))
    @Transactional
    public void consumerMemberDate(Channel channel, Message message) {
        processMessage(channel, message, reportMemberDateConsumerStrategy);
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = RabbitMQConstants.REPORT_MEMBER_TOTAL_QUEUE),
            exchange = @Exchange(value = RabbitMQConstants.CASH_DATA_STATISTICS_AGGREGATE_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = RabbitMQConstants.CASH_DATA_STATISTICS_AGGREGATE_CASH_ALL_ROUTE_KEY))
    @Transactional
    public void consumerMemberTotal(Channel channel, Message message) {
        processMessage(channel, message, reportMemberTotalConsumerStrategy);
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = RabbitMQConstants.REPORT_MEMBER_GAME_DATE_QUEUE),
            exchange = @Exchange(value = RabbitMQConstants.CASH_DATA_STATISTICS_AGGREGATE_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = RabbitMQConstants.CASH_DATA_STATISTICS_AGGREGATE_CASH_BET_ORDER_ALL_ROUTE_KEY))
    @Transactional
    public void consumerMemberGameDate(Channel channel, Message message) {
        processMessage(channel, message, reportMemberGameDateConsumerStrategy);
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = RabbitMQConstants.REPORT_MEMBER_ACTIVITY_TOTAL_QUEUE),
            exchange = @Exchange(value = RabbitMQConstants.CASH_DATA_STATISTICS_AGGREGATE_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = RabbitMQConstants.CASH_DATA_STATISTICS_AGGREGATE_CASH_WALLET_LOG_HIS_ROUTE_KEY))
    @Transactional
    public void consumerMemberActivityTotal(Channel channel, Message message) {
        processMessage(channel, message, reportMemberActivityTotalConsumerStrategy);
    }

    private <K, V> void processMessage(Channel channel, Message message, ReportConsumerStrategy<K,V> reportConsumerStrategy) {
        String msg = CommonUtil.decompress(message.getBody());
        try {
            List<CashDataStatisticsDTO> cashDataStatisticsDTOList = getCashDataStatisticsDTOList(channel, message, msg);
            if (cashDataStatisticsDTOList == null) return;

            Map<K, V> resultMap = new HashMap<>();
            for (CashDataStatisticsDTO cashDataStatisticsDTO : cashDataStatisticsDTOList) {
                reportConsumerStrategy.assemble(cashDataStatisticsDTO, resultMap);
            }

            if (CollUtil.isEmpty(resultMap)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            reportConsumerStrategy.saveOrUpdateBatch(resultMap.values());
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            exceptionNotAck(channel, message, e, msg);
        }
    }

    @Nullable
    private static List<CashDataStatisticsDTO> getCashDataStatisticsDTOList(Channel channel, Message message, String msg) throws IOException {
        List<CashDataStatisticsDTO> cashDataStatisticsDTOList = JSONUtil.toBean(msg, new TypeReference<List<CashDataStatisticsDTO>>() {
        }, true);

        if (CollUtil.isEmpty(cashDataStatisticsDTOList)) {
            log.warn("cashDataStatisticsDTOList is empty, msg:{}", msg);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return null;
        }

        cashDataStatisticsDTOList = cashDataStatisticsDTOList.stream()
                .filter(cashDataStatisticsDTO -> cashDataStatisticsDTO.getMemberTypeEnum() == MemberTypeEnum.FORMAL_MEMBER)
                .sorted(Comparator.comparing(CashDataStatisticsDTO::getMemberId)
                        .thenComparing(CashDataStatisticsDTO::getBelongDate))
                .collect(Collectors.toList());
        return cashDataStatisticsDTOList;
    }

    private static void exceptionNotAck(Channel channel, Message message, Exception e, String msg) {
        try {
            log.error("{}-Not consumer exception, msg:{}", message.getMessageProperties().getConsumerQueue(), msg, e);
            TimeUnit.SECONDS.sleep(Constants.DEFAULT_RABBIT_CONSUMER_FAIL_SLEEP_SECONDS);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
        } catch (Exception ex) {
            log.error("{}-Not ack exception", message.getMessageProperties().getConsumerQueue(), ex);
        }
    }
}
