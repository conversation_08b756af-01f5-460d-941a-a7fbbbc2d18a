package com.wd.lottery.module.report.dto;

import com.wd.lottery.module.common.constants.GameCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@AllArgsConstructor
public class ReportMemberGameDateKeyDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "会员id")
    private Long memberId;

    @Schema(description = "数据归属日期")
    private LocalDate createDate;

    @Schema(description = "游戏代码")
    private String gameCode;

    private GameCategoryEnum gameCategoryEnum;

    @Schema(description = "游戏平台编码")
    private String platformCode;
}
