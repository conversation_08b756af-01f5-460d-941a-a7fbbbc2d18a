package com.wd.lottery.module.report.param;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.QuerySortEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.report.constans.ReportGamePlatformDateSortEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

@Data
public class ReportGamePlatformDateMemberQueryParam implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "開始日期", example = "2024-06-01")
    @NotNull
    private LocalDate startDate;

    @Schema(description = "結束日期", example = "2024-06-30")
    @NotNull
    private LocalDate endDate;

    @Schema(description = "遊戲平台", example = "KG")
    @NotBlank
    private String platformCode;

    private GameCategoryEnum gameCategoryEnum;

    @Schema(description = "UID", example = "1000000")
    private Long memberId;

    @Schema(description = "游戏code")
    private String gameCode;

    @Schema(description = "查询排序类型", example = "BET_AMOUNT")
    private ReportGamePlatformDateSortEnum reportGamePlatformDateSortEnum = ReportGamePlatformDateSortEnum.BET_AMOUNT;

    @Schema(description = "排序方式", example = "Desc")
    private QuerySortEnum sortEnum = QuerySortEnum.Desc;

    @Schema(hidden = true)
    private Long merchantId;

    @Schema(hidden = true)
    private CurrencyEnum currencyEnum;

}
