package com.wd.lottery.module.report.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.SqlUtil;
import com.wd.lottery.common.util.Tup2;
import com.wd.lottery.module.common.config.ExportExcelConfig;
import com.wd.lottery.module.common.constants.ExcelExportParamEnum;
import com.wd.lottery.module.common.constants.ExportTypeEnum;
import com.wd.lottery.module.common.export.FirstDepositExportExcelStrategy;
import com.wd.lottery.module.common.param.AsyncExportParam;
import com.wd.lottery.module.common.service.ExportExcelService;
import com.wd.lottery.module.member.service.MemberProxyService;
import com.wd.lottery.module.report.constans.DepositTypeEnum;
import com.wd.lottery.module.report.dto.*;
import com.wd.lottery.module.report.dto.FirstDepositRecordExportDTO;
import com.wd.lottery.module.report.dto.ReportDepositsStatisticsDTO;
import com.wd.lottery.module.report.dto.ReportFirstDepositDTO;
import com.wd.lottery.module.report.dto.ReportSummaryDTO;
import com.wd.lottery.module.report.entity.ReportFirstDepositHis;
import com.wd.lottery.module.report.mapper.ReportFirstDepositHisMapper;
import com.wd.lottery.module.report.param.ReportDataOverviewQueryParam;
import com.wd.lottery.module.report.service.ReportFirstDepositHisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReportFirstDepositHisServiceImpl extends ServiceImpl<ReportFirstDepositHisMapper, ReportFirstDepositHis> implements ReportFirstDepositHisService {

    private final ReportFirstDepositHisMapper reportFirstDepositHisMapper;

    private final MemberProxyService memberProxyService;

    private final ExportExcelConfig exportExcelConfig;

    private final ExportExcelService exportExcelService;

    /**
     * B端首充查詢
     *
     * @param reportFirstDepositDto dto
     * @return page
     */
    @Override
    public Page<ReportFirstDepositHis> search(ReportFirstDepositDTO reportFirstDepositDto) {
        Page<ReportFirstDepositHis> reportFirstDepositHisPage = new Page<>(reportFirstDepositDto.getCurrent(), reportFirstDepositDto.getSize());
        LambdaQueryWrapper<ReportFirstDepositHis> queryWrapper = new LambdaQueryWrapper<>();

        if (reportFirstDepositDto.getMemberId() != null) {
            queryWrapper.eq(ReportFirstDepositHis::getMemberId, reportFirstDepositDto.getMemberId());
        }

        if (StringUtils.isNotBlank(reportFirstDepositDto.getOrderNo())) {
            queryWrapper.eq(ReportFirstDepositHis::getOrderNo, reportFirstDepositDto.getOrderNo());
        }

        queryWrapper.eq(ReportFirstDepositHis::getMerchantId, reportFirstDepositDto.getMerchantId());
        queryWrapper.eq(ReportFirstDepositHis::getDepositTypeEnum, reportFirstDepositDto.getDepositTypeEnum());
        queryWrapper.eq(ReportFirstDepositHis::getCurrencyEnum, reportFirstDepositDto.getCurrencyEnum());
        queryWrapper.eq(CollectionUtil.isNotEmpty(reportFirstDepositDto.getChannelIdSet()),ReportFirstDepositHis::getIsDirect,BooleanEnum.TRUE);
        queryWrapper.in(CollectionUtil.isNotEmpty(reportFirstDepositDto.getChannelIdSet()),ReportFirstDepositHis::getChannelId,reportFirstDepositDto.getChannelIdSet());
        if (StrUtil.isNotBlank(reportFirstDepositDto.getStartPayTime()) && StrUtil.isNotBlank(reportFirstDepositDto.getEndPayTime())){
            queryWrapper.ge(ReportFirstDepositHis::getPayTime, reportFirstDepositDto.getStartPayTime());
            queryWrapper.le(ReportFirstDepositHis::getPayTime, reportFirstDepositDto.getEndPayTime());
            queryWrapper.orderByDesc(ReportFirstDepositHis::getPayTime);
        }
        if (StrUtil.isNotBlank(reportFirstDepositDto.getStartRegisterTime()) && StrUtil.isNotBlank(reportFirstDepositDto.getEndRegisterTime())){
            queryWrapper.ge(ReportFirstDepositHis::getRegisterTime, reportFirstDepositDto.getStartRegisterTime());
            queryWrapper.le(ReportFirstDepositHis::getRegisterTime, reportFirstDepositDto.getEndRegisterTime());
            queryWrapper.orderByDesc(ReportFirstDepositHis::getRegisterTime);
        }
        Page<ReportFirstDepositHis> page = super.page(reportFirstDepositHisPage, queryWrapper);
        reportFirstDepositHisPage.setRecords(page.getRecords());
        reportFirstDepositHisPage.setTotal(page.getTotal());
        reportFirstDepositHisPage.setPages(page.getPages());
        reportFirstDepositHisPage.setCurrent(page.getCurrent());
        reportFirstDepositHisPage.setSize(page.getSize());
        return reportFirstDepositHisPage;
    }

    @Override
    public List<ReportFirstDepositHis> searchFirstDepositByDate(Long merchantId, LocalDateTime startTime, LocalDateTime endTime, List<CurrencyEnum> currencyEnumList) {
        LambdaQueryWrapper<ReportFirstDepositHis> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(ReportFirstDepositHis::getMerchantId, merchantId);
        queryWrapper.in(ReportFirstDepositHis::getCurrencyEnum, currencyEnumList);
        queryWrapper.eq(ReportFirstDepositHis::getDepositTypeEnum, DepositTypeEnum.FIRST);
        queryWrapper.ge(ReportFirstDepositHis::getPayTime, startTime);
        queryWrapper.le(ReportFirstDepositHis::getPayTime, endTime);

        return list(queryWrapper);
    }

    /**
     * 查詢當前充值總金額
     *
     * @param reportFirstDepositDto dto
     * @return long
     */
    @Override
    public Long totalMoney(ReportFirstDepositDTO reportFirstDepositDto) {
        return reportFirstDepositHisMapper.totalMoney(reportFirstDepositDto);
    }

    /**
     * 首充報表統計(人數,金額)
     *
     * @param reportSummaryDto dto
     * @return dto
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_LONG_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<ReportDepositsStatisticsDTO> statistics(ReportSummaryDTO reportSummaryDto) {
        List<ReportDepositsStatisticsDTO> reportDepositsStatisticsDtoList = new ArrayList<>();
        List<DepositTypeEnum> depositTypeEnumList = Arrays.stream(DepositTypeEnum.values()).collect(Collectors.toList());
        depositTypeEnumList.forEach(depositTypeEnum -> {
            ReportDepositsStatisticsDTO reportDepositsStatisticsDto = new ReportDepositsStatisticsDTO();
            reportDepositsStatisticsDto.setDepositTypeEnum(depositTypeEnum);
            reportDepositsStatisticsDto.setCount(Constants.ZERO_LONG);
            reportDepositsStatisticsDto.setMoney(Constants.ZERO_LONG);
            reportDepositsStatisticsDtoList.add(reportDepositsStatisticsDto);
        });

        QueryWrapper<ReportFirstDepositHis> reportFirstDepositHisQueryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ReportFirstDepositHis> queryWrapper = reportFirstDepositHisQueryWrapper
                .select(SqlUtil.selectCount(ReportFirstDepositHis::getDepositPopulation),
                        SqlUtil.selectSum(ReportFirstDepositHis::getMoney, ReportFirstDepositHis::getTotalExchangeRealMoney),
                        SqlUtil.select(ReportFirstDepositHis::getDepositTypeEnum)
                ).lambda()
                .eq(ReportFirstDepositHis::getCurrencyEnum, reportSummaryDto.getCurrencyEnum())
                .eq(ReportFirstDepositHis::getMerchantId, reportSummaryDto.getMerchantId())
                .eq(CollectionUtil.isNotEmpty(reportSummaryDto.getChannelIdSet()),ReportFirstDepositHis::getIsDirect,BooleanEnum.TRUE)
                .in(CollectionUtil.isNotEmpty(reportSummaryDto.getChannelIdSet()), ReportFirstDepositHis::getChannelId, reportSummaryDto.getChannelIdSet())
                .between(ReportFirstDepositHis::getPayTime, reportSummaryDto.getStartTime(), reportSummaryDto.getEndTime())
                .groupBy(ReportFirstDepositHis::getDepositTypeEnum);

        Long highMemberId = reportSummaryDto.getHighMemberId();
        if (highMemberId != null) {
            List<Long> memberIds = new ArrayList<>();
            memberIds.add(highMemberId);
            List<Long> memberIdList = memberProxyService.getMemberIdByHighMemberIdAndLevel(reportSummaryDto.getHighMemberId(), reportSummaryDto.getMerchantId(), Constants.ONE_LONG);
            if (CollectionUtil.isNotEmpty(memberIdList)) {
                memberIds.addAll(memberIdList);
            }
            queryWrapper.in(ReportFirstDepositHis::getMemberId, memberIds);
        }

        List<ReportFirstDepositHis> reportFirstDepositHisList = super.list(queryWrapper);
        if (CollectionUtil.isEmpty(reportFirstDepositHisList)) {
            return reportDepositsStatisticsDtoList;
        }

        Map<DepositTypeEnum, ReportFirstDepositHis> reportFirstDepositHisMap = reportFirstDepositHisList.stream().collect(Collectors.toMap(ReportFirstDepositHis::getDepositTypeEnum, Function.identity()));

        for (ReportDepositsStatisticsDTO reportDepositsStatisticsDto : reportDepositsStatisticsDtoList) {
            if (reportFirstDepositHisMap.get(reportDepositsStatisticsDto.getDepositTypeEnum()) != null) {
                reportDepositsStatisticsDto.setDepositTypeEnum(reportFirstDepositHisMap.get(reportDepositsStatisticsDto.getDepositTypeEnum()).getDepositTypeEnum());
                reportDepositsStatisticsDto.setCount(reportFirstDepositHisMap.get(reportDepositsStatisticsDto.getDepositTypeEnum()).getDepositPopulation());
                reportDepositsStatisticsDto.setMoney(reportFirstDepositHisMap.get(reportDepositsStatisticsDto.getDepositTypeEnum()).getTotalExchangeRealMoney());
            }
        }

        return reportDepositsStatisticsDtoList;
    }

    /**
     * 查單筆首充紀錄
     *
     * @param memberId   member uid
     * @param merchantId merchant uid
     * @return entity
     */
    @Override
    public List<ReportFirstDepositHis> findByMemberIdAndMerchantId(Long memberId, Long merchantId, CurrencyEnum currencyEnum) {
        LambdaQueryWrapper<ReportFirstDepositHis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReportFirstDepositHis::getMemberId, memberId);
        queryWrapper.eq(ReportFirstDepositHis::getMerchantId, merchantId);
        queryWrapper.eq(ReportFirstDepositHis::getCurrencyEnum, currencyEnum);
        return super.list(queryWrapper);
    }

    @Override
    public ReportFirstDepositHis getFirstDepositByMemberIdAndMerchantId(Long memberId, Long merchantId, CurrencyEnum currencyEnum) {
        return super.lambdaQuery()
                .eq(ReportFirstDepositHis::getMemberId, memberId)
                .eq(ReportFirstDepositHis::getMerchantId, merchantId)
                .eq(ReportFirstDepositHis::getCurrencyEnum, currencyEnum)
                .eq(ReportFirstDepositHis::getDepositTypeEnum, DepositTypeEnum.FIRST)
                .one();
    }

    /**
     * 新增
     *
     * @param reportFirstDepositHis entity
     */
    @Override
    public void saveData(ReportFirstDepositHis reportFirstDepositHis) {
        if (reportFirstDepositHis.getMoney() > Constants.ZERO_LONG) {
            super.baseMapper.insertIgnore(reportFirstDepositHis);
        }
    }

    @Override
    public List<ReportFirstDepositHis> getFirstDepositByChannel(ReportDataOverviewQueryParam param) {
        return getBaseMapper().getFirstDepositByChannel(param);
    }

    @Override
    public List<ReportFirstDepositHis> getFirstDepositAndWithdrawDataGroupByChannel(ReportDataOverviewQueryParam param) {
        return getBaseMapper().getFirstDepositAndWithdrawDataGroupByChannel(param);
    }


    @Override
    public Long countFirstDepositByPayTime(Long merchantId, CurrencyEnum currencyEnum, boolean isPayTimeSameRegisterTime, LocalDateTime startPayTime, LocalDateTime endPayTime) {
        LambdaQueryWrapper<ReportFirstDepositHis> queryWrapper = new QueryWrapper<ReportFirstDepositHis>()
                .eqSql(isPayTimeSameRegisterTime, SqlUtil.fieldToDate(ReportFirstDepositHis::getPayTime), SqlUtil.fieldToDate(ReportFirstDepositHis::getRegisterTime))
                .lambda()
                .eq(ReportFirstDepositHis::getMerchantId, merchantId)
                .eq(ReportFirstDepositHis::getCurrencyEnum, currencyEnum)
                .eq(ReportFirstDepositHis::getDepositTypeEnum, DepositTypeEnum.FIRST)
                .between(ReportFirstDepositHis::getPayTime, startPayTime, endPayTime);
        return this.count(queryWrapper);
    }

    @Override
    public List<Long> getMemberIdListFirstDepositByPayTime(Long merchantId, CurrencyEnum currencyEnum, boolean isPayTimeSameRegisterTime, LocalDateTime startPayTime, LocalDateTime endPayTime) {
        LambdaQueryWrapper<ReportFirstDepositHis> queryWrapper = new QueryWrapper<ReportFirstDepositHis>()
                .eqSql(isPayTimeSameRegisterTime, SqlUtil.groupByDate(ReportFirstDepositHis::getPayTime), SqlUtil.groupByDate(ReportFirstDepositHis::getRegisterTime))
                .select(SqlUtil.selectDistinct(ReportFirstDepositHis::getMemberId, ReportFirstDepositHis::getMemberId))
                .lambda()
                .eq(ReportFirstDepositHis::getMerchantId, merchantId)
                .eq(ReportFirstDepositHis::getCurrencyEnum, currencyEnum)
                .eq(ReportFirstDepositHis::getDepositTypeEnum, DepositTypeEnum.FIRST)
                .between(ReportFirstDepositHis::getPayTime, startPayTime, endPayTime);
        return this.list(queryWrapper).stream().map(ReportFirstDepositHis::getMemberId).collect(Collectors.toList());
    }

    @Override
    public ApiResult<?> exportFirstDepositRecordReport(ReportFirstDepositDTO param) {
        FirstDepositRecordExportDTO exportParam = new FirstDepositRecordExportDTO();
        buildFirstDepositExportParam(exportParam,param);
        AsyncExportParam asyncExportParam = new AsyncExportParam();
        asyncExportParam.setMerchantId(param.getMerchantId());
        asyncExportParam.setLanguage(param.getLanguage());
        asyncExportParam.setCreateBy(param.getCreateBy());
        asyncExportParam.setExportType(ExportTypeEnum.FIRST_DEPOSIT_RECORD_REPORT);
        asyncExportParam.setFileName(ExportTypeEnum.FIRST_DEPOSIT_RECORD_REPORT.getDesc() + "_" + DateUtil.today() + ".xlsx");

        log.info("exportParam..{},AsyncExportParam...{},map...{}", param, asyncExportParam, JSONUtil.toJsonStr(exportParam));

        FirstDepositExportExcelStrategy strategy =
                new FirstDepositExportExcelStrategy(ExcelExportParamEnum.FIRST_DEPOSIT_RECORD_REPORT.getMapperMethod(),
                        ExcelExportParamEnum.FIRST_DEPOSIT_RECORD_REPORT.getNextSearchField(), exportParam, exportExcelConfig.getLimit(), param.getLanguage());
        exportExcelService.asyncExportExcel(asyncExportParam, strategy, param.getIsAll());
        return ApiResult.success();
    }

    @Override
    public Tup2<Long, Long> getFirstDepositTotalCountAndAmount(Long merchantId, Long highMemberId, Integer level, DepositTypeEnum depositTypeEnum, LocalDateTime startPayTime, LocalDateTime endPayTime) {
        Assert.notNull(merchantId, "merchantId must not be null");
        Assert.notNull(highMemberId, "highMemberId must not be null");
        Assert.notNull(depositTypeEnum, "depositTypeEnum must not be null");

        LambdaQueryWrapper<ReportFirstDepositHis> queryWrapper = new QueryWrapper<ReportFirstDepositHis>()
                .select(
                        SqlUtil.selectCountDistinct(ReportFirstDepositHis::getMemberId, ReportFirstDepositHis::getTotalCount),
                        SqlUtil.selectSum(ReportFirstDepositHis::getMoney, ReportFirstDepositHis::getTotalAmount))
                .lambda();

        queryWrapper.eq(ReportFirstDepositHis::getMerchantId, merchantId);
        queryWrapper.eq(ReportFirstDepositHis::getDepositTypeEnum, depositTypeEnum);
        queryWrapper.between(ReportFirstDepositHis::getPayTime, startPayTime, endPayTime);

        List<Long> subMemberIds = memberProxyService.getMemberIdByHighMemberId(merchantId, highMemberId, level, false);
        if (CollectionUtil.isEmpty(subMemberIds)) {
            // 如果没有直属会员，返回空结果
            return Tup2.of(0L, 0L);
        }
        queryWrapper.in(ReportFirstDepositHis::getMemberId, subMemberIds);


        ReportFirstDepositHis depositHis = this.getOne(queryWrapper);

        return Tup2.of(
                depositHis.getTotalCount() == null ? 0 : depositHis.getTotalCount(),
                depositHis.getTotalAmount() == null ? 0 : depositHis.getTotalAmount());
    }

    private void buildFirstDepositExportParam(FirstDepositRecordExportDTO exportParam, ReportFirstDepositDTO param) {
        exportParam.setMerchantId(param.getMerchantId());
        exportParam.setCurrencyEnum(param.getCurrencyEnum());
        if (Objects.nonNull(param.getMemberId())){
            exportParam.setMemberId(param.getMemberId());
        }
        if (Objects.nonNull(param.getDepositTypeEnum())){
            exportParam.setDepositTypeEnum(param.getDepositTypeEnum());
        }
        if (StringUtils.isNotBlank(param.getOrderNo())) {
            exportParam.setOrderNo(param.getOrderNo());
        }
        if (StringUtils.isNotBlank(param.getStartPayTime()) && StringUtils.isNotBlank(param.getEndPayTime())){
            exportParam.setStartPayTime(param.getStartPayTime());
            exportParam.setEndPayTime(param.getEndPayTime());
        }

        if (StrUtil.isNotBlank(param.getStartRegisterTime()) && StrUtil.isNotBlank(param.getEndRegisterTime())){
            exportParam.setStartRegisterTime(param.getStartRegisterTime());
            exportParam.setEndRegisterTime(param.getEndRegisterTime());
        }
        if (CollUtil.isNotEmpty(param.getChannelIdSet())) {
            exportParam.setChannelIdSet(param.getChannelIdSet());
            exportParam.setIsDirect(BooleanEnum.TRUE.getCode());
        }
        exportParam.setAdminId(param.getAdminId());
        exportParam.setRoleId(param.getRoleId());
        if (param.getIsAll()) {
            //导出全部
            exportParam.setLimit(Long.valueOf(exportExcelConfig.getLimit()));
            exportParam.setId(0);
        } else {
            //导出当前页
            exportParam.setLimit(Objects.nonNull(param.getSize()) ? param.getSize() : exportExcelConfig.getLimit());
        }
    }

}
