package com.wd.lottery.module.cash.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.Tup3;
import com.wd.lottery.module.cash.constatns.OrderStatusEnum;
import com.wd.lottery.module.cash.constatns.PlatformTypeEnum;
import com.wd.lottery.module.cash.dto.*;
import com.wd.lottery.module.cash.dto.*;
import com.wd.lottery.module.cash.entity.CashWithdrawOrder;
import com.wd.lottery.module.cash.param.CashWithdrawOrderApplyParam;
import com.wd.lottery.module.cash.param.CashWithdrawOrderQueryParam;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.payment.dto.payment.WithdrawResultDTO;
import com.wd.lottery.module.payment.entity.PaymentChannel;
import com.wd.lottery.module.payment.entity.PaymentMerchant;

import java.time.LocalDateTime;
import java.util.List;

public interface CashWithdrawOrderService {

    CashWithdrawOrder findById(Long id, Long merchantId);

    Page<CashWithdrawOrder> search(OrderSearchDTO orderSearchDto, Long merchantId, CurrencyEnum currencyEnum);

    TotalAmountDTO totalMoney(OrderSearchDTO orderSearchDto, Long merchantId, CurrencyEnum currencyEnum);

    List<OrderHistoryDTO> cGetList(CashWithdrawOrderQueryParam cashWithdrawOrderQueryParam);

    void apply(CashWithdrawOrderApplyParam cashWithdrawOrderApplyParam);

    CashWithdrawCountStatisticDTO getWithdrawCount(Long memberId, Long merchantId);

    CashWithdrawOrder createOrderByManual(Member member, CashManualPaymentDTO cashManualPaymentDto, CurrencyEnum currencyEnum, Long merchantId, LocalDateTime payTime);

    void updateThirdInformation(CashWithdrawOrder cashWithdrawOrder, WithdrawResultDTO withdrawResultDto, PaymentChannel paymentChannel, PaymentMerchant paymentMerchant, String adminName);

    void updateOrderToProgressing(CashWithdrawOrder cashWithdrawOrder);

    void updateThirdMessage(CashWithdrawOrder cashWithdrawOrder, String message, String channelName, Long paymentChannelId,
                            String paymentChannelName, Long paymentMerchantId, String paymentMerchantRemark);

    boolean updateOrderStatusOfAuto(CashWithdrawOrder cashWithdrawOrder, String message, LocalDateTime payTime, Long thirdFee, OrderStatusEnum orderStatusEnum);

    boolean updateRemark(OrderUpdateRemarkDTO orderUpdateRemarkDto);

    boolean updateOrderStatusOfManual(OrderUpdateStatusDTO orderUpdateStatusDto);

    boolean lock(OrderUpdateLockDTO orderUpdateLockDto);

    boolean unlock(OrderUpdateUnLockDTO orderUpdateUnLockDto);

    List<CashWithdrawOrder> getAggregateList(Long merchantId, LocalDateTime startTime, LocalDateTime endTime);

    List<OrderStatisticsDTO> withdrawStatistics(OrderStatisticsSearchDTO orderStatisticsSearchDto, CurrencyEnum currencyEnum);

    List<OrderStatusStatisticsDTO> withdrawStatusStatistics(OrderStatisticsSearchDTO orderStatisticsSearchDto, CurrencyEnum currencyEnum);

    ApiResult<?> exportCashWithdrawOrder(OrderSearchDTO orderSearchDto, Long merchantId, CurrencyEnum currencyEnum, String header, String adminName);

    /**
     * 查询待处理订单数量以及最后一笔订单的ID
     * @param merchantId
     * @param currencyEnum
     * @param platformTypeEnum
     * @return tup_1 待处理订单数量 tup_2 最后一笔订单ID tup_3 当前是否有锁定的订单
     */
    Tup3<Long, Long, Boolean> getPendingOrderCountAndLastOrderId(Long merchantId, CurrencyEnum currencyEnum, List<PlatformTypeEnum> platformTypeEnum, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查詢指定商戶中在指定時間前處於 PROGRESSING 狀態的出款訂單
     *
     * @param merchantId 商戶ID
     * @param pollingTime 輪詢時間門檻
     * @return 訂單列表
     */
    List<CashWithdrawOrder> getWithdrawOrderInProgressing(Long merchantId, LocalDateTime pollingTime);
}
