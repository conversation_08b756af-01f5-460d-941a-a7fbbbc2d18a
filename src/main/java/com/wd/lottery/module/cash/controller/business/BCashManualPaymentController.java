package com.wd.lottery.module.cash.controller.business;

import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.common.util.OperationLogUtil;
import com.wd.lottery.module.cash.constatns.ManualPaymentEnum;
import com.wd.lottery.module.cash.dto.CashManualPaymentDTO;
import com.wd.lottery.module.cash.param.CashManualPaymentBatchParam;
import com.wd.lottery.module.cash.service.CashManualPaymentService;
import com.wd.lottery.module.common.constants.LogSubTypeEnum;
import com.wd.lottery.module.common.constants.LogTypeEnum;
import com.wd.lottery.module.common.dto.CommonOperationLogDTO;
import com.wd.lottery.module.payment.util.PayUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Tag(name = "人工存提")
@RestController
@RequestMapping(value = "${business-path}/${module-path.cash}/manual/payment", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class BCashManualPaymentController {

    private final CashManualPaymentService cashManualPaymentService;

    @Operation(summary = "會員查詢")
    @GetMapping("/search")
    public ApiResult<?> search(@RequestParam @Valid @NotNull Long memberId) {
        return ApiResult.success(cashManualPaymentService.search(memberId, AdminTokenInfoUtil.getRequestMerchantIdNotNull(), AdminTokenInfoUtil.getRequestCurrencyEnumNotNull()));
    }

    @Operation(summary = "單筆存提")
    @PostMapping("singleDepositWithdraw")
    public ApiResult<?> single(@RequestBody @Valid CashManualPaymentDTO cashManualPaymentDto) {
        cashManualPaymentDto.setUpdateTime(LocalDateTime.now());
        cashManualPaymentDto.setAdminName(AdminTokenInfoUtil.getAdminName());
        boolean isSuccess = cashManualPaymentService.singleDepositWithdraw(cashManualPaymentDto, AdminTokenInfoUtil.getRequestMerchantIdNotNull(), AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        if (isSuccess) {
            saveOperatorLog(cashManualPaymentDto.getManualPaymentEnum(), cashManualPaymentDto.getMemberId(),
                    cashManualPaymentDto.getAmount(), cashManualPaymentDto.getAdminName(),
                    AdminTokenInfoUtil.getRequestMerchantIdNotNull(), AdminTokenInfoUtil.getBigIntegerIpFromRequest(),
                    cashManualPaymentDto.getAuditRate(), cashManualPaymentDto.getUpdateTime(),
                    AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        }
        return ApiResult.success(isSuccess);
    }

    @Operation(summary = "批量導入")
    @PostMapping("batchDepositWithdraw")
    public ApiResult<?> batchDepositWithdraw(@RequestBody @Valid CashManualPaymentBatchParam cashManualPaymentBatchParam) {
        cashManualPaymentBatchParam.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        cashManualPaymentBatchParam.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        cashManualPaymentBatchParam.setAdminName(AdminTokenInfoUtil.getAdminName());
        cashManualPaymentBatchParam.setUpdateTime(LocalDateTime.now());
        cashManualPaymentBatchParam.setRequestIp(AdminTokenInfoUtil.getBigIntegerIpFromRequest());
        cashManualPaymentService.batchDepositWithdraw(cashManualPaymentBatchParam);
        return ApiResult.success();
    }

    private static void saveOperatorLog(ManualPaymentEnum manualPaymentEnum, Long memberId, Long amount, String adminName,
                                        Long merchantId, BigInteger ip, BigDecimal auditRate, LocalDateTime createTime,
                                        CurrencyEnum currencyEnum) {
        String title;
        String audit = "";
        if (manualPaymentEnum == ManualPaymentEnum.MANUAL_DEPOSIT) {
            title = "人工存款";
            audit = ", 打码量倍数:" + auditRate;
        } else if (manualPaymentEnum == ManualPaymentEnum.MANUAL_WITHDRAW) {
            title = "人工提现";
        } else if (manualPaymentEnum == ManualPaymentEnum.BONUS_DEPOSIT) {
            title = "彩金充值";
            audit = ", 打码量倍数:" + auditRate;
        } else {
            title = "彩金扣除";
        }
        String content = String.format("%s 成功 UID:%s, 金額:%s%s", title, memberId, PayUtils.getMoney(amount), audit);
        CommonOperationLogDTO commonOperationLogDTO = new CommonOperationLogDTO();
        commonOperationLogDTO.setIp(ip);
        commonOperationLogDTO.setTypeEnum(LogTypeEnum.MANUAL_PAYMENT);
        commonOperationLogDTO.setSubTypeEnum(LogSubTypeEnum.CREATE);
        commonOperationLogDTO.setCreateBy(adminName);
        commonOperationLogDTO.setCreateTime(createTime);
        commonOperationLogDTO.setMerchantId(merchantId);
        commonOperationLogDTO.setCurrencyEnum(currencyEnum);
        commonOperationLogDTO.setContent(content);
        OperationLogUtil.sendToMq(commonOperationLogDTO);
    }


}
