package com.wd.lottery.module.cash.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.wd.lottery.GrapeApplication;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.Md5Util;
import com.wd.lottery.common.util.OperationLogUtil;
import com.wd.lottery.common.util.RequestUtil;
import com.wd.lottery.module.activity.constants.ActivityStatusEnum;
import com.wd.lottery.module.activity.constants.ActivityTypeEnum;
import com.wd.lottery.module.activity.constants.SubActivityTypeEnum;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.entity.ActivityRecord;
import com.wd.lottery.module.activity.service.ActivityRecordService;
import com.wd.lottery.module.cash.constatns.CashConstants;
import com.wd.lottery.module.cash.constatns.CashRedisKeyConstants;
import com.wd.lottery.module.cash.constatns.ManualPaymentEnum;
import com.wd.lottery.module.cash.constatns.SubTradeTypeEnum;
import com.wd.lottery.module.cash.dto.CashManualMemberDTO;
import com.wd.lottery.module.cash.dto.CashManualPaymentDTO;
import com.wd.lottery.module.cash.dto.CashMemberWalletDTO;
import com.wd.lottery.module.cash.entity.CashAudit;
import com.wd.lottery.module.cash.entity.CashDepositOrder;
import com.wd.lottery.module.cash.entity.CashWithdrawOrder;
import com.wd.lottery.module.cash.param.CashManualPaymentBatchParam;
import com.wd.lottery.module.cash.param.CashManualPaymentBatchRowParam;
import com.wd.lottery.module.cash.service.*;
import com.wd.lottery.module.cash.service.*;
import com.wd.lottery.module.common.constants.LogSubTypeEnum;
import com.wd.lottery.module.common.constants.LogTypeEnum;
import com.wd.lottery.module.common.dto.CommonOperationLogDTO;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.payment.service.PaymentResultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class CashManualPaymentServiceImpl implements CashManualPaymentService {

    private final MemberService memberService;

    private final PaymentResultService paymentResultService;

    private final CashDepositOrderService cashDepositOrderService;

    private final CashWithdrawOrderService cashWithdrawOrderService;

    private final ActivityRecordService activityRecordService;

    private final CashAuditService cashAuditService;

    private final CashMemberWalletService cashMemberWalletService;

    private final RedisService redisService;

    @Resource(name = Constants.CALL_RUN_THREAD_POOL)
    private ThreadPoolTaskExecutor callRunThreadPoolTaskExecutor;

    /**
     * 人工存提-會員查詢
     *
     * @param memberId   member uid
     * @param merchantId merchant uid
     * @return dto
     */
    @Override
    public CashManualMemberDTO search(Long memberId, Long merchantId, CurrencyEnum currencyEnum) {
        Member member = memberService.getMemberByIdAndCurrencyEnum(memberId, merchantId, currencyEnum);
        if (member == null) {
            throw new ApiException(CommonCode.MEMBER_NOT_EXIST);
        }

        CashAudit cashAudit = cashAuditService.findById(member.getId(), member.getMerchantId());
        if (cashAudit == null) {
            throw new ApiException(CommonCode.FAILED);
        }

        CashMemberWalletDTO cashMemberWalletDto = cashMemberWalletService.getByMemberId(member.getMerchantId(), member.getId());

        CashManualMemberDTO cashManualMemberDto = new CashManualMemberDTO();
        cashManualMemberDto.setMemberId(member.getId());
        cashManualMemberDto.setMemberName(member.getMemberName());
        cashManualMemberDto.setNickName(member.getNickName());
        cashManualMemberDto.setAuditBalance(cashAudit.getAuditBalance());
        cashManualMemberDto.setCashBalance(cashMemberWalletDto.getCashBalance());
        cashManualMemberDto.setRemark(member.getRemark());
        return cashManualMemberDto;
    }

    /**
     * 單筆存提
     *
     * @param cashManualPaymentDto dto
     * @param merchantId           merchant uid
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean singleDepositWithdraw(CashManualPaymentDTO cashManualPaymentDto, Long merchantId, CurrencyEnum currencyEnum) {
        ManualPaymentEnum manualPaymentEnum = cashManualPaymentDto.getManualPaymentEnum();
        LocalDateTime payTime = cashManualPaymentDto.getUpdateTime();

        this.checkParam(manualPaymentEnum, cashManualPaymentDto);

        Member member = memberService.getMemberByIdAndCurrencyEnum(cashManualPaymentDto.getMemberId(), merchantId, currencyEnum);
        if (member == null) {
            throw new ApiException(CommonCode.MEMBER_NOT_EXIST);
        }

        try {
            // 人工存款
            if (manualPaymentEnum == ManualPaymentEnum.MANUAL_DEPOSIT) {
                CashDepositOrder cashDepositOrder = cashDepositOrderService.createOrderByManual(member, cashManualPaymentDto, member.getCurrencyEnum(), merchantId, payTime);
                BooleanEnum isFirstCharge = paymentResultService.depositHis(cashDepositOrder, payTime);
                paymentResultService.memberWalletOperate(cashManualPaymentDto.getMemberId(), member.getMemberName(), cashDepositOrder.getId(),
                        cashManualPaymentDto.getAmount(), merchantId, payTime, SubTradeTypeEnum.MANUAL_DEPOSIT , cashDepositOrder.getRemark(),isFirstCharge);
            }

            // 人工提款
            if (manualPaymentEnum == ManualPaymentEnum.MANUAL_WITHDRAW) {
                CashWithdrawOrder cashWithdrawOrder = cashWithdrawOrderService.createOrderByManual(member, cashManualPaymentDto, member.getCurrencyEnum(), merchantId, payTime);
                paymentResultService.memberWalletOperate(cashManualPaymentDto.getMemberId(), member.getMemberName(), cashWithdrawOrder.getId(),
                        cashManualPaymentDto.getAmount(), merchantId, LocalDateTime.now(), SubTradeTypeEnum.MANUAL_WITHDRAW , cashWithdrawOrder.getRemark(),null);

                paymentResultService.withdrawHis(cashWithdrawOrder, payTime);
            }

            // 人工彩金充值
            if (manualPaymentEnum == ManualPaymentEnum.BONUS_DEPOSIT) {
                MemberTokenInfoDTO memberTokenInfoDto = this.setMemberInfo(member);
                Activity activity = this.setActivity(ActivityTypeEnum.BONUS_DEPOSIT, member.getCurrencyEnum());
                ActivityRecord activityRecord = activityRecordService.initActivityRecord(memberTokenInfoDto, activity,
                        SubActivityTypeEnum.BONUS_DEPOSIT, LocalDateTime.now(), RequestUtil.getBigIntegerIpFromRequest());
                activityRecord.setOperator(cashManualPaymentDto.getAdminName());
                activityRecordService.saveAndOperateWallet(this.setActivityRecord(activityRecord, member.getId(), cashManualPaymentDto.getAmount(), cashManualPaymentDto.getRemark()));
            }

            // 人工彩金扣除
            if (manualPaymentEnum == ManualPaymentEnum.BONUS_WITHDRAW) {
                MemberTokenInfoDTO memberTokenInfoDto = this.setMemberInfo(member);
                Activity activity = this.setActivity(ActivityTypeEnum.BONUS_DEDUCT, member.getCurrencyEnum());
                ActivityRecord activityRecord = activityRecordService.initActivityRecord(memberTokenInfoDto, activity,
                        SubActivityTypeEnum.BONUS_DEDUCT, LocalDateTime.now(), RequestUtil.getBigIntegerIpFromRequest());
                activityRecord.setOperator(cashManualPaymentDto.getAdminName());
                activityRecordService.saveAndOperateWallet(this.setActivityRecord(activityRecord, member.getId(), cashManualPaymentDto.getAmount(), cashManualPaymentDto.getRemark()));
            }

            // 打碼量處理
            if (manualPaymentEnum == ManualPaymentEnum.MANUAL_DEPOSIT || manualPaymentEnum == ManualPaymentEnum.BONUS_DEPOSIT) {
                Long audit = cashManualPaymentDto.getAuditRate().multiply(new BigDecimal(cashManualPaymentDto.getAmount())).longValue();
                cashAuditService.addAuditBalance(member.getId(), member.getMerchantId(), audit);
            }

            return true;
        } catch (Exception e) {
            log.error("Error in singleDepositWithdraw", e);
            throw new ApiException(CommonCode.CASH_SINGLE_DEPOSIT_WITHDRAW_FAIL);
        }
    }

    /**
     * 批次存提
     */
    @Override
    public void batchDepositWithdraw(CashManualPaymentBatchParam cashManualPaymentBatchParam) {
        Assert.notEmpty(cashManualPaymentBatchParam.getCashManualPaymentBatchRowParamList());
        Assert.notNull(cashManualPaymentBatchParam.getManualPaymentEnum());
        Assert.notNull(cashManualPaymentBatchParam.getMerchantId());
        Assert.notNull(cashManualPaymentBatchParam.getCurrencyEnum());

        if (cashManualPaymentBatchParam.getCashManualPaymentBatchRowParamList().size() > CashConstants.MANUAL_PAYMENT_BATCH_LIMIT) {
            throw new ApiException(CommonCode.CASH_MANUAL_PAYMENT_BATCH_EXCEED_LIMIT);
        }

        List<Long> memberIdList = cashManualPaymentBatchParam.getCashManualPaymentBatchRowParamList().stream().map(CashManualPaymentBatchRowParam::getMemberId).collect(Collectors.toList());
        memberService.checkMemberIdList(memberIdList, cashManualPaymentBatchParam.getMerchantId(), cashManualPaymentBatchParam.getCurrencyEnum());

        String lockKey = String.format(CashRedisKeyConstants.MANUAL_PAYMENT_BATCH_LOCK, cashManualPaymentBatchParam.getMerchantId(), cashManualPaymentBatchParam.getFilename());
        boolean lockResult = redisService.setIfAbsent(lockKey, LocalDateTime.now(), CashRedisKeyConstants.MANUAL_PAYMENT_BATCH_LOCK_SECONDS);
        if (!lockResult) {
            throw new ApiException(CommonCode.CASH_MANUAL_PAYMENT_BATCH_DUPLICATE);
        }

        callRunThreadPoolTaskExecutor.execute(() -> batchDepositWithdrawAsync(cashManualPaymentBatchParam));
    }

    public void batchDepositWithdrawAsync(CashManualPaymentBatchParam cashManualPaymentBatchParam) {
        Assert.notNull(cashManualPaymentBatchParam);
        Assert.notEmpty(cashManualPaymentBatchParam.getCashManualPaymentBatchRowParamList());

        List<Long> failMemberIdList = new ArrayList<>();
        // 这里循环单个操作是因为操作会员钱包可能被其它线程锁住导致钱包操作失败或者会员钱包余额不足扣除失败等异常时，
        // 可以保证其它会员可以正常被操作
        CashManualPaymentService cashManualPaymentService = GrapeApplication.getBean(this.getClass());
        for (CashManualPaymentBatchRowParam cashManualPaymentBatchRowParam : cashManualPaymentBatchParam.getCashManualPaymentBatchRowParamList()) {
            CashManualPaymentDTO cashManualPaymentDTO = BeanUtil.copyProperties(cashManualPaymentBatchRowParam, CashManualPaymentDTO.class);
            cashManualPaymentDTO.setManualPaymentEnum(cashManualPaymentBatchParam.getManualPaymentEnum());
            cashManualPaymentDTO.setAdminName(cashManualPaymentBatchParam.getAdminName());
            cashManualPaymentDTO.setUpdateTime(cashManualPaymentBatchParam.getUpdateTime());
            try {
                cashManualPaymentService.singleDepositWithdraw(cashManualPaymentDTO, cashManualPaymentBatchParam.getMerchantId(), cashManualPaymentBatchParam.getCurrencyEnum());
            } catch (Exception e) {
                log.warn("singleDepositWithdraw fail, cashManualPaymentDTO:{}, merchantId:{} ", cashManualPaymentDTO, cashManualPaymentBatchParam.getMerchantId(), e);
                failMemberIdList.add(cashManualPaymentDTO.getMemberId());
            }
        }

        String content = "批量人工充提成功";
        if (CollectionUtil.isNotEmpty(failMemberIdList)) {
            content = "批量人工充提失败UID列表：" + JSONUtil.toJsonStr(failMemberIdList);
        }

        CommonOperationLogDTO commonOperationLogDTO = new CommonOperationLogDTO();
        commonOperationLogDTO.setIp(cashManualPaymentBatchParam.getRequestIp());
        commonOperationLogDTO.setTypeEnum(LogTypeEnum.MANUAL_PAYMENT);
        commonOperationLogDTO.setSubTypeEnum(LogSubTypeEnum.CREATE);
        commonOperationLogDTO.setCreateBy(cashManualPaymentBatchParam.getAdminName());
        commonOperationLogDTO.setCreateTime(cashManualPaymentBatchParam.getUpdateTime());
        commonOperationLogDTO.setMerchantId(cashManualPaymentBatchParam.getMerchantId());
        commonOperationLogDTO.setCurrencyEnum(cashManualPaymentBatchParam.getCurrencyEnum());
        commonOperationLogDTO.setContent(content);
        OperationLogUtil.sendToMq(commonOperationLogDTO);
    }

    /**
     * 參數驗證
     *
     * @param manualPaymentEnum    enum
     * @param cashManualPaymentDto dto
     */
    private void checkParam(ManualPaymentEnum manualPaymentEnum, CashManualPaymentDTO cashManualPaymentDto) {
        Assert.notNull(cashManualPaymentDto.getManualPaymentEnum());
        Assert.notNull(cashManualPaymentDto.getMemberId());
        Assert.notNull(cashManualPaymentDto.getAmount());
        Assert.isTrue(cashManualPaymentDto.getAmount() > 0);
        if (manualPaymentEnum == ManualPaymentEnum.MANUAL_DEPOSIT || manualPaymentEnum == ManualPaymentEnum.BONUS_DEPOSIT) {
            Assert.notNull(cashManualPaymentDto.getAuditRate());
        }
    }

    /**
     * set memberInfo dto
     *
     * @return dto
     */
    private MemberTokenInfoDTO setMemberInfo(Member member) {
        MemberTokenInfoDTO memberTokenInfoDto = new MemberTokenInfoDTO();
        memberTokenInfoDto.setId(member.getId());
        memberTokenInfoDto.setMemberName(member.getMemberName());
        memberTokenInfoDto.setMerchantId(member.getMerchantId());
        memberTokenInfoDto.setCurrencyEnum(member.getCurrencyEnum());
        memberTokenInfoDto.setChannelId(member.getChannelId());
        memberTokenInfoDto.setIsDirect(member.getIsDirect());
        return memberTokenInfoDto;
    }


    private Activity setActivity(ActivityTypeEnum activityTypeEnum, CurrencyEnum currencyEnum) {
        Activity activity = new Activity();
        activity.setId(IdWorker.getId());
        activity.setActivityTypeEnum(activityTypeEnum);
        activity.setCurrencyEnum(currencyEnum);
        return activity;
    }

    private ActivityRecord setActivityRecord(ActivityRecord activityRecord, Long memberId, Long amount, String remark) {
        activityRecord.setUid(Md5Util.getMd5HexLowerCase(memberId + StrPool.COLON + IdWorker.getId()));
        activityRecord.setAmount(amount);
        activityRecord.setActivityStatusEnum(ActivityStatusEnum.RECEIVED);
        activityRecord.setRemark(remark);
        return activityRecord;
    }

}
