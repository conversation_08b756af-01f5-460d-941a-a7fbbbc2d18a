package com.wd.lottery.module.cash.dto;

import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.cash.constatns.SubTradeTypeEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.constants.InviteTypeEnum;
import com.wd.lottery.module.member.constants.MemberTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class CashDataStatisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "商户id")
    private Long merchantId;

    @Schema(description = "会员id")
    private Long memberId;

    @Schema(description = "会员名称")
    private String memberName;

    @Schema(description = "游戏服務費")
    private Long serviceFee = 0L;

    @Schema(description = "游戏下注金额")
    private Long betAmount = 0L;

    @Schema(description = "游戏下注数量")
    private Long betCount = 0L;

    @Schema(description = "中奖金额")
    private Long winAmount = 0L;

    @Schema(description = "有效投注金额")
    private Long validBetAmount = 0L;

    @Schema(description = "输赢金额")
    private Long winLossAmount = 0L;

    private Long jackpot = 0L;

    @Schema(description = "锦标赛奖金")
    private Long tournamentReward = 0L;

    @Schema(description = "游戏code")
    private String gameCode;

    @Schema(description = "游戏名称")
    private String gameName;

    @Schema(description = "游戏大类")
    private GameCategoryEnum gameCategoryEnum;

    @Schema(description = "平台代码")
    private String platformCode;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "货币类型")
    private CurrencyEnum currencyEnum;

    @Schema(description = "归属日期")
    private LocalDate belongDate;

    private SubTradeTypeEnum subTradeTypeEnum;

    @Schema(description = "钱包日志金额")
    private Long amount = 0L;

    @Schema(description = "钱包交易次数")
    private Long tradeCount = 0L;

    @Schema(description = "提現金額")
    private Long withdrawAmount = 0L;

    @Schema(description = "提現次數")
    private Long withdrawCount = 0L;

    @Schema(description = "会员类型")
    private MemberTypeEnum memberTypeEnum;

    @Schema(description = "邀请类型", example = "OFFICIAL")
    private InviteTypeEnum inviteTypeEnum;

    @Schema(description = "是否是直屬邀請", example = "TRUE")
    private BooleanEnum isDirect;

    @Schema(description = "渠道id", example = "123")
    private Long channelId;

}
