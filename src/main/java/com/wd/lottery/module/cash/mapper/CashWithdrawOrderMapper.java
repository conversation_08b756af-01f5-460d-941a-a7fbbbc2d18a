package com.wd.lottery.module.cash.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.cash.constatns.OrderStatusEnum;
import com.wd.lottery.module.cash.dto.*;
import com.wd.lottery.module.cash.dto.*;
import com.wd.lottery.module.cash.entity.CashWithdrawOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
public interface CashWithdrawOrderMapper extends BaseMapper<CashWithdrawOrder> {

    List<OrderStatisticsDTO> searchSummary(@Param("orderStatisticsSearchDto") OrderStatisticsSearchDTO orderStatisticsSearchDto,
                                           @Param("merchantId") Long merchantId,
                                           @Param("currencyEnum") CurrencyEnum currencyEnum,
                                           @Param("orderStatusEnum") OrderStatusEnum orderStatusEnum);

    TotalAmountDTO totalAmounts(@Param("orderSearchDto") OrderSearchDTO orderSearchDto,
                                @Param("merchantId") Long merchantId,
                                @Param("currencyEnum") CurrencyEnum currencyEnum);

    List<CashWithdrawOrder> exportCashWithdrawOrder(CashWithdrawExportDTO param);

}
