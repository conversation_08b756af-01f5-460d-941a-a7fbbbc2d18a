package com.wd.lottery.module.cash.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.GrapeApplication;
import com.wd.lottery.common.annotation.MasterOnly;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.RabbitMQConstants;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.SqlUtil;
import com.wd.lottery.module.activity.config.MQTimeOutConfig;
import com.wd.lottery.module.cash.constatns.SubTradeTypeEnum;
import com.wd.lottery.module.cash.constatns.TradeTypeEnum;
import com.wd.lottery.module.cash.constatns.TransferTypeEnum;
import com.wd.lottery.module.cash.dto.*;
import com.wd.lottery.module.cash.dto.*;
import com.wd.lottery.module.cash.entity.CashMemberWallet;
import com.wd.lottery.module.cash.entity.CashMemberWalletLog;
import com.wd.lottery.module.cash.mapper.CashMemberWalletMapper;
import com.wd.lottery.module.cash.service.CashAuditService;
import com.wd.lottery.module.cash.service.CashMemberWalletLogService;
import com.wd.lottery.module.cash.service.CashMemberWalletService;
import com.wd.lottery.module.common.constants.CommonConstants;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.merchant.dto.MerchantConfigAuditDTO;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.merchant.service.MerchantService;
import com.wd.lottery.module.third.constants.ConvertOrderTransferEnum;
import com.wd.lottery.module.third.dock.convertorder.DockConvertOrder;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 會員錢包 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
@Service
@Slf4j
public class CashMemberWalletServiceImpl extends ServiceImpl<CashMemberWalletMapper, CashMemberWallet> implements CashMemberWalletService {

    @Autowired
    private CashMemberWalletLogService cashMemberWalletLogService;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantConfigService merchantConfigService;

    @Autowired
    private CashAuditService cashAuditService;

    @Resource
    private MQTimeOutConfig mqTimeOutConfig;

    /**
     * 增加/减少会员钱包余额
     * 保存实时资金日志
     * 发mq保存历史资金日志
     * 事物传播使用Propagation.NESTED，用于解决外层事物如果try catch DuplicateKeyException异常时，
     * 仅内层事物回滚，外层事物可以正常提交
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    @Retryable(value = ApiException.class, exceptionExpression = "@cashMemberWalletServiceImpl.operateWalletShouldResty(#root)")
    public void operateWallet(CashMemberWalletOperateDTO dto) {
        try {
            CashMemberWallet wallet = operateWalletBeforeCheck(dto);

            Long newCashBalance = getNewCashBalance(dto, wallet);

            CashMemberWalletLog cashMemberWalletLog = saveCashMemberWalletLog(dto, wallet);

            updateCashBalance(wallet, newCashBalance);

            addAuditBalance(dto, wallet.getCurrencyEnum());

            sendMq(cashMemberWalletLog);
        } catch (Exception e) {
            if (e instanceof ApiException && CommonCode.CASH_MEMBER_WALLET_BALANCE_NOT_ENOUGH.equals(((ApiException) e).getErrorCode())) {
                log.info("operateWallet fail,dto:{}", dto, e);
            } else {
                log.error("operateWallet fail,dto:{}", dto, e);
            }
            throw e;
        }
    }

    /**
     * 金額增加計算打碼量
     *
     * @param dto dto
     */
    private void addAuditBalance(CashMemberWalletOperateDTO dto, CurrencyEnum currencyEnum) {
        if (dto.getSubTradeTypeEnum().getTradeTypeEnum().equals(TradeTypeEnum.IN) && dto.getSubTradeTypeEnum().getAuditEnable()) {
            MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(dto.getMerchantId(), CommonConstants.COMMON_DICT_KEY_AUDIT, currencyEnum);
            if (merchantConfig == null) {
                log.error("打碼量計算失敗, 當前商戶無配置, merchantId:{}, currencyEnum:{}", dto.getMerchantId(), currencyEnum.name());
                return;
            }
            List<MerchantConfigAuditDTO> merchantConfigAuditDtotList = merchantConfig.merchantConfigToListNotEmpty(MerchantConfigAuditDTO.class);

            for (MerchantConfigAuditDTO merchantConfigAuditDto : merchantConfigAuditDtotList) {
                SubTradeTypeEnum subTradeTypeEnum = merchantConfigAuditDto.getSubTradeTypeEnum();
                if (subTradeTypeEnum != null && subTradeTypeEnum == dto.getSubTradeTypeEnum()) {
                    BigDecimal auditRate = merchantConfigAuditDto.getAuditRate();
                    if (auditRate != null) {
                        Long auditBalance = auditRate.multiply(new BigDecimal(dto.getAmount())).longValue();
                        cashAuditService.addAuditBalance(dto.getMemberId(), dto.getMerchantId(), auditBalance);
                        break;
                    }
                }
            }
        }
    }

    /**
     * only for operateWallet resty
     */
    public Boolean operateWalletShouldResty(Exception e) {
        if (e instanceof ApiException) {
            if (((ApiException) e).getErrorCode() == CommonCode.CASH_MEMBER_WALLET_UPDATE_OTHER_THREAD) {
                log.warn("operateWalletShouldResty true", e);
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    private void sendMq(CashMemberWalletLog cashMemberWalletLog) {
        amqpTemplate.convertAndSend(RabbitMQConstants.CASH_MEMBER_WALLET_LOG_HIS_EXCHANGE, "", JSONUtil.toJsonStr(cashMemberWalletLog));
        //发送一个延迟的用户充值资金队列
        if (ObjectUtil.equal(cashMemberWalletLog.getSubTradeTypeEnum(),SubTradeTypeEnum.MEMBER_DEPOSIT)
                || ObjectUtil.equal(cashMemberWalletLog.getSubTradeTypeEnum(),SubTradeTypeEnum.MANUAL_DEPOSIT)) {
            sendDeliveryLog(cashMemberWalletLog);
        }
    }

    private void sendDeliveryLog(CashMemberWalletLog cashMemberWalletLog) {
        try {
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        // 事务提交后的逻辑
                        CashBetOrderDeliverDTO cashBetOrderDeliverDTO = new CashBetOrderDeliverDTO();
                        BeanUtils.copyProperties(cashMemberWalletLog,cashBetOrderDeliverDTO);
                        // 处理读写超时异常
                        final int retry = 0;
                        MessagePostProcessor messagePostProcessor = msg -> {
                            msg.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
                            msg.getMessageProperties().setHeader(Constants.REPLAY_COUNT, retry);
                            // 延迟时间
                            long time = Duration.ofSeconds(mqTimeOutConfig.getFirstChargeExpire()).toMillis();
                            msg.getMessageProperties().setHeader("x-delay", (int) time);
                            return msg;
                        };
                        log.info("send..deliver..msg..success..{}",cashBetOrderDeliverDTO);
                        amqpTemplate.convertAndSend(RabbitMQConstants.CASH_MEMBER_WALLET_DELIVER_EXCHANGE, RabbitMQConstants.CASH_MEMBER_WALLET_DELIVER_ROUTE_KEY, JSONUtil.toJsonStr(cashBetOrderDeliverDTO), messagePostProcessor);
                    }
                });
            }
        } catch (Exception e) {
            log.error("send..deliver..msg..error",e);
        }
    }

    private CashMemberWalletLog saveCashMemberWalletLog(CashMemberWalletOperateDTO dto, CashMemberWallet cashMemberWallet) {
        //保存实时资金日志
        CashMemberWalletLog cashMemberWalletLog = new CashMemberWalletLog();
        cashMemberWalletLog.setMemberId(cashMemberWallet.getId());
        cashMemberWalletLog.setMemberName(cashMemberWallet.getMemberName());
        cashMemberWalletLog.setMerchantId(cashMemberWallet.getMerchantId());
        cashMemberWalletLog.setCurrencyEnum(cashMemberWallet.getCurrencyEnum());

        cashMemberWalletLog.setPayNo(dto.getPayNo());
        cashMemberWalletLog.setTradeTypeEnum(dto.getSubTradeTypeEnum().getTradeTypeEnum());
        cashMemberWalletLog.setSubTradeTypeEnum(dto.getSubTradeTypeEnum());
        cashMemberWalletLog.setAmount(dto.getAmount());
        cashMemberWalletLog.setCashBalance(dto.getNewCashBalance());
        // KG-928 add transfer remark
        cashMemberWalletLog.setRemark(dto.getRemark());
        cashMemberWalletLog.setCreateTime(dto.getOperateTime() == null ? LocalDateTime.now() : dto.getOperateTime());
        cashMemberWalletLog.setInviteTypeEnum(cashMemberWallet.getInviteTypeEnum());
        cashMemberWalletLog.setIsDirect(cashMemberWallet.getIsDirect());
        cashMemberWalletLog.setChannelId(cashMemberWallet.getChannelId());
        cashMemberWalletLog.setMemberTypeEnum(cashMemberWallet.getMemberTypeEnum());
        cashMemberWalletLogService.save(cashMemberWalletLog);
        if (Objects.nonNull(dto.getIsFirstCharge())) {
            cashMemberWalletLog.setIsFirstCharge(dto.getIsFirstCharge());
        }
        return cashMemberWalletLog;
    }

    private void updateCashBalance(CashMemberWallet wallet, Long newCashBalance) {
        // 只更新cashBalance，使用CAS操作，不需要考虑ABA的问题
        boolean updateResult = this.lambdaUpdate()
                .set(CashMemberWallet::getCashBalance, newCashBalance)
                .eq(CashMemberWallet::getId, wallet.getId())
                .eq(CashMemberWallet::getMerchantId, wallet.getMerchantId())
                .eq(CashMemberWallet::getCashBalance, wallet.getCashBalance())
                .update();

        // 如果updateResult为false,说明数据被其它线程修改过,抛异常
        if (!updateResult) {
            throw new ApiException(CommonCode.CASH_MEMBER_WALLET_UPDATE_OTHER_THREAD);
        }
    }

    @NotNull
    private static Long getNewCashBalance(CashMemberWalletOperateDTO dto, CashMemberWallet wallet) {
        Long newCashBalance = null;
        if (TradeTypeEnum.OUT.equals(dto.getSubTradeTypeEnum().getTradeTypeEnum())) {
            newCashBalance = wallet.getCashBalance() - dto.getAmount();
        }
        if (TradeTypeEnum.IN.equals(dto.getSubTradeTypeEnum().getTradeTypeEnum())) {
            newCashBalance = wallet.getCashBalance() + dto.getAmount();
        }
        if (newCashBalance == null) {
            throw new ApiException(CommonCode.CASH_DEPOSIT_BALANCE_IS_NULL);
        }
        dto.setNewCashBalance(newCashBalance);
        return newCashBalance;
    }

    private CashMemberWallet operateWalletBeforeCheck(CashMemberWalletOperateDTO dto) {
        Assert.notNull(dto.getSubTradeTypeEnum());
        Assert.isTrue(dto.getMemberId() != null || StrUtil.isNotBlank(dto.getMemberName()), "memberId or memberName need one ");
        Assert.notNull(dto.getMerchantId());
        Assert.notEmpty(dto.getPayNo());
        Assert.isTrue(dto.getAmount() > 0);

        CashMemberWallet wallet = this.lambdaQuery()
                .select(CashMemberWallet::getId,
                        CashMemberWallet::getCashBalance,
                        CashMemberWallet::getMerchantId,
                        CashMemberWallet::getMemberName,
                        CashMemberWallet::getCurrencyEnum,
                        CashMemberWallet::getIsDirect,
                        CashMemberWallet::getChannelId,
                        CashMemberWallet::getInviteTypeEnum,
                        CashMemberWallet::getMemberTypeEnum)
                .eq(dto.getMemberId() != null, CashMemberWallet::getId, dto.getMemberId())
                .eq(StrUtil.isNotBlank(dto.getMemberName()), CashMemberWallet::getMemberName, dto.getMemberName())
                .eq(CashMemberWallet::getMerchantId, dto.getMerchantId())
                .last(Constants.SQL_LIMIT_1)
                .one();

        if (wallet == null) {
            throw new ApiException(CommonCode.CASH_MEMBER_WALLET_NOT_EXIST);
        }
        if (TradeTypeEnum.OUT.equals(dto.getSubTradeTypeEnum().getTradeTypeEnum())
                && !dto.getSubTradeTypeEnum().equals(SubTradeTypeEnum.RESETTLE_DIFF_OUT)) {
            if (wallet.getCashBalance() - dto.getAmount() < 0) {
                throw new ApiException(CommonCode.CASH_MEMBER_WALLET_BALANCE_NOT_ENOUGH);
            }
        }

        return wallet;
    }

    @Override
    public CashMemberWalletExternalDTO getByMemberName(Long merchantId, String memberName) {
        Assert.notNull(merchantId);
        Assert.notBlank(memberName);
        CashMemberWallet cashMemberWallet = this.lambdaQuery()
                .select(CashMemberWallet::getCashBalance)
                .eq(CashMemberWallet::getMerchantId, merchantId)
                .eq(CashMemberWallet::getMemberName, memberName)
                .one();
        return BeanUtil.toBean(Optional
                        .ofNullable(cashMemberWallet)
                        .orElseThrow(() -> new ApiException(CommonCode.CASH_MEMBER_WALLET_NOT_EXIST)),
                CashMemberWalletExternalDTO.class);
    }

    @Override
    public CashMemberWalletDTO getByMemberId(Long merchantId, Long memberId) {
        Assert.notNull(merchantId);
        Assert.notNull(memberId);
        CashMemberWallet cashMemberWallet = this.lambdaQuery()
                .select(CashMemberWallet::getCashBalance)
                .eq(CashMemberWallet::getId, memberId)
                .eq(CashMemberWallet::getMerchantId, merchantId)
                .one();
        return BeanUtil.toBean(Optional
                        .ofNullable(cashMemberWallet)
                        .orElseThrow(() -> new ApiException(CommonCode.CASH_MEMBER_WALLET_NOT_EXIST)),
                CashMemberWalletDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transfer(TransferDTO transferDTO) {
        Assert.notNull(transferDTO.getMerchantId());
        Assert.notNull(transferDTO.getAmount());
        Assert.notNull(transferDTO.getTransferTypeEnum());
        Assert.notBlank(transferDTO.getMemberName());
        Assert.notBlank(transferDTO.getPayNo());
        CashMemberWalletOperateDTO cashMemberWalletOperateDTO = new CashMemberWalletOperateDTO();
        cashMemberWalletOperateDTO.setMerchantId(transferDTO.getMerchantId());
        cashMemberWalletOperateDTO.setAmount(transferDTO.getAmount());
        cashMemberWalletOperateDTO.setPayNo(transferDTO.getPayNo());
        cashMemberWalletOperateDTO.setOperateTime(LocalDateTime.now());
        cashMemberWalletOperateDTO.setMemberName(transferDTO.getMemberName());
        cashMemberWalletOperateDTO.setRemark(transferDTO.getRemark());
        if (transferDTO.getTransferTypeEnum() == TransferTypeEnum.IN) {
            cashMemberWalletOperateDTO.setSubTradeTypeEnum(SubTradeTypeEnum.TRANSFER_IN);
        } else if (transferDTO.getTransferTypeEnum() == TransferTypeEnum.OUT) {
            cashMemberWalletOperateDTO.setSubTradeTypeEnum(SubTradeTypeEnum.TRANSFER_OUT);
        }
        CashMemberWalletService cashMemberWalletService = GrapeApplication.getBean(CashMemberWalletService.class);
        cashMemberWalletService.operateWallet(cashMemberWalletOperateDTO);
    }

    @Async
    @Override
    public void negativeToZeroBatch() {
        List<Long> enableMerchantIdList = merchantService.getEnableMerchantIdList();
        if (CollUtil.isEmpty(enableMerchantIdList)) {
            return;
        }
        for (Long merchantId : enableMerchantIdList) {
            List<CashMemberWallet> cashMemberWalletNagativeList = this.lambdaQuery()
                    .select(CashMemberWallet::getId,
                            CashMemberWallet::getMemberName,
                            CashMemberWallet::getMerchantId,
                            CashMemberWallet::getCashBalance,
                            CashMemberWallet::getCurrencyEnum)
                    .eq(CashMemberWallet::getMerchantId, merchantId)
                    .lt(CashMemberWallet::getCashBalance, 0)
                    .list();
            if (CollUtil.isEmpty(cashMemberWalletNagativeList)) {
                continue;
            }
            for (CashMemberWallet cashMemberWallet : cashMemberWalletNagativeList) {
                CashMemberWalletServiceImpl cashMemberWalletServiceImpl = GrapeApplication.getBean(this.getClass());
                cashMemberWalletServiceImpl.negativeToZeroSingle(cashMemberWallet);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void negativeToZeroSingle(CashMemberWallet cashMemberWallet) {
        Assert.notNull(cashMemberWallet);
        Assert.notNull(cashMemberWallet.getId());
        Assert.notNull(cashMemberWallet.getMemberName());
        Assert.notNull(cashMemberWallet.getMerchantId());
        Assert.isTrue(cashMemberWallet.getCashBalance() < 0);
        boolean updateResult = this.lambdaUpdate()
                .set(CashMemberWallet::getCashBalance, 0L)
                .eq(CashMemberWallet::getId, cashMemberWallet.getId())
                .eq(CashMemberWallet::getCashBalance, cashMemberWallet.getCashBalance())
                .update();
        if (!updateResult) {
            // 更新失败则放弃该会员本次清零
            log.warn("Member wallet update by other thread,Give up update negative to zero, cashMemberWallet:{}", cashMemberWallet);
            return;
        }
        CashMemberWalletLog cashMemberWalletLog = new CashMemberWalletLog();
        cashMemberWalletLog.setMemberId(cashMemberWallet.getId());
        cashMemberWalletLog.setMemberName(cashMemberWallet.getMemberName());
        cashMemberWalletLog.setMerchantId(cashMemberWallet.getMerchantId());
        cashMemberWalletLog.setCurrencyEnum(cashMemberWallet.getCurrencyEnum());

        cashMemberWalletLog.setPayNo(IdWorker.getIdStr());
        cashMemberWalletLog.setTradeTypeEnum(SubTradeTypeEnum.NEGATIVE_TO_ZERO.getTradeTypeEnum());
        cashMemberWalletLog.setSubTradeTypeEnum(SubTradeTypeEnum.NEGATIVE_TO_ZERO);
        cashMemberWalletLog.setAmount(-cashMemberWallet.getCashBalance());
        cashMemberWalletLog.setCashBalance(0L);
        cashMemberWalletLog.setCreateTime(LocalDateTime.now());
        cashMemberWalletLog.setInviteTypeEnum(cashMemberWallet.getInviteTypeEnum());
        cashMemberWalletLog.setIsDirect(cashMemberWallet.getIsDirect());
        cashMemberWalletLog.setChannelId(cashMemberWallet.getChannelId());
        cashMemberWalletLog.setMemberTypeEnum(cashMemberWallet.getMemberTypeEnum());
        cashMemberWalletLogService.save(cashMemberWalletLog);
        sendMq(cashMemberWalletLog);
    }

    @Override
    public void saveByMember(Member member) {
        CashMemberWallet cashMemberWallet = new CashMemberWallet();
        cashMemberWallet.setId(member.getId());
        cashMemberWallet.setMerchantId(member.getMerchantId());
        cashMemberWallet.setMemberName(member.getMemberName());
        cashMemberWallet.setCurrencyEnum(member.getCurrencyEnum());
        cashMemberWallet.setCashBalance(0L);
        cashMemberWallet.setCreateTime(member.getCreateTime());
        cashMemberWallet.setUpdateTime(member.getCreateTime());
        cashMemberWallet.setInviteTypeEnum(member.getInviteTypeEnum());
        cashMemberWallet.setIsDirect(member.getIsDirect());
        cashMemberWallet.setChannelId(member.getChannelId());
        cashMemberWallet.setMemberTypeEnum(member.getMemberTypeEnum());
        super.save(cashMemberWallet);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void convertOrderTransfer(DockConvertOrder dockConvertOrder) {
        transfer(buildMemberWalletTransferParam(dockConvertOrder));
    }

    @MasterOnly
    @Override
    public CashMemberWalletDTO getMemberWalletMasterOnly(Long merchantId, Long memberId) {
        return getByMemberId(merchantId, memberId);
    }

    @Override
    public long getAllMemberBalance(Long merchantId, CurrencyEnum currency) {
        QueryWrapper<CashMemberWallet> wrapper = new QueryWrapper<>();
        wrapper.select(SqlUtil.selectSum(CashMemberWallet::getCashBalance))
                .lambda()
                .eq(CashMemberWallet::getMerchantId, merchantId)
                .eq(CashMemberWallet::getCurrencyEnum, currency);

        CashMemberWallet total = this.getOne(wrapper);
        if (Objects.isNull(total)) {
            return 0L;
        }
        return total.getCashBalance();
    }

    @Override
    public Map<Long, Long> getMemberBalance(List<Long> memberIds) {
        List<CashMemberWallet> list = this.list(new LambdaQueryWrapper<CashMemberWallet>()
                .select(CashMemberWallet::getId, CashMemberWallet::getCashBalance)
                .in(CashMemberWallet::getId, memberIds));
        if (CollectionUtil.isNotEmpty(list)) {
            return list.stream().collect(Collectors.toMap(CashMemberWallet::getId, CashMemberWallet::getCashBalance));
        }
        return Maps.newHashMap();
    }

    private TransferDTO buildMemberWalletTransferParam(DockConvertOrder dockConvertOrder) {

        TransferDTO transferDTO = new TransferDTO();
        transferDTO.setMerchantId(dockConvertOrder.getMerchantId());
        transferDTO.setPayNo(dockConvertOrder.getOrderNo());
        transferDTO.setAmount(dockConvertOrder.getBalance());
        transferDTO.setMemberName(dockConvertOrder.getMemberName());
        // add transfer remark
        transferDTO.setRemark(dockConvertOrder.getConvertText());

        if (dockConvertOrder.getTransferEnum() == ConvertOrderTransferEnum.OUT) {
            transferDTO.setTransferTypeEnum(TransferTypeEnum.IN);
        } else {
            transferDTO.setTransferTypeEnum(TransferTypeEnum.OUT);
        }

        return transferDTO;
    }

}
