package com.wd.lottery.module.cash.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.Tup2;
import com.wd.lottery.module.cash.constatns.OrderStatusEnum;
import com.wd.lottery.module.cash.constatns.PlatformTypeEnum;
import com.wd.lottery.module.cash.dto.*;
import com.wd.lottery.module.cash.dto.*;
import com.wd.lottery.module.cash.entity.CashDepositOrder;
import com.wd.lottery.module.member.constants.DeviceEnum;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.entity.PaymentMerchantOffline;
import com.wd.lottery.module.payment.param.PaymentOfflineDepositParam;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CashDepositOrderService extends IService<CashDepositOrder> {

    Long totalDepositMoney(Long merchantId,OrderStatusEnum orderStatusEnum, List<Long> memberIds, LocalDateTime startTime, LocalDateTime endTime);

    Map<PlatformTypeEnum, List<Map<String, List<String>>>> options(Long merchantId, CurrencyEnum currencyEnum);

    CashDepositOrder findById(Long id, Long merchantId);

    Page<CashDepositOrder> search(OrderSearchDTO orderSearchDto, Long merchantId, CurrencyEnum currencyEnum);

    TotalAmountDTO totalMoney(OrderSearchDTO orderSearchDto, Long merchantId, CurrencyEnum currencyEnum);

    List<OrderHistoryDTO> searchByMerchantId(OrderSearchDTO orderSearchDto, Long merchantId, Long memberId, CurrencyEnum currencyEnum);

    CashDepositOrder createOnlineOrder(PaymentMerchant paymentMerchant, MemberTokenInfoDTO memberTokenInfoDTO, Long orderMoney, Long exchangeOrderMoney, DeviceEnum deviceEnum, String payName, String memberName, LocalDateTime localDateTime);

    CashDepositOrder createOfflineOrder(PaymentMerchantOffline paymentMerchantOffline, PaymentOfflineDepositParam paymentOfflineDepositParam, MemberTokenInfoDTO memberTokenInfoDTO);

    CashDepositOrder createOrderByManual(Member member, CashManualPaymentDTO cashManualPaymentDto, CurrencyEnum currencyEnum, Long merchantId, LocalDateTime payTime);

    void tryUpdateThirdNo(CashDepositOrder cashDepositOrder, String thirdNo);

    void updateStatusAndMessage(CashDepositOrder cashDepositOrder, OrderStatusEnum statusEnum, String message);

    boolean updateOrderStatusOfAuto(CashDepositOrder cashDepositOrder, String message, LocalDateTime payTime, Long thirdFee, OrderStatusEnum orderStatusEnum);

    boolean updateOrderStatusOfManualBySuccess(OrderUpdateSuccessDTO orderUpdateSuccessDto);

    void updateOrderStatusOfManualByFailed(OrderUpdateFailDTO orderUpdateFailDto);

    boolean updateRemark(OrderUpdateRemarkDTO orderUpdateRemarkDto);

    void orderStatusChangedToFailed(Date date);

    List<OrderStatisticsDTO> depositStatistics(OrderStatisticsSearchDTO orderStatisticsSearchDto, CurrencyEnum currencyEnum);

    List<OrderStatusStatisticsDTO> depositStatusStatistics(OrderStatisticsSearchDTO orderStatisticsSearchDto, CurrencyEnum currencyEnum);

    List<CashDepositOrder> getMemberIdAndDepositMoneyByDate(GetMemberIdAndMoneyDTO getMemberIdAndMoneyDTO);

    ApiResult export(OrderSearchDTO orderSearchDto, Long merchantId, CurrencyEnum currencyEnum, String language, String createBy);
    boolean hasNonCancelledOrderWithSameTxId(Long merchantId, String txId);

    Long countDepositMemberByCreateTime(Long merchantId, CurrencyEnum currencyEnum, List<Long> memberIds, LocalDateTime startTime, LocalDateTime endTime);
    List<Long> getDepositMemberIdListByCreateTime(Long merchantId, CurrencyEnum currencyEnum, LocalDateTime startTime, LocalDateTime endTime);
    List<Tup2<LocalDate, Long>> getDepositMemberIdListGroupByCreateTime(Long merchantId, CurrencyEnum currencyEnum, List<Long> memberIds, LocalDateTime startTime, LocalDateTime endTime);

    Long countFirstDepositFailedMemberByCreateTime(Long merchantId, CurrencyEnum currencyEnum, List<Long> memberIds, LocalDateTime startTime, LocalDateTime endTime);
    List<Tup2<LocalDate, Long>> getFirstDepositFailedMemberIdListGroupByCreateTime(Long merchantId, CurrencyEnum currencyEnum, List<Long> memberIds, LocalDateTime startTime, LocalDateTime endTime);

    Long countGt30dayDepositMemberByCreateTime(Long merchantId, CurrencyEnum currencyEnum, LocalDateTime startTime, LocalDateTime endTime);
    List<Long> selectGt30dayDepositMemberIdListByCreateTime(Long merchantId, CurrencyEnum currencyEnum, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询待处理订单数量以及最后一笔订单的ID
     * @param merchantId
     * @param currencyEnum
     * @param platformTypeEnums
     * @return tup_1 待处理订单数量 tup_2 最后一笔订单ID
     */
    Tup2<Long, Long> getPendingOrderCountAndLastOrderId(Long merchantId, CurrencyEnum currencyEnum, List<PlatformTypeEnum> platformTypeEnums, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查詢指定商戶中在指定時間前處於 PROGRESSING 狀態的訂單
     *
     * @param merchantId 商戶ID
     * @param pollingTime 輪詢時間門檻
     * @return 訂單列表
     */
    List<CashDepositOrder> getDepositOrderInProgressing(Long merchantId, LocalDateTime pollingTime);
}
