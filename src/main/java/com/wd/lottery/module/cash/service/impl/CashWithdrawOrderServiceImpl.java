package com.wd.lottery.module.cash.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.common.entity.CreateEntity;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.common.util.SqlUtil;
import com.wd.lottery.common.util.Tup3;
import com.wd.lottery.module.activity.constants.ActivityTypeEnum;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.param.vip.ActivityVipConfigParam;
import com.wd.lottery.module.activity.param.vip.ActivityVipParam;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.cash.constatns.*;
import com.wd.lottery.module.cash.dto.*;
import com.wd.lottery.module.cash.constatns.*;
import com.wd.lottery.module.cash.dto.*;
import com.wd.lottery.module.cash.entity.CashWithdrawOrder;
import com.wd.lottery.module.cash.mapper.CashWithdrawOrderMapper;
import com.wd.lottery.module.cash.param.CashWithdrawOrderApplyParam;
import com.wd.lottery.module.cash.param.CashWithdrawOrderQueryParam;
import com.wd.lottery.module.cash.service.CashAuditService;
import com.wd.lottery.module.cash.service.CashMemberWalletService;
import com.wd.lottery.module.cash.service.CashWithdrawOrderService;
import com.wd.lottery.module.cash.util.WithdrawCardNoUtil;
import com.wd.lottery.module.common.config.ExportExcelConfig;
import com.wd.lottery.module.common.constants.*;
import com.wd.lottery.module.common.constants.*;
import com.wd.lottery.module.common.export.CashWithdrawOrderExportExcelStrategy;
import com.wd.lottery.module.common.param.AsyncExportParam;
import com.wd.lottery.module.common.service.CommonMessageMemberService;
import com.wd.lottery.module.common.service.ExportExcelService;
import com.wd.lottery.module.member.constants.MemberStateEnum;
import com.wd.lottery.module.member.constants.MemberTypeEnum;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.entity.MemberWithdrawAccount;
import com.wd.lottery.module.member.service.CMemberService;
import com.wd.lottery.module.member.service.MemberProxyService;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.member.service.MemberWithdrawAccountService;
import com.wd.lottery.module.merchant.dto.MerchantConfigWithdrawLimitDTO;
import com.wd.lottery.module.merchant.dto.MerchantConfigWithdrawPlatformDTO;
import com.wd.lottery.module.merchant.dto.MerchantConfigWithdrawPlatformPaymentBankDTO;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.payment.config.PaymentFrequencyConfig;
import com.wd.lottery.module.payment.constants.PaymentRedisKeyConstants;
import com.wd.lottery.module.payment.dto.payment.WithdrawResultDTO;
import com.wd.lottery.module.payment.entity.PaymentChannel;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.service.PaymentChannelService;
import com.wd.lottery.module.payment.service.PaymentMerchantService;
import com.wd.lottery.module.payment.service.PaymentResultService;
import com.wd.lottery.module.report.entity.ReportFirstWithdrawHis;
import com.wd.lottery.module.report.service.ReportFirstWithdrawHisService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CashWithdrawOrderServiceImpl extends ServiceImpl<CashWithdrawOrderMapper, CashWithdrawOrder> implements CashWithdrawOrderService {
    private final PaymentResultService paymentResultService;

    private final CashWithdrawOrderMapper cashWithdrawOrderMapper;

    private final ReportFirstWithdrawHisService reportFirstWithdrawHisService;

    private final MerchantConfigService merchantConfigService;

    private final CashMemberWalletService cashMemberWalletService;

    private final CashAuditService cashAuditService;

    private final MemberWithdrawAccountService memberWithdrawAccountService;

    private final MemberService memberService;

    private final CMemberService cMemberService;

    private final RedisService redisService;

    private final PaymentMerchantService paymentMerchantService;

    private final MemberProxyService memberProxyService;

    private final PaymentChannelService paymentChannelService;

    private final CommonMessageMemberService commonMessageMemberService;

    private final ActivityService activityService;

    private final ExportExcelService exportExcelService;

    private final ExportExcelConfig exportExcelConfig;

    private final PaymentFrequencyConfig paymentFrequencyConfig;
    @Override
    public CashWithdrawOrder findById(Long id, Long merchantId) {
        LambdaQueryWrapper<CashWithdrawOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashWithdrawOrder::getId, id);
        queryWrapper.eq(CashWithdrawOrder::getMerchantId, merchantId);
        return super.getOne(queryWrapper);
    }

    /**
     * 訂單查詢
     *
     * @param orderSearchDto dto
     * @return page
     */
    @Override
    public Page<CashWithdrawOrder> search(OrderSearchDTO orderSearchDto, Long merchantId, CurrencyEnum currencyEnum) {
        Page<CashWithdrawOrder> basePage = new Page<>(orderSearchDto.getCurrent(), orderSearchDto.getSize());
        try {
            LambdaQueryWrapper<CashWithdrawOrder> queryWrapper = new LambdaQueryWrapper<>();

            if (orderSearchDto.getMemberId() != null) {
                queryWrapper.eq(CashWithdrawOrder::getMemberId, orderSearchDto.getMemberId());
            }
            if (orderSearchDto.getOrderNo() != null) {
                queryWrapper.eq(CashWithdrawOrder::getId, orderSearchDto.getOrderNo());
            }

            if (StringUtils.isNotBlank(orderSearchDto.getChannelName())) {
                queryWrapper.eq(CashWithdrawOrder::getChannelName, orderSearchDto.getChannelName());
            }

            if (orderSearchDto.getStatusEnum() != null) {
                queryWrapper.eq(CashWithdrawOrder::getStatusEnum, orderSearchDto.getStatusEnum());
            }

            if (CollectionUtil.isNotEmpty(orderSearchDto.getStatusEnums())) {
                queryWrapper.in(CashWithdrawOrder::getStatusEnum, orderSearchDto.getStatusEnums());
            }

            if (orderSearchDto.getPlatformEnumList() != null) {
                queryWrapper.in(CashWithdrawOrder::getPlatformEnum, orderSearchDto.getPlatformEnumList());
            }

            if (orderSearchDto.getPlatformTypeEnum() != null) {
                queryWrapper.eq(CashWithdrawOrder::getPlatformTypeEnum, orderSearchDto.getPlatformTypeEnum());
            }

            if (orderSearchDto.getMinAmount() != null && orderSearchDto.getMaxAmount() != null) {
                queryWrapper.between(CashWithdrawOrder::getExchangeRealMoney, orderSearchDto.getMinAmount(), orderSearchDto.getMaxAmount());
            }

            if (orderSearchDto.getStartTime() != null && orderSearchDto.getEndTime() != null) {
                queryWrapper.between(CashWithdrawOrder::getCreateTime, orderSearchDto.getStartTime(), orderSearchDto.getEndTime());
            }

            if (orderSearchDto.getStartPayTime() != null && orderSearchDto.getEndPayTime() != null) {
                queryWrapper.between(CashWithdrawOrder::getPayTime, orderSearchDto.getStartPayTime(), orderSearchDto.getEndPayTime());
            }

            if (orderSearchDto.getStartUpdateTime() != null && orderSearchDto.getEndUpdateTime() != null) {
                queryWrapper.between(CashWithdrawOrder::getUpdateTime, orderSearchDto.getStartUpdateTime(), orderSearchDto.getEndUpdateTime());
            }

            queryWrapper.eq(Objects.nonNull(orderSearchDto.getMemberTypeEnum()), CashWithdrawOrder::getMemberTypeEnum, orderSearchDto.getMemberTypeEnum());
            queryWrapper.eq(CashWithdrawOrder::getMerchantId, merchantId);
            queryWrapper.eq(CashWithdrawOrder::getCurrencyEnum, currencyEnum);
            queryWrapper.eq(CollectionUtil.isNotEmpty(orderSearchDto.getChannelIdSet()),CashWithdrawOrder::getIsDirect, BooleanEnum.TRUE);
            queryWrapper.in(CollectionUtil.isNotEmpty(orderSearchDto.getChannelIdSet()),CashWithdrawOrder::getChannelId,orderSearchDto.getChannelIdSet());
            queryWrapper.orderByDesc(CreateEntity::getCreateTime);
            Page<CashWithdrawOrder> page = super.page(basePage, queryWrapper);
            boolean bln = Objects.equals(Boolean.TRUE, orderSearchDto.getIsSearchUserRemark());
            if(bln&&CollectionUtil.isNotEmpty(page.getRecords())){
                List<Long> ids = page.getRecords().stream().map(CashWithdrawOrder::getMemberId).distinct().collect(Collectors.toList());
                Map<Long, String> memberByIdsForMap = memberService.getMemberRemarkByIdsForMap(ids, merchantId);
                if(MapUtil.isNotEmpty(memberByIdsForMap)) {
                    page.getRecords().forEach(item ->{
                        item.setUserRemark(memberByIdsForMap.get(item.getMemberId()));
                    });
                }
            }
            page.setRecords(this.withdrawCardNoNeedShow(page.getRecords(), bln));
            return page;
        } catch (BadSqlGrammarException e) {
            log.info("當前商戶無任何提現訂單, merchantId:{}", merchantId, e);
            return basePage;
        }
    }

    private List<CashWithdrawOrder> withdrawCardNoNeedShow(List<CashWithdrawOrder> records, boolean bln) {
        if (AdminTokenInfoUtil.isAdmin()) {
            return records;
        }
        Boolean isShow = WithdrawCardNoUtil.isShowWithdrawCardNo(bln);
        return CollStreamUtil.toList(records, item -> {
            String bankAccount = item.getBankAccount();
            if (!isShow && StrUtil.isNotEmpty(bankAccount)) {
                item.setBankAccount(bankAccount.length() > 4 ? "****".concat(bankAccount.substring(bankAccount.length() - 4)) : "****".concat(bankAccount));
            }
            return item;
        });
    }

    /**
     * 查總計
     *
     * @param orderSearchDto dto
     * @param merchantId     merchant uid
     * @param currencyEnum   currency enum
     * @return long
     */
    @Override
    public TotalAmountDTO totalMoney(OrderSearchDTO orderSearchDto, Long merchantId, CurrencyEnum currencyEnum) {
        return cashWithdrawOrderMapper.totalAmounts(orderSearchDto, merchantId, currencyEnum);
    }

    /**
     * c端訂單查詢
     */
    @Override
    public List<OrderHistoryDTO> cGetList(CashWithdrawOrderQueryParam cashWithdrawOrderQueryParam) {
        List<OrderHistoryDTO> orderHistoryDtoList = new ArrayList<>();
        LambdaQueryChainWrapper<CashWithdrawOrder> lambdaQueryChainWrapper = super.lambdaQuery()
                .select(
                        CashWithdrawOrder::getId,
                        CashWithdrawOrder::getExchangeOrderMoney,
                        CashWithdrawOrder::getStatusEnum,
                        CashWithdrawOrder::getCreateTime,
                        CashWithdrawOrder::getPlatformEnum)
                .eq(CashWithdrawOrder::getMerchantId, cashWithdrawOrderQueryParam.getMerchantId())
                .eq(CashWithdrawOrder::getMemberId, cashWithdrawOrderQueryParam.getMemberId())
                .in(CashWithdrawOrder::getPlatformEnum, PlatformEnum.getCWithdrawSearchPlatformEnumList())
                .eq(Objects.nonNull(cashWithdrawOrderQueryParam.getPlatformEnum()), CashWithdrawOrder::getPlatformEnum, cashWithdrawOrderQueryParam.getPlatformEnum())
                .between(Objects.nonNull(cashWithdrawOrderQueryParam.getStartTime()) && Objects.nonNull(cashWithdrawOrderQueryParam.getEndTime()), CashWithdrawOrder::getCreateTime, cashWithdrawOrderQueryParam.getStartTime(), cashWithdrawOrderQueryParam.getEndTime())
                .orderByDesc(CreateEntity::getCreateTime);

        if (Objects.nonNull(cashWithdrawOrderQueryParam.getOrderStatusEnum())) {
            if (cashWithdrawOrderQueryParam.getOrderStatusEnum().equals(OrderStatusEnum.PROGRESSING)) {
                lambdaQueryChainWrapper.in(CashWithdrawOrder::getStatusEnum, OrderStatusEnum.getProgressWithdrawOrderStatusEnumList());
            } else {
                lambdaQueryChainWrapper.eq(CashWithdrawOrder::getStatusEnum, cashWithdrawOrderQueryParam.getOrderStatusEnum());
            }
        }

        Page<CashWithdrawOrder> page = lambdaQueryChainWrapper.page(Page.of(cashWithdrawOrderQueryParam.getCurrent(), cashWithdrawOrderQueryParam.getSize(), false));
        List<CashWithdrawOrder> cashWithdrawOrderList = page.getRecords();
        if (CollectionUtil.isEmpty(cashWithdrawOrderList)) {
            return orderHistoryDtoList;
        }

        cashWithdrawOrderList.forEach(cashWithdrawOrder -> {
            OrderHistoryDTO orderHistoryDto = BeanUtil.copyProperties(cashWithdrawOrder, OrderHistoryDTO.class);
            orderHistoryDto.setOrderNo(String.valueOf(cashWithdrawOrder.getId()));
            orderHistoryDto.setAmount(cashWithdrawOrder.getExchangeOrderMoney());
            orderHistoryDtoList.add(orderHistoryDto);
        });
        return orderHistoryDtoList;
    }

    /**
     * c端用戶申請提現
     *
     * @param cashWithdrawOrderApplyParam dto
     */
    @Transactional
    @Override
    public void apply(CashWithdrawOrderApplyParam cashWithdrawOrderApplyParam) {
        Long exchangeOrderMoney = cashWithdrawOrderApplyParam.getExchangeOrderMoney();
        if (exchangeOrderMoney <= 0L) {
            throw new ApiException(CommonCode.AMOUNT_CANNOT_BE_ZERO);
        }
        Member member = memberService.getMemberByIdNotNull(cashWithdrawOrderApplyParam.getMemberId(), cashWithdrawOrderApplyParam.getMerchantId());
        if (ObjectUtil.equal(member.getMemberStateEnum(), MemberStateEnum.DISABLE)) {
            throw new ApiException(CommonCode.MEMBER_DISABLED);
        }
        Boolean withdrawPasswordCheck = cMemberService.checkMerchantWithdrawPwdConfig(cashWithdrawOrderApplyParam.getMerchantId(), cashWithdrawOrderApplyParam.getCurrencyEnum(),
                CommonDictKeyEnum.SWITCH_WITHDRAW_PASSWORD_VERIFICATION);
        if (withdrawPasswordCheck) {
            Assert.notNull(cashWithdrawOrderApplyParam.getWithdrawPasswd(), "提现密码不能为空");
            cMemberService.checkMemberWithdrawPasswd(cashWithdrawOrderApplyParam.getWithdrawPasswd(), member);
        } else {
            Boolean loginPasswordCheck = cMemberService.checkMerchantWithdrawPwdConfig(cashWithdrawOrderApplyParam.getMerchantId(), cashWithdrawOrderApplyParam.getCurrencyEnum(),
                    CommonDictKeyEnum.SWITCH_WITHDRAW_LOGIN_PASSWORD_VERIFICATION);
            if (loginPasswordCheck) {
                Assert.notNull(cashWithdrawOrderApplyParam.getPasswd(), "登录密码不能为空");
                cMemberService.checkMemberPasswd(cashWithdrawOrderApplyParam.getPasswd(), member);
            }
        }
        checkMemberWithdrawStatus(member);
        redisService.frequencyControl(PaymentRedisKeyConstants.WITHDRAW_INTERVALS +
                        String.format("%s_%s", cashWithdrawOrderApplyParam.getMerchantId(),
                cashWithdrawOrderApplyParam.getMemberId()),
                paymentFrequencyConfig.getWithdrawIntervalsSeconds());

        existProgressWithdrawOrder(cashWithdrawOrderApplyParam);

        cashAuditService.checkAllowWithdraw(cashWithdrawOrderApplyParam.getMemberId(), cashWithdrawOrderApplyParam.getMerchantId());

        CashWithdrawOrder cashWithdrawOrder = createOrder(member, cashWithdrawOrderApplyParam);

        checkWithdrawLimit(cashWithdrawOrderApplyParam);

        checkVipWithdrawLimit(member, cashWithdrawOrder);

        saveCashWithdrawOrder(cashWithdrawOrder);
    }

    private void checkMemberWithdrawStatus(Member member) {
        if (ObjectUtil.equals(member.getWithdrawStatusEnum(),BooleanEnum.FALSE)) {
            throw new ApiException(CommonCode.MEMBER_WITHDRAW_DISABLED);
        }
    }

    /**
     * 每日成功提單次數
     *
     * @param memberId   member uid
     * @param merchantId merchant uid
     * @return count
     */
    @Override
    public CashWithdrawCountStatisticDTO getWithdrawCount(Long memberId, Long merchantId) {
        LocalDateTime now = LocalDateTime.now();
        List<CashWithdrawOrder> list = this.lambdaQuery()
                .select(CashWithdrawOrder::getExchangeRealMoney, CashWithdrawOrder::getStatusEnum)
                .eq(CashWithdrawOrder::getMemberId, memberId)
                .eq(CashWithdrawOrder::getMerchantId, merchantId)
                .in(CashWithdrawOrder::getStatusEnum, OrderStatusEnum.SUCCESS, OrderStatusEnum.PENDING, OrderStatusEnum.PROGRESSING)
                .between(CashWithdrawOrder::getCreateTime, LocalDateTimeUtil.beginOfDay(now), LocalDateTimeUtil.endOfDay(now))
                .list();
        CashWithdrawCountStatisticDTO dto = new CashWithdrawCountStatisticDTO();
        if (CollectionUtils.isEmpty(list)) {
            return dto;
        }
        long count = list.stream().filter(i -> i.getStatusEnum() == OrderStatusEnum.SUCCESS).count();
        dto.setCount((int)count);
        dto.setTotalAmount(list.stream().mapToLong(CashWithdrawOrder::getExchangeRealMoney).sum());
        return dto;
    }

    private void existProgressWithdrawOrder(CashWithdrawOrderApplyParam cashWithdrawOrderApplyParam) {
        MerchantConfig withdrawLimitMerchantConfig = merchantConfigService.getByMerchantIdAndDictKey(cashWithdrawOrderApplyParam.getMerchantId(), CommonConstants.COMMON_DICT_KEY_WITHDRAW_LIMIT, cashWithdrawOrderApplyParam.getCurrencyEnum());
        if (withdrawLimitMerchantConfig == null) {
            return;
        }

        MerchantConfigWithdrawLimitDTO merchantConfigWithdrawLimitDTO = withdrawLimitMerchantConfig.merchantConfigListGetFirstNotNull(MerchantConfigWithdrawLimitDTO.class);
        if (merchantConfigWithdrawLimitDTO == null ||
                merchantConfigWithdrawLimitDTO.getConcurrentWithdrawCount() == null ||
                merchantConfigWithdrawLimitDTO.getConcurrentWithdrawCount() <= 0) {
            return;
        }

        Integer concurrentWithdrawCount = merchantConfigWithdrawLimitDTO.getConcurrentWithdrawCount();

        Long count = super.lambdaQuery()
                .eq(CashWithdrawOrder::getMerchantId, cashWithdrawOrderApplyParam.getMerchantId())
                .eq(CashWithdrawOrder::getMemberId, cashWithdrawOrderApplyParam.getMemberId())
                .in(CashWithdrawOrder::getStatusEnum, OrderStatusEnum.getProgressWithdrawOrderStatusEnumList())
                .count();
        if (count >= concurrentWithdrawCount) {
            throw new ApiException(CommonCode.CASH_WITHDRAW_ORDER_PROGRESSING);
        }
    }

    private void checkWithdrawLimit(CashWithdrawOrderApplyParam cashWithdrawOrderApplyParam) {
        MerchantConfig withdrawLimitMerchantConfig = merchantConfigService.getByMerchantIdAndDictKey(cashWithdrawOrderApplyParam.getMerchantId(), CommonConstants.COMMON_DICT_KEY_WITHDRAW_LIMIT, cashWithdrawOrderApplyParam.getCurrencyEnum());
        if (withdrawLimitMerchantConfig == null) {
            return;
        }

        MerchantConfigWithdrawLimitDTO merchantConfigWithdrawLimitDTO = withdrawLimitMerchantConfig.merchantConfigListGetFirstNotNull(MerchantConfigWithdrawLimitDTO.class);
        LocalTime nowLocalTime = LocalTime.now();
        if ((merchantConfigWithdrawLimitDTO.getWithdrawStartTime() != null &&
                nowLocalTime.isBefore(merchantConfigWithdrawLimitDTO.getWithdrawStartTime())) ||
                (merchantConfigWithdrawLimitDTO.getWithdrawEndTime() != null &&
                        nowLocalTime.isAfter(merchantConfigWithdrawLimitDTO.getWithdrawEndTime()))) {
            throw new ApiException(CommonCode.CASH_WITHDRAW_ORDER_NOT_IN_WITHDRAW_TIME_RANGE);
        }

        if (isWithdrawalAmountOutOfLimit(merchantConfigWithdrawLimitDTO, cashWithdrawOrderApplyParam)) {
            throw new ApiException(CommonCode.CASH_WITHDRAW_ORDER_NOT_IN_WITHDRAW_AMOUNT_RANGE);
        }

        if (isWithdrawalCountOutOfLimit(merchantConfigWithdrawLimitDTO, cashWithdrawOrderApplyParam)) {
            throw new ApiException(CommonCode.CASH_WITHDRAW_ORDER_NOT_IN_WITHDRAW_DAILY_TIMES);
        }
    }

    private boolean isWithdrawalAmountOutOfLimit(MerchantConfigWithdrawLimitDTO limitConfig, CashWithdrawOrderApplyParam param) {
        MerchantConfigWithdrawLimitDTO.WithdrawPlatformConfig withdrawPlatformConfig = limitConfig.getWithdrawPlatformConfigByPlatform(param.getPlatformEnum());
        Long orderAmount = param.getExchangeOrderMoney();
        Long minWithdrawAmount = 1L;
        Long maxWithdrawAmount = Long.MAX_VALUE;

        if (withdrawPlatformConfig != null) {
            minWithdrawAmount = withdrawPlatformConfig.getMinWithdrawAmount() == null ? minWithdrawAmount : withdrawPlatformConfig.getMinWithdrawAmount();
            maxWithdrawAmount = withdrawPlatformConfig.getMaxWithdrawAmount() == null ? maxWithdrawAmount : withdrawPlatformConfig.getMaxWithdrawAmount();
        }
        return orderAmount < minWithdrawAmount || orderAmount > maxWithdrawAmount;
    }

    private boolean isWithdrawalCountOutOfLimit(MerchantConfigWithdrawLimitDTO limitConfig, CashWithdrawOrderApplyParam param) {
        if (limitConfig.getDailyWithdrawCount() == null || limitConfig.getDailyWithdrawCount() < Constants.ZERO_INTEGER) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        Long withdrawCount = super.lambdaQuery()
                .eq(CashWithdrawOrder::getMerchantId, param.getMerchantId())
                .eq(CashWithdrawOrder::getMemberId, param.getMemberId())
                .eq(CashWithdrawOrder::getStatusEnum, OrderStatusEnum.SUCCESS)
                .between(CashWithdrawOrder::getCreateTime, LocalDateTimeUtil.beginOfDay(now), LocalDateTimeUtil.endOfDay(now))
                .count();
        return withdrawCount + 1 > limitConfig.getDailyWithdrawCount();
    }

    private CashWithdrawOrder createOrder(Member member, CashWithdrawOrderApplyParam cashWithdrawOrderApplyParam) {
        MemberWithdrawAccount memberWithdrawAccount = memberWithdrawAccountService.findByIdAndMemberIdAndMerchantIdNotNull(cashWithdrawOrderApplyParam.getMemberWithdrawAccountId(), cashWithdrawOrderApplyParam.getMemberId(), cashWithdrawOrderApplyParam.getMerchantId());
        // 過渡用的set 之後移除
        cashWithdrawOrderApplyParam.setPlatformEnum(memberWithdrawAccount.getPlatformEnum());

        long orderMoney = getOrderMoney(cashWithdrawOrderApplyParam, memberWithdrawAccount);

        CashWithdrawOrder cashWithdrawOrder = new CashWithdrawOrder();
        cashWithdrawOrder.setId(IdWorker.getId());

        // 用戶資訊
        cashWithdrawOrder.setMemberId(member.getId());
        cashWithdrawOrder.setMemberName(member.getMemberName());
        cashWithdrawOrder.setRealName(member.getRealName());
        cashWithdrawOrder.setGroupId(member.getGroupId());
        cashWithdrawOrder.setGroupName(member.getGroupName());
        cashWithdrawOrder.setCurrencyEnum(member.getCurrencyEnum());
        cashWithdrawOrder.setMerchantId(member.getMerchantId());
        cashWithdrawOrder.setInviteTypeEnum(member.getInviteTypeEnum());
        cashWithdrawOrder.setIsDirect(member.getIsDirect());
        cashWithdrawOrder.setChannelId(member.getChannelId());
        cashWithdrawOrder.setMemberTypeEnum(member.getMemberTypeEnum());

        cashWithdrawOrder.setBankAccount(memberWithdrawAccount.getAccount());
        cashWithdrawOrder.setBankId(memberWithdrawAccount.getPaymentBankId());
        cashWithdrawOrder.setAdditional(memberWithdrawAccount.getAdditional());

        cashWithdrawOrder.setStatusEnum(OrderStatusEnum.PENDING);
        cashWithdrawOrder.setPlatformTypeEnum(PlatformTypeEnum.ONLINE);
        cashWithdrawOrder.setPlatformEnum(PlatformEnum.valueOf(memberWithdrawAccount.getPlatformEnum().name()));
        cashWithdrawOrder.setWithdrawMethodEnum(PaymentMethodEnum.AUTO);

        BigDecimal platformFeeRate = this.getMemberPlatformFeeRate(member, cashWithdrawOrderApplyParam);
        Long orderMoneyFee = getAmountOfFee(orderMoney, platformFeeRate);
        Long exchangeMoneyFee = getAmountOfFee(cashWithdrawOrderApplyParam.getExchangeOrderMoney(), platformFeeRate);

        cashWithdrawOrder.setPlatformFee(orderMoneyFee);
        cashWithdrawOrder.setOrderMoney(orderMoney);
        cashWithdrawOrder.setRealMoney(orderMoney - orderMoneyFee);
        cashWithdrawOrder.setExchangeOrderMoney(cashWithdrawOrderApplyParam.getExchangeOrderMoney());
        cashWithdrawOrder.setExchangeRealMoney(cashWithdrawOrderApplyParam.getExchangeOrderMoney() - exchangeMoneyFee);
        cashWithdrawOrder.setLockEnum(BooleanEnum.FALSE);
        cashWithdrawOrder.setUpdateTime(cashWithdrawOrderApplyParam.getUpdateTime());
        cashWithdrawOrder.setUpdateBy(cashWithdrawOrderApplyParam.getMemberName());
        cashWithdrawOrder.setCreateTime(cashWithdrawOrderApplyParam.getUpdateTime());
        cashWithdrawOrder.setCreateBy(cashWithdrawOrderApplyParam.getMemberName());

        // 判斷是否首次提款
        ReportFirstWithdrawHis reportFirstWithdrawHis = reportFirstWithdrawHisService.findByMemberIdAndMerchantId(member.getId(), member.getMerchantId(), member.getCurrencyEnum());
        if (reportFirstWithdrawHis == null) {
            cashWithdrawOrder.setFirstWithdrawalEnum(BooleanEnum.TRUE);
        } else {
            cashWithdrawOrder.setFirstWithdrawalEnum(BooleanEnum.FALSE);
        }

        return cashWithdrawOrder;
    }

    private BigDecimal getMemberPlatformFeeRate(Member member, CashWithdrawOrderApplyParam cashWithdrawOrderApplyParam) {
        MerchantConfig withdrawLimitMerchantConfig = merchantConfigService.getByMerchantIdAndDictKey(cashWithdrawOrderApplyParam.getMerchantId(), CommonConstants.COMMON_DICT_KEY_WITHDRAW_LIMIT, cashWithdrawOrderApplyParam.getCurrencyEnum());
        if (withdrawLimitMerchantConfig == null) {
            return BigDecimal.ZERO;
        }
        MerchantConfigWithdrawLimitDTO merchantConfigWithdrawLimitDTO = withdrawLimitMerchantConfig.merchantConfigListGetFirstNotNull(MerchantConfigWithdrawLimitDTO.class);
        BigDecimal platformFeeRate = Optional.ofNullable(merchantConfigWithdrawLimitDTO.getPlatformFeeRate()).orElse(BigDecimal.ZERO);
        // 活动未开启，表示无免费提现
        Activity vipActivity = activityService.getEnableOne(cashWithdrawOrderApplyParam.getMerchantId(), cashWithdrawOrderApplyParam.getCurrencyEnum(), ActivityTypeEnum.VIP);
        if (vipActivity == null) {
            log.info("商戶ID={} 未配置vip每日免费提现次数", cashWithdrawOrderApplyParam.getMerchantId());
            return platformFeeRate;
        }
        ActivityVipConfigParam vipLevelConfigByLevel = vipActivity.activityParamToVipLevelConfigByVipLevel(member.getVipLevel());
        Long countOfFreeWithdraw = Optional.ofNullable(vipLevelConfigByLevel.getFreeWithdrawCount()).orElse(0L);
        CashWithdrawCountStatisticDTO withdrawStatistic = getWithdrawCount(member.getId(), member.getMerchantId());
        if (withdrawStatistic.getCount() < countOfFreeWithdraw) {
            return BigDecimal.ZERO;
        }
        return platformFeeRate;
    }

    private Long getAmountOfFee(Long amount, BigDecimal platformFeeRate) {
        // 5050 = 50.5%
        return BigDecimal.valueOf(amount)
                .multiply(platformFeeRate)
                .divide(BigDecimal.valueOf(10000), 2, RoundingMode.UP)
                .longValue();
    }

    private long getOrderMoney(CashWithdrawOrderApplyParam cashWithdrawOrderApplyParam, MemberWithdrawAccount memberWithdrawAccount) {
        MerchantConfig withdrawPlatformMerchantConfig = merchantConfigService.getByMerchantIdAndDictKey(cashWithdrawOrderApplyParam.getMerchantId(), CommonConstants.COMMON_DICT_KEY_WITHDRAW_PLATFORM, cashWithdrawOrderApplyParam.getCurrencyEnum());
        if (withdrawPlatformMerchantConfig == null) {
            log.error("withdrawPlatformMerchantConfig is null");
            throw new ApiException(CommonCode.PARAM_INVALID);
        }
        List<MerchantConfigWithdrawPlatformDTO> merchantConfigWithdrawPlatformDTOList = withdrawPlatformMerchantConfig.merchantConfigToListNotEmpty(MerchantConfigWithdrawPlatformDTO.class);
        MerchantConfigWithdrawPlatformDTO merchantConfigWithdrawPlatformDTO = merchantConfigWithdrawPlatformDTOList
                .stream()
                .filter(o -> o.getPlatformEnum().equals(memberWithdrawAccount.getPlatformEnum()))
                .findFirst()
                .orElseThrow(() -> {
                    log.warn("merchantConfigWithdrawPlatformDTOList not config memberWithdrawAccount:{}, merchantConfigWithdrawPlatformDTOList:{}", memberWithdrawAccount, merchantConfigWithdrawPlatformDTOList);
                    return new ApiException(CommonCode.PARAM_INVALID);
                });

        List<MerchantConfigWithdrawPlatformPaymentBankDTO> merchantConfigWithdrawPlatformPaymentBankDTOList = merchantConfigWithdrawPlatformDTO.getPaymentBank();
        Assert.notEmpty(merchantConfigWithdrawPlatformPaymentBankDTOList, "merchantConfigWithdrawPlatformPaymentBankDTOList is empty:{}", merchantConfigWithdrawPlatformPaymentBankDTOList);

        MerchantConfigWithdrawPlatformPaymentBankDTO merchantConfigWithdrawPlatformPaymentBankDTO = merchantConfigWithdrawPlatformPaymentBankDTOList.stream()
                .filter(o -> o.getPaymentBankId().equals(memberWithdrawAccount.getPaymentBankId()))
                .findFirst().orElseThrow(() -> {
                            log.warn("merchantConfigWithdrawPlatformPaymentBankDTO not config memberWithdrawAccount:{}, merchantConfigWithdrawPlatformPaymentBankDTOList:{}", memberWithdrawAccount, merchantConfigWithdrawPlatformPaymentBankDTOList);
                            return new ApiException(CommonCode.WITHDRAW_CHANNEL_IS_NOT_SUPPORTED);
                        }
                );

        BigDecimal exchangeRate = merchantConfigWithdrawPlatformPaymentBankDTO.getExchangeRate();
        if (exchangeRate == null || BigDecimal.ZERO.equals(exchangeRate)) {
            return cashWithdrawOrderApplyParam.getExchangeOrderMoney();
        }
        long orderMoney = new BigDecimal(cashWithdrawOrderApplyParam.getExchangeOrderMoney()).divide(exchangeRate, RoundingMode.DOWN).longValue();
        if (orderMoney <= Constants.ZERO_LONG) {
            throw new ApiException(CommonCode.PARAM_INVALID);
        }
        return orderMoney;
    }

    private void operateWithdrawAmount(CashWithdrawOrder cashWithdrawOrder) {
        // 會員錢包處理(用戶提單時就先扣除, 三方回調不管如何就不用在處理會員錢包, 只有在後台取消出款才將金額補回會員)
        CashMemberWalletOperateDTO cashMemberWalletOperateDto = new CashMemberWalletOperateDTO();
        cashMemberWalletOperateDto.setMemberId(cashWithdrawOrder.getMemberId());
        cashMemberWalletOperateDto.setMemberName(cashWithdrawOrder.getMemberName());
        cashMemberWalletOperateDto.setMerchantId(cashWithdrawOrder.getMerchantId());
        cashMemberWalletOperateDto.setPayNo(String.valueOf(cashWithdrawOrder.getId()));
        //exchangeOrderMoney 提现需扣除金额，exchangeRealMoney 实际给用户出款金额。
        cashMemberWalletOperateDto.setAmount(cashWithdrawOrder.getExchangeOrderMoney());
        cashMemberWalletOperateDto.setOperateTime(cashWithdrawOrder.getCreateTime());
        cashMemberWalletOperateDto.setSubTradeTypeEnum(SubTradeTypeEnum.WITHDRAW);
        cashMemberWalletService.operateWallet(cashMemberWalletOperateDto);
    }

    /**
     * 建立訂單(人工出款)
     *
     * @param member               entity
     * @param cashManualPaymentDto dto
     * @param currencyEnum         currency enum
     * @param merchantId           merchant uid
     * @return order entity
     */
    @Override
    public CashWithdrawOrder createOrderByManual(Member member, CashManualPaymentDTO cashManualPaymentDto, CurrencyEnum currencyEnum, Long merchantId, LocalDateTime payTime) {
        CashWithdrawOrder cashWithdrawOrder = new CashWithdrawOrder();
        // 用戶資訊
        cashWithdrawOrder.setMemberId(member.getId());
        cashWithdrawOrder.setMemberName(member.getMemberName());
        cashWithdrawOrder.setRealName(StringUtils.isNotBlank(member.getRealName())?member.getRealName():StringUtils.EMPTY);
        cashWithdrawOrder.setGroupId(member.getGroupId());
        cashWithdrawOrder.setGroupName(member.getGroupName());

        // 收付資訊
        cashWithdrawOrder.setStatusEnum(OrderStatusEnum.SUCCESS);
        cashWithdrawOrder.setPayTime(payTime);
        cashWithdrawOrder.setPlatformTypeEnum(PlatformTypeEnum.MANUAL);
        cashWithdrawOrder.setChannelName(OrderChannelNameEnum.MANUAL_WITHDRAW.getText());
        cashWithdrawOrder.setPlatformEnum(PlatformEnum.MANUAL);
        cashWithdrawOrder.setPlatformFee(Constants.ZERO_LONG);
        cashWithdrawOrder.setOrderMoney(cashManualPaymentDto.getAmount());
        cashWithdrawOrder.setExchangeOrderMoney(cashManualPaymentDto.getAmount());
        cashWithdrawOrder.setRealMoney(cashManualPaymentDto.getAmount());
        cashWithdrawOrder.setExchangeRealMoney(cashManualPaymentDto.getAmount());
        cashWithdrawOrder.setCurrencyEnum(currencyEnum);
        cashWithdrawOrder.setMerchantId(merchantId);
        cashWithdrawOrder.setWithdrawMethodEnum(PaymentMethodEnum.MANUAL);
        cashWithdrawOrder.setRemark(cashManualPaymentDto.getRemark());
        cashWithdrawOrder.setLockEnum(BooleanEnum.FALSE);
        cashWithdrawOrder.setUpdateBy(cashManualPaymentDto.getAdminName());
        cashWithdrawOrder.setUpdateTime(cashManualPaymentDto.getUpdateTime());
        cashWithdrawOrder.setCreateBy(cashManualPaymentDto.getAdminName());
        cashWithdrawOrder.setCreateTime(cashManualPaymentDto.getUpdateTime());
        cashWithdrawOrder.setInviteTypeEnum(member.getInviteTypeEnum());
        cashWithdrawOrder.setIsDirect(member.getIsDirect());
        cashWithdrawOrder.setChannelId(member.getChannelId());
        cashWithdrawOrder.setMemberTypeEnum(member.getMemberTypeEnum());

        // 判斷是否首次提款
        ReportFirstWithdrawHis reportFirstWithdrawHis = reportFirstWithdrawHisService.findByMemberIdAndMerchantId(member.getId(), member.getMerchantId(), currencyEnum);
        if (reportFirstWithdrawHis == null) {
            cashWithdrawOrder.setFirstWithdrawalEnum(BooleanEnum.TRUE);
        } else {
            cashWithdrawOrder.setFirstWithdrawalEnum(BooleanEnum.FALSE);
        }

        boolean isSave = super.save(cashWithdrawOrder);
        if (!isSave) {
            throw new ApiException(CommonCode.CREATE_ORDER_FAIL);
        }

        log.info("[人工提現] 建立訂單:{}", cashWithdrawOrder);

        return cashWithdrawOrder;
    }

    /**
     * 更新三方訂單號狀態等三方資訊(代付提單)
     */
    @Override
    public void updateThirdInformation(CashWithdrawOrder cashWithdrawOrder,
                                       WithdrawResultDTO withdrawResultDto,
                                       PaymentChannel paymentChannel,
                                       PaymentMerchant paymentMerchant,
                                       String adminName) {
        String thirdNo = withdrawResultDto.getThirdOrderNo();
        Long paymentChannelId = paymentChannel.getId();
        if (StrUtil.isBlank(cashWithdrawOrder.getMessage())) {
            cashWithdrawOrder.setMessage("");
        }

        LambdaUpdateWrapper<CashWithdrawOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .set(StrUtil.isNotBlank(thirdNo), CashWithdrawOrder::getThirdNo, thirdNo)
                .set(CashWithdrawOrder::getPaymentChannelId, paymentChannelId)
                .set(CashWithdrawOrder::getPaymentChannelName, paymentChannel.getName())
                .set(CashWithdrawOrder::getChannelName, paymentMerchant.getChannelName())
                .set(CashWithdrawOrder::getPaymentMerchantId, paymentMerchant.getId())
                .set(CashWithdrawOrder::getPaymentMerchantRemark, paymentMerchant.getRemark())
                .set(CashWithdrawOrder::getMessage, withdrawResultDto.getMessage())
                .set(CashWithdrawOrder::getUpdateTime, LocalDateTime.now())
                .set(CashWithdrawOrder::getUpdateBy, adminName);

        updateWrapper.eq(CashWithdrawOrder::getId, cashWithdrawOrder.getId())
                .eq(CashWithdrawOrder::getMerchantId, cashWithdrawOrder.getMerchantId());

        super.update(updateWrapper);
    }

    @Override
    public void updateOrderToProgressing(CashWithdrawOrder cashWithdrawOrder) {
        LambdaUpdateWrapper<CashWithdrawOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CashWithdrawOrder::getStatusEnum, OrderStatusEnum.PROGRESSING);
        updateWrapper.eq(CashWithdrawOrder::getId, cashWithdrawOrder.getId())
                .eq(CashWithdrawOrder::getMerchantId, cashWithdrawOrder.getMerchantId())
                .in(CashWithdrawOrder::getStatusEnum, OrderStatusEnum.PENDING, OrderStatusEnum.FAILED);
        boolean success = update(updateWrapper);
        Assert.isTrue(success, () -> new ApiException(CommonCode.CASH_WITHDRAW_ORDER_PROGRESSING));
    }

    /**
     * 更新三方訊息(代付提單)
     *
     * @param cashWithdrawOrder payDepositOrder entity
     * @param message           三方訊息
     */
    @Override
    public void updateThirdMessage(CashWithdrawOrder cashWithdrawOrder, String message, String channelName, Long paymentChannelId,
                                   String paymentChannelName, Long paymentMerchantId, String paymentMerchantRemark) {
        if (!Objects.equals(cashWithdrawOrder.getChannelName(), channelName)) {
            cashWithdrawOrder.setChannelName(channelName);
        }
        cashWithdrawOrder.setPaymentChannelId(paymentChannelId);
        cashWithdrawOrder.setPaymentChannelName(paymentChannelName);
        cashWithdrawOrder.setPaymentMerchantId(paymentMerchantId);
        cashWithdrawOrder.setPaymentMerchantRemark(paymentMerchantRemark);
        cashWithdrawOrder.setStatusEnum(OrderStatusEnum.FAILED);
        cashWithdrawOrder.setMessage(message);
        cashWithdrawOrder.setUpdateTime(LocalDateTime.now());

        LambdaQueryWrapper<CashWithdrawOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashWithdrawOrder::getId, cashWithdrawOrder.getId());
        queryWrapper.eq(CashWithdrawOrder::getMerchantId, cashWithdrawOrder.getMerchantId());
        queryWrapper.eq(CashWithdrawOrder::getStatusEnum, OrderStatusEnum.PROGRESSING);
        super.update(cashWithdrawOrder, queryWrapper);
    }

    /**
     * 更新訂單(代付回調)
     *
     * @param cashWithdrawOrder entity
     * @param message           三方回調狀態訊息
     * @param payTime           三方入款時間
     * @param thirdFee          三方手續費
     * @param orderStatusEnum   狀態
     */
    @Override
    public boolean updateOrderStatusOfAuto(CashWithdrawOrder cashWithdrawOrder, String message, LocalDateTime payTime, Long thirdFee, OrderStatusEnum orderStatusEnum) {
        CashWithdrawOrder newCashWithdrawOrder = this.findById(cashWithdrawOrder.getId(), cashWithdrawOrder.getMerchantId());
        if (newCashWithdrawOrder.getStatusEnum() != OrderStatusEnum.PROGRESSING) {
            log.error("代付訂單, 訂單號:{}, 狀態:{}, 訂單狀態非代付中", cashWithdrawOrder.getId(), newCashWithdrawOrder.getStatusEnum());
            return false;
        }

        if (newCashWithdrawOrder.getStatusEnum() == cashWithdrawOrder.getStatusEnum()) {
            cashWithdrawOrder.setStatusEnum(orderStatusEnum);
            cashWithdrawOrder.setUpdateTime(LocalDateTime.now());
            if (orderStatusEnum == OrderStatusEnum.FAILED) {
                cashWithdrawOrder.setMessage(message);
            } else {
                cashWithdrawOrder.setPayTime(payTime);
                cashWithdrawOrder.setThirdFee(thirdFee);
                cashWithdrawOrder.setLockEnum(BooleanEnum.FALSE);
                cashWithdrawOrder.setLocker("");
            }

            LambdaQueryWrapper<CashWithdrawOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CashWithdrawOrder::getId, cashWithdrawOrder.getId());
            queryWrapper.eq(CashWithdrawOrder::getMerchantId, cashWithdrawOrder.getMerchantId());
            queryWrapper.eq(CashWithdrawOrder::getStatusEnum, OrderStatusEnum.PROGRESSING);
            boolean isUpdate = super.update(cashWithdrawOrder, queryWrapper);
            if (!isUpdate) {
                log.error("代付訂單, 訂單號:{}, 更新訂單失敗, 原訂單狀態:{}, 待更新訂單狀態:{}", cashWithdrawOrder.getId(), cashWithdrawOrder.getStatusEnum().name(), orderStatusEnum.name());
                return false;
            }
        }
        return true;
    }

    /**
     * 後台增加備註
     *
     * @param orderUpdateRemarkDto dto
     */
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.CASH_WITHDRAW_ORDER, subType = LogSubTypeConstants.UPDATE,
            success = "提现审核 添加备注, 订单号:{{#orderUpdateRemarkDto.id}}, 备注:{{#orderUpdateRemarkDto.remark}}")
    public boolean updateRemark(OrderUpdateRemarkDTO orderUpdateRemarkDto) {
        LambdaQueryWrapper<CashWithdrawOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashWithdrawOrder::getId, orderUpdateRemarkDto.getId());
        queryWrapper.eq(CashWithdrawOrder::getMerchantId, orderUpdateRemarkDto.getMerchantId());
        CashWithdrawOrder cashWithdrawOrder = super.getOne(queryWrapper);
        if (cashWithdrawOrder == null) {
            throw new ApiException(CommonCode.BET_ORDER_NOT_EXISTS);
        }

        cashWithdrawOrder.setRemark(orderUpdateRemarkDto.getRemark());
        cashWithdrawOrder.setUpdateTime(orderUpdateRemarkDto.getUpdateTime());
        cashWithdrawOrder.setUpdateBy(orderUpdateRemarkDto.getAdminName());

        boolean isUpdate = super.update(cashWithdrawOrder, queryWrapper);
        if (!isUpdate) {
            throw new ApiException(CommonCode.FAILED);
        }

        return true;
    }

    /**
     * 更新訂單(後台手動更新)
     *
     * @param orderUpdateStatusDto dto
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.CASH_WITHDRAW_ORDER, subType = LogSubTypeConstants.UPDATE,
            success = "提现审核 {{#title}}, uid:{{#memberId}}, 订单号:{{#orderUpdateStatusDto.id}}")
    public boolean updateOrderStatusOfManual(OrderUpdateStatusDTO orderUpdateStatusDto) {
        OrderStatusEnum orderStatusEnum = orderUpdateStatusDto.getOrderStatusEnum();

        LambdaQueryWrapper<CashWithdrawOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashWithdrawOrder::getId, orderUpdateStatusDto.getId());
        queryWrapper.eq(CashWithdrawOrder::getMerchantId, orderUpdateStatusDto.getMerchantId());
        queryWrapper.eq(CashWithdrawOrder::getCurrencyEnum, orderUpdateStatusDto.getCurrencyEnum());

        CashWithdrawOrder cashWithdrawOrder = super.getOne(queryWrapper);

        if (cashWithdrawOrder == null) {
            throw new ApiException(CommonCode.BET_ORDER_NOT_EXISTS);
        }

        if (cashWithdrawOrder.getLockEnum() == BooleanEnum.FALSE) {
            throw new ApiException(CommonCode.LOCK_STATUS_ERROR);
        }

        if (!cashWithdrawOrder.getLocker().equals(orderUpdateStatusDto.getAdminName())) {
            throw new ApiException(CommonCode.LOCKER_ERROR);
        }

        if (cashWithdrawOrder.getStatusEnum() != OrderStatusEnum.PENDING && cashWithdrawOrder.getStatusEnum() != OrderStatusEnum.FAILED && cashWithdrawOrder.getStatusEnum() != OrderStatusEnum.PROGRESSING) {
            throw new ApiException(CommonCode.STATUS_ERROR);
        }

        if (StringUtils.isNotBlank(orderUpdateStatusDto.getRemark())) {
            cashWithdrawOrder.setRemark(orderUpdateStatusDto.getRemark());
        }

        if (orderStatusEnum == OrderStatusEnum.SUCCESS) {
            cashWithdrawOrder.setPayTime(orderUpdateStatusDto.getPayTime());
        }

        if (cashWithdrawOrder.getStatusEnum() != OrderStatusEnum.PROGRESSING && cashWithdrawOrder.getStatusEnum() != OrderStatusEnum.FAILED) {
            cashWithdrawOrder.setChannelName(OrderChannelNameEnum.MANUAL_WITHDRAW.getText());
        }

        cashWithdrawOrder.setStatusEnum(orderStatusEnum);
        cashWithdrawOrder.setPlatformTypeEnum(PlatformTypeEnum.MANUAL);
        cashWithdrawOrder.setWithdrawMethodEnum(PaymentMethodEnum.MANUAL);
        cashWithdrawOrder.setUpdateBy(orderUpdateStatusDto.getAdminName());
        cashWithdrawOrder.setUpdateTime(orderUpdateStatusDto.getPayTime());

        boolean isUpdate = super.update(cashWithdrawOrder, queryWrapper);
        if (!isUpdate) {
            throw new ApiException(CommonCode.FAILED);
        }

        // 取消出款要退回用戶餘額
        if (orderStatusEnum == OrderStatusEnum.CANCEL) {
            paymentResultService.memberWalletOperate(cashWithdrawOrder.getMemberId(), cashWithdrawOrder.getMemberName(), cashWithdrawOrder.getId(),
                    cashWithdrawOrder.getExchangeRealMoney(), orderUpdateStatusDto.getMerchantId(), cashWithdrawOrder.getUpdateTime(), SubTradeTypeEnum.WITHDRAW_CANCEL , null,null);
        }

        // 成功要紀錄首提紀錄
        if (orderStatusEnum == OrderStatusEnum.SUCCESS) {
            paymentResultService.withdrawHis(cashWithdrawOrder, orderUpdateStatusDto.getPayTime());

            // 增加每日累積金額, 次數
            paymentMerchantService.addDailyDepositLimit(cashWithdrawOrder.getPaymentMerchantId(), cashWithdrawOrder.getOrderMoney());
        }

        // 取消出款, 站內信判斷
        if (orderStatusEnum == OrderStatusEnum.CANCEL && orderUpdateStatusDto.isSend() && StringUtils.isNotBlank(orderUpdateStatusDto.getRemark()) && StringUtils.isNotBlank(orderUpdateStatusDto.getTitle())) {
            Map<Long, Long> orderNoAndMemberIdMap = new HashMap<>();
            orderNoAndMemberIdMap.put(cashWithdrawOrder.getId(), cashWithdrawOrder.getMemberId());
            commonMessageMemberService.sendMessageByPaymentOrderCancel(orderNoAndMemberIdMap, orderUpdateStatusDto.getAdminName(),
                    orderUpdateStatusDto.getMerchantId(), orderUpdateStatusDto.getCurrencyEnum(), orderUpdateStatusDto.getRemark(), orderUpdateStatusDto.getTitle());
        }

        String title = "取消出款";
        if (orderStatusEnum == OrderStatusEnum.SUCCESS) {
            title = "人工出款";
        }
        LogRecordContext.putVariable("title", title);
        LogRecordContext.putVariable("memberId", cashWithdrawOrder.getMemberId());
        return true;
    }

    /**
     * 鎖定/解鎖訂單
     *
     * @param orderUpdateLockDto dto
     * @return boolean
     */
    @Override
    public boolean lock(OrderUpdateLockDTO orderUpdateLockDto) {
        LambdaQueryWrapper<CashWithdrawOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashWithdrawOrder::getId, orderUpdateLockDto.getId());
        queryWrapper.eq(CashWithdrawOrder::getMerchantId, orderUpdateLockDto.getMerchantId());
        CashWithdrawOrder cashWithdrawOrder = super.getOne(queryWrapper);
        if (cashWithdrawOrder == null) {
            throw new ApiException(CommonCode.BET_ORDER_NOT_EXISTS);
        }

        BooleanEnum isLock = cashWithdrawOrder.getLockEnum();
        String locker = cashWithdrawOrder.getLocker();

        BooleanEnum lockEnum = orderUpdateLockDto.getLockEnum();
        if (isLock == BooleanEnum.FALSE) {
            if (lockEnum == BooleanEnum.FALSE) {
                throw new ApiException(CommonCode.LOCK_STATUS_ERROR);
            }

            cashWithdrawOrder.setLockEnum(BooleanEnum.TRUE);
            cashWithdrawOrder.setLocker(orderUpdateLockDto.getAdminName());
        } else {
            if (lockEnum == BooleanEnum.TRUE) {
                throw new ApiException(CommonCode.LOCK_STATUS_ERROR);
            }

            if (lockEnum == BooleanEnum.FALSE && !locker.equals(orderUpdateLockDto.getAdminName())) {
                throw new ApiException(CommonCode.LOCKER_ERROR);
            }

            cashWithdrawOrder.setLockEnum(BooleanEnum.FALSE);
            cashWithdrawOrder.setLocker("");
        }

        return super.update(cashWithdrawOrder, queryWrapper);
    }

    /**
     * 最大權限解鎖
     *
     * @param orderUpdateUnLockDto dto
     * @return boolean
     */
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.CASH_WITHDRAW_ORDER, subType = LogSubTypeConstants.UPDATE,
            success = "提现审核 最大权限解锁, uid:{{#memberId}}, 订单号:{{#orderUpdateUnLockDto.id}}")
    public boolean unlock(OrderUpdateUnLockDTO orderUpdateUnLockDto) {
        LambdaQueryWrapper<CashWithdrawOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashWithdrawOrder::getId, orderUpdateUnLockDto.getId());
        queryWrapper.eq(CashWithdrawOrder::getMerchantId, orderUpdateUnLockDto.getMerchantId());
        CashWithdrawOrder cashWithdrawOrder = super.getOne(queryWrapper);
        if (cashWithdrawOrder == null) {
            throw new ApiException(CommonCode.BET_ORDER_NOT_EXISTS);
        }

        BooleanEnum isLock = cashWithdrawOrder.getLockEnum();
        if (isLock == BooleanEnum.FALSE) {
            throw new ApiException(CommonCode.LOCK_STATUS_ERROR);
        }

        cashWithdrawOrder.setLockEnum(BooleanEnum.FALSE);
        cashWithdrawOrder.setLocker("");

        LogRecordContext.putVariable("memberId", cashWithdrawOrder.getMemberId());

        return super.update(cashWithdrawOrder, queryWrapper);
    }

    @Override
    public List<CashWithdrawOrder> getAggregateList(Long merchantId, LocalDateTime startTime, LocalDateTime endTime) {

        QueryWrapper<CashWithdrawOrder> cashMemberWalletLogHisQueryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<CashWithdrawOrder> cashMemberWalletLogHisLambdaQueryWrapper = cashMemberWalletLogHisQueryWrapper
                .select(SqlUtil.selectCount(CashWithdrawOrder::getWithdrawCount),
                        SqlUtil.selectSum(CashWithdrawOrder::getExchangeOrderMoney),
                        SqlUtil.selectDate(CashWithdrawOrder::getPayTime),
                        SqlUtil.selectMax(CashWithdrawOrder::getMerchantId),
                        SqlUtil.selectMax(CashWithdrawOrder::getMemberId),
                        SqlUtil.selectMax(CashWithdrawOrder::getMemberName),
                        SqlUtil.selectMax(CashWithdrawOrder::getCurrencyEnum),
                        SqlUtil.selectMax(CashWithdrawOrder::getIsDirect),
                        SqlUtil.selectMax(CashWithdrawOrder::getChannelId),
                        SqlUtil.selectMax(CashWithdrawOrder::getInviteTypeEnum),
                        SqlUtil.selectMax(CashWithdrawOrder::getMemberTypeEnum)
                ).lambda()
                .eq(CashWithdrawOrder::getMerchantId, merchantId)
                .gt(CashWithdrawOrder::getPayTime, startTime)
                .le(CashWithdrawOrder::getPayTime, endTime)
                .groupBy(CashWithdrawOrder::getMemberId,
                        CashWithdrawOrder::getPayTime);

        return this.list(cashMemberWalletLogHisLambdaQueryWrapper);
    }

    /**
     * 提現概要
     *
     * @param orderStatisticsSearchDto dto
     * @param currencyEnum             currency enum
     * @return list dto
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<OrderStatisticsDTO> withdrawStatistics(OrderStatisticsSearchDTO orderStatisticsSearchDto, CurrencyEnum currencyEnum) {
        List<OrderStatisticsDTO> orderStatisticsDtoList = new ArrayList<>();
        orderStatisticsDtoList.add(OrderStatisticsDTO.builder()
                .channelName(OrderChannelNameEnum.MANUAL_WITHDRAW.getText())
                .totalExchangeRealMoney(Constants.ZERO_LONG)
                .withdrawCount(Constants.ZERO_LONG)
                .withdrawPopulation(Constants.ZERO_LONG)
                .build());

        List<PaymentChannel> paymentChannelList = paymentChannelService.findByEnableEnum(EnableEnum.TRUE);
        List<Long> isWithdrawPaymentChannelIdList = paymentChannelList.stream()
                .filter(s -> s.getWithdrawEnableEnum() == EnableEnum.TRUE)
                .map(PaymentChannel::getId).collect(Collectors.toList());

        List<PaymentMerchant> paymentMerchantList = paymentMerchantService.findByPaymentChannelIds(isWithdrawPaymentChannelIdList, orderStatisticsSearchDto.getMerchantId(), currencyEnum);
        if (CollectionUtil.isEmpty(paymentMerchantList)) {
            return orderStatisticsDtoList;
        }

        paymentMerchantList.forEach(paymentMerchant ->
                orderStatisticsDtoList.add(OrderStatisticsDTO.builder()
                        .channelName(paymentMerchant.getChannelName())
                        .totalExchangeRealMoney(Constants.ZERO_LONG)
                        .withdrawCount(Constants.ZERO_LONG)
                        .withdrawPopulation(Constants.ZERO_LONG)
                        .build()));


        QueryWrapper<CashWithdrawOrder> cashWithdrawOrderQueryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<CashWithdrawOrder> queryWrapper = cashWithdrawOrderQueryWrapper
                .select(SqlUtil.select(CashWithdrawOrder::getChannelName),
                        SqlUtil.selectCount(CashWithdrawOrder::getWithdrawCount),
                        SqlUtil.selectSum(CashWithdrawOrder::getExchangeRealMoney, CashWithdrawOrder::getTotalExchangeRealMoney),
                        SqlUtil.selectCountDistinct(CashWithdrawOrder::getMemberId, CashWithdrawOrder::getWithdrawPopulation)
                ).lambda()
                .eq(CashWithdrawOrder::getCurrencyEnum, currencyEnum)
                .eq(CashWithdrawOrder::getMerchantId, orderStatisticsSearchDto.getMerchantId())
                .eq(CashWithdrawOrder::getStatusEnum, OrderStatusEnum.SUCCESS)
                .eq(CashWithdrawOrder::getMemberTypeEnum, MemberTypeEnum.FORMAL_MEMBER)
                .eq(CollectionUtil.isNotEmpty(orderStatisticsSearchDto.getChannelIdSet()), CashWithdrawOrder::getIsDirect,BooleanEnum.TRUE)
                .in(CollectionUtil.isNotEmpty(orderStatisticsSearchDto.getChannelIdSet()), CashWithdrawOrder::getChannelId, orderStatisticsSearchDto.getChannelIdSet())
                .between(CashWithdrawOrder::getUpdateTime, orderStatisticsSearchDto.getStartTime(), orderStatisticsSearchDto.getEndTime())
                .groupBy(CashWithdrawOrder::getChannelName);

        Long highMemberId = orderStatisticsSearchDto.getHighMemberId();
        if (highMemberId != null) {
            List<Long> memberIds = new ArrayList<>();
            memberIds.add(highMemberId);
            List<Long> memberIdList = memberProxyService.getMemberIdByHighMemberIdAndLevel(orderStatisticsSearchDto.getHighMemberId(), orderStatisticsSearchDto.getMerchantId(), Constants.ONE_LONG);
            if (CollectionUtil.isNotEmpty(memberIdList)) {
                memberIds.addAll(memberIdList);
            }
            queryWrapper.in(CashWithdrawOrder::getMemberId, memberIds);
        }

        List<CashWithdrawOrder> cashWithdrawOrderList = super.list(queryWrapper);
        if (CollectionUtil.isEmpty(cashWithdrawOrderList)) {
            return orderStatisticsDtoList;
        }

        Map<String, OrderStatisticsDTO> orderStatisticsDtoMap =
                BeanUtil.copyToList(cashWithdrawOrderList, OrderStatisticsDTO.class)
                        .stream().collect(Collectors.toMap(OrderStatisticsDTO::getChannelName, Function.identity()));

        for (OrderStatisticsDTO orderStatisticsDto : orderStatisticsDtoList) {
            if (orderStatisticsDtoMap.get(orderStatisticsDto.getChannelName()) != null) {
                orderStatisticsDto.setTotalExchangeRealMoney(orderStatisticsDtoMap.get(orderStatisticsDto.getChannelName()).getTotalExchangeRealMoney());
                orderStatisticsDto.setWithdrawCount(orderStatisticsDtoMap.get(orderStatisticsDto.getChannelName()).getWithdrawCount());
                orderStatisticsDto.setWithdrawPopulation(orderStatisticsDtoMap.get(orderStatisticsDto.getChannelName()).getWithdrawPopulation());
            }
        }

        return orderStatisticsDtoList;
    }

    /**
     * 提現狀態概要
     *
     * @param orderStatisticsSearchDto dto
     * @param currencyEnum             currency enum
     * @return list dto
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<OrderStatusStatisticsDTO> withdrawStatusStatistics(OrderStatisticsSearchDTO orderStatisticsSearchDto, CurrencyEnum currencyEnum) {
        List<OrderStatusStatisticsDTO> orderStatusStatisticsDtoList = new ArrayList<>();
        List<OrderStatusEnum> orderStatusEnumList = Arrays.stream(OrderStatusEnum.values()).collect(Collectors.toList());
        orderStatusEnumList.forEach(orderStatusEnum -> {
            OrderStatusStatisticsDTO orderStatusStatisticsDto = new OrderStatusStatisticsDTO();
            orderStatusStatisticsDto.setStatusEnum(orderStatusEnum);
            orderStatusStatisticsDto.setTotalExchangeRealMoney(Constants.ZERO_LONG);
            orderStatusStatisticsDto.setWithdrawCount(Constants.ZERO_LONG);
            orderStatusStatisticsDto.setWithdrawPopulation(Constants.ZERO_LONG);
            orderStatusStatisticsDtoList.add(orderStatusStatisticsDto);
        });

        QueryWrapper<CashWithdrawOrder> cashWithdrawOrderQueryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<CashWithdrawOrder> queryWrapper = cashWithdrawOrderQueryWrapper
                .select(SqlUtil.select(CashWithdrawOrder::getStatusEnum),
                        SqlUtil.selectCount(CashWithdrawOrder::getWithdrawCount),
                        SqlUtil.selectSum(CashWithdrawOrder::getExchangeRealMoney, CashWithdrawOrder::getTotalExchangeRealMoney),
                        SqlUtil.selectCountDistinct(CashWithdrawOrder::getMemberId, CashWithdrawOrder::getWithdrawPopulation)
                ).lambda()
                .eq(CashWithdrawOrder::getCurrencyEnum, currencyEnum)
                .eq(CashWithdrawOrder::getMerchantId, orderStatisticsSearchDto.getMerchantId())
                .eq(CashWithdrawOrder::getMemberTypeEnum, MemberTypeEnum.FORMAL_MEMBER)
                .eq(CollectionUtil.isNotEmpty(orderStatisticsSearchDto.getChannelIdSet()), CashWithdrawOrder::getIsDirect, BooleanEnum.TRUE)
                .in(CollectionUtil.isNotEmpty(orderStatisticsSearchDto.getChannelIdSet()), CashWithdrawOrder::getChannelId, orderStatisticsSearchDto.getChannelIdSet())
                .between(CashWithdrawOrder::getUpdateTime, orderStatisticsSearchDto.getStartTime(), orderStatisticsSearchDto.getEndTime())
                .groupBy(CashWithdrawOrder::getStatusEnum);

        Long highMemberId = orderStatisticsSearchDto.getHighMemberId();
        if (highMemberId != null) {
            List<Long> memberIds = new ArrayList<>();
            memberIds.add(highMemberId);
            List<Long> memberIdList = memberProxyService.getMemberIdByHighMemberIdAndLevel(orderStatisticsSearchDto.getHighMemberId(), orderStatisticsSearchDto.getMerchantId(), Constants.ONE_LONG);
            if (CollectionUtil.isNotEmpty(memberIdList)) {
                memberIds.addAll(memberIdList);
            }
            queryWrapper.in(CashWithdrawOrder::getMemberId, memberIds);
        }

        List<CashWithdrawOrder> cashWithdrawOrderList = super.list(queryWrapper);
        if (CollectionUtil.isEmpty(cashWithdrawOrderList)) {
            return orderStatusStatisticsDtoList;
        }

        Map<OrderStatusEnum, OrderStatusStatisticsDTO> orderStatusSummaryDtoMap =
                BeanUtil.copyToList(cashWithdrawOrderList, OrderStatusStatisticsDTO.class)
                        .stream().collect(Collectors.toMap(OrderStatusStatisticsDTO::getStatusEnum, Function.identity()));

        for (OrderStatusStatisticsDTO orderStatusStatisticsDto : orderStatusStatisticsDtoList) {
            if (orderStatusSummaryDtoMap.get(orderStatusStatisticsDto.getStatusEnum()) != null) {
                orderStatusStatisticsDto.setTotalExchangeRealMoney(orderStatusSummaryDtoMap.get(orderStatusStatisticsDto.getStatusEnum()).getTotalExchangeRealMoney());
                orderStatusStatisticsDto.setWithdrawCount(orderStatusSummaryDtoMap.get(orderStatusStatisticsDto.getStatusEnum()).getWithdrawCount());
                orderStatusStatisticsDto.setWithdrawPopulation(orderStatusSummaryDtoMap.get(orderStatusStatisticsDto.getStatusEnum()).getWithdrawPopulation());
            }
        }

        return orderStatusStatisticsDtoList;
    }

    private void checkVipWithdrawLimit(Member member, CashWithdrawOrder cashWithdrawOrder) {
        Activity vipActivity = activityService.getEnableOne(cashWithdrawOrder.getMerchantId(), cashWithdrawOrder.getCurrencyEnum(), ActivityTypeEnum.VIP);
        if (vipActivity == null) {
            log.info("商戶ID={} 未配置或开启每日提现限制", cashWithdrawOrder.getMerchantId());
            return;
        }

        ActivityVipParam activityVipParam = vipActivity.activityParamToBean(ActivityVipParam.class);
        if (!Objects.equals(EnableEnum.TRUE, activityVipParam.getIsWithdrawLimit())) {
            return;
        }
        Long memberTotalWithdrawalRealAmount = getMemberTotalWithdrawalRealAmount(cashWithdrawOrder);
        Long withdrawLimitAmount = activityVipParam.getActivityVipConfigParamList().get(member.getVipLevel()).getWithdrawLimitAmount();
        if (memberTotalWithdrawalRealAmount + cashWithdrawOrder.getExchangeRealMoney() > withdrawLimitAmount) {
            throw new ApiException(CommonCode.CASH_WITHDRAW_ORDER_AMOUNT_EXCEEDS_LIMIT);
        }
    }

    private Long getMemberTotalWithdrawalRealAmount(CashWithdrawOrder cashWithdrawOrder) {
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<CashWithdrawOrder> wrapper = new QueryWrapper<CashWithdrawOrder>()
                .select(SqlUtil.selectSum(CashWithdrawOrder::getExchangeRealMoney))
                .lambda()
                .eq(CashWithdrawOrder::getMerchantId, cashWithdrawOrder.getMerchantId())
                .eq(CashWithdrawOrder::getMemberId, cashWithdrawOrder.getMemberId())
                .eq(CashWithdrawOrder::getStatusEnum, OrderStatusEnum.SUCCESS)
                .between(CashWithdrawOrder::getCreateTime, LocalDateTimeUtil.beginOfDay(now), LocalDateTimeUtil.endOfDay(now));
        return Optional.ofNullable(getOne(wrapper))
                .map(CashWithdrawOrder::getExchangeRealMoney)
                .orElse(0L);
    }


    private void saveCashWithdrawOrder(CashWithdrawOrder cashWithdrawOrder) {
        operateWithdrawAmount(cashWithdrawOrder);
        boolean isSave = super.save(cashWithdrawOrder);
        if (!isSave) {
            throw new ApiException(CommonCode.CREATE_ORDER_FAIL);
        }
        log.info("[{}] 建立訂單:{}", PayRequestEnum.WITHDRAW.getText(), cashWithdrawOrder);
    }

    @Override
    public ApiResult<?> exportCashWithdrawOrder(OrderSearchDTO orderSearchDto, Long merchantId, CurrencyEnum currencyEnum, String language, String createBy) {
        CashWithdrawExportDTO exportParam = new CashWithdrawExportDTO();
        buildParamMap(orderSearchDto, exportParam, merchantId, currencyEnum);
        AsyncExportParam asyncExportParam = new AsyncExportParam();
        asyncExportParam.setMerchantId(merchantId);
        asyncExportParam.setLanguage(language);
        asyncExportParam.setCreateBy(createBy);
        asyncExportParam.setExportType(ExportTypeEnum.CASH_WITHDRAW);
        asyncExportParam.setFileName(ExportTypeEnum.CASH_WITHDRAW.getDesc() + "_" + DateUtil.today() + ".xlsx");
        log.info("exportParam..{},AsyncExportParam...{},map...{}", orderSearchDto, asyncExportParam, JSONUtil.toJsonStr(exportParam));
        CashWithdrawOrderExportExcelStrategy strategy =
                new CashWithdrawOrderExportExcelStrategy(ExcelExportParamEnum.CASH_WITHDRAW.getMapperMethod(),
                        ExcelExportParamEnum.CASH_WITHDRAW.getNextSearchField(), exportParam, exportExcelConfig.getLimit(), language);
        exportExcelService.asyncExportExcel(asyncExportParam, strategy, orderSearchDto.getIsAll());
        return ApiResult.success();
    }

    private void buildParamMap(OrderSearchDTO orderSearchDto, CashWithdrawExportDTO exportParam, Long merchantId, CurrencyEnum currencyEnum) {
        exportParam.setMerchantId(merchantId);
        exportParam.setCurrencyEnum(currencyEnum);
        if (orderSearchDto.getMemberId() != null) {
            exportParam.setMemberId(orderSearchDto.getMemberId());
        }

        if (StringUtils.isNotBlank(orderSearchDto.getChannelName())) {
            exportParam.setChannelName(orderSearchDto.getChannelName());
        }

        if (orderSearchDto.getStatusEnum() != null) {
            exportParam.setStatusEnum(orderSearchDto.getStatusEnum());
        }

        if (CollectionUtil.isNotEmpty(orderSearchDto.getStatusEnums())) {
            exportParam.setStatusEnums(orderSearchDto.getStatusEnums());
        }

        if (orderSearchDto.getPlatformEnumList() != null) {
            exportParam.setPlatformEnumList(orderSearchDto.getPlatformEnumList());
        }

        if (orderSearchDto.getPlatformTypeEnum() != null) {
            exportParam.setPlatformTypeEnum(orderSearchDto.getPlatformTypeEnum());
        }

        if (orderSearchDto.getMinAmount() != null && orderSearchDto.getMaxAmount() != null) {
            exportParam.setMinAmount(orderSearchDto.getMinAmount());
            exportParam.setMaxAmount(orderSearchDto.getMaxAmount());
        }

        if (orderSearchDto.getStartTime() != null && orderSearchDto.getEndTime() != null) {
            exportParam.setStartTime(orderSearchDto.getStartTime());
            exportParam.setEndTime(orderSearchDto.getEndTime());
        }
        if (orderSearchDto.getStartPayTime() != null && orderSearchDto.getEndPayTime() != null) {
            exportParam.setStartPayTime(orderSearchDto.getStartPayTime());
            exportParam.setEndPayTime(orderSearchDto.getEndPayTime());
        }
        if (orderSearchDto.getStartUpdateTime() != null && orderSearchDto.getEndUpdateTime() != null) {
            exportParam.setStartUpdateTime(orderSearchDto.getStartUpdateTime());
            exportParam.setEndUpdateTime(orderSearchDto.getEndUpdateTime());
        }
        if (CollectionUtil.isNotEmpty(orderSearchDto.getChannelIdSet())) {
            exportParam.setChannelIdSet(orderSearchDto.getChannelIdSet());
        }
        exportParam.setAdminId(orderSearchDto.getAdminId());
        exportParam.setRoleId(orderSearchDto.getRoleId());
        if (orderSearchDto.getIsAll()) {
            //导出全部
            exportParam.setLimit(Long.valueOf(exportExcelConfig.getLimit()));
            exportParam.setId(0);
        } else {
            //导出当前页
            exportParam.setLimit(Objects.nonNull(orderSearchDto.getSize()) ? orderSearchDto.getSize() : exportExcelConfig.getLimit());
        }

    }

    @Override
    public Tup3<Long, Long, Boolean> getPendingOrderCountAndLastOrderId(Long merchantId, CurrencyEnum currencyEnum, List<PlatformTypeEnum> platformTypeEnums, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<CashWithdrawOrder> orderLambdaQueryWrapper = buildPendingOrderSearchWrapper(
                new QueryWrapper<CashWithdrawOrder>().select(SqlUtil.selectMax(CashWithdrawOrder::getId), SqlUtil.selectCount(CashWithdrawOrder::getWithdrawCount)),
                merchantId, currencyEnum, platformTypeEnums, startTime, endTime);
        CashWithdrawOrder countAndMaxId = this.baseMapper.selectOne(orderLambdaQueryWrapper);

        //KG-1780 要求未锁定的给提示音
        Long lockCount = this.baseMapper.selectCount(
                buildPendingOrderSearchWrapper(
                        new QueryWrapper<>(),
                        merchantId, currencyEnum, platformTypeEnums, startTime, endTime
                ).eq(CashWithdrawOrder::getLockEnum, BooleanEnum.FALSE)
        );

        return Tup3.of(countAndMaxId == null ? 0 : countAndMaxId.getWithdrawCount(),
                countAndMaxId == null ? null : countAndMaxId.getId(),
                lockCount != null && lockCount > 0
                );
    }

    public LambdaQueryWrapper<CashWithdrawOrder> buildPendingOrderSearchWrapper(QueryWrapper<CashWithdrawOrder> wrapper, Long merchantId, CurrencyEnum currencyEnum, List<PlatformTypeEnum> platformTypeEnums, LocalDateTime startTime, LocalDateTime endTime) {
        return wrapper.lambda()
                .eq(CashWithdrawOrder::getMerchantId, merchantId)
                .eq(CashWithdrawOrder::getCurrencyEnum, currencyEnum)
                .in(CashWithdrawOrder::getStatusEnum, Arrays.asList(OrderStatusEnum.PENDING, OrderStatusEnum.PROGRESSING))
                .in(CollectionUtil.isNotEmpty(platformTypeEnums), CashWithdrawOrder::getPlatformTypeEnum, platformTypeEnums)
                .between(CashWithdrawOrder::getCreateTime, startTime, endTime);
    }

    /**
     * 查詢指定商戶中在指定時間前處於 PROGRESSING 狀態的出款訂單
     *
     * @param merchantId 商戶ID
     * @param pollingTime 輪詢時間門檻
     * @return 訂單列表
     */
    @Override
    public List<CashWithdrawOrder> getWithdrawOrderInProgressing(Long merchantId, LocalDateTime pollingTime) {
        LambdaQueryWrapper<CashWithdrawOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashWithdrawOrder::getMerchantId, merchantId)
                .eq(CashWithdrawOrder::getStatusEnum, OrderStatusEnum.PROGRESSING)
                .ge(CashWithdrawOrder::getUpdateTime, pollingTime);

        return this.list(queryWrapper);
    }
}
