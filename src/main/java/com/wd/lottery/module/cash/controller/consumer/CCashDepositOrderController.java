package com.wd.lottery.module.cash.controller.consumer;

import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.util.MemberTokenInfoUtil;
import com.wd.lottery.module.cash.dto.OrderSearchDTO;
import com.wd.lottery.module.cash.service.CashDepositOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "訂單管理")
@RestController
@RequestMapping(value = "${consumer-path}/${module-path.cash}/cashDepositOrder", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class CCashDepositOrderController {

    private final CashDepositOrderService cashDepositOrderService;

    @Operation(summary = "充值訂單-多條件查詢")
    @GetMapping("/getList")
    public ApiResult<?> depositFindByCondition(@ParameterObject OrderSearchDTO orderSearchDto) {
        return ApiResult.success(cashDepositOrderService.searchByMerchantId(orderSearchDto,
                MemberTokenInfoUtil.getMerchantId(), MemberTokenInfoUtil.getMemberId(), MemberTokenInfoUtil.getCurrencyEnum()));
    }

}
