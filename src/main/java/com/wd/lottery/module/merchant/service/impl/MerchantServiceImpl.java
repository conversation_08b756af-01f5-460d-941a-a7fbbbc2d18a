package com.wd.lottery.module.merchant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wd.lottery.GrapeApplication;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.*;
import com.wd.lottery.module.common.constants.CommonDictKeyEnum;
import com.wd.lottery.module.common.constants.LogSubTypeEnum;
import com.wd.lottery.module.common.constants.LogTypeEnum;
import com.wd.lottery.module.common.dto.CommonOperationLogDTO;
import com.wd.lottery.module.common.service.AwsS3Service;
import com.wd.lottery.module.common.vo.IPAccessDenyRespVO;
import com.wd.lottery.module.common.vo.MaitainRespVO;
import com.wd.lottery.module.member.service.MemberGroupService;
import com.wd.lottery.module.merchant.consts.MerchantConstants;
import com.wd.lottery.module.merchant.dto.*;
import com.wd.lottery.module.merchant.entity.Merchant;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.entity.MerchantKey;
import com.wd.lottery.module.merchant.mapper.MerchantMapper;
import com.wd.lottery.module.merchant.param.*;
import com.wd.lottery.module.merchant.service.MerchantAdminService;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.merchant.service.MerchantKeyService;
import com.wd.lottery.module.merchant.service.MerchantService;
import com.wd.lottery.module.third.service.ThirdSiteBalanceInnerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.wd.lottery.module.common.constants.CommonDictKeyEnum.COMMON_LANGUAGE;

/**
 * Description:
 *
 * <p>
 * Create on 2023/10/31.
 * <p>
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class MerchantServiceImpl extends ServiceImpl<MerchantMapper, Merchant>
        implements MerchantService {

    private static final String PATH_SPLIT = "/";
    @Resource
    private MerchantAdminService adminService;
    @Resource
    private MerchantKeyService keyService;
    @Resource
    private ThirdSiteBalanceInnerService thirdSiteBalanceInnerService;
    @Resource
    private MemberGroupService memberGroupService;
    @Resource
    private AmqpTemplate amqpTemplate;
    @Resource
    private MerchantConfigService merchantConfigService;
    @Resource
    private AwsS3Service awsS3Service;


    /**
     * 分页查询商户信息
     */
    @Override
    public Page<MerchantDTO> getMerchantPage(MerchantPageParam param) {

        // 非admin只能看自己和下级商户数据
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        Merchant merchant = this.getById(merchantId);
        if (Objects.isNull(merchant) || merchant.getEnableEnum() == EnableEnum.FALSE) {
            throw new ApiException(CommonCode.MERCHANT_ILLEGAL_STATUS);
        }

        LambdaQueryWrapper<Merchant> queryWrapper = new LambdaQueryWrapper<Merchant>()
                .eq(Objects.nonNull(param.getEnableEnum()), Merchant::getEnableEnum, param.getEnableEnum())
                .like(Objects.nonNull(param.getCode()), Merchant::getCode, param.getCode())
                .likeRight(Merchant::getPath, merchant.getPath())
                .orderByDesc(Merchant::getCreateTime);

        Page<Merchant> page = this.page(new Page<>(param.getCurrent(), param.getSize()), queryWrapper);
        Page<MerchantDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(page, dtoPage, "records");

        List<MerchantDTO> dtoList = convert2DTO(page.getRecords());
        dtoPage.setRecords(dtoList);

        return dtoPage;
    }

    private List<MerchantDTO> convert2DTO(List<Merchant> records) {
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        Set<Long> pidSet = records.stream().map(Merchant::getPid).collect(Collectors.toSet());
        Map<Long, String> parentMap = this.lambdaQuery()
                .select(Merchant::getId, Merchant::getCode)
                .in(Merchant::getId, pidSet)
                .list()
                .stream()
                .collect(Collectors.toMap(Merchant::getId, Merchant::getCode, (v1, v2) -> v1));
        return records.stream().map(i -> {
            MerchantDTO dto = new MerchantDTO();
            BeanUtils.copyProperties(i, dto);
            dto.setPcode(parentMap.get(i.getPid()));
            String currencyJson = i.getCurrencyEnumListJson();
            // 币种去重，处理脏数据
            List<String> collect = filterInvalidCurrencies(JacksonUtil.toJavaObject(currencyJson, new TypeReference<List<String>>() {}))
                    .stream()
                    .distinct()
                    .collect(Collectors.toList());
            dto.setCurrency(JacksonUtil.toJSONString(collect));
            dto.setEnable(i.getEnableEnum().getCode());
            String businessIpJson = i.getBusinessWhitelistIpJson();
            if (Objects.nonNull(businessIpJson)) {
                dto.setBusinessWhitelist(JSONUtil.toList(businessIpJson, String.class));
            }
            String externalIpJson = i.getExternalWhitelistIpJson();
            if (Objects.nonNull(externalIpJson)) {
                dto.setExternalWhitelist(JSONUtil.toList(externalIpJson, String.class));
            }

            return dto;
        }).collect(Collectors.toList());
    }


    /**
     * 开通商户
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String createMerchant(MerchantSaveParam param) {
        // 校验是否存在
        if (merchantCodeExists(param.getCode())) {
            log.info("create merchant failed, cause by merchant code already exist");
            throw new ApiException(CommonCode.MERCHANT_ACCOUNT_ALREADY_EXISTS);
        }
        // 校验上级
        Merchant parentMerchant = this.getById(param.getPid());
        if (Objects.nonNull(parentMerchant) && EnableEnum.FALSE == parentMerchant.getEnableEnum()) {
            throw new ApiException(CommonCode.MERCHANT_ILLEGAL_PARENT);
        }
        // 保存商户
        Merchant m = convert2Merchant(param);
        // fill merchant path
        String prentPath = Objects.nonNull(parentMerchant) ? parentMerchant.getPath() : PATH_SPLIT;
        m.setPath(prentPath + m.getCode() + PATH_SPLIT);

        this.save(m);
        try {
            // 商户信息初始化
           return initMerchant(m);
        } catch (Exception e) {
            log.error("Init merchant account failed", e);
            throw e;
        }
    }

    /**
     * 更新商户信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMerchant(MerchantUpdateParam param) {

        Merchant m = this.getById(param.getId());
        if (Objects.isNull(m)) {
            throw new ApiException(CommonCode.MERCHANT_ACCOUNT_ALREADY_EXISTS);
        }
        CurrencyEnum defaultCurrencyEnum = param.getDefaultCurrencyEnum();
        List<String> currencies;
        try {
            currencies = filterInvalidCurrencies(JSONUtil.toList(param.getCurrency(), String.class));
            if (CollectionUtils.isEmpty(currencies) || !currencies.contains(defaultCurrencyEnum.name())) {
                throw new ApiException(CommonCode.MERCHANT_CURRENCY_INVALID);
            }
        } catch (Exception e) {
            throw new ApiException(CommonCode.MERCHANT_CURRENCY_INVALID);
        }
        this.lambdaUpdate()
                .set(Merchant::getCurrencyEnumListJson, JacksonUtil.toJSONString(currencies))
                .set(Objects.nonNull(param.getRemark()), Merchant::getRemark, param.getRemark())
                .set(Merchant::getDefaultCurrencyEnum, defaultCurrencyEnum)
                .set(Objects.nonNull(param.getMname()), Merchant::getMname, param.getMname())
                .eq(Merchant::getId, param.getId())
                .update();

        memberGroupService.tryCreateMerchantDefaultByCurrencyEnumList(m.getCurrencyEnumList(), m.getId());
    }

    /**
     * 启用商户
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enableMerchant(Long mid) {
        Merchant m = this.getById(mid);
        if (Objects.isNull(m)) {
            throw new IllegalArgumentException("Merchant not exist");
        }
        this.lambdaUpdate()
                .set(Merchant::getEnableEnum, EnableEnum.TRUE)
                .set(Merchant::getUpdateTime, LocalDateTime.now())
                .eq(Merchant::getId, mid)
                .update();

        // 启用登陆帐号
        adminService.enableAccount(m.getCode());
    }

    /**
     * 禁用商户
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void disableMerchant(Long mid) {
        Merchant m = this.getById(mid);
        if (Objects.isNull(m)) {
            throw new IllegalArgumentException("Merchant not exist");
        }
        this.lambdaUpdate()
                .set(Merchant::getEnableEnum, EnableEnum.FALSE)
                .set(Merchant::getUpdateTime, LocalDateTime.now())
                .eq(Merchant::getId, mid)
                .update();

        // 启用登陆帐号
        adminService.disableAccount(m.getCode());
    }

    @Override
    public void resetApiSecret(Long merchantId) {
        keyService.resetSecretByMerchantId(merchantId);
    }

    @Override
    public List<MerchantOptionDTO> getMerchantOptions() {
        List<Merchant> list;
        if (AdminTokenInfoUtil.superAdminMerchant()) {
            list = this.lambdaQuery()
                    .select(Merchant::getId, Merchant::getCode)
                    .eq(Merchant::getEnableEnum, EnableEnum.TRUE)
                    .list();
        }else{
            list = this.lambdaQuery()
                    .select(Merchant::getId, Merchant::getCode)
                    .eq(Merchant::getEnableEnum, EnableEnum.TRUE)
                    .and(i->i.eq(Merchant::getId, AdminTokenInfoUtil.getMerchantId())
                            .or()
                            .eq(Merchant::getPid, AdminTokenInfoUtil.getMerchantId()))
                    .list();
        }
        return list.stream()
                .map(i -> {
                    MerchantOptionDTO opt = new MerchantOptionDTO();
                    opt.setValue(i.getId());
                    opt.setLabel(i.getCode());
                    return opt;
                }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBusinessWhitelist(UpdateWhitelistParam param) {

        Merchant m = this.getById(param.getMerchantId());
        if (Objects.isNull(m)) {
            throw new ApiException(CommonCode.MERCHANT_ILLEGAL_PARAM);
        }
        List<IPRule> rules = param.getRules();

        String ruleJson;
        if (Objects.nonNull(rules) && !rules.isEmpty()) {
            ruleJson = JSONUtil.toJsonStr(rules);
        } else {
            ruleJson = null;
        }
        this.lambdaUpdate().set(Merchant::getBusinessWhitelistIpJson, ruleJson)
                .set(Merchant::getUpdateTime, LocalDateTime.now())
                .eq(Merchant::getId, param.getMerchantId())
                .update();


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateExternalWhitelist(UpdateWhitelistParam param) {
        Merchant m = this.getById(param.getMerchantId());
        if (Objects.isNull(m)) {
            throw new ApiException(CommonCode.MERCHANT_ILLEGAL_PARAM);
        }
        List<IPRule> rules = param.getRules();

        String ruleJson;
        if (Objects.nonNull(rules) && !rules.isEmpty()) {
            ruleJson = JSONUtil.toJsonStr(rules);
        } else {
            ruleJson = null;
        }
        this.lambdaUpdate().set(Merchant::getExternalWhitelistIpJson, ruleJson)
                .set(Merchant::getUpdateTime, LocalDateTime.now())
                .eq(Merchant::getId, param.getMerchantId())
                .update();
    }

    private String initMerchant(Merchant m) {
        // 初始化帐号
        String passwd = adminService.initMerchantAccount(m.getId(), m.getCode());

        // 初始化api key
        MerchantKey key = new MerchantKey();
        key.setMerchantId(m.getId());

        String operatorToken = SecureUtil.getSecureMd5();
        key.setOperatorToken(operatorToken);
        LocalDateTime now = LocalDateTime.now();
        key.setSecretKey(SecureUtil.getSecureMd5());
        key.setCreateTime(now);
        keyService.save(key);

        memberGroupService.tryCreateMerchantDefaultByCurrencyEnumList(m.getCurrencyEnumList(), m.getId());

        // 初始化站点额度
        thirdSiteBalanceInnerService.initThirdSiteBalance(m.getId(), m.getCode());

        return passwd;
    }

    private Merchant convert2Merchant(MerchantSaveParam param) {
        Merchant m = new Merchant();
        BeanUtils.copyProperties(param, m);

        if (Objects.isNull(param.getPid())) {
            m.setPid(MerchantConstants.SUPER_ADMIN_MERCHANT_ID);
        }

        String currencyJson = param.getCurrency();
        // 币种去重，防御性编程
        List<String> collect = JacksonUtil.toJavaObject(currencyJson, new TypeReference<List<String>>() {})
                .stream()
                .distinct()
                .collect(Collectors.toList());
        m.setCurrencyEnumListJson(JacksonUtil.toJSONString(collect));

        CurrencyEnum defaultCurrencyEnum = param.getDefaultCurrencyEnum();
        if (Objects.isNull(defaultCurrencyEnum)) {
            String str = JSONUtil.toList(currencyJson, String.class).get(0);
            m.setDefaultCurrencyEnum(CurrencyEnum.valueOf(str));
        }
        m.setDefaultCurrencyEnum(defaultCurrencyEnum);

        LocalDateTime now = LocalDateTime.now();
        m.setCreateTime(now);
        m.setUpdateTime(now);
        return m;
    }

    private boolean merchantCodeExists(String code) {
        long count = this.count(new LambdaQueryWrapper<Merchant>().eq(Merchant::getCode, code));
        return count > 0;
    }

    @Override
    public List<Long> getEnableMerchantIdList() {
        List<Merchant> merchantList = this.lambdaQuery()
                .select(Merchant::getId)
                .ne(Merchant::getId, Constants.DEFAULT_SYSTEM_MERCHANT_ID)
                .eq(Merchant::getEnableEnum, EnableEnum.TRUE)
                .list();
        if (CollUtil.isEmpty(merchantList)) {
            return new ArrayList<>();
        }
        return merchantList.stream().map(Merchant::getId).collect(Collectors.toList());
    }

    @Override
    public List<Merchant> getEnableMerchantList() {
        return this.lambdaQuery()
                .ne(Merchant::getId, Constants.DEFAULT_SYSTEM_MERCHANT_ID)
                .eq(Merchant::getEnableEnum, EnableEnum.TRUE)
                .list();
    }

    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_MERCHANT_KEY, key = "#operatorToken")
    public MerchantKeyVerifyInfoDTO getVerifyInfo(String operatorToken) {
        return loadVerifyInfo(operatorToken);
    }

    @Override
    public MerchantKeyVerifyInfoDTO loadVerifyInfo(String operatorToken) {
        Assert.notBlank(operatorToken);
        MerchantKey merchantKey = keyService.lambdaQuery()
                .select(MerchantKey::getMerchantId, MerchantKey::getSecretKey)
                .eq(MerchantKey::getOperatorToken, operatorToken)
                .last(Constants.SQL_LIMIT_1)
                .one();

        MerchantKeyVerifyInfoDTO merchantKeyVerifyInfoDTO = new MerchantKeyVerifyInfoDTO();

        if (merchantKey == null) {
            return merchantKeyVerifyInfoDTO;
        }

        Merchant merchant = this.lambdaQuery()
                .select(Merchant::getExternalWhitelistIpJson)
                .eq(Merchant::getId, merchantKey.getMerchantId())
                .last(Constants.SQL_LIMIT_1)
                .one();
        if (merchant != null) {
            merchantKeyVerifyInfoDTO.setExternalWhitelistIpJson(merchant.getExternalWhitelistIpJson());
        }

        merchantKeyVerifyInfoDTO.setMerchantId(merchantKey.getMerchantId());
        merchantKeyVerifyInfoDTO.setSecretKey(merchantKey.getSecretKey());
        return merchantKeyVerifyInfoDTO;
    }

    @Override
    public List<MerchantOptionDTO> getMerchantAgentOptions(String code) {
        // 非admin只能看自己和下级商户数据
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        Merchant merchant = this.getById(merchantId);
        if (Objects.isNull(merchant) || merchant.getEnableEnum() == EnableEnum.FALSE) {
            throw new ApiException(CommonCode.MERCHANT_ILLEGAL_STATUS);
        }

        List<Merchant> list = this.lambdaQuery()
                .select(Merchant::getId, Merchant::getCode)
                .likeRight(Merchant::getPath, merchant.getPath())
                .like(Objects.nonNull(code), Merchant::getCode, code)
                .orderByDesc(Merchant::getCode)
                .list();

        return list.stream().map(i -> {
            MerchantOptionDTO optionDTO = new MerchantOptionDTO();
            optionDTO.setLabel(i.getCode());
            optionDTO.setValue(i.getId());
            return optionDTO;
        })
        .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public Merchant getByIdCache(Long merchantId){
        Assert.notNull(merchantId);
        return getById(merchantId);
    }

    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public MerchantCurrencyDTO getCurrencyListByMerchantId(Long merchantId) {
        Assert.notNull(merchantId);
        Merchant merchant = getById(merchantId);
        MerchantCurrencyDTO merchantCurrencyDTO = new MerchantCurrencyDTO();
        merchantCurrencyDTO.setDefaultCurrencyEnum(merchant.getDefaultCurrencyEnum());
        merchantCurrencyDTO.setCurrencyEnumList(merchant.getCurrencyEnumList());
        merchantCurrencyDTO.setSupportedLanguagesMap(getSupportedLanguagesMap(merchantId));
        return merchantCurrencyDTO;
    }

    @Override
    public MerchantCurrencyDTO getCurrencyListAndIpCurrency(Long merchantId, String ip) {
        MerchantServiceImpl merchantService = GrapeApplication.getBean(this.getClass());
        MerchantCurrencyDTO currencyListByMerchantId = merchantService.getCurrencyListByMerchantId(merchantId);
        String ipCountryIsoCode = IPUtils.getCountryIsoCode(ip);
        CurrencyEnum ipCurrencyEnum = CurrencyEnum.findByCountryIsoCode(ipCountryIsoCode);
        currencyListByMerchantId.setIpCountryIsoCode(ipCountryIsoCode);
        currencyListByMerchantId.setIpCurrencyEnum(ipCurrencyEnum);
        return currencyListByMerchantId;
    }

    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    @Override
    public boolean judgeIsChildrenMerchant(Long pid, Long childrenId) {
        Merchant parent = this.getById(pid);
        if (Objects.isNull(parent) || parent.getEnableEnum() == EnableEnum.FALSE) {
            return false;
        }
        long count = this.lambdaQuery()
                .likeRight(Merchant::getPath, parent.getPath())
                .eq(Merchant::getId, childrenId)
                .count();
        return count > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resolveMerchantPath() {
        // resolve super admin path
        resolvePath(MerchantConstants.SUPER_ADMIN_MERCHANT_ID, PATH_SPLIT);
        // resolve children path
        resolveChildrenPath(MerchantConstants.SUPER_ADMIN_MERCHANT_ID, PATH_SPLIT);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void maintainMerchant(MerchantMaintainParam param) {
        Merchant merchant = this.getById(param.getMerchantId());
        if (Objects.isNull(merchant)) {
            throw new ApiException(CommonCode.MERCHANT_ACCOUNT_NOT_EXISTS);
        }
        //修改当前商户以及所有下级商户为维护状态
        this.lambdaUpdate().set(Merchant::getMaintainStatusEnum, param.getMaintainStatus())
                .set(Merchant::getMaintainEndTime,Objects.nonNull(param.getMaintainEndTime())?param.getMaintainEndTime():null)
                .set(Merchant::getMaintainRemark,Objects.nonNull(param.getMaintainRemark())?param.getMaintainRemark():null)
                .set(Merchant::getUpdateTime, LocalDateTime.now())
                .likeRight(Merchant::getPath, merchant.getPath())
                .update();

        //保存日志
        CommonOperationLogDTO commonOperationLogDTO = new CommonOperationLogDTO();

        commonOperationLogDTO.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        commonOperationLogDTO.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());

        commonOperationLogDTO.setTypeEnum(LogTypeEnum.MERCHANT_CONFIG);
        commonOperationLogDTO.setSubTypeEnum(LogSubTypeEnum.OTHER);
        if(BooleanEnum.TRUE.equals(param.getMaintainStatus())){
            commonOperationLogDTO.setContent("开启维护,维护时间:"+param.getMaintainEndTime()+",维护公告:"+param.getMaintainRemark());
        }else{
            commonOperationLogDTO.setContent("关闭维护");
        }
        commonOperationLogDTO.setIp(RequestUtil.getBigIntegerIpFromRequest());
        commonOperationLogDTO.setCreateBy(AdminTokenInfoUtil.getAdminName());
        commonOperationLogDTO.setCreateTime(LocalDateTime.now());
        amqpTemplate.convertAndSend(RabbitMQConstants.COMMON_OPERATION_LOG_EXCHANGE, "", JSONUtil.toJsonStr(commonOperationLogDTO));
    }

    @Cacheable(cacheNames = Constants.LOCAL_CACHE_MERCHANT_MAINTAIN_INFO, key = "#merchantId")
    @Override
    public MaitainRespVO merchantMaintainInfoCache(Long merchantId) {
        Merchant merchant = this.getById(merchantId);
        if(Objects.nonNull(merchant) &&
                ((EnableEnum.TRUE.equals(merchant.getMaintainStatusEnum())) ||
                        EnableEnum.FALSE.equals(merchant.getEnableEnum()))) {
            log.debug("merchant is maintain , merchant:{} ",merchant );
            //取默认币种
            CurrencyEnum currencyEnum = merchant.getDefaultCurrencyEnum();
            MerchantConfig merchantConfig = merchantConfigService.loadByMerchantIdAndDictKey(merchant.getId(), CommonDictKeyEnum.COPYWRITING_CUSTOMER_SERVICE.getValue(), currencyEnum);
            MaitainRespVO maitainRespVO = new MaitainRespVO();
            maitainRespVO.setId(merchant.getId());
            maitainRespVO.setMaintainEndTime(merchant.getMaintainEndTime());
            maitainRespVO.setMaintainRemark(merchant.getMaintainRemark());
            maitainRespVO.setMaintainStatusEnum(merchant.getMaintainStatusEnum());
            maitainRespVO.setMerchantEnableEnum(merchant.getEnableEnum());
            maitainRespVO.setSharePlatform(Objects.nonNull(merchantConfig)?merchantConfig.getDictValue():"");
            maitainRespVO.setImgReadServerUrl(awsS3Service.getReadDomain());
            maitainRespVO.setCurrencyEnum(currencyEnum);
            return maitainRespVO;
        }
        return null;
    }

    @Cacheable(cacheNames = Constants.LOCAL_CACHE_LONG_NAME, key = "#merchantId")
    @Override
    public IPAccessDenyRespVO merchantIPAccessDenyInfoCache(Long merchantId) {
        Merchant merchant = this.getById(merchantId);
        if (merchant == null) {
            return null;
        }
        CurrencyEnum currencyEnum = merchant.getDefaultCurrencyEnum();
        List<MerchantConfigCopywritingContactWindowDTO> customerServiceConfig = merchantConfigService.getListByMerchantIdAndDictKey(
                merchantId,
                CommonDictKeyEnum.COPYWRITING_CONTACT_WINDOW.getValue(),
                currencyEnum,
                MerchantConfigCopywritingContactWindowDTO.class);

        MerchantConfig merchantConfig = merchantConfigService
                .getByMerchantIdAndDictKey(merchantId, COMMON_LANGUAGE.getValue(), currencyEnum);

        MerchantConfigCommonUserColorDTO colorStyleConfig = merchantConfigService.getFirstByMerchantIdAndDictKey(
                merchantId,
                CommonDictKeyEnum.USER_SIDE_COLOR_STYLE.getValue(),
                currencyEnum,
                MerchantConfigCommonUserColorDTO.class);

        String colorStyle = Optional.ofNullable(colorStyleConfig)
                .map(MerchantConfigCommonUserColorDTO::getColor).orElse(null);

        IPAccessDenyRespVO respVO = new IPAccessDenyRespVO();
        respVO.setContactWindowConfigs(customerServiceConfig);
        respVO.setCurrencyEnum(currencyEnum);
        respVO.setLayout(merchant.getLayout());
        respVO.setColorStyle(colorStyle);

        try {
            //通用逻辑，避免此处异常导致整体业务失败
            String language = Optional.ofNullable(merchantConfig)
                    .map(MerchantConfig::getMerchantConfigCommonLanguageDTO)
                    .map(MerchantConfigCommonLanguageDTO::getSelectedLanguage).orElse(null);
            respVO.setLanguage(language);
        } catch (Exception e) {
            log.error("merchant ip access deny error, merchantId:{} currency:{}", merchantId, currencyEnum, e);
        }

        return respVO;
    }

    private void resolveChildrenPath(Long pid, String parentPath) {
        List<Merchant> list = this.lambdaQuery()
                .select(Merchant::getId, Merchant::getCode, Merchant::getPath)
                .eq(Merchant::getPid, pid)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (Merchant merchant : list) {
            String path = parentPath + merchant.getCode() + PATH_SPLIT;
            resolvePath(merchant.getId(), path);

            resolveChildrenPath(merchant.getId(), path);
        }
    }

    private void resolvePath(Long merchantId, String path){
        this.lambdaUpdate().eq(Merchant::getId, merchantId)
                .set(Merchant::getPath, path)
                .update();
    }

    private Map<CurrencyEnum, String> getSupportedLanguagesMap(Long merchantId) {
        List<MerchantConfig> merchantConfigList = merchantConfigService.getConfigListByMerchantIdAndDictKey(merchantId, COMMON_LANGUAGE.getValue());
        return merchantConfigList.stream()
                .collect(Collectors.toMap(
                        MerchantConfig::getCurrencyEnum,
                        MerchantConfig::getDictValue,
                        // 重复的键，保留最新值
                        (existing, replacement) -> replacement
                ));
    }

    /**
     * 更新商户参数过滤非法币种
     *
     * @param strCurrList 币种参数
     * @return 合法币种列表
     */
    private List<String> filterInvalidCurrencies(List<String> strCurrList) {
        if (CollectionUtils.isEmpty(strCurrList)) {
            return Collections.emptyList();
        }
        Set<String> systemCurrList = CurrencyEnum.getCurrencyEnumListFRemoveAll()
                .stream()
                .map(Enum::name)
                .collect(Collectors.toSet());
        return strCurrList.stream()
                .filter(systemCurrList::contains)
                .collect(Collectors.toList());
    }

}
