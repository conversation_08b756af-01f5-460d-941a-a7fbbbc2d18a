package com.wd.lottery.module.merchant.param;

import com.wd.lottery.common.constans.CurrencyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

import java.io.Serial;
import java.io.Serializable;

/**
 * Description: account init param
 *
 * <p>
 * Create on 2023/10/12.
 * <p>
 *
 * <AUTHOR>
 * @version 0.1
 */
@Schema(description = "创建代理商户参数")
@Data
public class MerchantSaveParam implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 上级代理 id
     */
    @Schema(description = "上级代理商户id，没有上级则不传值", example = "12345")
    private Long pid;
    /**
     * 商户编码
     */
    @Schema(description = "商户编号为小写英文和数字，长度为6-50个字符", example = "merchant1", pattern = "^[a-z][a-z0-9]{3,9}$")
    @NotBlank
    @Pattern(regexp = "^[a-z][a-z0-9_]{3,9}$", message = "商户编码要求4到10位英文及数字，不能数字开头")
    private String code;

    @Schema(description = "商户名称, 限制长度为4-20个字符", pattern = "^.{4,20}$", example = "商户张三")
    @NotBlank
    @Pattern(regexp = "^.{4,20}$")
    private String mname;
    /**
     * 币种
     */
    @Schema(description = "代理商户币种，可选多个支持的币种", example = "[\"THB\",\"VND\"]")
    @NotBlank
    private String currency;

    /**
     * 商户默认币种
     */
    @Schema(description = "代理商户默认币种", example = "THB")
    private CurrencyEnum defaultCurrencyEnum;

    /**
     * 备注
     */
    @Schema(description = "代理商户备注，选填项", example = "这里填写的是商户说明")
    private String remark;

    @Schema(description = "版面", example = "9987")
    @NotNull
    private String layout;

}
