package com.wd.lottery.module.merchant.service;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.merchant.consts.SmsTypeEnum;
import com.wd.lottery.module.merchant.dto.*;
import com.wd.lottery.module.merchant.dto.*;

import java.math.BigInteger;
import java.util.List;

public interface MerchantSmsService {

    List<MerchantSmsSearchDTO> search(Long merchantId, CurrencyEnum currencyEnum);

    Long create(MerchantSmsCreateDTO merchantSmsCreateDto);

    Long update(MerchantSmsUpdateDTO merchantSmsUpdateDto);

    Long enable(MerchantSmsEnableDTO merchantSmsEnableDto);

    Long delete(Long id, SmsTypeEnum smsTypeEnum);

    SmsRecordDTO test(MerchantSmsTestDTO merchantSmsTestDto);

    SmsRecordDTO pollingSmsSendCode(Long merchantId, CurrencyEnum currencyEnum, String areaCode, String mobile, BigInteger ipBigInteger);

    void checkCode(Long merchantId, CurrencyEnum currencyEnum, String area, String mobile, String code);

    void clearDailyOrMonthCount();

}
