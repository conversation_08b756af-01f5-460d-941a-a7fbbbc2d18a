package com.wd.lottery.module.merchant.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.merchant.consts.*;
import com.wd.lottery.module.merchant.consts.NoticeTypeEnum;
import com.wd.lottery.module.merchant.consts.PopupLocationEnum;
import com.wd.lottery.module.merchant.consts.PopupTypeEnum;
import com.wd.lottery.module.merchant.consts.StyleTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@TableName("merchant_notice")
@Data
public class MerchantNotice implements Serializable {
    @TableId(value = "id")
    private Long id;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    @Schema(description = "商户id")
    private Long merchantId;

    @Schema(description = "公告标题")
    private String  title;

    private NoticeTypeEnum noticeTypeEnum;

    @Schema(description = "排序序号")
    private Integer sort;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    private PopupLocationEnum popupLocationEnum;

    private PopupTypeEnum popupTypeEnum;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private CurrencyEnum currencyEnum;

    private StyleTypeEnum styleTypeEnum;

    @Schema(description = "公告内容")
    private String  content;

    private EnableEnum enableEnum;

    @TableField(updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

}
