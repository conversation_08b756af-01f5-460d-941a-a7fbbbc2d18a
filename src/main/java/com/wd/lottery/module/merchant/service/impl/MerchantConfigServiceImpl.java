package com.wd.lottery.module.merchant.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wd.lottery.GrapeApplication;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.common.util.JsonSlashRemoveUtil;
import com.wd.lottery.common.util.SqlUtil;
import com.wd.lottery.module.cash.constatns.SubTradeTypeEnum;
import com.wd.lottery.module.common.constants.*;
import com.wd.lottery.module.common.constants.*;
import com.wd.lottery.module.common.dto.IndexDataDTO;
import com.wd.lottery.module.common.dto.MerchantSeoConfig;
import com.wd.lottery.module.common.entity.CommonDict;
import com.wd.lottery.module.common.service.AwsS3Service;
import com.wd.lottery.module.common.service.CommonDictService;
import com.wd.lottery.module.common.vo.InitDataVO;
import com.wd.lottery.module.common.vo.SeoConfigVO;
import com.wd.lottery.module.merchant.annotation.MerchantConfigLog;
import com.wd.lottery.module.merchant.dto.*;
import com.wd.lottery.module.merchant.dto.*;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.mapper.MerchantConfigMapper;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.payment.dto.payment_bank.PaymentBankUpdateDTO;
import com.wd.lottery.module.payment.dto.payment_merchant_offline.PaymentMerchantOfflineOptionsDTO;
import com.wd.lottery.module.payment.dto.paymentmerchant.PaymentMerchantOptionsDTO;
import com.wd.lottery.module.payment.entity.PaymentBank;
import com.wd.lottery.module.payment.service.PaymentBankService;
import com.wd.lottery.module.payment.service.PaymentMerchantOfflineService;
import com.wd.lottery.module.payment.service.PaymentMerchantService;
import com.google.common.collect.Maps;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantConfigServiceImpl extends ServiceImpl<MerchantConfigMapper, MerchantConfig> implements MerchantConfigService {

    private final CommonDictService commonDictService;

    private final PaymentBankService paymentBankService;
    private final AwsS3Service awsS3Service;
    private static final String IMG_LOG_TEXT = "图片URL后缀";

    private static final String CONTENT_NULL = "[無資料]";

    /**
     * c端初始化
     *
     * @param initDataVO vo
     */
    @Override
    public void getAllByMerchantIdForCIndex(IndexDataDTO indexDataDTO, InitDataVO initDataVO) {
        Assert.notNull(indexDataDTO);
        Assert.notNull(indexDataDTO.getMerchantId());
        Assert.notNull(indexDataDTO.getCurrencyEnum());
        List<CommonDict> commonDictList = commonDictService.findMerchantConfigList();
        if (CollUtil.isEmpty(commonDictList)) {
            return;
        }
        Map<String, CommonDict> commonDictMap = commonDictList.stream().collect(Collectors.toMap(CommonDict::getDictKey, Function.identity()));

        List<MerchantConfig> merchantConfigList = this.lambdaQuery().select(MerchantConfig::getDictCode, MerchantConfig::getDictKey, MerchantConfig::getDictValue).eq(MerchantConfig::getMerchantId, indexDataDTO.getMerchantId()).eq(MerchantConfig::getCurrencyEnum, indexDataDTO.getCurrencyEnum()).eq(MerchantConfig::getEnableEnum, EnableEnum.TRUE).eq(MerchantConfig::getEnableShowClientEnum, EnableEnum.TRUE).in(MerchantConfig::getDictKey, commonDictMap.keySet()).orderByDesc(MerchantConfig::getId).list();

        this.filterEnableDictValue(merchantConfigList);

        initDataVO.setMerchantConfigList(merchantConfigList);
    }

    private void filterEnableDictValue(List<MerchantConfig> merchantConfigList) {
        if (CollUtil.isEmpty(merchantConfigList)) {
            return;
        }
        merchantConfigList.forEach(item -> {
            if (CommonDictCodeEnum.WITHDRAW.getValue().equals(item.getDictCode()) && CommonDictKeyEnum.WITHDRAW_PLATFORM.getValue().equals(item.getDictKey())) {
                List<MerchantConfigWithdrawPlatformDTO> dtoList = item.merchantConfigToList(MerchantConfigWithdrawPlatformDTO.class);
                Collection<MerchantConfigWithdrawPlatformDTO> filterDtoList = CollUtil.filterNew(dtoList, every -> Objects.isNull(every.getEnableEnum()) || every.getEnableEnum() == BooleanEnum.TRUE);
                if (CollectionUtils.isNotEmpty(filterDtoList)) {
                    item.setDictValue(parseDictValue(filterDtoList));
                } else {
                    item.setDictValue("[]");
                }
            }
        });
    }

    /**
     * 依商戶uid查當前系統有開啟的配置
     *
     * @param merchantId merchant uid
     * @return list entity
     */
    @Override
    public Map<String, MerchantConfig> getAllByMerchantId(Long merchantId, List<CommonDict> commonDictList, CurrencyEnum currencyEnum) {
        Assert.notNull(merchantId);

        Map<String, CommonDict> commonDictMap = commonDictList.stream().collect(Collectors.toMap(CommonDict::getDictKey, Function.identity()));

        List<MerchantConfig> merchantConfigList = this.lambdaQuery().eq(MerchantConfig::getMerchantId, merchantId).eq(MerchantConfig::getCurrencyEnum, currencyEnum).in(MerchantConfig::getDictKey, commonDictMap.keySet()).list();

        Map<String, MerchantConfig> merchantConfigMap = merchantConfigList.stream().collect(Collectors.toMap(MerchantConfig::getDictKey, Function.identity()));

        // todo use factory pattern
        MerchantConfig merchantConfig = initMerchantAuditConfig(merchantId, merchantConfigMap, commonDictMap);

        // switch 開關配置無資料時需要先新增
        initMerchantSwitchConfig(merchantId, merchantConfigMap, commonDictList, currencyEnum);

        merchantConfigMap.put(merchantConfig.getDictKey(), merchantConfig);

        return merchantConfigMap;
    }

    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_MERCHANT_CONFIG_AUDIT_RESET_ZERO, key = "#merchantIdList.toString()")
    public List<MerchantConfig> getAuditResetZero(List<Long> merchantIdList) {
        return loadAuditResetZero(merchantIdList);
    }

    @Override
    public List<MerchantConfig> loadAuditResetZero(List<Long> merchantIdList) {
        return super.lambdaQuery()
                .in(MerchantConfig::getMerchantId, merchantIdList)
                .eq(MerchantConfig::getDictKey, CommonConstants.COMMON_DICT_KEY_AUDIT_RESET_ZERO).list();
    }

    /**
     * 查詢當筆資料
     *
     * @param merchantId merchant uid
     * @param dictKey    dictKey
     * @return entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_MERCHANT_CONFIG, key = "#merchantId + ':' + #dictKey + ':' + #currencyEnum")
    public MerchantConfig getByMerchantIdAndDictKey(Long merchantId, String dictKey, CurrencyEnum currencyEnum) {
        return loadByMerchantIdAndDictKey(merchantId, dictKey, currencyEnum);
    }

    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public <T> T getFirstByMerchantIdAndDictKey(Long merchantId, String dictKey, CurrencyEnum currencyEnum, Class<T> tClass) {
        MerchantConfig merchantConfig = this.loadByMerchantIdAndDictKey(merchantId, dictKey, currencyEnum);
        if (Objects.isNull(merchantConfig)) {
            return null;
        }
        return merchantConfig.merchantConfigListGetFirst(tClass);
    }

    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public <T> List<T> getListByMerchantIdAndDictKey(Long merchantId, String dictKey, CurrencyEnum currencyEnum, Class<T> tClass) {
        MerchantConfig merchantConfig = this.loadByMerchantIdAndDictKey(merchantId, dictKey, currencyEnum);
        if (Objects.isNull(merchantConfig)) {
            return null;
        }
        return merchantConfig.merchantConfigToList(tClass);
    }

    @Override
    public MerchantConfig loadByMerchantIdAndDictKey(Long merchantId, String dictKey, CurrencyEnum currencyEnum) {
        return super.lambdaQuery()
                .eq(MerchantConfig::getMerchantId, merchantId)
                .eq(MerchantConfig::getCurrencyEnum, currencyEnum)
                .eq(MerchantConfig::getDictKey, dictKey).one();
    }

    private static MerchantConfig initMerchantAuditConfig(Long merchantId, Map<String, MerchantConfig> merchantConfigMap, Map<String, CommonDict> commonDictMap) {
        MerchantConfig merchantConfig = getMerchantConfig(CommonConstants.COMMON_DICT_KEY_AUDIT, merchantId, merchantConfigMap, commonDictMap);
        return assembleAudit(merchantConfig);
    }

    private void initMerchantSwitchConfig(Long merchantId, Map<String, MerchantConfig> merchantConfigMap, List<CommonDict> commonDictList, CurrencyEnum currencyEnum) {
        List<MerchantConfig> merchantConfigList = new ArrayList<>();
        List<CommonDict> switchCommonDictList = commonDictList.stream().filter(commonDict -> commonDict.getDictCode().equals(CommonConstants.COMMON_DICT_CODE_SWITCH)).collect(Collectors.toList());
        for (CommonDict commonDict : switchCommonDictList) {
            if (merchantConfigMap.get(commonDict.getDictKey()) == null) {
                MerchantConfig merchantConfig = new MerchantConfig();
                merchantConfig.setCommonDictId(commonDict.getId());
                merchantConfig.setMerchantId(merchantId);
                merchantConfig.setUpdateTime(LocalDateTime.now());
                merchantConfig.setDictCode(commonDict.getDictCode());
                merchantConfig.setDictKey(commonDict.getDictKey());
                merchantConfig.setDictValue(commonDict.getDictValue());
                merchantConfig.setEnableEnum(commonDict.getEnableSwitchDefaultEnum());
                merchantConfig.setEnableShowClientEnum(commonDict.getEnableShowClientEnum());
                merchantConfig.setCurrencyEnum(currencyEnum);
                merchantConfig.setUpdateBy(Constants.DEFAULT_SYSTEM_NAME);
                merchantConfigList.add(merchantConfig);
            }
        }
        if (CollectionUtil.isNotEmpty(merchantConfigList)) {
            boolean isSave = super.saveBatch(merchantConfigList);
            if (isSave) {
                merchantConfigList.forEach(merchantConfig -> merchantConfigMap.put(merchantConfig.getDictKey(), merchantConfig));
            } else {
                log.error("商戶配置, 初始化SWITCH資料異常 merchantId:{}, currency:{}", merchantId, currencyEnum);
            }
        }
    }

    @NotNull
    private static MerchantConfig getMerchantConfig(String commonDictKey, Long merchantId, Map<String, MerchantConfig> merchantConfigMap, Map<String, CommonDict> commonDictMap) {
        MerchantConfig merchantConfig = merchantConfigMap.get(commonDictKey);
        if (merchantConfig == null) {
            CommonDict commonDict = commonDictMap.get(commonDictKey);
            merchantConfig = new MerchantConfig();
            merchantConfig.setCommonDictId(commonDict.getId());
            merchantConfig.setMerchantId(merchantId);
            merchantConfig.setUpdateTime(LocalDateTime.now());
            merchantConfig.setDictCode(commonDict.getDictCode());
            merchantConfig.setDictKey(commonDict.getDictKey());
            merchantConfig.setEnableEnum(commonDict.getEnableEnum());
            merchantConfig.setEnableShowClientEnum(commonDict.getEnableShowClientEnum());
            merchantConfigMap.put(merchantConfig.getDictKey(), merchantConfig);
        }
        return merchantConfig;
    }

    private static MerchantConfig assembleAudit(MerchantConfig merchantConfig) {
        Assert.notNull(merchantConfig);
        Assert.notNull(merchantConfig.getDictKey());
        if (!merchantConfig.getDictKey().equals(CommonConstants.COMMON_DICT_KEY_AUDIT)) {
            return merchantConfig;
        }

        Map<SubTradeTypeEnum, MerchantConfigAuditDTO> merchantConfigAuditParamSet = merchantConfig.merchantConfigAuditToMap();
        Set<SubTradeTypeEnum> enableAuditSubTradeTypeEnumSet = SubTradeTypeEnum.getEnableAuditSubTradeTypeEnumSet();
        for (SubTradeTypeEnum subTradeTypeEnum : enableAuditSubTradeTypeEnumSet) {
            if (merchantConfigAuditParamSet.containsKey(subTradeTypeEnum)) {
                continue;
            }
            merchantConfigAuditParamSet.put(subTradeTypeEnum, new MerchantConfigAuditDTO(subTradeTypeEnum, new BigDecimal(Constants.ZERO_LONG)));
        }
        List<MerchantConfigAuditDTO> merchantConfigAuditDTOList = new ArrayList<>(merchantConfigAuditParamSet.values());
        merchantConfigAuditDTOList = merchantConfigAuditDTOList.stream().sorted(Comparator.comparing(MerchantConfigAuditDTO::getSubTradeTypeEnum)).collect(Collectors.toList());
        merchantConfig.setDictValue(JSONUtil.toJsonStr(merchantConfigAuditDTOList));

        return merchantConfig;
    }

    /**
     * 新增/編輯
     *
     * @param merchantConfigDtoList dto list
     * @param currencyEnum          幣種
     * @param merchantId            merchant uid
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.MERCHANT_CONFIG, subType = LogSubTypeConstants.UPDATE,
            success = "商户配置>{{#title}}>{{#subTitle}}, {{#content}}")
    public void saveOrUpdateData(List<MerchantConfigDTO> merchantConfigDtoList, CurrencyEnum currencyEnum, Long merchantId, String adminName) {
        if (CollectionUtils.isEmpty(merchantConfigDtoList)) {
            return;
        }
        List<CommonDict> commonDictList = commonDictService.findAll();

        List<String> dictValues = merchantConfigDtoList.stream().map(dto -> JsonSlashRemoveUtil.normalizeJsonString(dto.getDictValue())).collect(Collectors.toList());

        try {
            dictValues.forEach(s -> {
                if (!JSONUtil.isTypeJSONArray(s)) {
                    log.error("商戶配置, 格式異常, 資料:{}", s);
                    throw new ApiException(CommonCode.PARAM_INVALID);
                }
                //isTypeJSONArray 仅判断了前后是否是中括号，还需要实际解析进行验证
                JSONUtil.parseArray(s);
            });
        } catch (Exception e) {
            log.error("商戶配置, json解析異常", e);
            throw new ApiException(CommonCode.FAILED);
        }
        Map<Long, CommonDict> commonDictMap = commonDictList.stream().collect(Collectors.toMap(CommonDict::getId, Function.identity()));

        List<Long> commonDictIdList = merchantConfigDtoList.stream().map(MerchantConfigDTO::getCommonDictId).collect(Collectors.toList());

        for (Long commonDictId : commonDictIdList) {
            if (commonDictMap.get(commonDictId).getEnableEnum() == EnableEnum.FALSE) {
                log.error("商戶配置, 配置異常, 系統配置內包含停用資料, commonDictId:{}", commonDictId);
                throw new ApiException(CommonCode.FAILED);
            }
        }
        List<MerchantConfig> merchantConfigs = convert2MerchantConfigEntityBatch(merchantConfigDtoList, merchantId, commonDictMap, currencyEnum, adminName);

        MerchantConfig merchantConfig = merchantConfigs.get(0);

        CommonDict commonDict = commonDictMap.get(merchantConfig.getCommonDictId());
        LogRecordContext.putVariable("title", CommonDictCodeEnum.getCommonDictCodeText(commonDict.getDictCode()));
        LogRecordContext.putVariable("subTitle", CommonDictKeyEnum.getCommonDictKeyText(commonDict.getDictKey()));
        LogRecordContext.putVariable("content", this.setOperatorLogContent(merchantConfigs.get(0), CommonDictKeyEnum.valueOf(commonDict.getDictKey()), merchantId, currencyEnum));

        log.info("[{}]商戶配置, 當前配置:{}", merchantId, merchantConfigs);
        saveOrUpdateOnUniqueKey(merchantConfigs);
    }

    private List<MerchantConfig> convert2MerchantConfigEntityBatch(List<MerchantConfigDTO> merchantConfigDtoList, Long merchantId, Map<Long, CommonDict> commonDictMap, CurrencyEnum currencyEnum, String adminName) {
        LocalDateTime localDateTime = LocalDateTime.now();
        List<MerchantConfig> configList = new ArrayList<>(merchantConfigDtoList.size());
        for (MerchantConfigDTO merchantConfigDto : merchantConfigDtoList) {
            MerchantConfig config = new MerchantConfig();

            config.setId(IdWorker.getId());
            config.setMerchantId(merchantId);
            config.setDictCode(commonDictMap.get(merchantConfigDto.getCommonDictId()).getDictCode());
            config.setDictKey(commonDictMap.get(merchantConfigDto.getCommonDictId()).getDictKey());
            config.setCurrencyEnum(currencyEnum);
            config.setCommonDictId(merchantConfigDto.getCommonDictId());
            config.setEnableShowClientEnum(commonDictMap.get(merchantConfigDto.getCommonDictId()).getEnableShowClientEnum());
            if (commonDictMap.get(merchantConfigDto.getCommonDictId()).getDictCode().equals(CommonConstants.COMMON_DICT_CODE_SWITCH)) {
                config.setEnableEnum(merchantConfigDto.getEnableEnum());
            } else {
                config.setEnableEnum(EnableEnum.TRUE);
            }
            config.setUpdateTime(LocalDateTime.now());
            config.setDictValue(JsonSlashRemoveUtil.normalizeJsonString(merchantConfigDto.getDictValue()));
            config.setUpdateTime(localDateTime);
            config.setUpdateBy(adminName);

            configList.add(config);
        }
        return configList;
    }

    private void saveOrUpdateOnUniqueKey(List<MerchantConfig> merchantConfigs) {

        List<MerchantConfig> addList = new ArrayList<>();
        for (MerchantConfig config : merchantConfigs) {
            MerchantConfig row = getMerchantConfigByDictKey(config.getDictKey(), config.getMerchantId(), config.getCurrencyEnum());
            if (Objects.isNull(row)) {
                addList.add(config);
            }else{
                BeanUtils.copyProperties(config, row, "id");
                this.updateById(row);
            }
        }
        if (!addList.isEmpty()) {
            this.saveBatch(addList);
        }
    }

    private MerchantConfig getMerchantConfigByDictKey(String dictKey, Long merchantId, CurrencyEnum currencyEnum) {
        return this.lambdaQuery()
                .eq(MerchantConfig::getMerchantId, merchantId)
                .eq(MerchantConfig::getCurrencyEnum, currencyEnum)
                .eq(MerchantConfig::getDictKey, dictKey)
                .one();
    }

    @Override
    public Map<String, MerchantConfig> getMerchantSwitchConfig(Long merchantId, CurrencyEnum currencyEnum) {
        List<MerchantConfig> list = this.lambdaQuery()
                .eq(MerchantConfig::getMerchantId, merchantId)
                .eq(MerchantConfig::getCurrencyEnum, currencyEnum)
                .eq(MerchantConfig::getDictCode, CommonConstants.COMMON_DICT_CODE_SWITCH)
                .list();
        return list.stream().collect(Collectors.toMap(MerchantConfig::getDictKey, v -> v));
    }

    @Override
    public List<MerchantConfig> getConfigListByMerchantIdAndDictKey(Long merchantId, String dictKey) {
        return lambdaQuery()
                .eq(MerchantConfig::getMerchantId, merchantId)
                .eq(MerchantConfig::getDictKey, dictKey)
                .list();
    }

    @Override
    public MerchantSeoConfig getMerchantSeoConfig(Long merchantId, CurrencyEnum currencyEnum) {
        MerchantConfig config = this.getMerchantConfigByDictKey(CommonDictKeyEnum.COPYWRITING_SEO.getValue(), merchantId, currencyEnum);
        if (Objects.isNull(config)) {
            throw new ApiException(CommonCode.MERCHANT_SEO_NOT_CONFIGURED);
        }
        return getMerchantSeoConfigs(config);
    }

    @NotNull
    private static MerchantSeoConfig getMerchantSeoConfigs(MerchantConfig config) {
        String jsonArr = config.getDictValue();
        List<MerchantSeoConfig> seoConfigs = JacksonUtil.toJavaObject(jsonArr, new TypeReference<List<MerchantSeoConfig>>() {
        });
        if (CollectionUtils.isEmpty(seoConfigs)) {
            throw new ApiException(CommonCode.MERCHANT_SEO_NOT_CONFIGURED);
        }
        return seoConfigs.get(0);
    }

    @Override
    public List<SeoConfigVO> getSeoConfigVOList() {
        List<MerchantConfig> merchantConfigList = getAllMerchantMaxDictValueSetConfigList();
        return convertToSeoConfigVoList(merchantConfigList);
    }

    private List<MerchantConfig> getAllMerchantMaxDictValueSetConfigList() {
        LambdaQueryWrapper<MerchantConfig> merchantConfigLambdaQueryWrapper = new QueryWrapper<MerchantConfig>().select(
                        SqlUtil.selectMax(MerchantConfig::getMerchantId),
                        SqlUtil.selectMax(MerchantConfig::getDictValue)
                ).lambda()
                .eq(MerchantConfig::getDictKey, CommonDictKeyEnum.COPYWRITING_SEO)
                .groupBy(MerchantConfig::getMerchantId);
        return super.list(merchantConfigLambdaQueryWrapper);
    }

    @NotNull
    private List<SeoConfigVO> convertToSeoConfigVoList(List<MerchantConfig> merchantConfigList) {
        if (CollUtil.isEmpty(merchantConfigList)) {
            return Collections.emptyList();
        }
        String imgPrefix = awsS3Service.getDomain();
        List<SeoConfigVO> seoConfigVOList = new ArrayList<>(merchantConfigList.size());

        for (MerchantConfig merchantConfig : merchantConfigList) {
            try {
                MerchantSeoConfig merchantSeoConfig = getMerchantSeoConfigs(merchantConfig);
                SeoConfigVO seoConfigVO = new SeoConfigVO();
                seoConfigVO.setSiteId(merchantConfig.getMerchantId());
                seoConfigVO.setSiteDesc(merchantSeoConfig.getDescription());
                seoConfigVO.setSiteKeywords(merchantSeoConfig.getTitle());

                if (StrUtil.isNotBlank(merchantSeoConfig.getBgImgUrl())) {
                    seoConfigVO.setThumbnailUrl(imgPrefix + merchantSeoConfig.getBgImgUrl());
                }

                if (StrUtil.isNotBlank(merchantSeoConfig.getImgUrl())) {
                    seoConfigVO.setIconUrl(imgPrefix + merchantSeoConfig.getImgUrl());
                }

                seoConfigVOList.add(seoConfigVO);
            } catch (Exception e) {
                log.info("Exception occurred while processing merchantConfig: {}", merchantConfig, e);
            }
        }
        return seoConfigVOList;
    }


    @Override
    public boolean isMerchantQuickRegisterEnable(Long merchantId, CurrencyEnum curr) {
        MerchantConfig config = this.getMerchantConfigByDictKey(CommonDictKeyEnum.SWITCH_QUICK_REGISTER.getValue(), merchantId, curr);
        if (Objects.isNull(config)) {
            return false;
        }
        return config.getEnableEnum() == EnableEnum.TRUE;
    }

    @Override
    public MerchantConfigCommonAppInviteDomainDTO getMerchantAppInviteDomian(Long merchantId, CurrencyEnum curr) {
        MerchantConfig config = this.getMerchantConfigByDictKey(CommonDictKeyEnum.COMMON_APP_INVITE_DOMAIN.getValue(), merchantId, curr);
        if (Objects.isNull(config)) {
            throw new ApiException(CommonCode.MERCHANT_APP_INVITE_DOMAIN_NOT_CONFIGURED);
        }
        List<MerchantConfigCommonAppInviteDomainDTO> inviteDomainConfigList = JacksonUtil.toJavaObject(config.getDictValue(), new TypeReference<List<MerchantConfigCommonAppInviteDomainDTO>>() {
        });
        if (CollectionUtils.isNotEmpty(inviteDomainConfigList)) {
            return inviteDomainConfigList.get(0);
        }
        return null;
    }

    @Override
    public void updateMerchantWithdrawChannelConfig(PaymentBankUpdateDTO paymentBankUpdateDto) {
        MerchantConfig withdrawPlatformMerchantConfig = this.getMerchantConfigByDictKey(CommonDictKeyEnum.WITHDRAW_PLATFORM.getValue(),
                paymentBankUpdateDto.getMerchantId(), paymentBankUpdateDto.getCurrencyEnum());
        if (withdrawPlatformMerchantConfig == null) {
            return;
        }
        List<MerchantConfigWithdrawPlatformDTO> merchantConfigWithdrawPlatformDTOList = withdrawPlatformMerchantConfig.merchantConfigToListNotEmpty(MerchantConfigWithdrawPlatformDTO.class);
        for (MerchantConfigWithdrawPlatformDTO platformDTO : merchantConfigWithdrawPlatformDTOList) {
            if (CollectionUtil.isEmpty(platformDTO.getPaymentBank())) {
                continue;
            }
            for (MerchantConfigWithdrawPlatformPaymentBankDTO bankDTO : platformDTO.getPaymentBank()) {
                if (!Objects.equals(bankDTO.getPaymentBankId(), paymentBankUpdateDto.getId())) {
                    continue;
                }
                bankDTO.setImg(paymentBankUpdateDto.getImage());
                bankDTO.setSubPayName(paymentBankUpdateDto.getName());
            }
        }
        withdrawPlatformMerchantConfig.setDictValue(parseDictValue(merchantConfigWithdrawPlatformDTOList));
        this.updateById(withdrawPlatformMerchantConfig);
    }

    private String setOperatorLogContent(MerchantConfig merchantConfig, CommonDictKeyEnum commonDictKeyEnum, Long merchantId, CurrencyEnum currencyEnum) {
        switch (commonDictKeyEnum) {
            case COPYWRITING_MOBILE_SLIDER:
                List<MerchantConfigCopywritingMobileSliderDTO> merchantConfigCopywritingMobileSliderDtoList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingMobileSliderDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingMobileSliderDtoList));
            case COPYWRITING_CUSTOMER_SERVICE:
                List<MerchantConfigCopywritingCustomerServiceDTO> merchantConfigCopywritingCustomerServiceDtoList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingCustomerServiceDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingCustomerServiceDtoList));
            case COPYWRITING_FLOATING_BUTTON:
                List<MerchantConfigCopywritingFloatingButtonDTO> copywritingFloatingButtonDTOList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingFloatingButtonDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(copywritingFloatingButtonDTOList));
            case COPYWRITING_ANDROID_DOWNLOAD_LINK:
                List<MerchantConfigCopywritingAndroidDownloadLinkDTO> merchantConfigCopywritingAndroidDownloadLinkDtoList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingAndroidDownloadLinkDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingAndroidDownloadLinkDtoList));
            case COPYWRITING_IOS_DOWNLOAD_LINK:
                List<MerchantConfigCopywritingIosDownloadLinkDTO> merchantConfigCopywritingIosDownloadLinkDtoList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingIosDownloadLinkDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingIosDownloadLinkDtoList));
            case COPYWRITING_MERCHANT_LOGO:
            case COPYWRITING_ACCOUNT_PAGE_ICON:
                List<MerchantConfigCopywritingMerchantLogoDTO> merchantConfigCopywritingMerchantLogoDtoList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingMerchantLogoDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingMerchantLogoDtoList));
            case AUDIT:
                List<MerchantConfigAuditDTO> merchantConfigAuditDtoList = merchantConfig.merchantConfigToList(MerchantConfigAuditDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigAuditDtoList));
            case AUDIT_RESET_ZERO:
                List<MerchantConfigResetZeroAuditBalanceDTO> merchantConfigResetZeroAuditBalanceDtoList = merchantConfig.merchantConfigToList(MerchantConfigResetZeroAuditBalanceDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigResetZeroAuditBalanceDtoList));
            case DEPOSIT_PLATFORM:
                List<MerchantConfigDepositPlatformDTO> merchantConfigDepositPlatformDtoList = merchantConfig.merchantConfigToList(MerchantConfigDepositPlatformDTO.class);
                PaymentMerchantService paymentMerchantService = GrapeApplication.getBean(PaymentMerchantService.class);
                List<PaymentMerchantOptionsDTO> paymentMerchantOptionsDtoList = paymentMerchantService.findAllOptions(merchantId, currencyEnum);
                Map<Long, String> paymentMerchantMap = paymentMerchantOptionsDtoList.stream().collect(Collectors.toMap(PaymentMerchantOptionsDTO::getId, PaymentMerchantOptionsDTO::getChannelName));

                PaymentMerchantOfflineService paymentMerchantOfflineService = GrapeApplication.getBean(PaymentMerchantOfflineService.class);
                List<PaymentMerchantOfflineOptionsDTO> paymentMerchantOfflineOptionsDtoList = paymentMerchantOfflineService.findAllOptions(merchantId, currencyEnum);
                Map<Long, String> paymentMerchantOfflineMap = paymentMerchantOfflineOptionsDtoList.stream().collect(Collectors.toMap(PaymentMerchantOfflineOptionsDTO::getId, PaymentMerchantOfflineOptionsDTO::getChannelName));
                //抽取出更新的字段
                Object diffDepositDTO = getDiffMerchantConfigDTO(merchantConfigDepositPlatformDtoList, commonDictKeyEnum, merchantId, currencyEnum);
                merchantConfigDepositPlatformDtoList =JSONUtil.toList(JSONUtil.toJsonStr(diffDepositDTO),MerchantConfigDepositPlatformDTO.class);
                log.info("平台入款配置merchantConfigDepositPlatformDtoList{}:",JSONUtil.toJsonStr(merchantConfigDepositPlatformDtoList));
                if (CollectionUtil.isEmpty(merchantConfigDepositPlatformDtoList)) {
                    return CONTENT_NULL;
                } else {
                    MerchantConfigContextDTO contextDTO = new MerchantConfigContextDTO(merchantConfigDepositPlatformDtoList);
                    contextDTO.setPaymentMerchantMap(paymentMerchantMap);
                    contextDTO.setPaymentMerchantOfflineMap(paymentMerchantOfflineMap);
                    return this.parseContentForDepositAndWithdraw(contextDTO ,false);
                }
            case WITHDRAW_PLATFORM:
                List<MerchantConfigWithdrawPlatformDTO> merchantConfigWithdrawPlatformDtoList = merchantConfig.merchantConfigToList(MerchantConfigWithdrawPlatformDTO.class);
                List<PaymentBank> paymentBankList = paymentBankService.findAll();
                Map<Long, String> paymentBankMap = paymentBankList.stream().collect(Collectors.toMap(PaymentBank::getId, PaymentBank::getName));
                //抽取出更新的字段
                Object diffWithdrawDTO = getDiffMerchantConfigDTO(merchantConfigWithdrawPlatformDtoList, commonDictKeyEnum, merchantId, currencyEnum);
                merchantConfigWithdrawPlatformDtoList =JSONUtil.toList(JSONUtil.toJsonStr(diffWithdrawDTO),MerchantConfigWithdrawPlatformDTO.class);
                log.info("平台出款配置merchantConfigWithdrawPlatformDtoList{}:",JSONUtil.toJsonStr(merchantConfigWithdrawPlatformDtoList));
                if(CollectionUtil.isEmpty(merchantConfigWithdrawPlatformDtoList)){
                    return CONTENT_NULL;
                }else {
                    MerchantConfigContextDTO contextDTO = new MerchantConfigContextDTO(merchantConfigWithdrawPlatformDtoList);
                    contextDTO.setPaymentBankMap(paymentBankMap);
                    return this.parseContentForDepositAndWithdraw(contextDTO,false);
                }
            case WITHDRAW_LIMIT:
                List<MerchantConfigWithdrawLimitDTO> merchantConfigWithdrawLimitDtoList = merchantConfig.merchantConfigToList(MerchantConfigWithdrawLimitDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigWithdrawLimitDtoList));
            case WITHDRAW_BANK_ACCOUNT_BIND:
                List<MerchantConfigWithdrawBankAccountBindDTO> merchantConfigWithdrawBankAccountBindDtoList = merchantConfig.merchantConfigToList(MerchantConfigWithdrawBankAccountBindDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigWithdrawBankAccountBindDtoList));
            case SWITCH_INVITE_CODE_REQUIRED:
            case SWITCH_MOBILE_REGISTER:
            case SWITCH_EMAIL_REGISTER:
            case SWITCH_SMS_VERIFICATION_CODE:
            case SWITCH_HOMEPAGE_FLOATING_BUTTON:
            case SWITCH_QUICK_REGISTER:
            case SWITCH_OPEN_INSTALL_INVITE_CODE:
            case SWITCH_HOMEPAGE_DOWNLOAD_BUTTON:
            case SWITCH_HOMEPAGE_ADD_DESKTOP_BUTTON:
            case SWITCH_WITHDRAW_PASSWORD_VERIFICATION:
            case SWITCH_WITHDRAW_LOGIN_PASSWORD_VERIFICATION:
            case SWITCH_GOOGLE_REGISTER:
            case SWITCH_FIRST_CHARGE_ACTIVITY_POP_UP_DISPLAYED:
            case SWITCH_APP_INSTALL_BOTTOM_POP_UP:
            case SWITCH_APP_INSTALL_TOP_POP_UP:
            case SWITCH_WEB_PUSH:
                return merchantConfig.getEnableEnum() == EnableEnum.TRUE ? "是" : "否";
            case COMMON_GAME_SERVICE_FEE_RATE:
                List<MerchantConfigCommonGameServiceFeeRateDTO> merchantConfigCommonGameServiceFeeRateDtoList = merchantConfig.merchantConfigToList(MerchantConfigCommonGameServiceFeeRateDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCommonGameServiceFeeRateDtoList));
            case COMMON_SHARE_PLATFORM:
                List<MerchantConfigCommonSharePlatformDTO> merchantConfigCommonSharePlatformDtoList = merchantConfig.merchantConfigToList(MerchantConfigCommonSharePlatformDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCommonSharePlatformDtoList));
            case COMMON_APP_PUSH:
                List<MerchantConfigCommonAppPushDTO> merchantConfigCommonAppPushDTOList = merchantConfig.merchantConfigToList(MerchantConfigCommonAppPushDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCommonAppPushDTOList));
            case COMMON_APP_INVITE_DOMAIN:
                List<MerchantConfigCommonAppInviteDomainDTO> merchantConfigCommonAppInviteDomainDtoList = merchantConfig.merchantConfigToList(MerchantConfigCommonAppInviteDomainDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCommonAppInviteDomainDtoList));
            case COPYWRITING_DATA_ANALYSIS:
                List<MerchantConfigCopywritingDataAnalysisDTO> merchantConfigCopywritingDataAnalysisDtoList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingDataAnalysisDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingDataAnalysisDtoList));
            case COPYWRITING_SEO:
                List<MerchantConfigCopywritingSeoDTO> merchantConfigCopywritingSeoDtoList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingSeoDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingSeoDtoList));
            case USER_SIDE_COLOR_STYLE:
                List<MerchantConfigCommonUserColorDTO> merchantConfigCommonUserColorDTOList = merchantConfig.merchantConfigToList(MerchantConfigCommonUserColorDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCommonUserColorDTOList));
            case COPYWRITING_SHARE_PICTURE:
                List<MerchantConfigCopywritingSharePictureDTO> merchantConfigCopywritingSharePictureDTOList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingSharePictureDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingSharePictureDTOList));
            case COMMON_LANGUAGE:
                Map<String, Integer> commonLanguageMap = Maps.newHashMap();
                MerchantConfigCommonLanguageDTO merchantConfigCommonLanguageDTOList = merchantConfig.getMerchantConfigCommonLanguageDTO();
                for (CommonLanguageConfigDTO languageConfig : merchantConfigCommonLanguageDTOList.getLanguageConfigs()) {
                    commonLanguageMap.put(languageConfig.getLanguageCode(),languageConfig.getValue());
                }
                MerchantConfigContextDTO contextDTO = new MerchantConfigContextDTO(merchantConfigCommonLanguageDTOList);
                contextDTO.setCommonLanguageMap(commonLanguageMap);
                return this.parseContent(contextDTO);
            case COPYWRITING_DOWNLOAD_CONTENT:
                List<MerchantConfigCopywritingDownloadContent> merchantConfigCopywritingDownloadContentList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingDownloadContent.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingDownloadContentList));
            case COPYWRITING_TEXT_CONFIG:
                List<MerchantConfigCopywritingTextConfigDTO> merchantConfigCopywritingTextConfigDTOList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingTextConfigDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingTextConfigDTOList));
            case COPYWRITING_DOWNLOAD_SLIDER:
                List<MerchantConfigCopywritingDownloadSliderDTO> merchantConfigCopywritingDownloadSliderDTOList = merchantConfig.merchantConfigToList(MerchantConfigCopywritingDownloadSliderDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingDownloadSliderDTOList));
            case COMMON_OPEN_INSTALL_APPKEY:
                List<MerchantConfigCommonOpenInstallAppkeyDTO> merchantConfigCommonOpenInstallAppkeyDTOS = merchantConfig.merchantConfigToList(MerchantConfigCommonOpenInstallAppkeyDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCommonOpenInstallAppkeyDTOS));
            case WITHDRAW_PASSWORD_ERROR_LIMIT:
                List<MerchantConfigWithdrawPasswordErrorLimitDTO> merchantConfigWithdrawPasswordErrorLimitDTOS = merchantConfig.merchantConfigToList(MerchantConfigWithdrawPasswordErrorLimitDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigWithdrawPasswordErrorLimitDTOS));
            case COPYWRITING_GAME_CATEGORY:
                List<MerchantConfigCopywritingGameCategoryDTO> merchantConfigCopywritingGameCategoryDTOS = merchantConfig.merchantConfigToList(MerchantConfigCopywritingGameCategoryDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingGameCategoryDTOS));
            case COPYWRITING_NAV_CONFIG:
                List<MerchantConfigCopywritingNavConfigDTO> merchantConfigCopywritingNavConfigDTOS = merchantConfig.merchantConfigToList(MerchantConfigCopywritingNavConfigDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigCopywritingNavConfigDTOS));
            case COMMON_FACE_BOOK_APP_ID_SETTINGS:
                List<MerchantConfigFaceBookAppIdConfigDTO> merchantConfigFaceBookAppIdConfigDTOS = merchantConfig.merchantConfigToList(MerchantConfigFaceBookAppIdConfigDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigFaceBookAppIdConfigDTOS));
            case COMMON_THIRD_REGISTER_CONFIG:
                MerchantConfigThirdRegisterConfigDTO merchantConfigThirdRegisterConfigDTO = merchantConfig.merchantConfigListGetFirst(MerchantConfigThirdRegisterConfigDTO.class);
                return this.parseContent(new MerchantConfigContextDTO(merchantConfigThirdRegisterConfigDTO));
            default:
                return "";
        }
    }

    private Object getDiffMerchantConfigDTO(Object submitList , CommonDictKeyEnum commonDictKeyEnum , Long merchantId, CurrencyEnum currencyEnum){
        try {
            MerchantConfig row = getMerchantConfigByDictKey(commonDictKeyEnum.getValue(), merchantId, currencyEnum);
            if(Objects.nonNull(row)&&Objects.nonNull(row.getDictValue())){
                List<Map<String, Object>> differences;
                switch (commonDictKeyEnum) {
                    case DEPOSIT_PLATFORM:
                        List<MerchantConfigDepositPlatformDTO> diffDepositList = new ArrayList<>();
                        List<MerchantConfigDepositPlatformDTO> newDepositList = JSONUtil.toList(JSONUtil.toJsonStr(submitList),MerchantConfigDepositPlatformDTO.class);
                        List<MerchantConfigDepositPlatformDTO> oldDepositList = JSONUtil.toList(row.getDictValue(), MerchantConfigDepositPlatformDTO.class);
                        log.info("入款平台配置跟新字段信息 , newWithdrawList:{} , oldWithdrawList:{}",JSONUtil.toJsonStr(newDepositList) , JSONUtil.toJsonStr(oldDepositList));
                        differences = findDifferences(oldDepositList, newDepositList , MerchantConfigDepositPlatformDTO.class);
                        log.info("入款平台配置已更新的字段信息:{}",JSONUtil.toJsonStr(differences));
                        if(CollectionUtils.isNotEmpty(differences)){
                            for (Map<String, Object> diff : differences) {
                                ObjectMapper objectMapper = new ObjectMapper();
                                MerchantConfigDepositPlatformDTO dto = objectMapper.convertValue(diff, MerchantConfigDepositPlatformDTO.class);
                                diffDepositList.add(dto);
                            }
                        }
                        log.info("入款平台配置跟新字段信息:{}",JSONUtil.toJsonStr(diffDepositList));
                        return diffDepositList;
                    case WITHDRAW_PLATFORM:
                        List<MerchantConfigWithdrawPlatformDTO> diffWithdrawList = new ArrayList<>();
                        List<MerchantConfigWithdrawPlatformDTO> newWithdrawList = JSONUtil.toList(JSONUtil.toJsonStr(submitList),MerchantConfigWithdrawPlatformDTO.class);
                        List<MerchantConfigWithdrawPlatformDTO> oldWithdrawList = JSONUtil.toList(row.getDictValue(), MerchantConfigWithdrawPlatformDTO.class);
                        log.info("出款平台配置跟新字段信息 , newWithdrawList:{} , oldWithdrawList:{}",JSONUtil.toJsonStr(newWithdrawList) , JSONUtil.toJsonStr(oldWithdrawList));
                        differences = findDifferences(oldWithdrawList, newWithdrawList , MerchantConfigWithdrawPlatformDTO.class);
                        log.info("出款平台配置已更新的字段信息:{}",JSONUtil.toJsonStr(differences));
                        if(CollectionUtils.isNotEmpty(differences)){
                            for (Map<String, Object> diff : differences) {
                                ObjectMapper objectMapper = new ObjectMapper();
                                MerchantConfigWithdrawPlatformDTO dto = objectMapper.convertValue(diff, MerchantConfigWithdrawPlatformDTO.class);
                                diffWithdrawList.add(dto);
                            }
                        }
                        log.info("出款平台配置跟新字段信息:{}",JSONUtil.toJsonStr(diffWithdrawList));
                        return diffWithdrawList;
                    default:
                        //忽略
                }
            }
        } catch (IllegalAccessException e) {
            log.error("处理日志更新字段失败:",e);
            throw new RuntimeException(e);
        }
        return null;
    }

    private String parseContent(MerchantConfigContextDTO contextDTO) {
        Object merchantConfigLogData = contextDTO.getMerchantConfigLogData();
        StringBuilder content = new StringBuilder();
        try {
            Collection<?> objectCollection;
            if (merchantConfigLogData instanceof Collection) {
                objectCollection = (Collection<?>) merchantConfigLogData;
            } else {
                objectCollection = Collections.singletonList(merchantConfigLogData);
            }

            for (Object o : objectCollection) {
                content.append("[");

                Class<?> clazz = o.getClass();
                BeanInfo beanInfo = Introspector.getBeanInfo(clazz, Object.class);
                PropertyDescriptor[] pds = beanInfo.getPropertyDescriptors();

                for (PropertyDescriptor pd : pds) {
                    String displayName = pd.getName();
                    Field declaredField = clazz.getDeclaredField(displayName);

                    MerchantConfigLog merchantConfigLog = declaredField.getDeclaredAnnotation(MerchantConfigLog.class);
                    if (merchantConfigLog != null && StringUtils.isNotBlank(merchantConfigLog.text())) {
                        String description = merchantConfigLog.text();
                        Method readMethod = pd.getReadMethod();
                        Object value = readMethod.invoke(o);

                        // AUDIT打碼量配置要額外處理,因為型態是enum
                        if (MerchantConfigAuditDTO.class.isAssignableFrom(clazz)) {
                            if (SubTradeTypeEnum.class.isAssignableFrom(pd.getPropertyType())) {
                                description = SubTradeTypeEnum.valueOf(value.toString()).getDesc();
                                content.append(description).append("倍数, ");
                            } else {
                                content.append(value);
                            }
                        } else if (merchantConfigLog.customize()) {
                            content.append(description).append(":{").append(parseContent(new MerchantConfigContextDTO(value))).append("}, ");
                        } else {
                            content.append(description).append(":");
                            if (merchantConfigLog.divided() > Constants.ZERO_INTEGER) {
                                Long valueLong = (Long) value;
                                content.append(valueLong / merchantConfigLog.divided());
                            } else if (merchantConfigLog.paymentMerchant()) {
                                content.append(contextDTO.getPaymentMerchantMap().get((Long) value));
                            } else if (merchantConfigLog.paymentMerchantOffline()) {
                                content.append(contextDTO.getPaymentMerchantOfflineMap().get((Long) value));
                            } else if (merchantConfigLog.paymentBank()) {
                                content.append(contextDTO.getPaymentBankMap().get((Long) value));
                            } else if (merchantConfigLog.commonLanguage()) {
                                Map<String, Integer> commonLanguageMap = contextDTO.getCommonLanguageMap();
                                for (String key : commonLanguageMap.keySet()) {
                                    content.append(key).append(":").append(commonLanguageMap.get(key)).append(", ");
                                }
                            } else {
                                content.append(value);
                            }
                            content.append(", ");
                        }
                    }
                }
                content.setLength(content.length() - 2);
                content.append("] ");
            }
        } catch (Exception e) {
            log.error("商戶配置, 解析操作異常", e);
        }
        return content.toString();
    }

    private String parseContentForDepositAndWithdraw(MerchantConfigContextDTO contextDTO , Boolean isTrue) {
        Object merchantConfigLogData = contextDTO.getMerchantConfigLogData();
        StringBuilder content = new StringBuilder();
        try {
            Collection<?> objectCollection;
            if (merchantConfigLogData instanceof Collection) {
                objectCollection = (Collection<?>) merchantConfigLogData;
            } else {
                objectCollection = Collections.singletonList(merchantConfigLogData);
            }

            for (Object o : objectCollection) {
                content.append(isTrue?"[":"<br>[");


                Class<?> clazz = o.getClass();
                BeanInfo beanInfo = Introspector.getBeanInfo(clazz, Object.class);
                PropertyDescriptor[] pds = beanInfo.getPropertyDescriptors();

                for (PropertyDescriptor pd : pds) {
                    String displayName = pd.getName();
                    Field declaredField = clazz.getDeclaredField(displayName);

                    MerchantConfigLog merchantConfigLog = declaredField.getDeclaredAnnotation(MerchantConfigLog.class);
                    if (merchantConfigLog != null && StringUtils.isNotBlank(merchantConfigLog.text())) {
                        String description = merchantConfigLog.text();
                        Method readMethod = pd.getReadMethod();
                        Object value = readMethod.invoke(o);

                        // AUDIT打碼量配置要額外處理,因為型態是enum
                        if (MerchantConfigAuditDTO.class.isAssignableFrom(clazz)) {
                            if (SubTradeTypeEnum.class.isAssignableFrom(pd.getPropertyType())) {
                                description = SubTradeTypeEnum.valueOf(value.toString()).getDesc();
                                content.append(description).append("倍数, ");
                            } else {
                                content.append(value);
                            }
                        } else if (merchantConfigLog.customize()) {
                            if(Objects.nonNull(value)){
                                content.append(description).append(":{").append(parseContentForDepositAndWithdraw(new MerchantConfigContextDTO(value) ,true)).append("}, ");
                            }
                        } else {
                            if (merchantConfigLog.divided() > Constants.ZERO_INTEGER) {
                                content.append(description).append(":");
                                Long valueLong = (Long) value;
                                content.append(valueLong / merchantConfigLog.divided());
                                content.append(", ");
                            } else if (merchantConfigLog.paymentMerchant()) {
                                if(Objects.nonNull(contextDTO.getPaymentMerchantMap().get((Long) value))){
                                    content.append(description).append(":");
                                    content.append(contextDTO.getPaymentMerchantMap().get((Long) value));
                                    content.append(", ");
                                }
                            } else if (merchantConfigLog.paymentMerchantOffline()) {
                                if(Objects.nonNull(contextDTO.getPaymentMerchantOfflineMap().get((Long) value))){
                                    content.append(description).append(":");
                                    content.append(contextDTO.getPaymentMerchantOfflineMap().get((Long) value));
                                    content.append(", ");
                                }
                            } else if (merchantConfigLog.paymentBank()) {
                                if(Objects.nonNull(contextDTO.getPaymentBankMap().get((Long) value))){
                                    content.append(description).append(":");
                                    content.append(contextDTO.getPaymentBankMap().get((Long) value));
                                    content.append(", ");
                                }
                            } else {
                                if(Objects.nonNull(value)){
                                    content.append(description).append(":");
                                    content.append(IMG_LOG_TEXT.equals(description)?"更新了图片":value);
                                    content.append(", ");
                                }
                            }
                        }
                    }
                }
                content.setLength(content.length() - 2);
                content.append("]");
            }
        } catch (Exception e) {
            log.error("商戶配置, 解析操作異常", e);
        }
        return content.toString();
    }


    public static <T> List<Map<String, Object>> findDifferences(List<T> oldList, List<T> newList, Class<T> clazz) throws IllegalAccessException {
        List<Map<String, Object>> differences = new ArrayList<>();
        // 确保两个列表长度相同（如果不相同，你可能需要额外的逻辑来处理）
        int minSize = Math.min(newList.size(), oldList.size());
        for (int i = 0; i < minSize; i++) {
            T obj1 = oldList.get(i);
            T obj2 = newList.get(i);
            // 使用反射获取对象的所有属性
            Map<String, Object> diffMap = new HashMap<>();
            for (java.lang.reflect.Field field : clazz.getDeclaredFields()) {
                // 设置属性为可访问
                field.setAccessible(true);
                // 获取两个对象中当前属性的值
                Object value1 = field.get(obj1);
                Object value2 = field.get(obj2);
                // 比较属性值
                if (Objects.nonNull(value1)&&Objects.nonNull(value2)&&(!value1.equals(value2))) {
                    // 属性值不同，记录差异
                    diffMap.put(field.getName(), value2);
                }
            }
            // 如果diffMap不为空，则添加到差异列表中
            if (!diffMap.isEmpty()) {
                differences.add(diffMap);
            }
        }
        //新提交的集合超出的处理
        if(newList.size()>minSize){
            List<T> subNewList = newList.subList(minSize, newList.size());
//            List<Map<String, Object>> maps = BeanUtils.beansToMaps(subNewList);
//            differences.addAll(maps);
            for(T obj : subNewList){
                Map<String, Object> map = BeanUtil.beanToMap(obj);
                differences.add(map);
            }
        }
        return differences;
    }

    private static String parseDictValue(Object obj) {
        JSONConfig jsonConfig = new JSONConfig();
        jsonConfig.setWriteLongAsString(true);
        return JSONUtil.toJsonStr(obj, jsonConfig);
    }

}
