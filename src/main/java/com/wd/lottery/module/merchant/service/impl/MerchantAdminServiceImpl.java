package com.wd.lottery.module.merchant.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.BCrypt;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.common.util.GoogleAuthUtil;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.common.util.RSAUtil;
import com.wd.lottery.module.common.constants.LogSubTypeConstants;
import com.wd.lottery.module.common.constants.LogTypeConstants;
import com.wd.lottery.module.merchant.consts.AccountStatusEnum;
import com.wd.lottery.module.merchant.consts.MerchantConstants;
import com.wd.lottery.module.merchant.consts.MerchantRedisKeyConstants;
import com.wd.lottery.module.merchant.consts.ResourceTypeEnum;
import com.wd.lottery.module.merchant.dto.MerchantAdminDTO;
import com.wd.lottery.module.merchant.dto.ResourceDTO;
import com.wd.lottery.module.merchant.entity.MerchantAdmin;
import com.wd.lottery.module.merchant.entity.MerchantResource;
import com.wd.lottery.module.merchant.entity.MerchantRole;
import com.wd.lottery.module.merchant.entity.MerchantRoleResource;
import com.wd.lottery.module.merchant.mapper.MerchantAdminMapper;
import com.wd.lottery.module.merchant.param.*;
import com.wd.lottery.module.merchant.service.*;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.wd.lottery.module.merchant.param.*;
import com.wd.lottery.module.merchant.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.util.Assert;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description: 商戶登陸帳號
 *
 * <p>
 * Create on 2023/10/31.
 * <p>
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class MerchantAdminServiceImpl extends ServiceImpl<MerchantAdminMapper, MerchantAdmin>
        implements MerchantAdminService {

    @Resource
    private MerchantResourceService resourceService;
    @Resource
    private MerchantRoleService roleService;

    @Resource
    private RedisService redisService;
    @Resource
    @Lazy
    private MerchantService merchantService;


    @Value("${rsa.private-key: xxx}")
    private String rsaPrivateKey;

    @Value("${rsa.public-key: xxx}")
    private String rsaPublicKey;

    @Autowired
    private MerchantRoleResourceService merchantRoleResourceService;


    @Override
    public Page<MerchantAdminDTO> getAccountPage(MerchantAdminPageParam param) {
        Page<MerchantAdmin> page = this.page(new Page<>(param.getCurrent(), param.getSize()),
                new LambdaQueryWrapper<MerchantAdmin>()
                        .eq(Objects.nonNull(param.getStatus()), MerchantAdmin::getAdminStateEnum, param.getStatus())
                        .eq(MerchantAdmin::getMerchantId, param.getMerchantId())
                        .like(Objects.nonNull(param.getName()), MerchantAdmin::getAdminName, param.getName()));

        Page<MerchantAdminDTO> dtoPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<MerchantAdminDTO> dtoList = page.getRecords()
                .stream()
                .map(this::convert2Dto)
                .collect(Collectors.toList());

        dtoPage.setRecords(dtoList);

        return dtoPage;
    }

    @Override
    public MerchantAdmin getByAdminNameAndMerchantId(String adminName, Long merchantId, AccountStatusEnum accountStatusEnum) {
        return super.lambdaQuery()
                .eq(MerchantAdmin::getAdminName, adminName)
                .eq(MerchantAdmin::getAdminStateEnum, accountStatusEnum)
                .eq(MerchantAdmin::getMerchantId, merchantId).one();
    }

    @Override
    public String checkGoogleCode(UpdateAccountGoogleAuthParam param, String code) {
        this.validateRequestMerchantIdIsSame(param.getMerchantId(), param.getId());
        String secretKey = MerchantRedisKeyConstants.MERCHANT_GOOGLE_TOTP + param.getId();
        String secret = String.valueOf(redisService.get(secretKey));
        if (StringUtils.isBlank(secret)) {
            throw new ApiException(CommonCode.BIND_TIME_OUT);
        }

        boolean isCheck = GoogleAuthUtil.checkGoogleAuth(secret, Integer.valueOf(code));
        if (!isCheck) {
            throw new ApiException(CommonCode.BIND_GOOGLE_AUTHENTICATOR_ERROR);
        }
        return secret;
    }

    @Override
    public void updateSecret(Long id, Long merchantId, String secret) {
        boolean isUpdate = super.lambdaUpdate()
                .set(MerchantAdmin::getGaSecret, secret)
                .set(MerchantAdmin::getEnableGaEnum, EnableEnum.TRUE)
                .eq(MerchantAdmin::getId, id)
                .eq(MerchantAdmin::getAdminStateEnum, AccountStatusEnum.ENABLE)
                .eq(MerchantAdmin::getMerchantId, merchantId).update();

        if (!isUpdate) {
            throw new ApiException(CommonCode.BIND_GOOGLE_AUTHENTICATOR_ERROR);
        }
    }

    @Override
    public void googleUnBind(UpdateAccountGoogleAuthParam param) {
        this.validateRequestMerchantIdIsSame(param.getMerchantId(), param.getId());
        boolean isUpdate = super.lambdaUpdate()
                .set(MerchantAdmin::getGaSecret, "")
                .set(MerchantAdmin::getEnableGaEnum, EnableEnum.FALSE)
                .eq(MerchantAdmin::getId, param.getId())
                .eq(MerchantAdmin::getMerchantId, param.getMerchantId()).update();

        if (!isUpdate) {
            throw new ApiException(CommonCode.UN_BIND_GOOGLE_AUTHENTICATOR_ERROR);
        }
    }

    @Override
    public String createGoogleAuth(UpdateAccountGoogleAuthParam param) {
        MerchantAdmin merchantAdmin = super.getById(param.getId());
        if (merchantAdmin == null) {
            throw new ApiException(CommonCode.MERCHANT_ACCOUNT_NOT_EXISTS);
        }
        Assert.equals(merchantAdmin.getMerchantId(), param.getMerchantId());

        if (merchantAdmin.getAdminStateEnum() == AccountStatusEnum.DISABLED) {
            throw new ApiException(CommonCode.MERCHANT_ILLEGAL_STATUS);
        }
        String secret = GoogleAuthUtil.genSecretKey();
        String secretKey = MerchantRedisKeyConstants.MERCHANT_GOOGLE_TOTP + merchantAdmin.getId();

        redisService.set(secretKey, secret, MerchantRedisKeyConstants.MERCHANT_GOOGLE_TOTP_TIME_OUT_SECONDS);

        return GoogleAuthUtil.createGoogleAuthenticatorKeyUri(secret, merchantAdmin.getAdminName());
    }

    @Override
    public List<ResourceDTO> getAccountResources(Long accountId) {
        // get account role
        MerchantAdmin admin = this.getById(accountId);
        requireAccountExists(admin);

        // list role resources
        List<ResourceDTO> list;
        if (AdminTokenInfoUtil.isAdmin()) {
            list = resourceService.getResourceList();
        } else {
            list = resourceService.getResourceByRoleId(admin, true);
        }

        return list;
    }

    //    @Cacheable(cacheNames = Constants.LOCAL_CACHE_SHORT_NAME,
//            key = "T(com.wd.lottery.module.merchant.consts.MerchantConstants).ADMIN_PERMISSION_CACHE_KEY + #accountId")
    @Override
    public List<String> getAccountRights(Long accountId) {

        MerchantAdmin admin = this.getById(accountId);
        requireAccountExists(admin);

        List<ResourceDTO> list;
        if (AdminTokenInfoUtil.isAdmin()) {
            list = resourceService.getButtonList();
        } else {
            list = resourceService.getResourceByRoleId(admin, false);
        }

        List<String> permissions = list.stream()
                .filter(i -> ResourceTypeEnum.BUTTON.getCode().toString().equals(i.getType()))
                .map(ResourceDTO::getCode)
                .collect(Collectors.toList());
        log.debug("get user permissions, merchant:{}, permissions: {}", admin.getAdminName(), JSONUtil.toJsonStr(permissions));
        return permissions;
    }


    @Override
    public MerchantAdminDTO login(MerchantAdminLoginParam loginParam) {
        MerchantAdmin acc = this.lambdaQuery()
                .eq(MerchantAdmin::getAdminName, loginParam.getAccount())
                .one();
        requireAccountExists(acc);

        //校验商户的维护状态
        if(Objects.nonNull(merchantService.merchantMaintainInfoCache(acc.getMerchantId()))){
            throw new ApiException(CommonCode.MERCHANT_MAINTENANCE);
        }

        // check account status
        if (AccountStatusEnum.ENABLE != acc.getAdminStateEnum()) {
            throw new ApiException(CommonCode.MERCHANT_ILLEGAL_ACCOUNT);
        }
        // 密碼解密
        String realPasswd = RSAUtil.decode(loginParam.getPasswd(), rsaPrivateKey);

        // check passwd
        boolean validatePassword = BCrypt.checkpw(realPasswd, acc.getPassword());
        if (!validatePassword) {
            throw new ApiException(CommonCode.MERCHANT_ILLEGAL_ACCOUNT);
        }
        // google GA
        if (isGaEnabled(acc)) {
            if (StringUtils.isBlank(acc.getGaSecret())) {
                throw new ApiException(CommonCode.GOOGLE_AUTH_ERROR);
            }

            if (StringUtils.isBlank(loginParam.getGoogleCode())) {
                throw new ApiException(CommonCode.GOOGLE_AUTH_ERROR);
            }

            if (!GoogleAuthUtil.checkGoogleAuth(acc.getGaSecret(), Integer.parseInt(loginParam.getGoogleCode()))) {
                throw new ApiException(CommonCode.GOOGLE_AUTH_ERROR);
            }
        }

        MerchantAdminDTO accountDTO = new MerchantAdminDTO();
        BeanUtils.copyProperties(acc, accountDTO);

        return accountDTO;
    }

    /**
     * 新建帳號
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.BACKEND_ACCOUNT_LIST, subType = LogSubTypeConstants.CREATE,
            success = "创建帐号 账号:{{#param.adminName}} 状态:{{#param.status.desc}} 角色:{{#roleName}}")
    public Long addAccount(MerchantAdminSaveParam param) {
        // check if exists
        if (isAccountExists(param.getAdminName())) {
            throw new ApiException(CommonCode.MERCHANT_ACCOUNT_ALREADY_EXISTS);
        }
        String decodePasswd = RSAUtil.decode(param.getPassword(), rsaPrivateKey);
        param.setPassword(BCrypt.hashpw(decodePasswd));
        MerchantAdmin m = convert2Entity(param);

        LocalDateTime now = LocalDateTime.now();
        m.setCreateTime(now);
        m.setUpdateTime(now);

        this.save(m);
        MerchantRole role = roleService.getById(param.getRoleId());
        LogRecordContext.putVariable("roleName", role.getName());
        return m.getId();
    }

    /**
     * 修改密碼
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.BACKEND_ACCOUNT_LIST, subType = LogSubTypeConstants.UPDATE,
            success = "修改了帐号:{{#adminName}}的密码")
    public void updatePasswd(UpdatePasswdParam param) {
        MerchantAdmin admin = this.getById(param.getAccId());
        requireAccountExists(admin);
        LogRecordContext.putVariable("adminName", admin.getAdminName());
        // validate merchant
        AdminTokenInfoUtil.validateMerchantId(admin.getMerchantId());
        String hisPasswd = RSAUtil.decode(param.getHisPasswd(), rsaPrivateKey);
        boolean valid = BCrypt.checkpw(hisPasswd, admin.getPassword());
        if (!valid) {
            throw new ApiException(CommonCode.MERCHANT_ILLEGAL_PASSWD);
        }

        String newPasswd = RSAUtil.decode(param.getNewPasswd(), rsaPrivateKey);

        String hashpw = BCrypt.hashpw(newPasswd);
        this.lambdaUpdate()
                .set(MerchantAdmin::getPassword, hashpw)
                .set(MerchantAdmin::getUpdateTime, LocalDateTime.now())
                .eq(MerchantAdmin::getId, param.getAccId())
                .update();
    }

    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.BACKEND_ACCOUNT_LIST, subType = LogSubTypeConstants.UPDATE,
            success = "修改其他admin帐号:{{#adminName}}的密码")
    public void updateOtherAdminPasswd(UpdateOtherAdminPasswdParam param) {
        MerchantAdmin admin = this.getById(param.getAccId());
        requireAccountExists(admin);
        LogRecordContext.putVariable("adminName", admin.getAdminName());
        String newPasswd = RSAUtil.decode(param.getNewPasswd(), rsaPrivateKey);
        String hashpw = BCrypt.hashpw(newPasswd);
        this.lambdaUpdate()
                .set(MerchantAdmin::getPassword, hashpw)
                .set(MerchantAdmin::getUpdateTime, LocalDateTime.now())
                .eq(MerchantAdmin::getId, param.getAccId())
                .eq(MerchantAdmin::getMerchantId, param.getMerchantId())
                .update();
    }

    /**
     * 重置密碼
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.BACKEND_ACCOUNT_LIST, subType = LogSubTypeConstants.UPDATE,
            success = "重置了帐号:{{#adminName}}的密码")
    public void resetPasswd(ResetPasswdParam param) {

        String newPasswd = BCrypt.hashpw(RandomUtil.randomString(MerchantConstants.DEFAULT_PASSWD_LENGTH), rsaPublicKey);
        this.lambdaUpdate()
                .set(MerchantAdmin::getPassword, newPasswd)
                .set(MerchantAdmin::getUpdateTime, LocalDateTime.now())
                .eq(MerchantAdmin::getId, param.getAccId())
                .eq(MerchantAdmin::getMerchantId, param.getMerchantId())
                .update();
        MerchantAdmin admin = this.getById(param.getAccId());
        LogRecordContext.putVariable("adminName", admin.getAdminName());
    }

    /**
     * 修改帳號狀態
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.BACKEND_ACCOUNT_LIST, subType = LogSubTypeConstants.UPDATE,
            success = "更新帐号:{{#adminName}} 的状态为:{{#param.status.desc}}")
    public void updateAccountStatus(UpdateAccountStatusParam param) {

        this.lambdaUpdate()
                .set(MerchantAdmin::getAdminStateEnum, param.getStatus())
                .set(MerchantAdmin::getUpdateTime, LocalDateTime.now())
                .eq(MerchantAdmin::getId, param.getAccId())
                .eq(MerchantAdmin::getMerchantId, param.getMerchantId())
                .update();
        MerchantAdmin admin = this.getById(param.getAccId());
        LogRecordContext.putVariable("adminName", admin.getAdminName());
    }

    /**
     * 启用登陆帐号
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enableAccount(String code) {
        MerchantAdmin acc = this.lambdaQuery().eq(MerchantAdmin::getAdminName, code).one();
        requireAccountExists(acc);
        this.lambdaUpdate()
                .set(MerchantAdmin::getAdminStateEnum, AccountStatusEnum.ENABLE)
                .set(MerchantAdmin::getUpdateTime, LocalDateTime.now())
                .eq(MerchantAdmin::getId, acc.getId())
                .update();
    }

    /**
     * 启用登陆帐号
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void disableAccount(String code) {
        MerchantAdmin acc = this.lambdaQuery().eq(MerchantAdmin::getAdminName, code).one();
        requireAccountExists(acc);
        this.lambdaUpdate()
                .set(MerchantAdmin::getAdminStateEnum, AccountStatusEnum.DISABLED)
                .set(MerchantAdmin::getUpdateTime, LocalDateTime.now())
                .eq(MerchantAdmin::getId, acc.getId())
                .update();
    }

    @Override
    public MerchantAdminDTO getLoginAccount() {
        Long merchantAdminId = AdminTokenInfoUtil.getMerchantAdminId();
        return Optional.ofNullable(this.getById(merchantAdminId))
                .map(this::convert2Dto)
                .orElseThrow(() -> new ApiException(CommonCode.MERCHANT_ACCOUNT_NOT_EXISTS));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.BACKEND_ACCOUNT_LIST, subType = LogSubTypeConstants.UPDATE,
            success = "更新帐号:{{#adminName}}的角色,角色:{{#roleName}}")
    public void updateRole(UpdateAccountRoleParam param) {

        this.lambdaUpdate()
                .set(MerchantAdmin::getRoleId, param.getRoleId())
                .eq(MerchantAdmin::getId, param.getAccId())
                .eq(MerchantAdmin::getMerchantId, param.getMerchantId())
                .update();
        MerchantRole role = roleService.getById(param.getRoleId());
        LogRecordContext.putVariable("roleName", role.getName());
        MerchantAdmin admin = this.getById(param.getAccId());
        LogRecordContext.putVariable("adminName", admin.getAdminName());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String initMerchantAccount(Long merchantId, String merchantCode) {
        if (isAccountExists(merchantCode)) {
            throw new ApiException(CommonCode.MERCHANT_ACCOUNT_ALREADY_EXISTS);
        }
        String passwd = RandomUtil.randomString(MerchantConstants.DEFAULT_PASSWD_LENGTH);

        MerchantAdmin entity = new MerchantAdmin();
        entity.setMerchantId(merchantId);
        entity.setAdminName(merchantCode);
        entity.setPassword(BCrypt.hashpw(passwd));
        entity.setAdminStateEnum(AccountStatusEnum.ENABLE);
        entity.setEnableGaEnum(EnableEnum.FALSE);

        // 初始化角色
        Long roleId = roleService.initMerchantRole(merchantId, merchantCode);
        entity.setRoleId(roleId);

        LocalDateTime now = LocalDateTime.now();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);

        this.save(entity);

        return passwd;
    }

    @Override
    public boolean hasResource(Long accountId, String code) {
        // get account role
        MerchantAdmin admin = this.getById(accountId);
        requireAccountExists(admin);
        if (log.isDebugEnabled()) {
            log.debug("hasResource:{}", JacksonUtil.toJSONString(admin));
        }

        if (AdminTokenInfoUtil.isAdmin()) {
            return true;
        }

        Optional<MerchantResource> optional = this.resourceService.lambdaQuery()
                .eq(!AdminTokenInfoUtil.superAdminMerchant(admin.getMerchantId()), MerchantResource::getSuperAdminMerchantEnum, BooleanEnum.FALSE)
                .eq(MerchantResource::getCode, code)
                .oneOpt();
        Assert.isTrue(optional.isPresent());
        MerchantResource merchantResource = optional.get();
        if (log.isDebugEnabled()) {
            log.debug("hasResource:{}-{}-{}", accountId, code, JacksonUtil.toJSONString(merchantResource));
        }

        return this.merchantRoleResourceService.lambdaQuery()
                .eq(MerchantRoleResource::getRoleId, admin.getRoleId())
                .eq(MerchantRoleResource::getResourceId, merchantResource.getId())
                .exists();
    }


    private static void requireAccountExists(MerchantAdmin acc) {
        if (Objects.isNull(acc)) {
            throw new ApiException(CommonCode.MERCHANT_ACCOUNT_NOT_EXISTS);
        }
    }

    private boolean isAccountExists(String accName) {
        long count = this.count(new LambdaQueryWrapper<MerchantAdmin>().eq(MerchantAdmin::getAdminName, accName));
        return count > 0;
    }

    private boolean isGaEnabled(MerchantAdmin acc) {
        return EnableEnum.TRUE == acc.getEnableGaEnum();
    }

    private MerchantAdmin convert2Entity(MerchantAdminSaveParam param) {

        MerchantAdmin m = new MerchantAdmin();
        BeanUtils.copyProperties(param, m);

        m.setAdminName(param.getAdminName());
        m.setPassword(param.getPassword());
        m.setRoleId(param.getRoleId());
        m.setMerchantId(param.getMerchantId());
        AccountStatusEnum status = param.getStatus();
        if (Objects.isNull(status)) {
            status = AccountStatusEnum.ENABLE;
        }
        m.setAdminStateEnum(status);

        m.setEnableGaEnum(EnableEnum.FALSE);
        return m;
    }

    private MerchantAdminDTO convert2Dto(MerchantAdmin entity) {
        MerchantAdminDTO dto = new MerchantAdminDTO();
        BeanUtils.copyProperties(entity, dto);
        dto.setStatus(entity.getAdminStateEnum());
        return dto;
    }


    private void validateRequestMerchantIdIsSame(Long merchantId, Long adminId) {
        MerchantAdmin target = super.getById(adminId);
        requireAccountExists(target);
        Assert.equals(merchantId, target.getMerchantId());
    }

}
