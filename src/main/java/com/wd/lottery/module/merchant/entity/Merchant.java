package com.wd.lottery.module.merchant.entity;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.google.common.collect.Lists;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: 商戶
 * <p>
 * <p> Created by wendell on 2023/10/30.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Data
@TableName("merchant")
public class Merchant {

  @TableId(type = IdType.AUTO)
  private Long id;
  private Long pid;
  private String code;

  /**
   * merchant name, KG-663
   */
  private String mname;

  /**
   * 商户代理层级路径
   */
  private String path;

  private String currencyEnumListJson;
  private CurrencyEnum defaultCurrencyEnum;
  private String remark;
  private EnableEnum enableEnum;

  private String businessWhitelistIpJson;
  private String externalWhitelistIpJson;

  private LocalDateTime createTime;
  private LocalDateTime updateTime;

  /**
   * 开启ip规则 0-否 1-是
   */
  private EnableEnum ipRuleEnum;

  //版面
  private String layout;
  /**
   * 维护状态
   **/
  private EnableEnum maintainStatusEnum;
  /**
   * 维护结束时间
   **/
  private String maintainEndTime;
  /**
   * 维护备注
   **/
  private String maintainRemark;

  public List<CurrencyEnum> getCurrencyEnumList() {
    if (StrUtil.isBlank(currencyEnumListJson)) {
      return new ArrayList<>();
    }
    List<String> currencyEnumStrList = JSONUtil.toList(currencyEnumListJson, String.class);
    if (CollUtil.isEmpty(currencyEnumStrList)) {
      return new ArrayList<>();
    }
    List<CurrencyEnum> list = Lists.newArrayListWithCapacity(currencyEnumStrList.size());
    for (String currName : currencyEnumStrList) {
      try {
        CurrencyEnum item = CurrencyEnum.valueOf(currName);
        list.add(item);
      } catch (Exception e) {
        // ignore
      }
    }
    return list;
  }

  public boolean supportCurrencyEnum(CurrencyEnum currencyEnum) {
    List<CurrencyEnum> currencyEnumList = getCurrencyEnumList();
    return currencyEnumList.contains(currencyEnum);
  }

}
