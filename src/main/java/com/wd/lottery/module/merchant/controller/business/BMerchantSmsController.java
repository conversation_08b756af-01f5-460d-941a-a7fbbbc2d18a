package com.wd.lottery.module.merchant.controller.business;

import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.module.common.constants.CommonDictCodeEnum;
import com.wd.lottery.module.common.entity.CommonDict;
import com.wd.lottery.module.common.service.CommonDictService;
import com.wd.lottery.module.merchant.dto.*;
import com.wd.lottery.module.merchant.dto.*;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.merchant.service.MerchantSmsRecordService;
import com.wd.lottery.module.merchant.service.MerchantSmsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Tag(name = "短信配置")
@Slf4j
@RestController
@RequestMapping(value = "${business-path}/${module-path.merchant}/sms", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class BMerchantSmsController {

    private final MerchantSmsService merchantSmsService;

    private final MerchantSmsRecordService merchantSmsRecordService;

    private final CommonDictService commonDictService;

    private final MerchantConfigService merchantConfigService;

    @Operation(summary = "短信商options")
    @GetMapping("/options")
    public ApiResult<?> options() {
        List<CommonDict> commonDictList = commonDictService.findAll();
        List<CommonDict> smsOptions = commonDictList.stream()
                .filter(commonDict -> Objects.equals(commonDict.getDictCode(), CommonDictCodeEnum.SMS.getValue()))
                .filter(commonDict -> commonDict.getEnableEnum() == EnableEnum.TRUE)
                .collect(Collectors.toList());
        return ApiResult.success(smsOptions);
    }

    @Operation(summary = "查詢")
    @GetMapping("/search")
    public ApiResult<?> search() {
        return ApiResult.success(merchantSmsService.search(AdminTokenInfoUtil.getRequestMerchantIdNotNull(), AdminTokenInfoUtil.getRequestCurrencyEnumNotNull()));
    }

    @Operation(summary = "新增")
    @PostMapping("/create")
    public ApiResult<?> create(@RequestBody @Valid MerchantSmsCreateDTO merchantSmsCreateDto) {
        merchantSmsCreateDto.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        merchantSmsCreateDto.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        merchantSmsCreateDto.setCreateTime(LocalDateTime.now());
        merchantSmsCreateDto.setCreateBy(AdminTokenInfoUtil.getAdminName());
        return ApiResult.success(merchantSmsService.create(merchantSmsCreateDto));
    }

    @Operation(summary = "更新")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody @Valid MerchantSmsUpdateDTO merchantSmsUpdateDto) {
        merchantSmsUpdateDto.setUpdateTime(LocalDateTime.now());
        merchantSmsUpdateDto.setUpdateBy(AdminTokenInfoUtil.getAdminName());
        return ApiResult.success(merchantSmsService.update(merchantSmsUpdateDto));
    }

    @Operation(summary = "啟停用")
    @PostMapping("/enable")
    public ApiResult<?> enable(@RequestBody @Valid MerchantSmsEnableDTO merchantSmsEnableDto) {
        merchantSmsEnableDto.setUpdateTime(LocalDateTime.now());
        merchantSmsEnableDto.setUpdateBy(AdminTokenInfoUtil.getAdminName());
        return ApiResult.success(merchantSmsService.enable(merchantSmsEnableDto));
    }

    @Operation(summary = "刪除")
    @PostMapping("/delete")
    public ApiResult<?> update(@RequestBody @Valid MerchantSmsDeleteDTO merchantSmsDeleteDto) {
        return ApiResult.success(merchantSmsService.delete(merchantSmsDeleteDto.getId(), merchantSmsDeleteDto.getSmsTypeEnum()));
    }

    @Operation(summary = "測試")
    @PostMapping("/test")
    public ApiResult<?> test(@RequestBody @Valid MerchantSmsTestDTO merchantSmsTestDto) {
        SmsRecordDTO smsRecordDto = merchantSmsService.test(merchantSmsTestDto);
        merchantSmsRecordService.saveData(smsRecordDto, AdminTokenInfoUtil.getRequestMerchantIdNotNull(), null, AdminTokenInfoUtil.getRequestCurrencyEnumNotNull(),
                merchantSmsTestDto.getAreaCode(), merchantSmsTestDto.getMobile(), LocalDateTime.now());
        return ApiResult.success();
    }

}