package com.wd.lottery.module.merchant.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wd.lottery.module.merchant.consts.AccountStatusEnum;
import com.wd.lottery.module.merchant.dto.MerchantAdminDTO;
import com.wd.lottery.module.merchant.dto.ResourceDTO;
import com.wd.lottery.module.merchant.entity.MerchantAdmin;
import com.wd.lottery.module.merchant.param.*;
import com.wd.lottery.module.merchant.param.*;

import java.util.List;

/**
 * Description:
 *
 * <p>
 * Create on 2023/10/31.
 * <p>
 *
 * <AUTHOR>
 * @version 0.1
 */
public interface MerchantAdminService extends IService<MerchantAdmin> {
    Page<MerchantAdminDTO> getAccountPage(MerchantAdminPageParam param);

    MerchantAdmin getByAdminNameAndMerchantId(String adminName, Long merchantId, AccountStatusEnum accountStatusEnum);

    String checkGoogleCode(UpdateAccountGoogleAuthParam param, String code);

    void updateSecret(Long id, Long merchantId, String secret);

    void googleUnBind(UpdateAccountGoogleAuthParam param);

    String createGoogleAuth(UpdateAccountGoogleAuthParam param);

    List<ResourceDTO> getAccountResources(Long accountId);

    List<String> getAccountRights(Long accountId);

    MerchantAdminDTO login(MerchantAdminLoginParam loginParam);

    Long addAccount(MerchantAdminSaveParam param);

    void updatePasswd(UpdatePasswdParam param);

    void updateOtherAdminPasswd(UpdateOtherAdminPasswdParam param);

    void resetPasswd(ResetPasswdParam param);

    void updateAccountStatus(UpdateAccountStatusParam param);

    void enableAccount(String code);

    void disableAccount(String code);

    MerchantAdminDTO getLoginAccount();

    void updateRole(UpdateAccountRoleParam param);

    String initMerchantAccount(Long merchantId, String merchantCode);

    boolean hasResource(Long accountId, String code);
}
