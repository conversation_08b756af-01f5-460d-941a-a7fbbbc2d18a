package com.wd.lottery.module.merchant.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.component.auth.ApiNotAuth;
import com.wd.lottery.component.auth.GoogleVerify;
import com.wd.lottery.module.merchant.consts.AccountStatusEnum;
import com.wd.lottery.module.merchant.dto.MerchantAdminDTO;
import com.wd.lottery.module.merchant.param.*;
import com.wd.lottery.module.merchant.param.*;
import com.wd.lottery.module.merchant.service.BMerchantAdminService;
import com.wd.lottery.module.merchant.service.MerchantTokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * Description:
 *
 * <p>
 * Create on 2023/11/3.
 * <p>
 *
 * <AUTHOR>
 * @version 0.1
 */
@Tag(name = "商户帐号")
@RestController
@RequestMapping("/${business-path}/${module-path.merchant}/merchantAdmin")
public class MerchantAdminController {

    @Resource
    private BMerchantAdminService bAdminService;

    @Resource
    private MerchantTokenService merchantTokenService;

    @Value("${spring.jackson.time-zone}")
    private String timeZone;

    @Operation(summary = "登陆")
    @PostMapping(path = "login", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @ApiNotAuth
    public ApiResult<String> login(@Valid @ParameterObject MerchantAdminLoginParam loginParam, HttpServletRequest request) {
        loginParam.setGoogleCode(request.getHeader(Constants.GOOGLE_CODE));
        MerchantAdminDTO adminDTO = bAdminService.login(loginParam);
        return ApiResult.success(merchantTokenService.createToken(adminDTO));
    }

    @Operation(summary = "注销登陆")
    @PostMapping(path = "logout", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public ApiResult<String> logout(HttpServletRequest request) {

        String token = request.getHeader(Constants.BUSINESS_TOKEN_HEADER);
        merchantTokenService.clearCache(token);
        return ApiResult.success();
    }

    @Operation(summary = "分页查询帐号")
    @GetMapping("page")
    public ApiResult<Page<MerchantAdminDTO>> page(@ParameterObject MerchantAdminPageParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        Page<MerchantAdminDTO> page = bAdminService.getAccountPage(param);

        return ApiResult.success(page);
    }

    @Operation(summary = "查询当前登陆帐号")
    @GetMapping("getAccountInfo")
    public ApiResult<MerchantAdminDTO> getLoginInfo() {

        MerchantAdminDTO admin = bAdminService.getLoginAccount();
        admin.setTimeZone(timeZone);
        return ApiResult.success(admin);
    }

    @Operation(summary = "创建帐号")
    @PostMapping("create")
    public ApiResult<Long> createAccount(@Valid @RequestBody MerchantAdminSaveParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        Long accId = bAdminService.addAccount(param);
        return ApiResult.success(accId);
    }

    @Operation(summary = "修改密码")
    @PostMapping("modifyPasswd")
    public ApiResult<Boolean> modifyPasswd(@Valid @RequestBody UpdatePasswdParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        bAdminService.updatePasswd(param);

        return ApiResult.success(true);
    }

    @Operation(summary = "修改其他admin的密码")
    @PostMapping("modifyOtherAdminPasswd")
    public ApiResult<Boolean> modifyOtherAdminPasswd(@Valid @RequestBody UpdateOtherAdminPasswdParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        bAdminService.updateOtherAdminPasswd(param);

        return ApiResult.success(true);
    }


    @Operation(summary = "重置密码")
    @PostMapping("resetPasswd")
    public ApiResult<Boolean> resetPasswd(@Valid @RequestBody ResetPasswdParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        bAdminService.resetPasswd(param);

        return ApiResult.success(true);
    }

    @Operation(summary = "更新帐号角色")
    @PostMapping("updateRole")
    public ApiResult<Boolean> updateRole(@Valid @RequestBody UpdateAccountRoleParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        bAdminService.updateRole(param);
        return ApiResult.success();
    }

    @Operation(summary = "启用帐号")
    @PostMapping("enable")
    public ApiResult<Boolean> enable(@Valid @RequestBody UpdateAccountStatusParam param) {
        param.setStatus(AccountStatusEnum.ENABLE);
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        bAdminService.updateAccountStatus(param);
        return ApiResult.success();
    }

    @Operation(summary = "禁用帐号")
    @PostMapping("disable")
    public ApiResult<Boolean> disable(@Valid @RequestBody UpdateAccountStatusParam param) {
        param.setStatus(AccountStatusEnum.DISABLED);
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        bAdminService.updateAccountStatus(param);
        return ApiResult.success();
    }

    @Operation(summary = "google totp 生成url")
    @GetMapping("createGoogleAuth")
    public ApiResult<String> createGoogleAuth(@Valid @ParameterObject UpdateAccountGoogleAuthParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        return ApiResult.success(bAdminService.createGoogleAuth(param));
    }

    @Operation(summary = "google totp 綁定")
    @PostMapping("googleBind")
    public ApiResult<?> googleBind(@Valid @RequestBody UpdateAccountGoogleAuthParam param, HttpServletRequest request) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        String secret = bAdminService.checkGoogleCode(param, request.getHeader(Constants.GOOGLE_CODE));
        bAdminService.updateSecret(param.getId(), AdminTokenInfoUtil.getRequestMerchantIdNotNull(), secret);
        return ApiResult.success();
    }

    @Operation(summary = "google totp 解綁")
    @PostMapping("googleUnBind")
    @GoogleVerify
    public ApiResult<?> googleUnBind(@Valid @RequestBody UpdateAccountGoogleAuthParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        bAdminService.googleUnBind(param);
        return ApiResult.success();
    }

}
