package com.wd.lottery.module.merchant.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.module.merchant.dto.MerchantDTO;
import com.wd.lottery.module.merchant.dto.MerchantOptionDTO;
import com.wd.lottery.module.merchant.param.*;
import com.wd.lottery.module.merchant.param.*;
import com.wd.lottery.module.merchant.service.MerchantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * Description:
 *
 * <p>
 * Create on 2023/11/3.
 * <p>
 *
 * <AUTHOR>
 * @version 0.1
 */
@Tag(name = "商户模块")
@RestController
@RequestMapping("/${business-path}/${module-path.merchant}/merchant")
public class MerchantController {

    @Resource
    private MerchantService merchantService;


    @Operation(summary = "查询商户分页")
    @GetMapping("page")
    public ApiResult<Page<MerchantDTO>> page(@ParameterObject MerchantPageParam param) {

        if (Objects.isNull(param)) {
            param = new MerchantPageParam();
        }

        Page<MerchantDTO> page = merchantService.getMerchantPage(param);

        return ApiResult.success(page);
    }


    @Operation(summary = "查询商户下拉选项")
    @GetMapping("getOptions")
    public ApiResult<List<MerchantOptionDTO>> getOptions() {

        List<MerchantOptionDTO> options = merchantService.getMerchantOptions();
        return ApiResult.success(options);
    }


    @Operation(summary = "新增/开通商户")
    @PostMapping("add")
    public ApiResult<String> addMerchant(@Valid @RequestBody MerchantSaveParam param) {
        String passwd = merchantService.createMerchant(param);
        return ApiResult.success(passwd);
    }


    @Operation(summary = "更新商户信息")
    @PostMapping("update")
    public ApiResult<Boolean> updateMerchant(@Valid @RequestBody MerchantUpdateParam param) {
        merchantService.updateMerchant(param);
        return ApiResult.success();
    }


    @Operation(summary = "禁用商户")
    @PostMapping("disable")
    public ApiResult<Boolean> disable(@RequestParam @NotNull Long merchantId) {
        merchantService.disableMerchant(merchantId);
        return ApiResult.success();
    }


    @Operation(summary = "启用商户")
    @PostMapping("enable")
    public ApiResult<Boolean> enable(@RequestParam @NotNull Long merchantId) {
        merchantService.enableMerchant(merchantId);
        return ApiResult.success();
    }


    @Operation(summary = "重置 API Secret")
    @PostMapping("resetApiSecret")
    public ApiResult<Boolean> resetSecret(@RequestParam @NotNull Long merchantId) {
        merchantService.resetApiSecret(merchantId);
        return ApiResult.success();
    }

    @Operation(summary = "更新B端IP白名单")
    @PostMapping("updateBusinessWhitelist")
    public ApiResult<Boolean> updateBusinessWhitelist(@RequestBody @Valid UpdateWhitelistParam param) {
        merchantService.updateBusinessWhitelist(param);
        return ApiResult.success();
    }

    @Operation(summary = "更新E端IP白名单")
    @PostMapping("updateExternalWhitelist")
    public ApiResult<Boolean> updateExternalWhitelist(@RequestBody @Valid UpdateWhitelistParam param) {
        merchantService.updateExternalWhitelist(param);
        return ApiResult.success();
    }

    @Operation(summary = "查询商户代理下拉数据")
    @GetMapping("getMerchantAgentOptions")
    public ApiResult<List<MerchantOptionDTO>> getMerchantAgentOptions(@RequestParam(required = false) String code){
        List<MerchantOptionDTO> options = merchantService.getMerchantAgentOptions(code);

        return ApiResult.success(options);
    }

    @Operation(summary = "重新整理商户代理path", hidden = true)
    @GetMapping("resolveMerchantPath")
    public ApiResult<Boolean> resolveMerchantPath(){
        merchantService.resolveMerchantPath();
        return ApiResult.success();
    }

    @Operation(summary = "维护/恢复商户")
    @PostMapping("maintain")
    public ApiResult<Boolean> maintain(@RequestBody @Valid MerchantMaintainParam param) {
        merchantService.maintainMerchant(param);
        return ApiResult.success();
    }



}
