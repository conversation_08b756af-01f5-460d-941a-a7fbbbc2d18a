package com.wd.lottery.module.merchant.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wd.lottery.module.common.vo.IPAccessDenyRespVO;
import com.wd.lottery.module.common.vo.MaitainRespVO;
import com.wd.lottery.module.merchant.dto.MerchantCurrencyDTO;
import com.wd.lottery.module.merchant.dto.MerchantDTO;
import com.wd.lottery.module.merchant.dto.MerchantKeyVerifyInfoDTO;
import com.wd.lottery.module.merchant.dto.MerchantOptionDTO;
import com.wd.lottery.module.merchant.entity.Merchant;
import com.wd.lottery.module.merchant.param.*;
import com.wd.lottery.module.merchant.param.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Description:
 *
 * <p>
 * Create on 2023/10/31.
 * <p>
 *
 * <AUTHOR>
 * @version 0.1
 */
public interface MerchantService extends IService<Merchant> {

    Page<MerchantDTO> getMerchantPage(MerchantPageParam param);

    String createMerchant(MerchantSaveParam param);

    void updateMerchant(MerchantUpdateParam param);


    @Transactional(rollbackFor = Exception.class)
    void enableMerchant(Long mid);

    @Transactional(rollbackFor = Exception.class)
    void disableMerchant(Long mid);

    void resetApiSecret(Long merchantId);

    List<MerchantOptionDTO> getMerchantOptions();

    void updateBusinessWhitelist(UpdateWhitelistParam param);

    void updateExternalWhitelist(UpdateWhitelistParam param);

    List<Long> getEnableMerchantIdList();

    List<Merchant> getEnableMerchantList();

    MerchantKeyVerifyInfoDTO getVerifyInfo(String operatorToken);

    MerchantKeyVerifyInfoDTO loadVerifyInfo(String operatorToken);

    List<MerchantOptionDTO> getMerchantAgentOptions(String code);

    Merchant getByIdCache(Long merchantId);

    MerchantCurrencyDTO getCurrencyListByMerchantId(Long merchantId);

    MerchantCurrencyDTO getCurrencyListAndIpCurrency(Long merchantId, String ip);

    boolean judgeIsChildrenMerchant(Long pid, Long childrenId);

    void resolveMerchantPath();
    void maintainMerchant(MerchantMaintainParam param);

    MaitainRespVO merchantMaintainInfoCache(Long merchantId);

    IPAccessDenyRespVO merchantIPAccessDenyInfoCache(Long merchantId);

}
