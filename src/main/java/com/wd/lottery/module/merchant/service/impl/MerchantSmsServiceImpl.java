package com.wd.lottery.module.merchant.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.AesUtil;
import com.wd.lottery.module.common.constants.CommonDictKeyEnum;
import com.wd.lottery.module.common.constants.LogSubTypeConstants;
import com.wd.lottery.module.common.constants.LogTypeConstants;
import com.wd.lottery.module.common.entity.CommonMessage;
import com.wd.lottery.module.common.service.CommonMessageService;
import com.wd.lottery.module.merchant.consts.MerchantRedisKeyConstants;
import com.wd.lottery.module.merchant.consts.SmsTypeEnum;
import com.wd.lottery.module.merchant.dto.*;
import com.wd.lottery.module.merchant.dto.*;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.entity.MerchantSms;
import com.wd.lottery.module.merchant.mapper.MerchantSmsMapper;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.merchant.service.MerchantSmsService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.unimtx.Uni;
import com.unimtx.UniException;
import com.unimtx.UniResponse;
import com.unimtx.model.UniMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.wd.lottery.module.merchant.consts.MerchantConstants.*;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class MerchantSmsServiceImpl extends ServiceImpl<MerchantSmsMapper, MerchantSms> implements MerchantSmsService {

    @Value("${sms.error-count: 3}")
    private Integer smsErrorCount;

    @Value("${sms.time-out: 10}")
    private Long smsTimeOut;

    @Value("${sms.test: false}")
    private boolean test;

    private final RedisService redisService;

    private final MerchantConfigService merchantConfigService;

    private final CommonMessageService commonMessageService;

    private final MerchantSmsMapper merchantSmsMapper;

    /**
     * 查詢
     *
     * @param merchantId   merchant uid
     * @param currencyEnum currency enum
     * @return list dto
     */
    @Override
    public List<MerchantSmsSearchDTO> search(Long merchantId, CurrencyEnum currencyEnum) {
        LambdaQueryWrapper<MerchantSms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(MerchantSms::getId, MerchantSms::getSmsTypeEnum, MerchantSms::getDayCount, MerchantSms::getDayLimit,
                MerchantSms::getMonthCount, MerchantSms::getMonthLimit, MerchantSms::getEnableEnum);
        queryWrapper.eq(MerchantSms::getMerchantId, merchantId);
        queryWrapper.eq(MerchantSms::getCurrencyEnum, currencyEnum);
        List<MerchantSms> merchantSmsList = super.list(queryWrapper);
        if (CollectionUtil.isEmpty(merchantSmsList)) {
            return new ArrayList<>();
        }

        return BeanUtil.copyToList(merchantSmsList, MerchantSmsSearchDTO.class);
    }

    /**
     * 新增短信商
     *
     * @param merchantSmsCreateDto dto
     * @return uid
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.MERCHANT_SMS, subType = LogSubTypeConstants.CREATE,
            success = "{{#smsName}}短信商, 每日短信上限:{{#merchantSmsCreateDto.dayLimit}}, " +
                    "每月短信上限:{{#merchantSmsCreateDto.monthLimit}}, 状态:{{#enable}}")
    public Long create(MerchantSmsCreateDTO merchantSmsCreateDto) {
        MerchantSms merchantSms = BeanUtil.copyProperties(merchantSmsCreateDto, MerchantSms.class);
        merchantSms.setId(IdWorker.getId());
        merchantSms.setDayCount(Constants.ZERO_INTEGER);
        merchantSms.setMonthCount(Constants.ZERO_INTEGER);
        merchantSms.setCreateBy(merchantSmsCreateDto.getCreateBy());
        merchantSms.setUpdateBy(merchantSmsCreateDto.getCreateBy());
        merchantSms.setCreateTime(merchantSmsCreateDto.getCreateTime());
        merchantSms.setUpdateTime(merchantSmsCreateDto.getCreateTime());
        this.setSecretByCreate(merchantSms);

        boolean isSave = super.save(merchantSms);
        if (!isSave) {
            log.error("短信配置新增失敗, merchantId:{}, currencyEnum:{}, smsType:{}, secret:{}", merchantSmsCreateDto.getMerchantId(),
                    merchantSmsCreateDto.getCurrencyEnum(), merchantSmsCreateDto.getSmsTypeEnum().getDesc(), merchantSmsCreateDto.getSecret());
            throw new ApiException(CommonCode.FAILED);
        }

        LogRecordContext.putVariable("smsName", merchantSmsCreateDto.getSmsTypeEnum().getDesc());
        LogRecordContext.putVariable("enable", merchantSmsCreateDto.getEnableEnum() == EnableEnum.TRUE ? "启用" : "禁用");
        return merchantSms.getId();
    }

    /**
     * 更新
     *
     * @param merchantSmsUpdateDto dto
     * @return uid
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.MERCHANT_SMS, subType = LogSubTypeConstants.UPDATE,
            success = "{{#smsName}}短信商, 每日短信上限:{{#merchantSmsUpdateDto.dayLimit}}, " +
                    "每月短信上限:{{#merchantSmsUpdateDto.monthLimit}}, 状态:{{#enable}}, {{#secret}}")
    public Long update(MerchantSmsUpdateDTO merchantSmsUpdateDto) {
        String secret = null;
        Long id = merchantSmsUpdateDto.getId();
        MerchantSms originalMerchantSms = super.getById(id);
        if (originalMerchantSms == null) {
            throw new ApiException(CommonCode.SMS_NOT_CONFIGURED);
        }

        if (StringUtils.isNotBlank(merchantSmsUpdateDto.getSecret())) {
            secret = this.setSecretByUpdate(originalMerchantSms, merchantSmsUpdateDto);
        }

        MerchantSms merchantSms = BeanUtil.copyProperties(merchantSmsUpdateDto, MerchantSms.class);
        if (StringUtils.isNotBlank(secret)) {
            merchantSms.setSecret(secret);
        }

        super.updateById(merchantSms);
        LogRecordContext.putVariable("smsName", merchantSms.getSmsTypeEnum().getDesc());
        LogRecordContext.putVariable("secret", StringUtils.isNotBlank(secret) ? "更新密钥配置" : "");
        LogRecordContext.putVariable("enable", merchantSms.getEnableEnum() == EnableEnum.TRUE ? "启用" : "禁用");
        return id;
    }

    /**
     * 啟停用
     *
     * @param merchantSmsEnableDto dto
     * @return uid
     */
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.MERCHANT_SMS, subType = LogSubTypeConstants.UPDATE,
            success = "{{#enable}} {{#smsName}}短信商")
    public Long enable(MerchantSmsEnableDTO merchantSmsEnableDto) {
        MerchantSms merchantSms = super.getById(merchantSmsEnableDto.getId());
        if (merchantSms == null) {
            throw new ApiException(CommonCode.SMS_NOT_CONFIGURED);
        }

        if (merchantSms.getEnableEnum() != merchantSmsEnableDto.getEnableEnum()) {
            super.updateById(BeanUtil.copyProperties(merchantSmsEnableDto, MerchantSms.class));
        }

        LogRecordContext.putVariable("smsName", merchantSms.getSmsTypeEnum().getDesc());
        LogRecordContext.putVariable("enable", merchantSmsEnableDto.getEnableEnum() == EnableEnum.TRUE ? "启用" : "禁用");
        return merchantSms.getId();
    }

    /**
     * 刪除
     *
     * @param id          uid
     * @param smsTypeEnum sys type enum
     * @return uid
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.MERCHANT_SMS, subType = LogSubTypeConstants.DELETE,
            success = "{{#smsName}}短信商")
    public Long delete(Long id, SmsTypeEnum smsTypeEnum) {
        boolean isRemove = super.removeById(id);
        if (!isRemove) {
            throw new ApiException(CommonCode.FAILED);
        }

        LogRecordContext.putVariable("smsName", smsTypeEnum.getDesc());
        return id;
    }

    /**
     * 發送測試sms
     *
     * @param merchantSmsTestDto dto
     * @return dto
     */
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.MERCHANT_SMS, subType = LogSubTypeConstants.OTHER,
            success = "{{#smsName}}短信商, 短信发送测试, 区码:{{#merchantSmsTestDto.areaCode}}, 电话:{{#merchantSmsTestDto.mobile}}, 内容:{{#merchantSmsTestDto.content}}")
    public SmsRecordDTO test(MerchantSmsTestDTO merchantSmsTestDto) {
        MerchantSms merchantSms = super.getById(merchantSmsTestDto.getId());
        if (merchantSms == null) {
            throw new ApiException(CommonCode.SMS_NOT_CONFIGURED);
        }

        SmsRecordDTO smsRecordDto = this.sendSms(merchantSms, merchantSmsTestDto.getAreaCode(), String.valueOf(merchantSmsTestDto.getMobile()), merchantSmsTestDto.getContent());

        LogRecordContext.putVariable("smsName", merchantSms.getSmsTypeEnum().getDesc());
        return smsRecordDto;
    }

    /**
     * 輪詢發送sms驗證碼
     *
     * @param merchantId   merchant uid
     * @param currencyEnum currency enum
     * @param areaCode     區碼
     * @param mobile       電話
     */
    @Override
    @Transactional
    public SmsRecordDTO pollingSmsSendCode(Long merchantId, CurrencyEnum currencyEnum, String areaCode, String mobile, BigInteger ipBigInteger) {
        if (StringUtils.isBlank(areaCode) || StringUtils.isBlank(mobile)) {
            throw new ApiException(CommonCode.PLEASE_COMPLETE_PHONE_NUMBER_SETTINGS_FIRST);
        }

        String area = areaCode.replace("+", "");
        mobile = mobile.substring(area.length());
        String key = String.format(MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_CODE, area, mobile);
        String errorCountKey = String.format(MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_ERROR_COUNT, area, mobile);

        CommonMessage smsVerificationCodeTemplateMessage = commonMessageService.getSmsVerificationCodeTemplateMessage(merchantId, currencyEnum);
        if (smsVerificationCodeTemplateMessage == null) {
            log.error("無配置短信消息模板, merchantId:{}, currencyEnum:{}", merchantId, currencyEnum);
            throw new ApiException(CommonCode.FAILED);
        }

        checkMerchantSmsLimit(merchantId, currencyEnum, areaCode, mobile, ipBigInteger);

        List<MerchantSms> merchantSmsList = merchantSmsMapper.getMerchantSmsList(merchantId, currencyEnum);
        if (CollectionUtil.isEmpty(merchantSmsList)) {
            log.error("無啟用短信配置或以達每日、每月上限, merchantId:{}, currencyEnum:{}", merchantId, currencyEnum);
            throw new ApiException(CommonCode.SMS_CODE_ARRIVAL_LIMIT);
        }

        String code = (String) redisService.get(key);
        if (StrUtil.isEmpty(code)) {
            code = RandomUtil.randomNumbers(6);
            redisService.set(key, code, smsTimeOut, TimeUnit.MINUTES);
            redisService.set(errorCountKey, 0, smsTimeOut, TimeUnit.MINUTES);
        }

        String content = smsVerificationCodeTemplateMessage.getContent().replace(SMS_CODE_PLACEHOLDER, code);
        SmsRecordDTO smsRecordDto;
        if (test) {
            smsRecordDto = this.setSmsRecord(String.valueOf(IdWorker.getId()), "测试地区", content);
            smsRecordDto.setSmsTypeEnum(SmsTypeEnum.ALIBABA);
            log.info("测试短信发送, areaCode:{}, phone:{}, code:{}, content:{}", areaCode, mobile, code,content);
        } else {
            smsRecordDto = this.sendSms(merchantSmsList.get(RandomUtil.randomInt(merchantSmsList.size())), areaCode, mobile, content);
        }

        incrMerchantSmsLimit(merchantId, currencyEnum, areaCode, mobile, ipBigInteger);

        return smsRecordDto;
    }

    private void checkMerchantSmsLimit(Long merchantId, CurrencyEnum currencyEnum, String areaCode, String mobile, BigInteger ipBigInteger) {
        //记录参数列表
        log.debug("checkMerchantSmsLimit params merchantId:{}, currencyEnum:{}, areaCode:{}, mobile:{}, ipBigInteger:{}",
                merchantId, currencyEnum, areaCode, mobile, ipBigInteger);
        MerchantConfig smsSecurityConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonDictKeyEnum.COMMON_SMS_SECURITY.getValue(), currencyEnum);
        if (smsSecurityConfig == null) {
            log.debug("checkMerchantSmsLimit 未查询到商户短信安全配置");
            return;
        }

        SmsSecurityConfigDTO securityConfigDTO = smsSecurityConfig.merchantConfigListGetFirst(SmsSecurityConfigDTO.class);
        if (securityConfigDTO == null) {
            log.debug("checkMerchantSmsLimit 商户短信安全配置为null");
            return;
        }

        log.debug("checkMerchantSmsLimit config:{}", securityConfigDTO);

        if (securityConfigDTO.getIpDailyLimit() != null && securityConfigDTO.getMobileDailyLimit() > 0) {
            String mobileDailyLimitKey = String.format(MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_LIMIT,
                    String.format("%s_%s_%s",DateUtil.formatDate(new Date()), merchantId, currencyEnum.name())
                    , areaCode, mobile);
            Long mobileDailyLimit = redisService.incr(mobileDailyLimitKey, Constants.ZERO_INTEGER);
            redisService.expire(mobileDailyLimitKey, MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_LIMIT_TIME_OUT_HOURS, TimeUnit.HOURS);
            if (mobileDailyLimit != null && mobileDailyLimit >= securityConfigDTO.getMobileDailyLimit()) {
                log.debug("checkMerchantSmsLimit 超过手机号发送限制");
                throw new ApiException(CommonCode.SMS_CODE_ARRIVAL_LIMIT);
            }
        }
        //避免获取不到IP的情况下，进行了短信限制
        if (ipBigInteger == null) {
            return;
        }

        if (securityConfigDTO.getIpDailyLimit() != null && securityConfigDTO.getIpDailyLimit() > 0) {
            String ipDailyLimitKey = String.format(MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_LIMIT,
                    DateUtil.formatDate(new Date()),
                    String.format("%s_%s", merchantId, currencyEnum.name()),
                    ipBigInteger.toString());
            Long ipDailyLimit = redisService.incr(ipDailyLimitKey, Constants.ZERO_INTEGER);
            redisService.expire(ipDailyLimitKey, MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_LIMIT_TIME_OUT_HOURS, TimeUnit.HOURS);
            if (ipDailyLimit != null && ipDailyLimit >= securityConfigDTO.getIpDailyLimit()) {
                log.debug("checkMerchantSmsLimit 超过IP发送限制");
                throw new ApiException(CommonCode.SMS_CODE_ARRIVAL_LIMIT);
            }
        }
    }

    private void incrMerchantSmsLimit(Long merchantId, CurrencyEnum currencyEnum, String areaCode, String mobile, BigInteger ipBigInteger) {
        String mobileDailyLimitKey = String.format(MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_LIMIT,
                String.format("%s_%s_%s",DateUtil.formatDate(new Date()), merchantId, currencyEnum.name())
                , areaCode, mobile);
        redisService.incr(mobileDailyLimitKey, Constants.ONE_INTEGER);
        redisService.expire(mobileDailyLimitKey, MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_LIMIT_TIME_OUT_HOURS, TimeUnit.HOURS);

        if (ipBigInteger == null) {
            return;
        }

        String ipDailyLimitKey = String.format(MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_LIMIT,
                DateUtil.formatDate(new Date()),
                String.format("%s_%s", merchantId, currencyEnum.name()),
                ipBigInteger.toString());
        redisService.incr(ipDailyLimitKey, Constants.ONE_INTEGER);
        redisService.expire(ipDailyLimitKey, MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_LIMIT_TIME_OUT_HOURS, TimeUnit.HOURS);
    }

    /**
     * 檢查驗證碼
     *
     * @param merchantId   merchant uid
     * @param currencyEnum currency enum
     * @param area         區碼
     * @param mobile       電話
     * @param code         驗證碼
     * @return boolean
     */
    @Override
    public void checkCode(Long merchantId, CurrencyEnum currencyEnum, String area, String mobile, String code) {
        if (StrUtil.isBlank(area) || StrUtil.isBlank(mobile) || StrUtil.isBlank(code)) {
            log.error("用戶sms驗證失敗, area:{}, phone:{}, code:{}", area, mobile, code);
            throw new ApiException(CommonCode.PARAM_INVALID);
        }

        area = area.replace("+", "");
        mobile = mobile.substring(area.length());
        String key = String.format(MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_CODE, area, mobile);
        String errorCountKey = String.format(MerchantRedisKeyConstants.MERCHANT_SMS_VERIFICATION_ERROR_COUNT, area, mobile);
        String smsCode = (String) redisService.get(key);

        if (StringUtils.isBlank(smsCode)) {
            log.error("用戶驗證碼已失效, area:{}, phone:{}", area, mobile);
            throw new ApiException(CommonCode.SMS_CODE_ERROR);
        }

        if (!smsCode.equalsIgnoreCase(code)) {
            //同一个手机号验证三次失败则删除code，避免重复验证。 让其后续重新发送二次进行验证即可
            Long incr = redisService.incr(errorCountKey, Constants.ONE_INTEGER);
            if (incr != null && incr >= smsErrorCount) {
                redisService.del(key);
                redisService.del(errorCountKey);
                throw new ApiException(CommonCode.SMS_CODE_MAX_ERROR_COUNT);
            }
            log.debug("用戶驗證碼核验多次不正確, smsErrorCount：{}, area:{}, phone:{}", smsErrorCount,area, mobile);
            throw new ApiException(CommonCode.SMS_CODE_ERROR);
        }
        redisService.del(errorCountKey);
    }

    /**
     * 清除回調數量
     */
    @Override
    @Transactional
    public void clearDailyOrMonthCount() {
        LambdaUpdateWrapper<MerchantSms> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MerchantSms::getDayCount, Constants.ZERO_INTEGER);
        if (LocalDate.now().getDayOfMonth() == Constants.ONE_INTEGER) {
            updateWrapper.set(MerchantSms::getMonthCount, Constants.ZERO_INTEGER);
        }
        super.update(updateWrapper);
    }

    private void setSecretByCreate(MerchantSms merchantSms) {
        String secret = merchantSms.getSecret();
        SmsTypeEnum smsTypeEnum = merchantSms.getSmsTypeEnum();

        try {
            if (smsTypeEnum == SmsTypeEnum.ALIBABA) {
                SmsAlibabaDTO smsAlibabaDto = JSONUtil.toList(JSONUtil.parseArray(secret), SmsAlibabaDTO.class).get(0);
                smsAlibabaDto.setAccessKey(AesUtil.encryptBase64(smsAlibabaDto.getAccessKey()));
                smsAlibabaDto.setSecret(AesUtil.encryptBase64(smsAlibabaDto.getSecret()));
                secret = JSONUtil.toJsonStr(Collections.singletonList(smsAlibabaDto));
            } else if (smsTypeEnum == SmsTypeEnum.UNIMTX) {
                SmsUnimtxDTO smsUnimtxDto = JSONUtil.toList(JSONUtil.parseArray(secret), SmsUnimtxDTO.class).get(0);
                smsUnimtxDto.setAccessKey(AesUtil.encryptBase64(smsUnimtxDto.getAccessKey()));
                smsUnimtxDto.setSecret(AesUtil.encryptBase64(smsUnimtxDto.getSecret()));
                secret = JSONUtil.toJsonStr(Collections.singletonList(smsUnimtxDto));
            } else if (smsTypeEnum == SmsTypeEnum.ITNIOTECH) {
                SmsItniotechDTO smsItniotechDTO = JSONUtil.toList(JSONUtil.parseArray(secret), SmsItniotechDTO.class).get(0);
                smsItniotechDTO.setApiKey(AesUtil.encryptBase64(smsItniotechDTO.getApiKey()));
                smsItniotechDTO.setApiSecret(AesUtil.encryptBase64(smsItniotechDTO.getApiSecret()));
                smsItniotechDTO.setAppId(AesUtil.encryptBase64(smsItniotechDTO.getAppId()));
                secret = JSONUtil.toJsonStr(Collections.singletonList(smsItniotechDTO));
            } else if (smsTypeEnum == SmsTypeEnum.KMICLOUD) {
                SmsKmicloudDTO smsKmicloudDTO = JSONUtil.toList(JSONUtil.parseArray(secret), SmsKmicloudDTO.class).get(0);
                smsKmicloudDTO.setAccessKey(AesUtil.encryptBase64(smsKmicloudDTO.getAccessKey()));
                smsKmicloudDTO.setSecretKey(AesUtil.encryptBase64(smsKmicloudDTO.getSecretKey()));
                secret = JSONUtil.toJsonStr(Collections.singletonList(smsKmicloudDTO));
            }

            merchantSms.setSecret(secret);
        } catch (Exception e) {
            log.error("短信配置新增異常, smsType:{}, original secret:{}", merchantSms.getSmsTypeEnum().getDesc(), secret, e);
            throw new ApiException(CommonCode.FAILED);
        }
    }

    private String setSecretByUpdate(MerchantSms merchantSms, MerchantSmsUpdateDTO merchantSmsUpdateDto) {
        String entitySecret = merchantSms.getSecret();
        SmsTypeEnum smsTypeEnum = merchantSms.getSmsTypeEnum();

        try {
            if (smsTypeEnum == SmsTypeEnum.ALIBABA) {
                SmsAlibabaDTO smsAlibabaDto = JSONUtil.toList(JSONUtil.parseArray(merchantSmsUpdateDto.getSecret()), SmsAlibabaDTO.class).get(0);
                SmsAlibabaDTO ntitySmsAlibabaDto = JSONUtil.toList(JSONUtil.parseArray(entitySecret), SmsAlibabaDTO.class).get(0);
                if (StringUtils.isNotBlank(smsAlibabaDto.getAccessKey())) {
                    ntitySmsAlibabaDto.setAccessKey(AesUtil.encryptBase64(smsAlibabaDto.getAccessKey()));
                }
                if (StringUtils.isNotBlank(smsAlibabaDto.getSecret())) {
                    ntitySmsAlibabaDto.setSecret(AesUtil.encryptBase64(smsAlibabaDto.getSecret()));
                }
                entitySecret = JSONUtil.toJsonStr(Collections.singletonList(ntitySmsAlibabaDto));
            } else if (smsTypeEnum == SmsTypeEnum.UNIMTX) {
                SmsUnimtxDTO smsUnimtxDto = JSONUtil.toList(JSONUtil.parseArray(merchantSmsUpdateDto.getSecret()), SmsUnimtxDTO.class).get(0);
                SmsUnimtxDTO entitySmsUnimtxDto = JSONUtil.toList(JSONUtil.parseArray(entitySecret), SmsUnimtxDTO.class).get(0);
                if (StringUtils.isNotBlank(smsUnimtxDto.getAccessKey())) {
                    entitySmsUnimtxDto.setAccessKey(AesUtil.encryptBase64(smsUnimtxDto.getAccessKey()));
                }
                if (StringUtils.isNotBlank(smsUnimtxDto.getSecret())) {
                    entitySmsUnimtxDto.setSecret(AesUtil.encryptBase64(smsUnimtxDto.getSecret()));
                }
                entitySecret = JSONUtil.toJsonStr(Collections.singletonList(entitySmsUnimtxDto));
            } else if (smsTypeEnum == SmsTypeEnum.ITNIOTECH) {
                SmsItniotechDTO smsItniotechDTO = JSONUtil.toList(JSONUtil.parseArray(merchantSmsUpdateDto.getSecret()), SmsItniotechDTO.class).get(0);
                SmsItniotechDTO entitySmsItniotechDTO = JSONUtil.toList(JSONUtil.parseArray(entitySecret), SmsItniotechDTO.class).get(0);
                if (StringUtils.isNotBlank(smsItniotechDTO.getApiKey())) {
                    entitySmsItniotechDTO.setApiKey(AesUtil.encryptBase64(smsItniotechDTO.getApiKey()));
                }
                if (StringUtils.isNotBlank(smsItniotechDTO.getApiSecret())) {
                    entitySmsItniotechDTO.setApiSecret(AesUtil.encryptBase64(smsItniotechDTO.getApiSecret()));
                }
                if (StringUtils.isNotBlank(smsItniotechDTO.getAppId())) {
                    entitySmsItniotechDTO.setAppId(AesUtil.encryptBase64(smsItniotechDTO.getAppId()));
                }
                entitySecret = JSONUtil.toJsonStr(Collections.singletonList(entitySmsItniotechDTO));
            } else if (smsTypeEnum == SmsTypeEnum.KMICLOUD) {
                SmsKmicloudDTO smsKmicloudDTO = JSONUtil.toList(JSONUtil.parseArray(merchantSmsUpdateDto.getSecret()), SmsKmicloudDTO.class).get(0);
                SmsKmicloudDTO entitySmsKmicloudDTO = JSONUtil.toList(JSONUtil.parseArray(entitySecret), SmsKmicloudDTO.class).get(0);
                if (StringUtils.isNotBlank(smsKmicloudDTO.getAccessKey())) {
                    entitySmsKmicloudDTO.setAccessKey(AesUtil.encryptBase64(smsKmicloudDTO.getAccessKey()));
                }
                if (StringUtils.isNotBlank(smsKmicloudDTO.getSecretKey())) {
                    entitySmsKmicloudDTO.setSecretKey(AesUtil.encryptBase64(smsKmicloudDTO.getSecretKey()));
                }
                entitySecret = JSONUtil.toJsonStr(Collections.singletonList(entitySmsKmicloudDTO));
            }
        } catch (Exception e) {
            log.error("短信配置更新異常, id:{}, smsType:{}, secret:{}, new secret:{}", merchantSms.getId(), merchantSms.getSmsTypeEnum().getDesc(), entitySecret, merchantSmsUpdateDto.getSecret(), e);
            throw new ApiException(CommonCode.FAILED);
        }
        return entitySecret;
    }

    private SmsRecordDTO setSmsRecord(String messageId, String country, String content) {
        SmsRecordDTO smsRecordDto = new SmsRecordDTO();
        smsRecordDto.setMessageId(messageId);
        smsRecordDto.setCountry(country);
        smsRecordDto.setContent(content);
        return smsRecordDto;
    }

    private SmsRecordDTO sendSms(MerchantSms merchantSms, String areaCode, String mobile, String content) {
        SmsRecordDTO smsRecordDto = null;

        SmsTypeEnum smsTypeEnum = merchantSms.getSmsTypeEnum();

        if (smsTypeEnum == SmsTypeEnum.ALIBABA) {
            String alibabaPhone = areaCode.replace("+", "") + mobile;
            SmsAlibabaDTO smsAlibabaDto = JSONUtil.toList(JSONUtil.parseArray(merchantSms.getSecret()), SmsAlibabaDTO.class).get(0);
            smsAlibabaDto.setAccessKey(AesUtil.decryptStr(smsAlibabaDto.getAccessKey()));
            smsAlibabaDto.setSecret(AesUtil.decryptStr(smsAlibabaDto.getSecret()));
            smsRecordDto = this.sendSmsByAlibaba(smsAlibabaDto, alibabaPhone, content);
            smsRecordDto.setSmsTypeEnum(SmsTypeEnum.ALIBABA);
        } else if (smsTypeEnum == SmsTypeEnum.UNIMTX) {
            String unimtxPhone = areaCode + mobile;
            SmsUnimtxDTO smsUnimtxDto = JSONUtil.toList(JSONUtil.parseArray(merchantSms.getSecret()), SmsUnimtxDTO.class).get(0);
            smsUnimtxDto.setAccessKey(AesUtil.decryptStr(smsUnimtxDto.getAccessKey()));
            smsUnimtxDto.setSecret(AesUtil.decryptStr(smsUnimtxDto.getSecret()));
            smsRecordDto = this.sendSmsByUnimtx(smsUnimtxDto, unimtxPhone, content);
            smsRecordDto.setSmsTypeEnum(SmsTypeEnum.UNIMTX);
        } else if (smsTypeEnum == SmsTypeEnum.ITNIOTECH) {
            String unimtxPhone = areaCode.replace("+", "") + mobile;
            SmsItniotechDTO smsItniotechDTO = JSONUtil.toList(JSONUtil.parseArray(merchantSms.getSecret()), SmsItniotechDTO.class).get(0);
            smsItniotechDTO.setApiKey(AesUtil.decryptStr(smsItniotechDTO.getApiKey()));
            smsItniotechDTO.setApiSecret(AesUtil.decryptStr(smsItniotechDTO.getApiSecret()));
            smsItniotechDTO.setAppId(AesUtil.decryptStr(smsItniotechDTO.getAppId()));
            smsRecordDto = this.sendSmsByItniotech(smsItniotechDTO, unimtxPhone, content);
            smsRecordDto.setSmsTypeEnum(SmsTypeEnum.ITNIOTECH);
        } else if (smsTypeEnum == SmsTypeEnum.KMICLOUD) {
            String kmicloudPhone = areaCode.replace("+", "") + mobile;
            SmsKmicloudDTO smsKmicloudDTO = JSONUtil.toList(JSONUtil.parseArray(merchantSms.getSecret()), SmsKmicloudDTO.class).get(0);
            smsKmicloudDTO.setAccessKey(AesUtil.decryptStr(smsKmicloudDTO.getAccessKey()));
            smsKmicloudDTO.setSecretKey(AesUtil.decryptStr(smsKmicloudDTO.getSecretKey()));
            smsRecordDto = this.sendSmsByKmicloud(smsKmicloudDTO, kmicloudPhone, content);
            smsRecordDto.setSmsTypeEnum(SmsTypeEnum.KMICLOUD);
        }

        return smsRecordDto;
    }

    /**
     * 阿里雲短信發送
     *
     * @param smsAlibabaDto dto
     * @param phone         發送電話, 國碼+電話
     * @param content       發送內容
     */
    private SmsRecordDTO sendSmsByAlibaba(SmsAlibabaDTO smsAlibabaDto, String phone, String content) {
        CommonResponse response;

        DefaultProfile profile = DefaultProfile.getProfile("ap-southeast-1", smsAlibabaDto.getAccessKey(), smsAlibabaDto.getSecret());
        IAcsClient client = new DefaultAcsClient(profile);

        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.ap-southeast-1.aliyuncs.com");
        request.setSysVersion("2018-05-01");
        request.setSysAction("SendMessageToGlobe");
        request.putQueryParameter("RegionId", "ap-southeast-1");
        request.putQueryParameter("To", phone);
        request.putQueryParameter("Message", content);
        try {
            response = client.getCommonResponse(request);
            log.info("阿里雲短信發送response:{}", response.getData());
        } catch (Exception e) {
            log.error("阿里雲短信發送異常", e);
            throw new ApiException(CommonCode.FAILED);
        }

        JSONObject data = JSONUtil.parseObj(response.getData());
        String code = data.getStr("ResponseCode");
        if (!code.equals("OK")) {
            throw new ApiException(CommonCode.FAILED);
        }

        JSONObject detail = data.getJSONObject("NumberDetail");

        return this.setSmsRecord(data.getStr("MessageId"), detail.getStr("Country"), content);
    }

    /**
     * Unimtx短信發送
     *
     * @param smsUnimtxDto dto
     * @param phone        發送電話, 國碼+電話
     * @param content      發送內容
     */
    private SmsRecordDTO sendSmsByUnimtx(SmsUnimtxDTO smsUnimtxDto, String phone, String content) {
        Uni.init(smsUnimtxDto.getAccessKey(), smsUnimtxDto.getSecret());
        UniResponse res;

        UniMessage message = UniMessage.build()
                .setTo(phone)
                .setText(content);

        try {
            res = message.send();
            log.info("Unimtx短信發送response, http status:{}, code:{}, data:{}", res.status, res.code, res.data);
        } catch (UniException e) {
            log.error("Unimtx短信發送異常", e);
            throw new ApiException(CommonCode.FAILED);
        }

        if (!res.code.equals("0")) {
            throw new ApiException(CommonCode.FAILED);
        }

        JSONObject jsonObject = JSONUtil.parseObj(res.data.toString());
        JSONArray messages = jsonObject.getJSONArray("messages");
        JSONObject data = JSONUtil.parseObj(messages.get(0).toString());

        return this.setSmsRecord(data.getStr("id"), data.getStr("iso"), content);
    }

    private SmsRecordDTO sendSmsByItniotech(SmsItniotechDTO smsItniotechDTO, String phone, String content) {
        final String url = ITNIOTECH_SMS_URL;
        HttpRequest request = HttpRequest.post(url);
        final String datetime = String.valueOf(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());
        String apiKey = smsItniotechDTO.getApiKey();
        String apiSecret = smsItniotechDTO.getApiSecret();
        String appId = smsItniotechDTO.getAppId();

        final String sign = SecureUtil.md5(apiKey.concat(apiSecret).concat(datetime));
        request.header(Header.CONNECTION, "Keep-Alive")
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .header("Sign", sign)
                .header("Timestamp", datetime)
                .header("Api-Key", apiKey);

        final String params = JSONUtil.createObj()
                .set("appId", appId)
                .set("numbers", phone)
                .set("content", content)
                .toString();


        HttpResponse response = null;
        try {
            response = request.body(params).execute();
        } catch (Exception e) {
            log.error("Itniotech短信發送異常", e);
            throw new ApiException(CommonCode.FAILED);
        }
        if (!response.isOk()) {
            log.warn("Itniotech短信發送失敗:{}", response.body());
            throw new ApiException(CommonCode.FAILED);
        }

        String body = response.body();
        log.debug("Itniotech短信發送response:{}", body);
        JSONObject data = JSONUtil.parseObj(body);
        Integer status = data.getInt("status");
        if (!Objects.equals(status, 0)) {
            log.warn("Itniotech短信發送失敗:{}", body);
            throw new ApiException(CommonCode.FAILED);
        }

        JSONArray array = data.getJSONArray("array");
        if (array == null || array.size() == 0) {
            log.warn("Itniotech短信發送失敗:{}", body);
            throw new ApiException(CommonCode.FAILED);
        }
        //该平台运营商接口返回没有区域，此处数据记录为空字符串
        return this.setSmsRecord(array.getJSONObject(0).getStr("msgId"), "", content);
    }

    private SmsRecordDTO sendSmsByKmicloud(SmsKmicloudDTO smsKmicloudDTO, String phone, String content) {
        HttpRequest request = HttpRequest.post(KMICLOUD_SMS_URL);
        String accessKey = smsKmicloudDTO.getAccessKey();
        String secretKey = smsKmicloudDTO.getSecretKey();
        request.header(Header.CONNECTION, "Keep-Alive")
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .header(Header.ACCEPT,"application/json");

        final String params = JSONUtil.createObj()
                .set("accessKey", accessKey)
                .set("secretKey", secretKey)
                .set("to",phone)
                .set("message", content)
                .toString();
        log.debug("Kmicloud短信發送params:{}", params);

        HttpResponse response;
        try {
            response = request.body(params).execute();
        } catch (Exception e) {
            log.error("Kmicloud短信發送異常", e);
            throw new ApiException(CommonCode.FAILED);
        }

        String body = response.body();
        log.debug("Kmicloud短信發送response:{}", body);
        JSONObject data = JSONUtil.parseObj(body);
        Integer status = data.getInt("code");
        if (!Objects.equals(status, 200)) {
            log.warn("Kmicloud短信發送失敗:{}", body);
            throw new ApiException(CommonCode.FAILED);
        }
        JSONObject detail = data.getJSONObject("result");
        //该平台运营商接口返回没有区域，此处数据记录为空字符串
        return this.setSmsRecord(detail.getStr("smsId"), "", content);
    }

}
