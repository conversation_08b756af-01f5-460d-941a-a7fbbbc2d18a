package com.wd.lottery.module.merchant.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.exception.AccessDenyException;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.RequestUtil;
import com.wd.lottery.module.common.util.MerchantIpRegularValidator;
import com.wd.lottery.module.merchant.dto.MerchantAdminDTO;
import com.wd.lottery.module.merchant.param.*;
import com.wd.lottery.module.merchant.param.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description:
 *
 * <p> Created on 2024/8/2.
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class BMerchantAdminService {

    @Resource
    private MerchantAdminService adminService;

    @Autowired
    private MerchantIpRegularValidator merchantIpRegularValidator;

    public Page<MerchantAdminDTO> getAccountPage(MerchantAdminPageParam param){
        return adminService.getAccountPage(param);
    }

    public MerchantAdminDTO login(MerchantAdminLoginParam loginParam){
        MerchantAdminDTO login = adminService.login(loginParam);
        // 拦截IP规则
        try {
            merchantIpRegularValidator.validateIpRule(login.getMerchantId(), RequestUtil.getRequestIpFromRequest(), null, true);
        } catch (AccessDenyException e) {
            //前端反馈B端不用处理，此处将其转换为APIException
            throw new ApiException(e.getErrorCode(), e.getMessage());
        }
        return login;
    }

    public MerchantAdminDTO getLoginAccount(){
        return adminService.getLoginAccount();
    }

    public Long addAccount(MerchantAdminSaveParam param){
        return adminService.addAccount(param);
    }

    public void updatePasswd(UpdatePasswdParam param){
        adminService.updatePasswd(param);
    }

    public void updateOtherAdminPasswd(UpdateOtherAdminPasswdParam param){
        adminService.updateOtherAdminPasswd(param);
    }

    public void resetPasswd(ResetPasswdParam param){
        adminService.resetPasswd(param);
    }

    public void updateRole(UpdateAccountRoleParam param){
        adminService.updateRole(param);
    }

    public void updateAccountStatus(UpdateAccountStatusParam param){
        adminService.updateAccountStatus(param);
    }

    public String createGoogleAuth(UpdateAccountGoogleAuthParam param){
        return adminService.createGoogleAuth(param);
    }

    public String checkGoogleCode(UpdateAccountGoogleAuthParam param, String code){
        return adminService.checkGoogleCode(param, code);
    }

    public void updateSecret(Long id, Long merchantId, String secret){
        adminService.updateSecret(id, merchantId, secret);
    }

    public void googleUnBind(UpdateAccountGoogleAuthParam param){
        adminService.googleUnBind(param);
    }

}
