package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wd.lottery.common.annotation.MasterOnly;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.dto.ActivitySafeMemberWalletDTO;
import com.wd.lottery.module.activity.dto.ActivitySafeMemberWalletOperateDTO;
import com.wd.lottery.module.activity.dto.ActivitySafePerTransferOutDTO;
import com.wd.lottery.module.activity.dto.ActivitySafeRevenueAnalysisDTO;
import com.wd.lottery.module.activity.entity.*;
import com.wd.lottery.module.activity.entity.*;
import com.wd.lottery.module.activity.mapper.ActivitySafeAmountRecordMapper;
import com.wd.lottery.module.activity.param.ActivityParam;
import com.wd.lottery.module.activity.param.safe.ActivitySafeBatchTransferParam;
import com.wd.lottery.module.activity.param.safe.ActivitySafeConfigParam;
import com.wd.lottery.module.activity.param.safe.ActivitySafeRecordParam;
import com.wd.lottery.module.activity.param.safe.ActivitySafeWalletParam;
import com.wd.lottery.module.activity.param.vip.ActivityVipConfigParam;
import com.wd.lottery.module.activity.param.vip.ActivityVipParam;
import com.wd.lottery.module.activity.service.*;
import com.wd.lottery.module.activity.service.*;
import com.wd.lottery.module.cash.constatns.SubTradeTypeEnum;
import com.wd.lottery.module.cash.constatns.TradeTypeEnum;
import com.wd.lottery.module.cash.dto.CashMemberWalletOperateDTO;
import com.wd.lottery.module.cash.entity.CashAudit;
import com.wd.lottery.module.cash.service.CashAuditService;
import com.wd.lottery.module.cash.service.CashMemberWalletService;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivitySafeServiceImpl implements ActivitySafeService {

    @Resource
    private RedisService redisService;

    @Resource
    private ActivityVipMemberService activityVipMemberService;

    @Resource
    private ActivityService activityService;

    @Resource
    private ActivityRecordService activityRecordService;

    @Resource
    private CashMemberWalletService cashMemberWalletService;

    @Resource
    private ActivitySafeMemberWalletService activitySafeMemberWalletService;

    @Resource
    private ActivitySafeAmountRecordMapper activitySafeAmountRecordMapper;

    @Resource
    private CashAuditService cashAuditService;

    @Resource(name = Constants.SAFE_WALLET_BATCH_TRANSFER_OUT_THREAD_POOL)
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private TransactionDefinition transactionDefinition;

    @Override
    public void checkParam(ActivityParam activityParam) {
        ActivitySafeConfigParam activitySafeConfigParam = activityParam.getActivityParam(ActivitySafeConfigParam.class);
        Assert.notNull(activitySafeConfigParam);
        Assert.notNull(activitySafeConfigParam.getAmount());
        Assert.notNull(activitySafeConfigParam.getRate());

        //金额最小 100 最大 10000
        Assert.isTrue(activitySafeConfigParam.getAmount() >= 10000 && activitySafeConfigParam.getAmount() <= 1000000);
        //利率最小 0 最大 10000
        Assert.isTrue(activitySafeConfigParam.getRate() >= 0 && activitySafeConfigParam.getRate() <= 10000);
    }

    @Override
    public ActivitySafePerTransferOutDTO getPreTransferOutInfo(ActivitySafeWalletParam activitySafeWalletParam) {
        Long merchantId = activitySafeWalletParam.getMemberTokenInfoDTO().getMerchantId();
        Long memberId = activitySafeWalletParam.getMemberTokenInfoDTO().getId();

        Long outAmount = activitySafeWalletParam.getAmount();
        ActivitySafeMemberWalletDTO safeWallet = activitySafeMemberWalletService.getByMemberId(merchantId, memberId);
        if (safeWallet.getCashBalance() < outAmount) {
            throw new ApiException(CommonCode.CASH_MEMBER_WALLET_BALANCE_NOT_ENOUGH);
        }

        CurrencyEnum currencyEnum = activitySafeWalletParam.getMemberTokenInfoDTO().getCurrencyEnum();
        //此方法已做非空判断，如果是 null 会抛出异常
        Activity activity = getSafeActivityNotNull(currencyEnum, merchantId);

        ActivitySafeConfigParam safeConfigParam = activity.activityParamToBean(ActivitySafeConfigParam.class);
        //下列情况，理论不可能发生
        checkSafeConfigParam(activitySafeWalletParam, safeConfigParam);

        //获取当前用户保险箱金额变动记录
        List<ActivitySafeAmountRecord> safeAmountRecords = getSafeAmountRecords(merchantId, memberId);

        DateTime settleTime = DateUtil.parseDateTime(activitySafeWalletParam.getSettleTime());
        checkSettleTime(settleTime);

        //利率奖金要按照当前所有存入金额进行计算
        Result result = computeRewardAmount(safeAmountRecords, settleTime, memberId, activitySafeWalletParam.getAmount(), activity.getId());
        Long rewardAmount = result.rewardAmount;

        CashAudit cashAudit = cashAuditService.findById(memberId, merchantId);

        Long vipRate = getMemberVIPSafeRate(memberId, merchantId, currencyEnum);

        return ActivitySafePerTransferOutDTO.builder()
                .amount(safeWallet.getCashBalance())
                .rate(safeConfigParam.getRate())
                .vipRate(vipRate)
                .income(rewardAmount)
                .auditBalance(cashAudit.getAuditBalance())
                .build();
    }

    @MasterOnly
    @Override
    public ActivitySafeRevenueAnalysisDTO getRevenueAnalysis(Long merchantId, Long memberId, CurrencyEnum currencyEnum) {
        List<ActivitySafeAmountRecord> safeAmountRecords = getSafeAmountRecords(merchantId, memberId);
        Activity activity = getSafeActivityNotNull(currencyEnum, merchantId);
        Result result = computeRewardAmount(safeAmountRecords, DateUtil.date(), memberId, 0L, activity.getId());
        //累计收益
        Long rewardAmount = result.rewardAmount;
        Long allIncome = activitySafeMemberWalletService.getMemberTotalIncome(merchantId, memberId);
        ActivitySafeConfigParam safeConfigParam = activity.activityParamToBean(ActivitySafeConfigParam.class);
        Long vipSafeRate = getMemberVIPSafeRate(memberId, merchantId, currencyEnum);

        return new ActivitySafeRevenueAnalysisDTO(rewardAmount, allIncome, safeConfigParam.getRate(), vipSafeRate, result.avgRate, result.avgVipRate);
    }

    @Override
    public void extraOperateByUpdateEnable(ActivityParam param) {
        String locKey = String.format(ActivityRedisKeyConstants.ACTIVITY_SAFEBOX_BATCH_TRANSFER_LOCK_KEY,param.getMerchantId(),param.getCurrencyEnum());
        if (redisService.hasKey(locKey)) {
            throw new ApiException(CommonCode.ACTIVITY_SAFEBOX_BATCH_TRANSFER_OUT_ING);
        }
    }

    @Override
    public void batchTransferOut(ActivitySafeBatchTransferParam activitySafeBatchTransferParam) {
        //此操作需要在活动禁用下进行,先判断活动是否禁用
        Activity activity = checkActivity(activitySafeBatchTransferParam);
        //控制操作操作频率,至少锁20分钟
        String locKey = String.format(ActivityRedisKeyConstants.ACTIVITY_SAFEBOX_BATCH_TRANSFER_LOCK_KEY,activitySafeBatchTransferParam.getMerchantId(),activitySafeBatchTransferParam.getCurrencyEnum());
        redisService.frequencyControl(locKey, ActivityRedisKeyConstants.ACTIVITY_SAFEBOX_BATCH_TRANSFER_LOCK_SECONDS);
        Map<Long, ActivitySafeMemberWallet> activitySafeMemberWalletMap = getSafeMemberWalletMap(activitySafeBatchTransferParam);
        if (CollectionUtil.isEmpty(activitySafeMemberWalletMap)) {
            return;
        }
        List<Long> memberIds = new ArrayList<>(activitySafeMemberWalletMap.keySet());
        Map<Long,List<ActivitySafeAmountRecord>> safeAmountRecordMap = getSafeAmountRecordsMap(memberIds, activitySafeBatchTransferParam);
        LocalDateTime settleTime = LocalDateTime.now();

        threadPoolTaskExecutor.execute(() -> {
            try {
                for (Long memberId : activitySafeMemberWalletMap.keySet()) {
                    // 获取成员钱包信息
                    ActivitySafeMemberWallet memberWallet = activitySafeMemberWalletMap.get(memberId);
                    if (Objects.isNull(memberWallet)) {
                        continue;
                    }
                    if (CollectionUtil.isEmpty(safeAmountRecordMap.get(memberId))) {
                        continue;
                    }
                    TransactionStatus status = transactionManager.getTransaction(transactionDefinition);
                    try {
                        ActivitySafeWalletParam walletParam = buildWalletParam(activitySafeBatchTransferParam, memberWallet, settleTime);
                        batchOperateSafeWalletOut(walletParam, activity, memberWallet, safeAmountRecordMap.get(memberId));
                        transactionManager.commit(status);
                    } catch (Exception e) {
                        transactionManager.rollback(status);
                        log.error(String.format("batchTransferOutError..memberInfo:%s", memberWallet), e);
                    }
                }
            } catch (Exception e) {
                log.error("batchTransferOutError", e);
            } finally {
                redisService.del(locKey);
            }
        });
    }

    private void batchOperateSafeWalletOut(ActivitySafeWalletParam walletParam, Activity activity, ActivitySafeMemberWallet memberWallet, List<ActivitySafeAmountRecord> activitySafeAmountRecords) {
        //获取当前用户保险箱余额
        Long merchantId = walletParam.getMemberTokenInfoDTO().getMerchantId();
        Long memberId = walletParam.getMemberTokenInfoDTO().getId();
        //总提出金额
        Long outAmount = walletParam.getAmount();
        if (memberWallet.getCashBalance() < outAmount) {
            throw new ApiException(CommonCode.CASH_MEMBER_WALLET_BALANCE_NOT_ENOUGH);
        }
        DateTime settleTime = DateUtil.parseDateTime(walletParam.getSettleTime());
        //checkSettleTime(settleTime);

        //利率奖金要按照当前所有存入金额进行计算
        Result result = computeRewardAmount(activitySafeAmountRecords, settleTime, memberId, outAmount, activity.getId());

        log.debug("奖金计算日志：{}", result.rewardAmountLog);
        Long rewardAmount = result.rewardAmount;

        outAmount = settleOutAmount(activitySafeAmountRecords, merchantId, outAmount, settleTime);


        //操作 保险箱金额&用户金额
        ActivityRecord activityRecord = activityRecordService.initActivityRecord(walletParam.getMemberTokenInfoDTO(), activity,
                SubActivityTypeEnum.SAFE_REWARD, LocalDateTime.now(), walletParam.getRequestIp());
        activityRecord.setAmount(walletParam.getAmount());
        activityRecord.setActivityStatusEnum(ActivityStatusEnum.RECEIVED);
        activityRecord.setUid(getUid(activityRecord));
        Long safeWalletAmount = operateSafeWallet(activityRecord, rewardAmount, SubTradeTypeEnum.ACTIVITY_SAFE_IN);
        saveActivityRecord(activityRecord,walletParam,rewardAmount,result,safeWalletAmount);
        saveWalletRecord(walletParam, activityRecord, result, rewardAmount, safeWalletAmount);
    }

    private ActivityRecord saveActivityRecord(ActivityRecord activityRecord,ActivitySafeWalletParam walletParam, Long rewardAmount, Result result,Long safeWalletAmount) {
        operateWallet(activityRecord, SubTradeTypeEnum.ACTIVITY_SAFE_IN);
        activityRecord.setAmount(rewardAmount);
        activityRecord.setUid(getUid(activityRecord, SubTradeTypeEnum.ACTIVITY_SAFE_REWARD));
        if (rewardAmount > 0) {
            operateWallet(activityRecord, SubTradeTypeEnum.ACTIVITY_SAFE_REWARD);
        }

        ActivitySafeRecordParam recordParam = ActivitySafeRecordParam.builder()
                .rate(result.avgRate)
                .vipRate(result.avgVipRate)
                .amount(walletParam.getAmount())
                .income(rewardAmount)
                .tradeTypeEnum(TradeTypeEnum.OUT)
                .safeAmount(safeWalletAmount)
                .build();
        activityRecord.setRecordJson(JSONUtil.toJsonStr(recordParam));
        activityRecordService.save(activityRecord);
        return activityRecord;
    }

    private Long settleOutAmount(List<ActivitySafeAmountRecord> activitySafeAmountRecords, Long merchantId, Long outAmount, DateTime settleTime) {
        //更新提现金额&最新利率
        for (ActivitySafeAmountRecord safeAmountRecord : activitySafeAmountRecords) {
            Long recordAmount = safeAmountRecord.getAmount();
            Long recordWithdrawAmount = safeAmountRecord.getWithdrawAmount();

            LambdaUpdateWrapper<ActivitySafeAmountRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ActivitySafeAmountRecord::getId, safeAmountRecord.getId());
            updateWrapper.eq(ActivitySafeAmountRecord::getMerchantId, merchantId);
            updateWrapper.eq(ActivitySafeAmountRecord::getWithdrawAmount, recordWithdrawAmount);
            if (recordWithdrawAmount + outAmount <= recordAmount) {
                //提现金额小于等于当前记录金额
                SafeAmountRecordStatusEnum status = recordWithdrawAmount + outAmount == recordAmount ?
                        SafeAmountRecordStatusEnum.ALL_RECEIVED : SafeAmountRecordStatusEnum.NOT_RECEIVED;

                updateWrapper.set(ActivitySafeAmountRecord::getWithdrawAmount, recordWithdrawAmount + outAmount);
                updateWrapper.set(ActivitySafeAmountRecord::getUpdateTime, DateUtil.toLocalDateTime(settleTime));
                updateWrapper.set(ActivitySafeAmountRecord::getStatusEnum, status);

                outAmount = Constants.ZERO_LONG;
            } else {
                //提现金额大于当前记录金额
                updateWrapper.set(ActivitySafeAmountRecord::getWithdrawAmount, recordAmount);
                updateWrapper.set(ActivitySafeAmountRecord::getUpdateTime, DateUtil.toLocalDateTime(settleTime));
                updateWrapper.set(ActivitySafeAmountRecord::getStatusEnum, SafeAmountRecordStatusEnum.ALL_RECEIVED);

                outAmount -= (recordAmount - recordWithdrawAmount);
            }
            int updateCount = activitySafeAmountRecordMapper.update(updateWrapper);
            if (updateCount == Constants.ZERO_LONG) {
                throw new ApiException(CommonCode.FREQUENT_REQUESTS, "保险箱提现操作频繁，请稍后再试");
            }
            if (ObjectUtil.equals(Constants.ZERO_LONG,outAmount)) {
                break;
            }
        }
        if (outAmount > Constants.ZERO_INTEGER) {
            throw new ApiException(CommonCode.PARAM_INVALID, "提现金额超过存入金额");
        }
        return outAmount;
    }

    private void saveWalletRecord(ActivitySafeWalletParam walletParam, ActivityRecord activityRecord, Result result, Long rewardAmount, Long safeWalletAmount) {
        ActivitySafeAmountRecord safeAmountRecord = ActivitySafeAmountRecord.builder()
                .merchantId(activityRecord.getMerchantId())
                .recordId(activityRecord.getId())
                .memberId(activityRecord.getMemberId())
                .memberName(activityRecord.getMemberName())
                .tradeTypeEnum(TradeTypeEnum.OUT)
                .currencyEnum(activityRecord.getCurrencyEnum())
                .amount(walletParam.getAmount())
                .rate(result.avgRate)
                .vipRate(result.avgVipRate)
                .withdrawAmount(0L)
                .income(rewardAmount)
                .safeAmount(safeWalletAmount)
                .statusEnum(SafeAmountRecordStatusEnum.ALL_RECEIVED)
                .createTime(activityRecord.getCreateTime())
                .updateTime(activityRecord.getCreateTime())
                .build();
        activitySafeAmountRecordMapper.insert(safeAmountRecord);
    }

    private Map<Long, List<ActivitySafeAmountRecord>> getSafeAmountRecordsMap(List<Long> memberIds, ActivitySafeBatchTransferParam activitySafeBatchTransferParam) {
        List<ActivitySafeAmountRecord> activitySafeAmountRecords = activitySafeAmountRecordMapper.selectList(new LambdaQueryWrapper<ActivitySafeAmountRecord>()
                .eq(ActivitySafeAmountRecord::getMerchantId, activitySafeBatchTransferParam.getMerchantId())
                .in(ActivitySafeAmountRecord::getMemberId, memberIds)
                .eq(ActivitySafeAmountRecord::getStatusEnum, SafeAmountRecordStatusEnum.NOT_RECEIVED)
                .eq(ActivitySafeAmountRecord::getTradeTypeEnum, TradeTypeEnum.IN));
        if (CollectionUtil.isEmpty(activitySafeAmountRecords)) {
            return Collections.emptyMap();
        }
        return activitySafeAmountRecords.stream().collect(Collectors.groupingBy(ActivitySafeAmountRecord::getMemberId));
    }

    private Map<Long, ActivitySafeMemberWallet> getSafeMemberWalletMap(ActivitySafeBatchTransferParam activitySafeBatchTransferParam) {
        List<ActivitySafeMemberWallet> list = activitySafeMemberWalletService.list(new LambdaQueryWrapper<ActivitySafeMemberWallet>()
                .eq(ActivitySafeMemberWallet::getMerchantId, activitySafeBatchTransferParam.getMerchantId())
                .eq(ActivitySafeMemberWallet::getCurrencyEnum, activitySafeBatchTransferParam.getCurrencyEnum())
                .ge(ActivitySafeMemberWallet::getCashBalance, Constants.ZERO_LONG));
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(ActivitySafeMemberWallet::getId, Function.identity()));
    }

    private ActivitySafeWalletParam buildWalletParam(ActivitySafeBatchTransferParam activitySafeBatchTransferParam,ActivitySafeMemberWallet memberWallet, LocalDateTime settleTime) {
        ActivitySafeWalletParam walletParam = new ActivitySafeWalletParam();
        walletParam.setAmount(memberWallet.getCashBalance());
        walletParam.setTradeTypeEnum(TradeTypeEnum.OUT);
        walletParam.setSettleTime(settleTime.format(DateTimeFormatter.ofPattern(Constants.DEFAULT_DATE_TIME_PATTERN)));
        walletParam.setRequestIp(activitySafeBatchTransferParam.getRequestIp());
        MemberTokenInfoDTO memberTokenInfoDTO = new MemberTokenInfoDTO();
        memberTokenInfoDTO.setId(memberWallet.getId());
        memberTokenInfoDTO.setMemberName(memberWallet.getMemberName());
        memberTokenInfoDTO.setMerchantId(activitySafeBatchTransferParam.getMerchantId());
        walletParam.setMemberTokenInfoDTO(memberTokenInfoDTO);
        return walletParam;
    }

    private Activity checkActivity(ActivitySafeBatchTransferParam activitySafeBatchTransferParam) {
        Activity one = activityService.getOne(activitySafeBatchTransferParam.getMerchantId(), activitySafeBatchTransferParam.getCurrencyEnum(), ActivityTypeEnum.SAFE);
        if (Objects.isNull(one) || ObjectUtil.equal(one.getEnableEnum(), EnableEnum.TRUE)) {
            throw new ApiException(CommonCode.ACTIVITY_SAFEBOX_ENABLE);
        }
        return one;
    }

    private static void checkSettleTime(DateTime settleTime) {
        DateTime currentDate = DateUtil.date();
        Long diffNowMinutes = DateUtil.between(settleTime, currentDate, DateUnit.SECOND);
        //前端结算时间和当前时间相差超过60s，不允许提现
        if (settleTime.compareTo(currentDate) > 0 || diffNowMinutes > 60) {
            throw new ApiException(CommonCode.PARAM_INVALID, "当前时间为:" + DateUtil.formatDateTime(currentDate));
        }
    }

    private List<ActivitySafeAmountRecord> getSafeAmountRecords(Long merchantId, Long memberId) {
        return activitySafeAmountRecordMapper.selectList(new LambdaQueryWrapper<ActivitySafeAmountRecord>()
                .eq(ActivitySafeAmountRecord::getMerchantId, merchantId)
                .eq(ActivitySafeAmountRecord::getMemberId, memberId)
                .eq(ActivitySafeAmountRecord::getStatusEnum, SafeAmountRecordStatusEnum.NOT_RECEIVED)
                .eq(ActivitySafeAmountRecord::getTradeTypeEnum, TradeTypeEnum.IN)
                .orderByAsc(ActivitySafeAmountRecord::getId)
        );
    }

    private Long getSafeAmountRecordsAllIncome(Long merchantId, Long memberId) {
        ActivitySafeAmountRecord amountRecord = activitySafeAmountRecordMapper.selectOne(new QueryWrapper<ActivitySafeAmountRecord>()
                .select("ifnull(sum(income), 0) as income")
                .eq("merchant_id", merchantId)
                .eq("member_id", memberId)
                .eq("status_enum", SafeAmountRecordStatusEnum.ALL_RECEIVED)
                .eq("trade_type_enum", TradeTypeEnum.OUT)
        );
        return amountRecord == null || amountRecord.getIncome() == null ? 0L : amountRecord.getIncome();
    }

    private Activity getSafeActivityNotNull(CurrencyEnum currencyEnum, Long merchantId) {
        return activityService.getEnableOneNotNull(merchantId, currencyEnum, ActivityTypeEnum.SAFE);
    }

    private Long getMemberVIPSafeRate(Long memberId, Long merchantId, CurrencyEnum currencyEnum) {
        Integer vipLevel = null;
        try {
            ActivityVipMember activityVipMember = activityVipMemberService.getOneByMemberIdAndMerchantIdNotNull(memberId, merchantId);
            vipLevel = activityVipMember.getVipLevel();
        } catch (Exception e) {
        }

        if (vipLevel == null) {
            return 0L;
        }

        Activity activity = null;
        try {
            activity = activityService.getEnableOneNotNull(merchantId, currencyEnum, ActivityTypeEnum.VIP);
        } catch (Exception e) {
        }
        if (activity == null) {
            return 0L;
        }

        ActivityVipParam activityVipParam = activity.activityParamToBean(ActivityVipParam.class);
        if (activityVipParam == null || CollectionUtil.isEmpty(activityVipParam.getActivityVipConfigParamList())) {
            return 0L;
        }

        for (ActivityVipConfigParam activityVipConfigParam : activityVipParam.getActivityVipConfigParamList()) {
            if (activityVipConfigParam.getVipLevel().equals(vipLevel)) {
                return activityVipConfigParam.getIncreaseSafeBoxRate();
            }
        }

        return 0L;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operateSafeWallet(ActivitySafeWalletParam param) {
        Long memberId = param.getMemberTokenInfoDTO().getId();
        Long merchantId = param.getMemberTokenInfoDTO().getMerchantId();
        CurrencyEnum currencyEnum = param.getMemberTokenInfoDTO().getCurrencyEnum();
        //此方法已做非空判断，如果是 null 会抛出异常
        Activity activity = getSafeActivityNotNull(currencyEnum, merchantId);

        ActivitySafeConfigParam safeConfigParam = activity.activityParamToBean(ActivitySafeConfigParam.class);
        //下列情况，理论不可能发生
        checkSafeConfigParam(param, safeConfigParam);

        //控制操作操作频率， 60s 一次
        redisService.frequencyControl(ActivityRedisKeyConstants.ACTIVITY_FREQUENCY_LOCK_KEY + memberId +
                activity.getId() + ActivityTypeEnum.SAFE + SubActivityTypeEnum.SAFE_REWARD, ActivityRedisKeyConstants.ACTIVITY_FREQUENCY_LOCK_SECONDS);

        Long vipRate = getMemberVIPSafeRate(memberId, merchantId, currencyEnum);

        //TODO 需要再加一个Lock操作，避免意外情况
        if (param.getTradeTypeEnum() == TradeTypeEnum.IN) {
            cashAuditService.checkAllowWithdraw(memberId, merchantId);
            operateSafeWalletIn(param, activity, safeConfigParam, vipRate);
        } else {
            operateSafeWalletOut(param, activity, safeConfigParam, vipRate);
        }
    }

    private static void checkSafeConfigParam(ActivitySafeWalletParam activitySafeWalletParam, ActivitySafeConfigParam safeConfigParam) {
        Assert.notNull(safeConfigParam);
        Assert.isTrue(safeConfigParam.getAmount() >= 10000 && safeConfigParam.getAmount() <= 1000000);
        Assert.isTrue(safeConfigParam.getRate() >= 0 && safeConfigParam.getRate() <= 10000);

        if (activitySafeWalletParam.getAmount() < safeConfigParam.getAmount()) {
            throw new ApiException(CommonCode.PARAM_INVALID, "金额不能低于每份金额， 每份金额为" + safeConfigParam.getAmount());
        }

        if (activitySafeWalletParam.getAmount() % safeConfigParam.getAmount() != 0) {
            throw new ApiException(CommonCode.PARAM_INVALID, "金额必须为每份金额的整数倍， 每份金额为" + safeConfigParam.getAmount());
        }
    }

    private void operateSafeWalletIn(ActivitySafeWalletParam walletParam, Activity activity, ActivitySafeConfigParam configParam, Long vipRate) {
        ActivityRecord activityRecord = activityRecordService.initActivityRecord(walletParam.getMemberTokenInfoDTO(), activity,
                SubActivityTypeEnum.SAFE_REWARD, LocalDateTime.now(), walletParam.getRequestIp());
        activityRecord.setAmount(walletParam.getAmount());
        activityRecord.setActivityStatusEnum(ActivityStatusEnum.RECEIVED);
        activityRecord.setUid(getUid(activityRecord));

        //操作 保险箱金额&用户金额
        operateWallet(activityRecord, SubTradeTypeEnum.ACTIVITY_SAFE_OUT);
        Long safeWalletAmount = operateSafeWallet(activityRecord, 0L, SubTradeTypeEnum.ACTIVITY_SAFE_OUT);
        ActivitySafeAmountRecord safeAmountRecord = ActivitySafeAmountRecord.builder()
                .merchantId(activityRecord.getMerchantId())
                .recordId(activityRecord.getId())
                .memberId(activityRecord.getMemberId())
                .memberName(activityRecord.getMemberName())
                .tradeTypeEnum(TradeTypeEnum.IN)
                .currencyEnum(activityRecord.getCurrencyEnum())
                .amount(walletParam.getAmount())
                .rate(configParam.getRate())
                .vipRate(vipRate)
                .withdrawAmount(0L)
                .income(0L)
                .safeAmount(safeWalletAmount)
                .statusEnum(SafeAmountRecordStatusEnum.NOT_RECEIVED)
                .createTime(activityRecord.getCreateTime())
                .updateTime(activityRecord.getCreateTime())
                .channelId(walletParam.getMemberTokenInfoDTO().getChannelId())
                .isDirect(walletParam.getMemberTokenInfoDTO().getIsDirect())
                .build();
        activitySafeAmountRecordMapper.insert(safeAmountRecord);

    }

    private void operateSafeWalletOut(ActivitySafeWalletParam walletParam, Activity activity, ActivitySafeConfigParam configParam, Long vipRate) {
        //获取当前用户保险箱余额
        Long merchantId = walletParam.getMemberTokenInfoDTO().getMerchantId();
        Long memberId = walletParam.getMemberTokenInfoDTO().getId();
        //总提出金额
        Long outAmount = walletParam.getAmount();
        ActivitySafeMemberWalletDTO safeWallet = activitySafeMemberWalletService.getByMemberId(merchantId, memberId);

        if (safeWallet.getCashBalance() < outAmount) {
            throw new ApiException(CommonCode.CASH_MEMBER_WALLET_BALANCE_NOT_ENOUGH);
        }

        //获取当前用户保险箱金额变动记录
        List<ActivitySafeAmountRecord> safeAmountRecords = getSafeAmountRecords(merchantId, memberId);

        DateTime settleTime = DateUtil.parseDateTime(walletParam.getSettleTime());
        checkSettleTime(settleTime);

        //利率奖金要按照当前所有存入金额进行计算

        Result result = computeRewardAmount(safeAmountRecords, settleTime, memberId, outAmount, activity.getId());
        log.debug("奖金计算日志：{}", result.rewardAmountLog);
        Long rewardAmount = result.rewardAmount;

        //更新提现金额&最新利率
        for (ActivitySafeAmountRecord safeAmountRecord : safeAmountRecords) {
            Long recordAmount = safeAmountRecord.getAmount();
            Long recordWithdrawAmount = safeAmountRecord.getWithdrawAmount();

            LambdaUpdateWrapper<ActivitySafeAmountRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ActivitySafeAmountRecord::getId, safeAmountRecord.getId());
            updateWrapper.eq(ActivitySafeAmountRecord::getMerchantId, merchantId);
            updateWrapper.eq(ActivitySafeAmountRecord::getWithdrawAmount, recordWithdrawAmount);
            if (recordWithdrawAmount + outAmount <= recordAmount) {
                //提现金额小于等于当前记录金额
                SafeAmountRecordStatusEnum status = recordWithdrawAmount + outAmount == recordAmount ?
                        SafeAmountRecordStatusEnum.ALL_RECEIVED : SafeAmountRecordStatusEnum.NOT_RECEIVED;

                updateWrapper.set(ActivitySafeAmountRecord::getWithdrawAmount, recordWithdrawAmount + outAmount);
                updateWrapper.set(ActivitySafeAmountRecord::getUpdateTime, DateUtil.toLocalDateTime(settleTime));
                updateWrapper.set(ActivitySafeAmountRecord::getStatusEnum, status);

                outAmount = 0L;
            } else {
                //提现金额大于当前记录金额
                updateWrapper.set(ActivitySafeAmountRecord::getWithdrawAmount, recordAmount);
                updateWrapper.set(ActivitySafeAmountRecord::getUpdateTime, DateUtil.toLocalDateTime(settleTime));
                updateWrapper.set(ActivitySafeAmountRecord::getStatusEnum, SafeAmountRecordStatusEnum.ALL_RECEIVED);

                outAmount -= (recordAmount - recordWithdrawAmount);
            }
            int updateCount = activitySafeAmountRecordMapper.update(updateWrapper);
            if (updateCount == 0) {
                throw new ApiException(CommonCode.FREQUENT_REQUESTS, "保险箱提现操作频繁，请稍后再试");
            }
            if (outAmount == 0) {
                break;
            }
        }

        if (outAmount > 0) {
            throw new ApiException(CommonCode.PARAM_INVALID, "提现金额超过存入金额");
        }

        //操作 保险箱金额&用户金额
        ActivityRecord activityRecord = activityRecordService.initActivityRecord(walletParam.getMemberTokenInfoDTO(), activity,
                SubActivityTypeEnum.SAFE_REWARD, LocalDateTime.now(), walletParam.getRequestIp());
        activityRecord.setAmount(walletParam.getAmount());
        activityRecord.setActivityStatusEnum(ActivityStatusEnum.RECEIVED);
        activityRecord.setUid(getUid(activityRecord));

        Long safeWalletAmount = operateSafeWallet(activityRecord, rewardAmount, SubTradeTypeEnum.ACTIVITY_SAFE_IN);
        operateWallet(activityRecord, SubTradeTypeEnum.ACTIVITY_SAFE_IN);
        activityRecord.setAmount(rewardAmount);
        activityRecord.setUid(getUid(activityRecord, SubTradeTypeEnum.ACTIVITY_SAFE_REWARD));
        if (rewardAmount > 0) {
            operateWallet(activityRecord, SubTradeTypeEnum.ACTIVITY_SAFE_REWARD);
        }

        ActivitySafeRecordParam recordParam = ActivitySafeRecordParam.builder()
                .rate(result.avgRate)
                .vipRate(result.avgVipRate)
                .amount(walletParam.getAmount())
                .income(rewardAmount)
                .tradeTypeEnum(TradeTypeEnum.OUT)
                .safeAmount(safeWalletAmount)
                .build();
        activityRecord.setRecordJson(JSONUtil.toJsonStr(recordParam));
        activityRecordService.save(activityRecord);

        ActivitySafeAmountRecord safeAmountRecord = ActivitySafeAmountRecord.builder()
                .merchantId(activityRecord.getMerchantId())
                .recordId(activityRecord.getId())
                .memberId(activityRecord.getMemberId())
                .memberName(activityRecord.getMemberName())
                .tradeTypeEnum(TradeTypeEnum.OUT)
                .currencyEnum(activityRecord.getCurrencyEnum())
                .amount(walletParam.getAmount())
                .rate(result.avgRate)
                .vipRate(result.avgVipRate)
                .withdrawAmount(0L)
                .income(rewardAmount)
                .safeAmount(safeWalletAmount)
                .statusEnum(SafeAmountRecordStatusEnum.ALL_RECEIVED)
                .createTime(activityRecord.getCreateTime())
                .updateTime(activityRecord.getCreateTime())
                .channelId(walletParam.getMemberTokenInfoDTO().getChannelId())
                .isDirect(walletParam.getMemberTokenInfoDTO().getIsDirect())
                .build();
        activitySafeAmountRecordMapper.insert(safeAmountRecord);

    }

    private @NotNull Result computeRewardAmount(List<ActivitySafeAmountRecord> safeAmountRecords,
                                                       DateTime settleTime, Long memberId, Long outAmount, Long activityId) {
        Long rewardAmount = 0L;
        String rewardAmountLog = "用户：" + memberId + " 提现金额：" + outAmount + " 结算时间：" + settleTime + "\n";
        Long avgRate = 0L;
        Long avgVipRate = 0L;
        for (ActivitySafeAmountRecord safeAmountRecord : safeAmountRecords) {
            Long recordId = safeAmountRecord.getRecordId();
            Long amount = safeAmountRecord.getAmount() - safeAmountRecord.getWithdrawAmount();
            Long rate = safeAmountRecord.getRate() + safeAmountRecord.getVipRate();
            Date depositTime = Date.from(safeAmountRecord.getUpdateTime().atZone(ZoneId.systemDefault()).toInstant());

            Long diffMinutes = DateUtil.between(depositTime, settleTime, DateUnit.MINUTE);
            //防止极端情况产生的负数
            diffMinutes = diffMinutes < 0 ? 0 : diffMinutes;
            //配置的利率为日利率，实际计算时需按照分钟进行
            Long currentRewardAmount = BigDecimal.valueOf(rate / 24D / 60D * diffMinutes).divide(BigDecimal.valueOf(10000), 4, RoundingMode.DOWN)
                    .multiply(BigDecimal.valueOf(amount)).longValue();
            rewardAmount += currentRewardAmount;

            rewardAmountLog += "依赖存入：" + recordId + " 存入金额：" + amount + " 利率：" + rate + " 存入时间：" + depositTime + " 奖金：" + currentRewardAmount + "\n";
            avgRate += safeAmountRecord.getRate();
            avgVipRate += safeAmountRecord.getVipRate();
        }

        Result result = new Result(rewardAmount, rewardAmountLog, avgRate == 0 ? 0 : avgRate / safeAmountRecords.size(), avgVipRate == 0 ? 0 : avgVipRate / safeAmountRecords.size());
        return result;
    }

    private static class Result {
        public final Long rewardAmount;
        public final String rewardAmountLog;
        public final Long avgRate;
        private final Long avgVipRate;

        public Result(Long rewardAmount, String rewardAmountLog, Long avgRate, Long avgVipRate) {
            this.rewardAmount = rewardAmount;
            this.rewardAmountLog = rewardAmountLog;
            this.avgRate = avgRate;
            this.avgVipRate = avgVipRate;
        }
    }


    @Override
    public ActivityTypeEnum getActivityTypeEnum() {
        return ActivityTypeEnum.SAFE;
    }

    private void operateWallet(ActivityRecord activityRecord, SubTradeTypeEnum subTradeTypeEnum) {
        CashMemberWalletOperateDTO cashMemberWalletOperateDTO = new CashMemberWalletOperateDTO();
        cashMemberWalletOperateDTO.setMerchantId(activityRecord.getMerchantId());
        cashMemberWalletOperateDTO.setMemberId(activityRecord.getMemberId());
        cashMemberWalletOperateDTO.setPayNo(activityRecord.getUid());
        cashMemberWalletOperateDTO.setSubTradeTypeEnum(subTradeTypeEnum);
        cashMemberWalletOperateDTO.setOperateTime(activityRecord.getCreateTime());
        cashMemberWalletOperateDTO.setAmount(activityRecord.getAmount());
        cashMemberWalletService.operateWallet(cashMemberWalletOperateDTO);
    }

    private Long operateSafeWallet(ActivityRecord activityRecord, Long income, SubTradeTypeEnum subTradeTypeEnum) {
        ActivitySafeMemberWalletOperateDTO cashMemberWalletOperateDTO = new ActivitySafeMemberWalletOperateDTO();
        cashMemberWalletOperateDTO.setMerchantId(activityRecord.getMerchantId());
        cashMemberWalletOperateDTO.setMemberId(activityRecord.getMemberId());
        cashMemberWalletOperateDTO.setMemberName(activityRecord.getMemberName());
        cashMemberWalletOperateDTO.setSubTradeTypeEnum(subTradeTypeEnum);
        cashMemberWalletOperateDTO.setPayNo(activityRecord.getUid());
        cashMemberWalletOperateDTO.setAmount(activityRecord.getAmount());
        cashMemberWalletOperateDTO.setCurrencyEnum(activityRecord.getCurrencyEnum());
        cashMemberWalletOperateDTO.setIncome(income);
        return activitySafeMemberWalletService.operateWallet(cashMemberWalletOperateDTO);
    }

}
