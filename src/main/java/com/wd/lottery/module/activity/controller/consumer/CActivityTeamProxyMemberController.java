package com.wd.lottery.module.activity.controller.consumer;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.util.MemberTokenInfoUtil;
import com.wd.lottery.module.activity.constants.SearchDateTypeEnum;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.param.ActivityTeamProxyRecentAddLowerLevelUserParam;
import com.wd.lottery.module.activity.param.TeamProxyBonusStatParam;
import com.wd.lottery.module.activity.param.TeamProxyLowerMemberStatParam;
import com.wd.lottery.module.activity.service.ActivityTeamProxyBonusService;
import com.wd.lottery.module.activity.service.ActivityTeamProxyLowerLevelMemberService;
import com.wd.lottery.module.activity.service.ActivityTeamProxyService;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.service.MemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("${consumer-path}/${module-path.activity}/teamProxy")
@Tag(name = "团队代理模块")
public class CActivityTeamProxyMemberController {

    @Resource
    private ActivityTeamProxyService activityTeamProxyService;
    @Resource
    private ActivityTeamProxyBonusService activityTeamProxyBonusService;
    @Resource
    private ActivityTeamProxyLowerLevelMemberService activityTeamProxyLowerLevelMemberService;
    @Resource
    private MemberService memberService;

    @Operation(summary = "查询会员团队代理主页信息")
    @GetMapping(value = "getTeamProxyBonusStat")
    public ApiResult<TeamProxyMemberYesterdayStatDTO> getTeamProxyBonusStat() {
        return ApiResult.success(
                activityTeamProxyLowerLevelMemberService
                        .getTeamProxyLowerLevelMemberStat(MemberTokenInfoUtil.getMemberTokenInfo()));
    }

    @Operation(summary = "查询会员团队代理佣金明细")
    @GetMapping(value = "getTeamProxyBonus")
    public ApiResult<List<TeamProxyPlatformBonusDTO>> getTeamProxyBonus(@RequestParam(required = false) Integer level,
                                                                        @RequestParam(required = false) LocalDate periods,
                                                                        @RequestParam(required = false) GameCategoryEnum gameCategoryEnum) {

        TeamProxyBonusStatParam param = new TeamProxyBonusStatParam();
        param.setMemberId(MemberTokenInfoUtil.getMemberId());
        param.setMerchantId(MemberTokenInfoUtil.getMerchantId());
        param.setCurrencyEnum(MemberTokenInfoUtil.getCurrencyEnum());
        param.setLevel(level);
        param.setPeriods(periods);
        param.setGameCategoryEnum(gameCategoryEnum);

        return ApiResult.success(activityTeamProxyBonusService.getTeamProxyBonusStatByUser(param));
    }

    @Operation(summary = "查询会员团队代理下级统计信息")
    @GetMapping(value = "getTeamProxyLowerLevelUserStat")
    public ApiResult<TeamProxyLowerLevelMemberStatDTO> getTeamProxyLowerLevelUserStat(@RequestParam(required = false) Integer level,
                                                                                      @RequestParam(required = false) String periods,
                                                                                      @RequestParam(required = false) Long memberId) {

        TeamProxyLowerMemberStatParam param = new TeamProxyLowerMemberStatParam();
        param.setMemberTokenInfoDTO(MemberTokenInfoUtil.getMemberTokenInfo());
        param.setLevel(level);
        param.setPeriods(periods);
        param.setSubMemberId(memberId);

        return ApiResult.success(activityTeamProxyLowerLevelMemberService.getTeamProxyLowerLevelUserStat(param));
    }

    @Operation(summary = "查询会员团队代理下级分页信息")
    @GetMapping(value = "getTeamProxyLowerLevelUserBonusPage")
    public ApiResult<Page<TeamProxyLowerLevelMemberBonusDTO>> getTeamProxyLowerLevelUserBonusPage(@RequestParam(required = false) Integer level,
                                                                                                  @RequestParam(required = false) String periods,
                                                                                                  @RequestParam(required = false) Long memberId,
                                                                                                  @RequestParam Integer page,
                                                                                                  @RequestParam Integer limit
    ) {
        TeamProxyLowerMemberStatParam param = new TeamProxyLowerMemberStatParam();
        param.setMemberTokenInfoDTO(MemberTokenInfoUtil.getMemberTokenInfo());
        param.setLevel(level);
        param.setPeriods(periods);
        param.setSubMemberId(memberId);
        param.setPage(page);
        param.setLimit(limit);

        return ApiResult.success(activityTeamProxyLowerLevelMemberService.getTeamProxyLowerLevelUserByPage(param));
    }

    @Operation(summary = "查询会员团队代理近期新增用户")
    @GetMapping(value = "getTeamProxyLowerLevelUserPage")
    public ApiResult<Page<TeamProxyRecentAddLowerLevelUserDTO>> getTeamProxyLowerLevelUserPage(@RequestParam(required = false) SearchDateTypeEnum dateType,
                                                                                               @RequestParam Long current,
                                                                                               @RequestParam Long size
    ) {
        ActivityTeamProxyRecentAddLowerLevelUserParam param = new ActivityTeamProxyRecentAddLowerLevelUserParam();
        param.setHighMemberId(MemberTokenInfoUtil.getMemberId());
        param.setMerchantId(MemberTokenInfoUtil.getMerchantId());
        param.setCurrent(current);
        param.setSize(size);
        if (dateType == null || dateType == SearchDateTypeEnum.TODAY) {
            param.setStartTime(LocalDateTimeUtil.of(DateUtil.beginOfDay(new Date())));
            param.setEndTime(LocalDateTimeUtil.of(DateUtil.endOfDay(new Date())));
        } else if (dateType == SearchDateTypeEnum.YESTERDAY) {
            param.setStartTime(LocalDateTimeUtil.of(DateUtil.beginOfDay(DateUtil.yesterday())));
            param.setEndTime(LocalDateTimeUtil.of(DateUtil.endOfDay(DateUtil.yesterday())));
        } else if (dateType == SearchDateTypeEnum.MONTH) {
            param.setStartTime(LocalDateTimeUtil.of(DateUtil.beginOfMonth(DateUtil.yesterday())));
            param.setEndTime(LocalDateTimeUtil.of(DateUtil.endOfDay(new Date())));
        } else {
            return ApiResult.failed(CommonCode.PARAM_INVALID);
        }

        return ApiResult.success(activityTeamProxyLowerLevelMemberService.getTeamProxyRecentAddLowerLevelUserByPage(param));
    }
}
