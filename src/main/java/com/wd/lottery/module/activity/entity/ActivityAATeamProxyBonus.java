package com.wd.lottery.module.activity.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("activity_aa_team_proxy_bonus")
public class ActivityAATeamProxyBonus implements Serializable {
    private Long id;
    private Long merchantId;//站点id
    private Long memberId;//派发用户id
    private String memberName; //派发用户名

    private Integer level; //统计层级

    private LocalDate periods;//期号， 结算日期  20220111  yyyyMMdd

    private CurrencyEnum currencyEnum;

    private GameCategoryEnum gameCategoryEnum;
    private Integer betUserCount = 0;
    private Long betAmount = 0L;

    private LocalDateTime createTime; //创建时间


    private Long teamProxyBonus = 0L;
    private Integer rate = 0;
    private Integer teamLevel;

}
