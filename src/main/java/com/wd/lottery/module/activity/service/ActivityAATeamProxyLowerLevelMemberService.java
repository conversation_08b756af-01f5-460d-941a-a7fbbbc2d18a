package com.wd.lottery.module.activity.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.entity.ActivityAATeamProxyLowerLevelMember;
import com.wd.lottery.module.activity.param.AAActivityTeamProxyStatQueryParam;
import com.wd.lottery.module.activity.param.ActivityTeamProxyLowerLevelUserQueryParam;
import com.wd.lottery.module.activity.param.ActivityTeamProxyRecentAddLowerLevelUserParam;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;

import java.util.Set;

public interface ActivityAATeamProxyLowerLevelMemberService extends IService<ActivityAATeamProxyLowerLevelMember> {

    TeamProxyMemberHomePageDataDTO getHomePageData(MemberTokenInfoDTO dto);

    TeamProxyRecentAddLowerLevelUserStatDTO getTeamProxyLowerLevelUserStat(ActivityTeamProxyRecentAddLowerLevelUserParam param);

    Page<TeamProxyRecentAddLowerLevelUserDTO> getTeamProxyRecentAddLowerLevelUserByPage(ActivityTeamProxyLowerLevelUserQueryParam param);

    Page<TeamProxyLowerLevelMemberBonusDTO> getTeamProxyLowerLevelUserByPage(ActivityTeamProxyLowerLevelUserQueryParam param);

    AATeamProxyMemberStatDTO getTeamProxyLowerLevelMemberStat(AAActivityTeamProxyStatQueryParam param);

    /**
     * 批量查询已经有过充值记录的用户
     * @param merchantId 商户ID
     * @param memberIds 用户ID集合
     * @return 已经有过充值记录的用户ID集合
     */
    Set<Long> getHistoricalRechargeMembers(Long merchantId, Set<Long> memberIds);

}
