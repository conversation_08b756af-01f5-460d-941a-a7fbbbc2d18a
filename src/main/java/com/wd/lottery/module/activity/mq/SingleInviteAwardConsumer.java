package com.wd.lottery.module.activity.mq;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.RabbitMQConstants;
import com.wd.lottery.common.util.CommonUtil;
import com.wd.lottery.module.activity.constants.ActivityTypeEnum;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.entity.ActivitySingleInviteRecord;
import com.wd.lottery.module.activity.param.invite.ActivityInviteParam;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.activity.service.ActivitySingleInviteRecordService;
import com.wd.lottery.module.cash.dto.CashDataStatisticsDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.report.constans.ReportMemberGroupEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 单人次邀请奖励、被邀请奖励处理
 */
@Component
@Slf4j
public class SingleInviteAwardConsumer {

    @Resource
    private ActivitySingleInviteRecordService singleInviteRecordService;
    @Resource
    private ActivityService activityService;
    @Resource
    private MemberService memberService;
    @Resource(name = "caffeineCacheManager")
    private CacheManager cacheManager;


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = RabbitMQConstants.ACTIVITY_SINGLE_INVITE_AWARD_QUEUE, arguments = @Argument(name = RabbitMQConstants.X_SINGLE_ACTIVE_CONSUMER, value = RabbitMQConstants.TRUE_STR, type = RabbitMQConstants.BOOLEAN_TYPE_STR)),
            exchange = @Exchange(value = RabbitMQConstants.CASH_DATA_STATISTICS_AGGREGATE_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = {RabbitMQConstants.CASH_DATA_STATISTICS_AGGREGATE_CASH_WALLET_LOG_HIS_ROUTE_KEY, RabbitMQConstants.CASH_DATA_STATISTICS_AGGREGATE_CASH_BET_ORDER_ALL_ROUTE_KEY}))
    @Transactional
    public void activitySingleInviteProcess(Channel channel, Message message) {
        String msg = CommonUtil.decompress(message.getBody());
        if (log.isDebugEnabled()) {
            log.debug("activitySingleInviteProcess  msg:{}", msg);
        }
        MessageProperties properties = message.getMessageProperties();
        try {
            List<CashDataStatisticsDTO> dataList = JSONUtil.toBean(msg, new TypeReference<List<CashDataStatisticsDTO>>() {
            }, true);

            if (CollectionUtil.isEmpty(dataList)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            //0. 首先过滤数据是否符合要求
            dataList = dataList.stream()
                    .filter(it ->
                            (it.getSubTradeTypeEnum() != null &&ReportMemberGroupEnum.DEPOSIT_AMOUNT.equals(it.getSubTradeTypeEnum().getReportMemberGroupEnum()) &&
                                    it.getAmount() > 0L) ||
                                    (it.getBetAmount() != null && it.getBetAmount() > 0)).collect(Collectors.toList());
            if (CollUtil.isEmpty(dataList)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 1.有开启活动且活动时间范围有效的才累积充值
            List<Long> merchantIds = Lists.newArrayList();
            List<CurrencyEnum> currencyEnums = Lists.newArrayList();
            dataList.forEach(item -> {
                CollUtil.addIfAbsent(merchantIds, item.getMerchantId());
                CollUtil.addIfAbsent(currencyEnums, item.getCurrencyEnum());
            });
            List<Activity> availableActivities = this.getAvailableActivities(merchantIds, currencyEnums);
            dataList = this.filterAvailableData(dataList, availableActivities);
            if (CollUtil.isEmpty(dataList)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 2.查询activity_single_invite_record改成批量查询
            // 根据merchantId、memberId将多条数据转换成一条数据，累加 amount，
            // 然后通过 filterAndConvertToRecord 将其转换为 ActivitySingleInviteRecord 列表
            dataList = dataList.stream()
                    .collect(Collectors.groupingBy(it -> StrUtil.format("{}:{}", it.getMerchantId(), it.getMemberId())))
                    .values().stream().map(it -> {
                        CashDataStatisticsDTO dto = it.get(0);
                        dto.setAmount(it.stream().mapToLong(CashDataStatisticsDTO::getAmount).sum());
                        dto.setBetAmount(it.stream().mapToLong(CashDataStatisticsDTO::getBetAmount).sum());
                        return dto;
                    }).collect(Collectors.toList());
            Map<Long, List<CashDataStatisticsDTO>> dataGroup = CollStreamUtil.groupByKey(dataList, CashDataStatisticsDTO::getMerchantId);
            Map<Long, List<ActivitySingleInviteRecord>> recordGroup = Maps.newHashMapWithExpectedSize(CollUtil.size(dataGroup));
            CollUtil.forEach(dataGroup, (key, value, index) -> recordGroup.put(key, this.filterAndConvertToRecord(key, value)));

            Map<String, List<Activity>> activityMap = this.groupByActivity(availableActivities);
            List<ActivitySingleInviteRecord> allRecords = Lists.newArrayList();
            CollUtil.forEach(recordGroup, (key, value, index) -> {
                List<ActivitySingleInviteRecord> temporary = this.cleanData(key, value, activityMap);
                if (CollUtil.isNotEmpty(temporary)) {
                    allRecords.addAll(temporary);
                }
            });

            // 6.批量插入或更新可以参考 saveOrUpdateBatchOnDuplicate
            if (CollUtil.isNotEmpty(allRecords)) {
                log.info("ActivitySingleInviteProcess: {}", JSONUtil.toJsonStr(allRecords));
                this.singleInviteRecordService.saveOrUpdateBatchOnDuplicate(allRecords);
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("SingleInviteAwardConsumer process fail, queue_name:{} msg:{}", properties.getConsumerQueue(), msg, e);
            try {
                TimeUnit.SECONDS.sleep(Constants.DEFAULT_RABBIT_CONSUMER_FAIL_SLEEP_SECONDS);
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
            } catch (Exception e1) {
                log.error("SingleInviteAwardConsumer process not ack fail, msg:{}", msg, e1);
            }
        }
    }

    /**
     * 查询商户币种开启的邀请活动
     *
     * @param merchantIdList   该批次商户id
     * @param currencyEnumList 该批次币种类型
     * @return {@link List<Activity>}
     */
    private List<Activity> getAvailableActivities(List<Long> merchantIdList, List<CurrencyEnum> currencyEnumList) {
        LocalDateTime date = LocalDateTime.now();
        List<Activity> availableActivities = this.activityService.getListByMerchantListAndActivityTypeEnumCache(merchantIdList, ActivityTypeEnum.INVITE);
        availableActivities = availableActivities.stream()
                .filter(it -> date.isAfter(it.getStartTime()) && date.isBefore(it.getEndTime()))
                .filter(it -> currencyEnumList.contains(it.getCurrencyEnum()))
                .collect(Collectors.toList());

        if (log.isDebugEnabled()) {
            log.debug("GetAvailableActivities:{}", JSONUtil.toJsonStr(availableActivities));
        }
        return availableActivities;
    }

    /**
     * 有开启活动且活动时间范围有效的才累积充值
     *
     * @param dataList            消息数据
     * @param availableActivities 商户币种开启的邀请活动
     * @return {@link List<CashDataStatisticsDTO>}
     */
    private List<CashDataStatisticsDTO> filterAvailableData(List<CashDataStatisticsDTO> dataList, List<Activity> availableActivities) {
        String key = "{}:{}";
        Map<String, Activity> map = CollStreamUtil.toMap(availableActivities, data -> StrUtil.format(key, data.getMerchantId(), data.getCurrencyEnum()), Function.identity());
        List<CashDataStatisticsDTO> availableData = dataList.stream()
                .filter(item -> {
                    String dataKey = StrUtil.format(key, item.getMerchantId(), item.getCurrencyEnum());
                    Activity activity = map.get(dataKey);
                    if (Objects.isNull(activity)) {
                        return false;
                    }
                    long start = Date.from(activity.getStartTime().toLocalDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()).getTime();
                    long end = Date.from(activity.getEndTime().toLocalDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()).getTime();
                    long belong = Date.from(item.getBelongDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()).getTime();
                    return start <= belong && belong <= end;
                }).collect(Collectors.toList());
        List<CashDataStatisticsDTO> surplusData = CollUtil.subtractToList(dataList, availableData);
        if (log.isDebugEnabled()) {
            log.debug("FilterAvailableData: availableData:{} surplusData:{}", JSONUtil.toJsonStr(availableData), JSONUtil.toJsonStr(surplusData));
        }
        return availableData;
    }

    /**
     * filterAndConvertToRecord
     *
     * @param merchantId 商户id
     * @param dataList   消息数据
     * @return {@link List<ActivitySingleInviteRecord>}
     */
    private List<ActivitySingleInviteRecord> filterAndConvertToRecord(Long merchantId, List<CashDataStatisticsDTO> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        String key = "{}:{}";
        dataList = dataList.stream().filter(it -> {
            String dataKey = StrUtil.format(key, it.getMerchantId(), it.getMemberId());
            //不存在或者值为 false 则需要进行后续处理
            return !Objects.equals(getSingleInviteCache().get(dataKey, Boolean.class), Boolean.TRUE);
        }).collect(Collectors.toList());

        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        // 查询邀请活动记录
        List<Long> memberIds = CollStreamUtil.toList(dataList, CashDataStatisticsDTO::getMemberId);
        LambdaQueryWrapper<ActivitySingleInviteRecord> queryWrapper = Wrappers.lambdaQuery(ActivitySingleInviteRecord.class)
                .eq(ActivitySingleInviteRecord::getMerchantId, merchantId)
                .in(ActivitySingleInviteRecord::getId, memberIds);

        Map<String, ActivitySingleInviteRecord> recordMap = CollStreamUtil.toMap(this.singleInviteRecordService.list(queryWrapper), record -> StrUtil.format(key, record.getMerchantId(), record.getId()), Function.identity());
        if (log.isDebugEnabled()) {
            log.debug("FilterAndConvertToRecord: recordMap:{}", JSONUtil.toJsonStr(recordMap));
        }
        // 3.查询直属上级改成批量查询，直接查member表的invite_member_id,不是member_proxy表
        LambdaQueryWrapper<Member> mQueryWrapper = Wrappers.lambdaQuery(Member.class)
                .select(Member::getId, Member::getMerchantId, Member::getInviteMemberId, Member::getInviteTypeEnum, Member::getCurrencyEnum)
                .eq(Member::getMerchantId, merchantId)
                .in(Member::getId, memberIds);
        Map<Long, Member> memberMap = CollStreamUtil.toMap(this.memberService.list(mQueryWrapper), Member::getId, Function.identity());
        if (log.isDebugEnabled()) {
            log.debug("FilterAndConvertToRecord: memberMap:{}", JSONUtil.toJsonStr(memberMap));
        }
        // 组装数据
        List<ActivitySingleInviteRecord> records = Lists.newArrayList();
        //过滤所有已经结算过的数据
        for (CashDataStatisticsDTO data : dataList) {
            String dataKey = StrUtil.format(key, data.getMerchantId(), data.getMemberId());

            ActivitySingleInviteRecord record = recordMap.get(dataKey);
            // 结算完成且未缓存
            if (Objects.nonNull(record) && record.getSettledEnum() == BooleanEnum.TRUE) {
                getSingleInviteCache().put(dataKey, Boolean.TRUE);
                continue;
            }
            // 无直邀请会员id
            Member member = memberMap.get(data.getMemberId());
            if (Objects.isNull(member.getInviteMemberId())) {
                getSingleInviteCache().put(dataKey, Boolean.TRUE);
                continue;
            }
            // 结算记录不存在
            if (Objects.isNull(record)) {
                record = new ActivitySingleInviteRecord();
                record.setId(data.getMemberId());
                record.setMerchantId(merchantId);
                record.setHighMemberId(member.getInviteMemberId());
                record.setTotalDepositAmount(0L);
                record.setTotalBetAmount(0L);
                record.setCurrencyEnum(member.getCurrencyEnum());
                record.setSettledEnum(BooleanEnum.FALSE);
                record.setInviteReceivedEnum(BooleanEnum.FALSE);
                record.setBeInviteReceivedEnum(BooleanEnum.FALSE);
                record.setCreateTime(LocalDateTime.now());
                record.setInsertOrUpdate(true);
                records.add(record);
            }
            record.setTotalDepositAmount(record.getTotalDepositAmount() + data.getAmount());
            record.setTotalBetAmount(record.getTotalBetAmount() + data.getBetAmount());
            records.add(record);
        }
        return records;
    }

    /**
     * 已经商户id，币种，活动类型对活动进行分组
     *
     * @param availableActivities 活动列表
     * @return {@link Map}
     */
    private Map<String, List<Activity>> groupByActivity(List<Activity> availableActivities) {
        Map<String, List<Activity>> activityMap = CollStreamUtil.groupByKey(availableActivities,
                activity -> StrUtil.format("{}:{}:{}", activity.getMerchantId(), activity.getCurrencyEnum(), activity.getActivityTypeEnum()));
        if (log.isDebugEnabled()) {
            log.debug("GroupByActivity:{}", JSONUtil.toJsonStr(activityMap));
        }
        return activityMap;
    }

    /**
     * getActivityInviteParam
     *
     * @param record 活动记录
     * @return {@link ActivityInviteParam}
     */
    private ActivityInviteParam getActivityInviteParam(Map<String, List<Activity>> activityMap, ActivitySingleInviteRecord record) {
        List<Activity> activities = activityMap.getOrDefault(StrUtil.format("{}:{}:{}", record.getMerchantId(), record.getCurrencyEnum(), ActivityTypeEnum.INVITE), Collections.emptyList());
        Activity activity = CollUtil.getFirst(activities);
        if (log.isDebugEnabled()) {
            log.debug("GetActivityInviteParam: activities:{} activity:{}", JSONUtil.toJsonStr(activities), JSONUtil.toJsonStr(activity));
        }
        if (activity == null) {
            return null;
        }
        ActivityInviteParam activityInviteParam = activity.activityParamToBean(ActivityInviteParam.class);
        if (activityInviteParam.getInviteReward() == null) {
            activityInviteParam.setInviteReward(0L);
        }
        if (activityInviteParam.getBeInviteReward() == null) {
            activityInviteParam.setBeInviteReward(0L);
        }
        if (activityInviteParam.getDepositAmount() == null) {
            activityInviteParam.setDepositAmount(0L);
        }
        if (activityInviteParam.getBetAmount() == null) {
            activityInviteParam.setBetAmount(0L);
        }
        return activityInviteParam;
    }

    /**
     * 清洗邀请记录
     *
     * @param merchantId  商户id
     * @param dataList    邀请记录
     * @param activityMap 活动
     * @return {@link List<ActivitySingleInviteRecord>}
     */
    private List<ActivitySingleInviteRecord> cleanData(Long merchantId, List<ActivitySingleInviteRecord> dataList, Map<String, List<Activity>> activityMap) {
        if (log.isDebugEnabled()) {
            log.debug("CleanData: merchantId:{} dataList:{}", merchantId, JSONUtil.toJsonStr(dataList));
        }
        List<ActivitySingleInviteRecord> activityRecordList = Lists.newArrayList();
        CollUtil.forEach(dataList, (item, index) -> {
            ActivityInviteParam param = this.getActivityInviteParam(activityMap, item);
            if (Objects.nonNull(param) && item.getTotalDepositAmount() >= param.getDepositAmount() && item.getTotalBetAmount() >= param.getBetAmount()) {
                item.setSettledEnum(BooleanEnum.TRUE);
                item.setSettledTime(LocalDateTime.now());
                item.setInviteReward(param.getInviteReward());
                item.setBeInviteReward(param.getBeInviteReward());
            }
            activityRecordList.add(item);
        });
        return activityRecordList;
    }

    private Cache getSingleInviteCache() {
        return this.cacheManager.getCache(Constants.LOCAL_CACHE_ACTIVITY_SINGLE_INVITE_SETTLED);
    }
}