package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.entity.ActivityTeamProxyLowerLevelMember;
import com.wd.lottery.module.activity.mapper.ActivityTeamProxyLowerLevelMemberMapper;
import com.wd.lottery.module.activity.param.ActivityTeamProxyRecentAddLowerLevelUserParam;
import com.wd.lottery.module.activity.param.TeamProxyLowerMemberStatParam;
import com.wd.lottery.module.activity.service.ActivityTeamProxyBonusDailySummaryService;
import com.wd.lottery.module.activity.service.ActivityTeamProxyLowerLevelMemberService;
import com.wd.lottery.module.cash.constatns.SubTradeTypeEnum;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.entity.MemberProxy;
import com.wd.lottery.module.member.param.QueryMemberProxyParam;
import com.wd.lottery.module.member.service.MemberProxyService;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.report.entity.ReportMemberActivityTotal;
import com.wd.lottery.module.report.service.ReportMemberActivityTotalService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ActivityTeamProxyLowerLevelMemberServiceImpl
        extends ServiceImpl<ActivityTeamProxyLowerLevelMemberMapper, ActivityTeamProxyLowerLevelMember>
        implements ActivityTeamProxyLowerLevelMemberService {

    @Resource
    private ActivityTeamProxyBonusDailySummaryService teamProxyBonusDailySummaryService;

    @Resource
    private MemberService memberService;

    @Resource
    private MemberProxyService memberProxyService;

    @Resource
    private ReportMemberActivityTotalService reportMemberActivityTotalService;

    @Override
    public TeamProxyMemberYesterdayStatDTO getTeamProxyLowerLevelMemberStat(MemberTokenInfoDTO dto) {
        TeamProxyMemberYesterdayStatDTO stat = new TeamProxyMemberYesterdayStatDTO();

        LocalDate yesterdayPeriods = LocalDateTimeUtil.of(DateUtil.yesterday()).toLocalDate();
        Long merchantId = dto.getMerchantId();
        Long memberId = dto.getId();
        TeamProxyLowerLevelMemberStatMBDTO directStat = this.baseMapper.getTeamProxyLowerLevelMemberStat(merchantId, memberId, yesterdayPeriods, 1, dto.getCurrencyEnum(), null);
        if (directStat != null) {
            stat.setDirectRegisterUserCount(directStat.getRegisterUserCount());
            stat.setDirectRechargeUserCount(directStat.getRechargeUserCount());
            stat.setDirectRechargeAmount(directStat.getRechargeAmount());
            stat.setDirectFirstRechargeUserCount(directStat.getFirstRechargeUserCount());
        }

        TeamProxyLowerLevelMemberStatMBDTO teamStat = this.baseMapper.getTeamProxyLowerLevelMemberStat(merchantId, memberId, yesterdayPeriods, null, dto.getCurrencyEnum(), null);
        if (teamStat != null) {
            stat.setTeamRegisterUserCount(teamStat.getRegisterUserCount());
            stat.setTeamRechargeUserCount(teamStat.getRechargeUserCount());
            stat.setTeamRechargeAmount(teamStat.getRechargeAmount());
            stat.setTeamFirstRechargeUserCount(teamStat.getFirstRechargeUserCount());
        }

        Long yesterdayBonusAmount = teamProxyBonusDailySummaryService.getTotalBonusAmount(merchantId, memberId, yesterdayPeriods, yesterdayPeriods);
        stat.setYesterdayTeamProxyBonus(yesterdayBonusAmount);

        LocalDate weekStartPeriods = LocalDateTimeUtil.of(DateUtil.beginOfWeek(new Date())).toLocalDate();
        LocalDate weekEndPeriods = LocalDateTimeUtil.of(DateUtil.endOfWeek(new Date())).toLocalDate();
        Long weekBonusAmount = teamProxyBonusDailySummaryService.getTotalBonusAmount(merchantId, memberId, weekStartPeriods, weekEndPeriods);
        stat.setWeekTeamProxyBonus(weekBonusAmount);

        ReportMemberActivityTotal reportMemberActivityTotal = reportMemberActivityTotalService.getByMemberIdAndMerchantIdAndSubTradeTypeEnum(memberId, merchantId, SubTradeTypeEnum.ACTIVITY_TEAM_PROXY_REWARD);
        if (reportMemberActivityTotal != null) {
            stat.setTotalTeamProxyBonus(reportMemberActivityTotal.getActivityAmount());
        }

        Long direUserProxyCount = memberProxyService.getUserProxyCount(merchantId, memberId, 1);
        stat.setDirectLowerLevelUserCount(Math.toIntExact(direUserProxyCount));

        Long teamUserProxyCount = memberProxyService.getUserProxyCount(merchantId, memberId, null);
        stat.setTeamLowerLevelUserCount(Math.toIntExact(teamUserProxyCount));

        return stat;
    }

    @Override
    public TeamProxyLowerLevelMemberStatDTO getTeamProxyLowerLevelUserStat(TeamProxyLowerMemberStatParam param) {
        MemberTokenInfoDTO dto = param.getMemberTokenInfoDTO();
        LocalDate periods = LocalDateTimeUtil.of(DateUtil.parseDate(param.getPeriods())).toLocalDate();
        TeamProxyLowerLevelMemberStatMBDTO teamStat = this.baseMapper.getTeamProxyLowerLevelMemberStat(dto.getMerchantId(), dto.getId(), periods, null, dto.getCurrencyEnum(), param.getSubMemberId());

        TeamProxyLowerLevelMemberStatDTO stat = new TeamProxyLowerLevelMemberStatDTO();
        stat.setBetAmount(teamStat.getBetAmount());
        stat.setBetUserCount(teamStat.getBetUserCount());
        stat.setFirstRechargeAmount(teamStat.getFirstRechargeAmount());
        stat.setFirstRechargeUserCount(teamStat.getFirstRechargeUserCount());
        stat.setRechargeAmount(teamStat.getRechargeAmount());
        stat.setRechargeUserCount(teamStat.getRechargeUserCount());

        return stat;
    }

    @Override
    public Page<TeamProxyLowerLevelMemberBonusDTO> getTeamProxyLowerLevelUserByPage(TeamProxyLowerMemberStatParam param) {
        MemberTokenInfoDTO dto = param.getMemberTokenInfoDTO();
        LocalDate periods = LocalDateTimeUtil.of(DateUtil.parseDate(param.getPeriods())).toLocalDate();
        Page<ActivityTeamProxyLowerLevelMember> p = new Page<>(param.getPage(), param.getLimit());
        LambdaQueryWrapper<ActivityTeamProxyLowerLevelMember> wrapper = new LambdaQueryWrapper<ActivityTeamProxyLowerLevelMember>()
                .eq(ActivityTeamProxyLowerLevelMember::getMerchantId, dto.getMerchantId())
                .eq(ActivityTeamProxyLowerLevelMember::getHighMemberId, dto.getId())
                .eq(ActivityTeamProxyLowerLevelMember::getPeriods, periods)
                .eq(param.getSubMemberId() != null, ActivityTeamProxyLowerLevelMember::getMemberId, param.getSubMemberId())
                .eq(param.getLevel() != null, ActivityTeamProxyLowerLevelMember::getLevel, param.getLevel());

        Page<ActivityTeamProxyLowerLevelMember> page = baseMapper.selectPage(p, wrapper);

        List<TeamProxyLowerLevelMemberBonusDTO> list = page.getRecords().stream().map(v -> {
            TeamProxyLowerLevelMemberBonusDTO bonusDTO = new TeamProxyLowerLevelMemberBonusDTO();
            BeanUtil.copyProperties(v, bonusDTO);
            return bonusDTO;
        }).collect(Collectors.toList());

        Page<TeamProxyLowerLevelMemberBonusDTO> dtoPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        dtoPage.setRecords(list);
        return dtoPage;
    }

    @Override
    public Page<TeamProxyRecentAddLowerLevelUserDTO> getTeamProxyRecentAddLowerLevelUserByPage(ActivityTeamProxyRecentAddLowerLevelUserParam param) {
        Page<MemberProxy> memberProxyPage = memberProxyService.getMemberProxyByHighNameForPage(QueryMemberProxyParam.builder()
                .merchantId(param.getMerchantId())
                .highMemberId(param.getHighMemberId())
                .startTime(param.getStartTime())
                .endEnd(param.getEndTime())
                .level(1)
                .current(param.getCurrent())
                .size(param.getSize())
                .build());

        if (memberProxyPage == null || CollectionUtil.isEmpty(memberProxyPage.getRecords())) {
            return new Page<>(param.getCurrent(), param.getSize(), 0);
        }
        List<TeamProxyRecentAddLowerLevelUserDTO> collect = memberProxyPage.getRecords().stream().map(v -> {
            TeamProxyRecentAddLowerLevelUserDTO dto = new TeamProxyRecentAddLowerLevelUserDTO();
            BeanUtil.copyProperties(v, dto);
            return dto;
        }).collect(Collectors.toList());


        return new Page<TeamProxyRecentAddLowerLevelUserDTO>(memberProxyPage.getCurrent(),memberProxyPage.getSize(), memberProxyPage.getTotal())
                .setRecords(collect);
    }
}
