package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.dto.EnableDTO;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.Md5Util;
import com.wd.lottery.module.activity.constants.ActivityStatusEnum;
import com.wd.lottery.module.activity.constants.ActivityTypeEnum;
import com.wd.lottery.module.activity.constants.SubActivityTypeEnum;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.entity.ActivityGift;
import com.wd.lottery.module.activity.entity.ActivityGiftRecord;
import com.wd.lottery.module.activity.entity.ActivityRecord;
import com.wd.lottery.module.activity.mapper.ActivityGiftMapper;
import com.wd.lottery.module.activity.mapper.ActivityGiftRecordMapper;
import com.wd.lottery.module.activity.param.ActivityParam;
import com.wd.lottery.module.activity.param.ActivityRewardParam;
import com.wd.lottery.module.activity.param.gift.ActivityGiftConfigParam;
import com.wd.lottery.module.activity.param.gift.ActivityGiftParam;
import com.wd.lottery.module.activity.param.gift.ActivityGiftQueryParam;
import com.wd.lottery.module.activity.param.gift.ActivityGiftRecordParam;
import com.wd.lottery.module.activity.service.ActivityGiftService;
import com.wd.lottery.module.activity.service.ActivityRecordService;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.service.MemberWithdrawAccountService;
import com.wd.lottery.module.report.service.ReportMemberDateService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@AllArgsConstructor
@Slf4j
public class ActivityGiftServiceImpl extends ServiceImpl<ActivityGiftMapper, ActivityGift> implements ActivityGiftService {

    private final ActivityService activityService;

    private final ActivityRecordService activityRecordService;

    private final ActivityGiftRecordMapper activityGiftRecordMapper;

    private final MemberWithdrawAccountService memberWithdrawAccountService;

    @Resource
    private ReportMemberDateService reportMemberDateService;

    @Override
    public void checkParam(ActivityParam activityParam) {
        ActivityGiftConfigParam activityGiftConfigParam = activityParam.getActivityParam(ActivityGiftConfigParam.class);
        Assert.notNull(activityGiftConfigParam.getMinAmount());
        Assert.notNull(activityGiftConfigParam.getMaxAmount());
        Assert.isTrue(activityGiftConfigParam.getMaxAmount().compareTo(activityGiftConfigParam.getMinAmount()) >= 0);
    }

    @Override
    public List<ActivityRecord> getRewardList(ActivityRewardParam activityRewardParam) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivityRecord receiveReward(ActivityRewardParam activityRewardParam) {
        this.checkGiftCodeCondition(activityRewardParam);
        String giftCode = activityRewardParam.getOptional();

        DateTime now = DateUtil.date();
        LocalDateTime nowDateTime = now.toLocalDateTime();
        MemberTokenInfoDTO memberTokenInfoDTO = activityRewardParam.getMemberTokenInfoDTO();

        Activity activity = activityService.getEnableAndStartOneNotNull(memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getCurrencyEnum(), this.getActivityTypeEnum(), activityRewardParam.getActivityId());
        ActivityRecord activityRecord = activityRecordService.initActivityRecord(memberTokenInfoDTO, activity, SubActivityTypeEnum.GIFT_REWARD, nowDateTime, activityRewardParam.getRequestIp());
        ActivityGift activityGift = getActivityGiftByCode(giftCode, memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getCurrencyEnum());

        Long randomAmount = RandomUtil.randomLong(activityGift.getMinAmount(), activityGift.getMaxAmount() + 1);
        checkComplete(activityRecord, activityGift.getCode(), randomAmount);

        recordRewardHistory(activityGift, activityRecord);
        updateReceivedGiftAmount(activityGift);
        return activityRecordService.receiveReward(Collections.singletonList(activityRecord));
    }


    @Override
    public ActivityTypeEnum getActivityTypeEnum() {
        return ActivityTypeEnum.GIFT;
    }

    @Override
    public String getUid(ActivityRecord activityRecord, Object... others) {
        String giftCode = String.valueOf(others[0]);
        String uidStr = String.valueOf(activityRecord.getMerchantId()) + activityRecord.getCurrencyEnum()
                + activityRecord.getMemberId() + activityRecord.getActivityTypeEnum() + giftCode;
        return Md5Util.getMd5HexLowerCase(uidStr);
    }

    @Override
    public Boolean getUniqueActivity() {
        return Boolean.TRUE;
    }


    private void checkGiftCodeCondition(ActivityRewardParam activityRewardParam) {
        String giftCode = activityRewardParam.getOptional();
        Assert.notEmpty(giftCode);
        MemberTokenInfoDTO memberTokenInfoDTO = activityRewardParam.getMemberTokenInfoDTO();
        LocalDateTime now = LocalDateTime.now();

        ActivityGift activityGift = getActivityGiftByCode(giftCode, memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getCurrencyEnum());
        if (activityGift == null) {
            throw new ApiException(CommonCode.ACTIVITY_GIFT_NOT_EXISTS);
        }

        if (Objects.equals(activityGift.getEnableEnum(), EnableEnum.FALSE)) {
            throw new ApiException(CommonCode.ACTIVITY_GIFT_EXPIRED);
        }

        if (now.isBefore(activityGift.getStartTime()) || now.isAfter(activityGift.getEndTime())) {
            throw new ApiException(CommonCode.ACTIVITY_GIFT_EXPIRED);
        }

        //判断是否需要充值的条件
        if (ObjectUtil.equal(activityGift.getRechargeLimit(), EnableEnum.TRUE)) {
            Long totalRecharge = reportMemberDateService.getSumOfValidDepositAmount(memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getCurrencyEnum(),
                    memberTokenInfoDTO.getId(), LocalDate.now(), LocalDate.now());
            if (Objects.nonNull(totalRecharge) && totalRecharge <= Constants.ZERO_INTEGER) {
                throw new ApiException(CommonCode.ACTIVITY_EXCHANGE_CODE_NEED_RECHARGE);
            }
        }

        if (activityGift.getReceivedGiftAmount() >= activityGift.getGiftAmount()) {
            throw new ApiException(CommonCode.ACTIVITY_GIFT_EXPIRED);
        }

        ActivityGiftRecord activityGiftRecord = getMemberActivityGiftRecord(memberTokenInfoDTO, activityGift.getId());
        if (activityGiftRecord != null) {
            throw new ApiException(CommonCode.ACTIVITY_GIFT_ALREADY_RECEIVE);
        }

        if (Objects.equals(activityGift.getPhoneLimit(), EnableEnum.TRUE) && memberTokenInfoDTO.getMobile() == null) {
            throw new ApiException(CommonCode.ACTIVITY_GIFT_CONDITION_NOT_MEET);
        }

        if (Objects.equals(activityGift.getBankCardLimit(), EnableEnum.TRUE)
                && isBankCardAbsent(memberTokenInfoDTO)) {
            throw new ApiException(CommonCode.ACTIVITY_GIFT_CONDITION_NOT_MEET);
        }

        if (isUserNotEligibleForGiftCode(memberTokenInfoDTO.getId(), activityGift.getMemberIdListJson()))  {
            throw new ApiException(CommonCode.ACTIVITY_GIFT_CONDITION_NOT_MEET);
        }
    }

    @Override
    public ActivityGift getActivityGiftByCode(String giftCode, Long merchantId, CurrencyEnum currencyEnum) {
        LambdaQueryWrapper<ActivityGift> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityGift::getMerchantId, merchantId)
                .eq(ActivityGift::getCurrencyEnum, currencyEnum)
                .eq(ActivityGift::getCode, giftCode);
        return this.getOne(wrapper);
    }

    private ActivityGiftRecord getMemberActivityGiftRecord(MemberTokenInfoDTO memberTokenInfoDTO, Long giftId) {
        LambdaQueryWrapper<ActivityGiftRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityGiftRecord::getMerchantId, memberTokenInfoDTO.getMerchantId())
                .eq(ActivityGiftRecord::getCurrencyEnum, memberTokenInfoDTO.getCurrencyEnum())
                .eq(ActivityGiftRecord::getMemberId, memberTokenInfoDTO.getId())
                .eq(ActivityGiftRecord::getGiftId, giftId);
        return activityGiftRecordMapper.selectOne(wrapper);
    }

    private void checkComplete(ActivityRecord activityRecord, String giftCode, Long amount) {
        if (amount <= 0) {
            return;
        }
        activityRecord.setAmount(amount);
        activityRecord.setActivityStatusEnum(ActivityStatusEnum.WAIT_RECEIVE);
        activityRecord.setUid(this.getUid(activityRecord, giftCode));
        activityRecord.setRecordJson(JSONUtil.toJsonStr(new ActivityGiftRecordParam(giftCode)));
    }

    private void recordRewardHistory(ActivityGift activityGift, ActivityRecord activityRecord) {
        if (!Objects.equals(activityRecord.getActivityStatusEnum(), ActivityStatusEnum.WAIT_RECEIVE)) {
            throw new ApiException(CommonCode.FAILED);
        }
        ActivityGiftRecord entity = new ActivityGiftRecord();
        entity.setGiftId(activityGift.getId());
        entity.setMerchantId(activityRecord.getMerchantId());
        entity.setCurrencyEnum(activityRecord.getCurrencyEnum());
        entity.setMemberId(activityRecord.getMemberId());
        entity.setRewardAmount(activityRecord.getAmount());
        entity.setCreateTime(activityRecord.getCreateTime());
        int count = activityGiftRecordMapper.insert(entity);
        if (count != 1) {
            throw new ApiException(CommonCode.FAILED);
        }
    }

    private void updateReceivedGiftAmount(ActivityGift activityGift) {
        int updated = this.getBaseMapper().increaseReceivedGiftAmount(activityGift.getId());
        if (updated != 1) {
            throw new ApiException(CommonCode.ACTIVITY_GIFT_EXPIRED);
        }
    }

    @Override
    public Page<ActivityGift> getPage(ActivityGiftQueryParam activityGiftQueryParam) {
        LambdaQueryWrapper<ActivityGift> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityGift::getMerchantId, activityGiftQueryParam.getMerchantId())
                .eq(ActivityGift::getCurrencyEnum, activityGiftQueryParam.getCurrencyEnum())
                .eq(StrUtil.isNotBlank(activityGiftQueryParam.getGiftCode()), ActivityGift::getCode, activityGiftQueryParam.getGiftCode())
                .eq(!Objects.isNull(activityGiftQueryParam.getEnableEnum()), ActivityGift::getEnableEnum, activityGiftQueryParam.getEnableEnum());

        // 若开始时间和结束时间不为null，取时间段交集
        if (activityGiftQueryParam.getStartTime() != null && activityGiftQueryParam.getEndTime() != null) {
            wrapper.and(w -> w.between(ActivityGift::getStartTime, activityGiftQueryParam.getStartTime(), activityGiftQueryParam.getEndTime())
                    .or()
                    .between(ActivityGift::getEndTime, activityGiftQueryParam.getStartTime(), activityGiftQueryParam.getEndTime())
                    .or(w2 -> w2.le(ActivityGift::getStartTime, activityGiftQueryParam.getStartTime()).ge(ActivityGift::getEndTime, activityGiftQueryParam.getEndTime()))
                    );
        }

        return super.page(new Page<>(activityGiftQueryParam.getCurrent(), activityGiftQueryParam.getSize()), wrapper);
    }

    @Override
    public void saveByParam(ActivityGiftParam activityGiftParam) {
        validateGiftSaveRequest(activityGiftParam);

        ActivityGift entity = new ActivityGift();
        BeanUtil.copyProperties(activityGiftParam, entity);
        super.save(entity);
    }

    private void validateGiftSaveRequest(ActivityGiftParam activityGiftParam) {
        Assert.notEmpty(activityGiftParam.getCode());
        Assert.isTrue(activityGiftParam.getCode().length() >= 6 && activityGiftParam.getCode().length() <= 16);
        Assert.isTrue(activityGiftParam.getCode().matches("^[a-zA-Z0-9]+$"));
        Assert.isTrue(activityGiftParam.getMinAmount() > 0);
        Assert.isTrue(activityGiftParam.getMaxAmount() >= activityGiftParam.getMinAmount());
        //activityGiftParam.getmemberIdListJson数量 不能超过2000
        Assert.isTrue(activityGiftParam.getMemberIdListJson().size() <= 2000);
        Assert.isNull(this.getActivityGiftByCode(activityGiftParam.getCode(), activityGiftParam.getMerchantId(), activityGiftParam.getCurrencyEnum()),
                () -> new ApiException(CommonCode.ACTIVITY_GIFT_ALREADY_EXISTS));
    }

    @Override
    public void updateByParam(ActivityGiftParam activityGiftParam) {
        validateGiftUpdateRequest(activityGiftParam);

        ActivityGift updatedEntity = new ActivityGift();
        updatedEntity.setId(activityGiftParam.getId());
        updatedEntity.setStartTime(activityGiftParam.getStartTime());
        updatedEntity.setEndTime(activityGiftParam.getEndTime());
        updatedEntity.setGiftAmount(activityGiftParam.getGiftAmount());
        updatedEntity.setBankCardLimit(activityGiftParam.getBankCardLimit());
        updatedEntity.setPhoneLimit(activityGiftParam.getPhoneLimit());
        updatedEntity.setMemberIdListJson(activityGiftParam.getMemberIdListJson());
        updatedEntity.setRechargeLimit(activityGiftParam.getRechargeLimit());
        updatedEntity.setRemark(Objects.nonNull(activityGiftParam.getRemark())?activityGiftParam.getRemark(): StringUtils.EMPTY);

        super.updateById(updatedEntity);
    }

    private void validateGiftUpdateRequest(ActivityGiftParam activityGiftParam) {
        ActivityGift oldEntity = getById(activityGiftParam.getId());
        Assert.notNull(oldEntity);
        Assert.equals(oldEntity.getMerchantId(), activityGiftParam.getMerchantId());
        Assert.isTrue(activityGiftParam.getMemberIdListJson().size() <= 2000);
        Assert.equals(oldEntity.getEnableEnum(), EnableEnum.FALSE, () -> new ApiException(CommonCode.ACTIVITY_GIFT_ENABLE_DISABLE_UPDATE));
        Assert.isTrue(Objects.isNull(activityGiftParam.getGiftAmount()) || activityGiftParam.getGiftAmount() >= oldEntity.getReceivedGiftAmount());
    }

    @Override
    public Page<ActivityGiftRecord> getDetail(ActivityGiftQueryParam param) {
        LambdaQueryWrapper<ActivityGiftRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ActivityGiftRecord::getMemberId,
                        ActivityGiftRecord::getRewardAmount,
                        ActivityGiftRecord::getCreateTime)
                .eq(ActivityGiftRecord::getMerchantId, param.getMerchantId())
                .eq(ActivityGiftRecord::getCurrencyEnum, param.getCurrencyEnum())
                .eq(ActivityGiftRecord::getGiftId, param.getId())
                .orderBy(true, false, ActivityGiftRecord::getCreateTime);
        return activityGiftRecordMapper.selectPage(new Page<>(param.getCurrent(), param.getSize()), wrapper);
    }

    @Override
    public List<ActivityGift> enable(EnableDTO enableDTO) {
        List<Long> ids = enableDTO.getIds();
        Assert.isFalse(ids.isEmpty());
        Assert.notNull(enableDTO.getEnableEnum());

        List<ActivityGift> updatedList = new ArrayList<>();
        ids.forEach(giftCodeId -> {
            ActivityGift entity = new ActivityGift();
            entity.setId(giftCodeId);
            entity.setEnableEnum(enableDTO.getEnableEnum());
            entity.setUpdateBy(enableDTO.getAdminName());
            entity.setUpdateTime(enableDTO.getUpdateTime());
            updatedList.add(entity);
        });
        boolean isUpdate = super.updateBatchById(updatedList);
        if (!isUpdate) {
            log.error("礼品码, 啟/停用发生異常, ids:{}", ids);
            throw new ApiException(CommonCode.FAILED);
        }
        return updatedList;
    }

    private boolean isUserNotEligibleForGiftCode(Long memberId, List<Long> userIds) {
        if (CollectionUtil.isEmpty(userIds)) {
            return false;
        }
        return !userIds.contains(memberId);
    }

    private boolean isBankCardAbsent(MemberTokenInfoDTO memberTokenInfoDTO) {
        return !memberWithdrawAccountService.existMemberWithdrawAccount(memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getId());
    }

}
