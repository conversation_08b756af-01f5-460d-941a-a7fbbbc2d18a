package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.activity.constants.ActivityTypeEnum;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.mapper.ActivityMapper;
import com.wd.lottery.module.activity.param.ActivityParam;
import com.wd.lottery.module.activity.param.ActivityQueryParam;
import com.wd.lottery.module.activity.param.ActivityUpdateEnableEnumParam;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.common.dto.IndexDataDTO;
import com.wd.lottery.module.common.vo.ActivityInitDataVO;
import com.wd.lottery.module.common.vo.InitDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, Activity> implements ActivityService {

    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    @Override
    public Activity getOne(Long merchantId, CurrencyEnum currencyEnum, ActivityTypeEnum activityTypeEnum) {
        Assert.notNull(merchantId);
        Assert.notNull(currencyEnum);
        Assert.notNull(activityTypeEnum);
        return this.lambdaQuery()
                .eq(Activity::getMerchantId, merchantId)
                .eq(Activity::getActivityTypeEnum, activityTypeEnum)
                .eq(Activity::getCurrencyEnum, currencyEnum)
                .eq(Activity::getDelEnum, BooleanEnum.FALSE)
                .one();
    }

    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    @Override
    public Activity getEnableOne(Long merchantId, CurrencyEnum currencyEnum, ActivityTypeEnum activityTypeEnum) {
        Assert.notNull(merchantId);
        Assert.notNull(currencyEnum);
        Assert.notNull(activityTypeEnum);
        return this.lambdaQuery()
                .eq(Activity::getMerchantId, merchantId)
                .eq(Activity::getActivityTypeEnum, activityTypeEnum)
                .eq(Activity::getCurrencyEnum, currencyEnum)
                .eq(Activity::getEnableEnum, EnableEnum.TRUE)
                .eq(Activity::getDelEnum, BooleanEnum.FALSE)
                .one();
    }

    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    @Override
    public Activity getEnableOneNotNull(Long merchantId, CurrencyEnum currencyEnum, ActivityTypeEnum activityTypeEnum){
        Activity activity = getEnableOne(merchantId, currencyEnum, activityTypeEnum);
        Assert.notNull(activity, ()->new ApiException(CommonCode.ACTIVITY_NOT_EXISTS));
        return activity;
    }

    @Override
    public List<Activity> getAllCurrencyEnableActivityList(Long merchantId, ActivityTypeEnum activityTypeEnum) {
        Assert.notNull(merchantId);
        Assert.notNull(activityTypeEnum);
        List<Activity> activity = this.lambdaQuery()
                .eq(Activity::getMerchantId, merchantId)
                .eq(Activity::getActivityTypeEnum, activityTypeEnum)
                .eq(Activity::getEnableEnum, EnableEnum.TRUE)
                .eq(Activity::getDelEnum, BooleanEnum.FALSE)
                .list();
        return activity;
    }

    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    @Override
    public Activity getEnableOneNotNull(Long merchantId, CurrencyEnum currencyEnum, ActivityTypeEnum activityTypeEnum, Long activityId){
        Assert.notNull(merchantId);
        Assert.notNull(currencyEnum);
        Assert.notNull(activityId);
        Activity activity = this.lambdaQuery()
                .eq(Activity::getMerchantId, merchantId)
                .eq(Activity::getId, activityId)
                .eq(Activity::getActivityTypeEnum, activityTypeEnum)
                .eq(Activity::getCurrencyEnum, currencyEnum)
                .eq(Activity::getEnableEnum, EnableEnum.TRUE)
                .eq(Activity::getDelEnum, BooleanEnum.FALSE)
                .one();
        Assert.notNull(activity, () -> new ApiException(CommonCode.ACTIVITY_NOT_EXISTS));
        return activity;
    }

    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    @Override
    public Activity getEnableAndStartOneNotNull(Long merchantId, CurrencyEnum currencyEnum, ActivityTypeEnum activityTypeEnum, Long activityId){
        Assert.notNull(merchantId);
        Assert.notNull(currencyEnum);
        Assert.notNull(activityId);
        LocalDateTime now = LocalDateTime.now();
        Activity activity = this.lambdaQuery()
                .eq(Activity::getMerchantId, merchantId)
                .eq(Activity::getId, activityId)
                .eq(Activity::getActivityTypeEnum, activityTypeEnum)
                .eq(Activity::getCurrencyEnum, currencyEnum)
                .eq(Activity::getEnableEnum, EnableEnum.TRUE)
                .le(Activity::getStartTime, now)
                .ge(Activity::getEndTime, now)
                .eq(Activity::getDelEnum, BooleanEnum.FALSE)
                .one();
        Assert.notNull(activity, () -> new ApiException(CommonCode.ACTIVITY_NOT_EXISTS));
        return activity;
    }

    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    @Override
    public List<Activity> getListByMerchantListAndActivityTypeEnumCache(List<Long> merchantIdList, ActivityTypeEnum activityTypeEnum){
        Assert.notEmpty(merchantIdList);
        Assert.notNull(activityTypeEnum);
        return this.lambdaQuery()
                .in(Activity::getMerchantId, merchantIdList)
                .eq(Activity::getActivityTypeEnum, activityTypeEnum)
                .eq(Activity::getEnableEnum, EnableEnum.TRUE)
                .eq(Activity::getDelEnum, BooleanEnum.FALSE)
                .list();
    }

    @Override
    public void save(ActivityParam param) {
        Activity activity = new Activity();
        BeanUtil.copyProperties(param, activity);
        activity.setId(null);
        activity.setEnableEnum(EnableEnum.FALSE);
        activity.setDelEnum(BooleanEnum.FALSE);
        activity.setShowInActivityEnableEnum(EnableEnum.TRUE);
        this.save(activity);
    }

    @Override
    public void update(ActivityParam param) {
        Assert.notNull(param.getId());
        Activity activity = new Activity();
        BeanUtil.copyProperties(param, activity);
        activity.setUpdateTime(LocalDateTime.now());
        boolean update = this.lambdaUpdate()
                .eq(Activity::getId, param.getId())
                .eq(Activity::getMerchantId, param.getMerchantId())
                .eq(Activity::getCurrencyEnum, param.getCurrencyEnum())
                .eq(Activity::getEnableEnum, EnableEnum.FALSE)
                .update(activity);
        if (!update) {
            throw new ApiException(CommonCode.ACTIVITY_ENABLE_DISABLE_UPDATE);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEnableEnum(ActivityUpdateEnableEnumParam param) {
        Activity activity = this.baseMapper.selectById(param.getId());

        LocalDateTime updateTime = LocalDateTime.now();
        boolean update = this.lambdaUpdate()
                .set(Activity::getEnableEnum, param.getEnableEnum())
                .set(Activity::getUpdateBy, param.getUpdateBy())
                .set(Activity::getUpdateTime, updateTime)
                .eq(Activity::getId, param.getId())
                .eq(Activity::getMerchantId, param.getMerchantId())
                .eq(Activity::getEnableEnum, activity.getEnableEnum())
                .update();
    }

    @Override
    public void removeByIdAndMerchantId(Long id, Long merchantId) {
        Assert.notNull(id);
        Assert.notNull(merchantId);
        boolean update = this.lambdaUpdate()
                .set(Activity::getDelEnum, BooleanEnum.TRUE)
                .eq(Activity::getId, id)
                .eq(Activity::getMerchantId, merchantId)
                .eq(Activity::getEnableEnum, BooleanEnum.FALSE)
                .isNull(Activity::getUniqueActivity)
                .update();
        if (!update) {
            throw new ApiException(CommonCode.ACTIVITY_ENABLE_DISABLE_UPDATE);
        }
    }

    @Override
    public Page<Activity> getPage(ActivityQueryParam param) {
        Assert.notNull(param);
        Assert.notNull(param.getMerchantId());
        Assert.notNull(param.getCurrencyEnum());

        LambdaQueryWrapper<Activity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Activity::getMerchantId, param.getMerchantId())
                .eq(Activity::getCurrencyEnum, param.getCurrencyEnum())
                .eq(Activity::getDelEnum, BooleanEnum.FALSE)
                .eq(Objects.nonNull(param.getActivityTypeEnum()), Activity::getActivityTypeEnum, param.getActivityTypeEnum())
                .eq(Objects.nonNull(param.getEnableEnum()), Activity::getEnableEnum, param.getEnableEnum())
                .like(Objects.nonNull(param.getTitle()), Activity::getTitle, param.getTitle())
                .orderByDesc(Activity::getSort, Activity::getId);

        if (Objects.nonNull(param.getStartTime()) && Objects.nonNull(param.getEndTime())) {
            queryWrapper.and(w -> w.between(Activity::getStartTime, param.getStartTime(), param.getEndTime())
                    .or()
                    .between(Activity::getEndTime, param.getStartTime(), param.getEndTime())
                    .or(w2 -> w2.le(Activity::getStartTime, param.getStartTime()).ge(Activity::getEndTime, param.getEndTime()))
            );
        }

        return super.page(new Page<>(param.getCurrent(), param.getSize()), queryWrapper);
    }

    @Override
    public Set<ActivityTypeEnum> getEnableCreateActivityTypeEnumSet(Long merchantId, CurrencyEnum currencyEnum){
        Assert.notNull(merchantId);
        Assert.notNull(currencyEnum);
        List<Activity> activityList = this.lambdaQuery()
                .select(Activity::getActivityTypeEnum)
                .eq(Activity::getMerchantId, merchantId)
                .eq(Activity::getCurrencyEnum, currencyEnum)
                .eq(Activity::getUniqueActivity, true)
                .groupBy(Activity::getActivityTypeEnum)
                .list();
        Set<ActivityTypeEnum> activityTypeEnumSet = activityList.stream().map(Activity::getActivityTypeEnum).collect(Collectors.toSet());
        Set<ActivityTypeEnum> canCreateactivityTypeEnumAllSet = ActivityTypeEnum.getcanCreateActivityEnumSet();
        canCreateactivityTypeEnumAllSet.removeAll(activityTypeEnumSet);
        return canCreateactivityTypeEnumAllSet;
    }

    @Override
    public void getEnableAndStartActivityList(IndexDataDTO indexDataDTO, InitDataVO initDataVO) {
        try {
            Assert.notNull(indexDataDTO);
            Assert.notNull(indexDataDTO.getMerchantId());
            Assert.notNull(indexDataDTO.getCurrencyEnum());
            LocalDateTime now = LocalDateTime.now();
            List<Activity> list = this.lambdaQuery()
                    .eq(Activity::getMerchantId, indexDataDTO.getMerchantId())
                    .eq(Activity::getCurrencyEnum, indexDataDTO.getCurrencyEnum())
                    .eq(Activity::getEnableEnum, EnableEnum.TRUE)
                    .eq(Activity::getDelEnum, BooleanEnum.FALSE)
                    .le(Activity::getStartTime, now)
                    .ge(Activity::getEndTime, now)
                    .orderByDesc(Activity::getSort, Activity::getId)
                    .list();
            List<ActivityInitDataVO> noticeInitDataVOList = Optional.ofNullable(list).orElse(Collections.emptyList()).stream().map(v -> {
                ActivityInitDataVO vo = new ActivityInitDataVO();
                BeanUtils.copyProperties(v, vo);
                return vo;
            }).collect(Collectors.toList());
            initDataVO.setActivityList(noticeInitDataVOList);
        } catch (Exception e) {
            log.error("getEnableNoticeList fail, indexDataDTO:{}", indexDataDTO, e);
        }
    }

    @Override
    public List<Activity> getAllEnableActivityByType(ActivityTypeEnum activityTypeEnum) {
        return this.lambdaQuery()
                .eq(Activity::getEnableEnum, EnableEnum.TRUE)
                .eq(Activity::getDelEnum, BooleanEnum.FALSE)
                .eq(Activity::getActivityTypeEnum, activityTypeEnum)
                .list();
    }
}
