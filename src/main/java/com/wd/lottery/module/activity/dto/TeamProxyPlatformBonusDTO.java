package com.wd.lottery.module.activity.dto;

import com.wd.lottery.module.common.constants.GameCategoryEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class TeamProxyPlatformBonusDTO implements Serializable {

    private GameCategoryEnum gameCategoryEnum;
    private LocalDate periods;
    private Integer betUserCount = 0;
    private Long betAmount = 0L;
    private Integer teamLevel;
    private Long teamProxyBonus = 0L;

    private Integer rate;
    private Long teamLevelRate;

}
