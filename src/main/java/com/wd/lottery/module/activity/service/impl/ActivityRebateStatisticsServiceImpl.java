package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.SqlUtil;
import com.wd.lottery.module.activity.constants.ActivityTypeEnum;
import com.wd.lottery.module.activity.dto.ActivityRebateDTO;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.entity.ActivityRebateStatistics;
import com.wd.lottery.module.activity.exception.NeedRetryException;
import com.wd.lottery.module.activity.mapper.ActivityRebateStatisticsMapper;
import com.wd.lottery.module.activity.param.vip.ActivityVipConfigParam;
import com.wd.lottery.module.activity.service.ActivityRebateStatisticsService;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.cash.dto.CashDataStatisticsDTO;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityRebateStatisticsServiceImpl extends ServiceImpl<ActivityRebateStatisticsMapper, ActivityRebateStatistics> implements ActivityRebateStatisticsService {

    @Resource
    private ActivityRebateStatisticsMapper activityRebateStatisticsMapper;

    @Resource
    private ActivityService activityService;

    @Override
    public void updateRebateStatistics(ActivityRebateDTO dto, ActivityRebateStatistics statistics) {
        ActivityRebateStatistics newStatistics = new ActivityRebateStatistics();
        buildUpdateParam(dto,newStatistics,statistics);
        newStatistics.setUpdateTime(LocalDateTime.now());

        LambdaUpdateWrapper<ActivityRebateStatistics> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ActivityRebateStatistics::getMemberId, statistics.getMemberId());
        wrapper.eq(ActivityRebateStatistics::getMerchantId, statistics.getMerchantId());
        this.update(newStatistics,wrapper);
    }

    private void buildUpdateParam(ActivityRebateDTO dto, ActivityRebateStatistics newStatistics, ActivityRebateStatistics statistics) {
        if (Objects.nonNull(dto.getThreeReceivedRebateAmount())) {
            newStatistics.setReceivedThreeValidBetMoney(statistics.getThreeValidBetMoney());
        }
        if (Objects.nonNull(dto.getLotteryReceivedRebateAmount())) {
            newStatistics.setReceivedLotteryValidBetMoney(statistics.getLotteryValidBetMoney());
        }
        if (Objects.nonNull(dto.getSlotReceivedRebateAmount())) {
            newStatistics.setReceivedSlotValidBetMoney(statistics.getSlotValidBetMoney());
        }
        if (Objects.nonNull(dto.getFishReceivedRebateAmount())) {
            newStatistics.setReceivedFishValidBetMoney(statistics.getFishValidBetMoney());
        }
        if (Objects.nonNull(dto.getCasinoReceivedRebateAmount())) {
            newStatistics.setReceivedCasinoValidBetMoney(statistics.getCasinoValidBetMoney());
        }
        if (Objects.nonNull(dto.getPokerReceivedRebateAmount())) {
            newStatistics.setReceivedPokerValidBetMoney(statistics.getPokerValidBetMoney());
        }
        if (Objects.nonNull(dto.getMiniReceivedRebateAmount())) {
            newStatistics.setReceivedMiniValidBetMoney(statistics.getMiniValidBetMoney());
        }
        if (Objects.nonNull(dto.getSportReceivedRebateAmount())) {
            newStatistics.setReceivedSportValidBetMoney(statistics.getSportValidBetMoney());
        }
        if (Objects.nonNull(dto.getEsportReceivedRebateAmount())) {
            newStatistics.setReceivedEsportValidBetMoney(statistics.getEsportValidBetMoney());
        }
        if (Objects.nonNull(dto.getCockReceivedRebateAmount())) {
            newStatistics.setReceivedCockValidBetMoney(statistics.getCockValidBetMoney());
        }
        Long totalRebateMoney = Objects.nonNull(statistics.getTotalRebateMoney()) ? statistics.getTotalRebateMoney(): 0L;
        newStatistics.setTotalRebateMoney(totalRebateMoney + dto.getTotalCanReceivedRebateAmount());
    }

    @Override
    public Map<String,Activity> checkActivityEnable(MemberTokenInfoDTO memberTokenInfo) {
        Map<String,Activity> map = Maps.newHashMap();
        //查询反水比例
        Activity vipActivity = activityService.getEnableOne(memberTokenInfo.getMerchantId(),memberTokenInfo.getCurrencyEnum(), ActivityTypeEnum.VIP);

        if (Objects.isNull(vipActivity) || Objects.isNull(vipActivity.activityParamToVipLevelConfigByVipLevel(memberTokenInfo.getVipLevel()))) {
            return null;
        }
        //判断返水活动状态
        Activity rebateActivity = activityService.getEnableOne(memberTokenInfo.getMerchantId(),memberTokenInfo.getCurrencyEnum(), ActivityTypeEnum.REBATE);

        if (Objects.isNull(rebateActivity)) {
            return null;
        }
        map.put(ActivityTypeEnum.REBATE.name(),rebateActivity);
        map.put(ActivityTypeEnum.VIP.name(),vipActivity);
        return map;
    }

    @Override
    public ActivityRebateDTO getTotalRebate(ActivityRebateStatistics statistics, ActivityVipConfigParam activityVipConfigParam,Boolean flag) {
        GameCategoryEnum[] gameCategoryEnums = GameCategoryEnum.values();
        ActivityRebateDTO dto = new ActivityRebateDTO();
        Long canReceivedRebateAmount = 0L;
        if (flag) {
            //距上次领奖30分钟以内
            dto.setIncreaseThreeRebateRate(activityVipConfigParam.getIncreaseThreeRebateRate());
            dto.setIncreaseLotteryRebateRate(activityVipConfigParam.getIncreaseLotteryRebateRate());
            dto.setIncreaseSlotRebateRate(activityVipConfigParam.getIncreaseSlotRebateRate());
            dto.setIncreaseFishRebateRate(activityVipConfigParam.getIncreaseFishRebateRate());
            dto.setIncreaseCasinoRebateRate(activityVipConfigParam.getIncreaseCasinoRebateRate());
            dto.setIncreasePokerRebateRate(activityVipConfigParam.getIncreasePokerRebateRate());
            dto.setIncreaseMiniRebateRate(activityVipConfigParam.getIncreaseMiniRebateRate());
            dto.setIncreaseSportRebateRate(activityVipConfigParam.getIncreaseSportRebateRate());
            dto.setIncreaseEsportRebateRate(activityVipConfigParam.getIncreaseESportRebateRate());
            dto.setIncreaseCockRebateRate(activityVipConfigParam.getIncreaseCockRebateRate());
        } else {
            for (GameCategoryEnum gameCategoryEnum : gameCategoryEnums) {
                canReceivedRebateAmount += getCanReceivedRebateAmount(statistics, activityVipConfigParam, gameCategoryEnum, dto);
                log.debug("getCanReceivedRebateAmount...{}",canReceivedRebateAmount);
            }
            dto.setTotalCanReceivedRebateAmount(canReceivedRebateAmount);
            Long totalCanReceivedValidBetMoney = dto.getTotalThreeValidBetMoney() + dto.getTotalLotteryValidBetMoney() + dto.getTotalSlotValidBetMoney() + dto.getTotalFishValidBetMoney()
                    + dto.getTotalCasinoValidBetMoney() + dto.getTotalPokerValidBetMoney() + dto.getTotalMiniValidBetMoney() + dto.getTotalSportValidBetMoney() + dto.getTotalEsportValidBetMoney()
                    + dto.getTotalCockValidBetMoney();
            dto.setTotalCanReceivedValidBetMoney(totalCanReceivedValidBetMoney);
        }
        dto.setTotalValidBetMoney((Objects.nonNull(statistics) && Objects.nonNull(statistics.getTotalValidBetMoney())) ? statistics.getTotalValidBetMoney() : 0L);
        dto.setTotalRebateMoney((Objects.nonNull(statistics) && Objects.nonNull(statistics.getTotalRebateMoney()))? statistics.getTotalRebateMoney() : 0L);

        return dto;
    }

    private Long getCanReceivedRebateAmount(ActivityRebateStatistics statistics, ActivityVipConfigParam activityVipConfigParam, GameCategoryEnum gameCategoryEnum, ActivityRebateDTO dto) {
        Long canReceivedRebateAmount = 0L;
        switch (gameCategoryEnum) {
            case THREE:
                dto.setIncreaseThreeRebateRate(activityVipConfigParam.getIncreaseThreeRebateRate());
                Long threeRebateAmount = getRebateAmount(statistics.getThreeValidBetMoney(), statistics.getReceivedThreeValidBetMoney(), activityVipConfigParam.getIncreaseThreeRebateRate());
                if (Objects.nonNull(threeRebateAmount)) {
                    canReceivedRebateAmount = threeRebateAmount;
                    dto.setThreeReceivedRebateAmount(threeRebateAmount);
                    dto.setTotalThreeValidBetMoney(statistics.getThreeValidBetMoney() - statistics.getReceivedThreeValidBetMoney());
                }

                break;
            case LOTTERY:
                dto.setIncreaseLotteryRebateRate(activityVipConfigParam.getIncreaseLotteryRebateRate());
                Long lotteryRebateAmount = getRebateAmount(statistics.getLotteryValidBetMoney(), statistics.getReceivedLotteryValidBetMoney(), activityVipConfigParam.getIncreaseLotteryRebateRate());
                if (Objects.nonNull(lotteryRebateAmount)) {
                    canReceivedRebateAmount += lotteryRebateAmount;
                    dto.setLotteryReceivedRebateAmount(lotteryRebateAmount);
                    dto.setTotalLotteryValidBetMoney(statistics.getLotteryValidBetMoney() - statistics.getReceivedLotteryValidBetMoney());
                }

                break;
            case SLOT:
                dto.setIncreaseSlotRebateRate(activityVipConfigParam.getIncreaseSlotRebateRate());
                Long slotRebateAmount = getRebateAmount(statistics.getSlotValidBetMoney(), statistics.getReceivedSlotValidBetMoney(), activityVipConfigParam.getIncreaseSlotRebateRate());
                if (Objects.nonNull(slotRebateAmount)) {
                    canReceivedRebateAmount += slotRebateAmount;
                    dto.setSlotReceivedRebateAmount(slotRebateAmount);
                    dto.setTotalSlotValidBetMoney(statistics.getSlotValidBetMoney() - statistics.getReceivedSlotValidBetMoney());
                }
                break;
            case FISH:
                Long fishRebateAmount = getRebateAmount(statistics.getFishValidBetMoney(), statistics.getReceivedFishValidBetMoney(), activityVipConfigParam.getIncreaseFishRebateRate());
                dto.setIncreaseFishRebateRate(activityVipConfigParam.getIncreaseFishRebateRate());
                if (Objects.nonNull(fishRebateAmount)) {
                    canReceivedRebateAmount += fishRebateAmount;
                    dto.setFishReceivedRebateAmount(fishRebateAmount);
                    dto.setTotalFishValidBetMoney(statistics.getFishValidBetMoney() - statistics.getReceivedFishValidBetMoney());
                }
                break;
            case CASINO:
                Long casinoRebateAmount = getRebateAmount(statistics.getCasinoValidBetMoney(), statistics.getReceivedCasinoValidBetMoney(), activityVipConfigParam.getIncreaseCasinoRebateRate());
                dto.setIncreaseCasinoRebateRate(activityVipConfigParam.getIncreaseCasinoRebateRate());
                if (Objects.nonNull(casinoRebateAmount)) {
                    canReceivedRebateAmount += casinoRebateAmount;
                    dto.setCasinoReceivedRebateAmount(casinoRebateAmount);
                    dto.setTotalCasinoValidBetMoney(statistics.getCasinoValidBetMoney() - statistics.getReceivedCasinoValidBetMoney());
                }
                break;
            case POKER:
                Long pokerRebateAmount = getRebateAmount(statistics.getPokerValidBetMoney(), statistics.getReceivedPokerValidBetMoney(), activityVipConfigParam.getIncreasePokerRebateRate());
                dto.setIncreasePokerRebateRate(activityVipConfigParam.getIncreasePokerRebateRate());
                if (Objects.nonNull(pokerRebateAmount)) {
                    canReceivedRebateAmount += pokerRebateAmount;
                    dto.setPokerReceivedRebateAmount(pokerRebateAmount);
                    dto.setTotalPokerValidBetMoney(statistics.getPokerValidBetMoney() - statistics.getReceivedPokerValidBetMoney());
                }
                break;
            case MINI:
                Long miniRebateAmount = getRebateAmount(statistics.getMiniValidBetMoney(), statistics.getReceivedMiniValidBetMoney(), activityVipConfigParam.getIncreaseMiniRebateRate());
                dto.setIncreaseMiniRebateRate(activityVipConfigParam.getIncreaseMiniRebateRate());
                if (Objects.nonNull(miniRebateAmount)) {
                    canReceivedRebateAmount += miniRebateAmount;
                    dto.setMiniReceivedRebateAmount(miniRebateAmount);
                    dto.setTotalMiniValidBetMoney(statistics.getMiniValidBetMoney() - statistics.getReceivedMiniValidBetMoney());
                }
                break;
            case SPORT:
                Long sportRebateAmount = getRebateAmount(statistics.getSportValidBetMoney(), statistics.getReceivedSportValidBetMoney(), activityVipConfigParam.getIncreaseSportRebateRate());
                dto.setIncreaseSportRebateRate(activityVipConfigParam.getIncreaseSportRebateRate());
                if (Objects.nonNull(sportRebateAmount)) {
                    canReceivedRebateAmount += sportRebateAmount;
                    dto.setSportReceivedRebateAmount(sportRebateAmount);
                    dto.setTotalSportValidBetMoney(statistics.getSportValidBetMoney() - statistics.getReceivedSportValidBetMoney());
                }
                break;
            case E_SPORT:
                Long esportRebateAmount = getRebateAmount(statistics.getEsportValidBetMoney(), statistics.getReceivedEsportValidBetMoney(), activityVipConfigParam.getIncreaseESportRebateRate());
                dto.setIncreaseEsportRebateRate(activityVipConfigParam.getIncreaseESportRebateRate());
                if (Objects.nonNull(esportRebateAmount)) {
                    canReceivedRebateAmount += esportRebateAmount;
                    dto.setEsportReceivedRebateAmount(esportRebateAmount);
                    dto.setTotalEsportValidBetMoney(statistics.getEsportValidBetMoney() - statistics.getReceivedEsportValidBetMoney());
                }
                break;
            case COCK:
                dto.setIncreaseCockRebateRate(activityVipConfigParam.getIncreaseCockRebateRate());
                Long cockRebateAmount = getRebateAmount(statistics.getCockValidBetMoney(), statistics.getReceivedCockValidBetMoney(), activityVipConfigParam.getIncreaseCockRebateRate());
                if (Objects.nonNull(cockRebateAmount)) {
                    canReceivedRebateAmount += cockRebateAmount;
                    dto.setCockReceivedRebateAmount(cockRebateAmount);
                    dto.setTotalCockValidBetMoney(statistics.getCockValidBetMoney() - statistics.getReceivedCockValidBetMoney());
                }
                break;
            default:
                log.warn("gameCode not found...{}", gameCategoryEnum);
                break;
        }
        return canReceivedRebateAmount;
    }

    public Long getRebateAmount(Long validBetMoney,Long receivedValidBetMoney,Long rebateRate) {
        if (Objects.isNull(validBetMoney) || validBetMoney <= receivedValidBetMoney) {
            return null;
        }
        return new BigDecimal(validBetMoney - receivedValidBetMoney)
                .multiply(new BigDecimal(rebateRate)).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP).longValue();

    }


    @Override
    public void saveRebateStatistics(List<CashDataStatisticsDTO> cashDataStatisticsDtoList) {
        if (CollUtil.isEmpty(cashDataStatisticsDtoList)) {
            return;
        }
        //先过滤掉未开启活动站点的数据
        cashDataStatisticsDtoList = cashDataStatisticsDtoList.stream()
                .filter(it -> it.getValidBetAmount() > 0)
                .filter(it -> {
                    //有内存缓存
                    Activity rebateActivity = activityService.getEnableOne(it.getMerchantId(), it.getCurrencyEnum(), ActivityTypeEnum.REBATE);
                    Activity vipActivity = activityService.getEnableOne(it.getMerchantId(), it.getCurrencyEnum(), ActivityTypeEnum.VIP);
                    return Objects.nonNull(rebateActivity) && Objects.nonNull(vipActivity);
                })
                .collect(Collectors.toList());

        // 先按顺序进行扣减 - 处理投注金额扣减逻辑
        List<CashDataStatisticsDTO> processedDataList = processRebateFilterDeduction(cashDataStatisticsDtoList);

        List<ActivityRebateStatistics> statisticsList = processedDataList.stream()
                .collect(Collectors.groupingBy(CashDataStatisticsDTO::getMerchantId))
                .entrySet().stream()
                .filter(entry -> !CollUtil.isEmpty(entry.getValue()))
                .flatMap(entry -> entry.getValue().stream()
                        .collect(Collectors.groupingBy(CashDataStatisticsDTO::getCurrencyEnum))
                        .entrySet().stream()
                        .flatMap(currencyEntry -> currencyEntry.getValue().stream()
                                .collect(Collectors.groupingBy(CashDataStatisticsDTO::getMemberId))
                                .values().stream()
                                .filter(cashDataStatisticsDTOS -> !CollUtil.isEmpty(cashDataStatisticsDTOS))
                                .map(this::buildRebateStatistics)
                        )
                )
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(statisticsList)) {
            return;
        }

        activityRebateStatisticsMapper.saveOrUpdateBatchOnDuplicate(statisticsList);

    }

    /**
     * 处理返水过滤金额扣减逻辑
     * 对每个会员的投注金额进行扣减，直到rebateFilterMoney字段为零才能进行统计
     * 根据扣减后的未扣减金额，将剩余金额按比例设置回投注记录
     */
    private List<CashDataStatisticsDTO> processRebateFilterDeduction(List<CashDataStatisticsDTO> cashDataStatisticsDtoList) {
        List<CashDataStatisticsDTO> processedList = new ArrayList<>();

        // 按会员分组处理
        Map<String, List<CashDataStatisticsDTO>> memberGroupMap = cashDataStatisticsDtoList.stream()
                .collect(Collectors.groupingBy(dto -> dto.getMerchantId() + "_" + dto.getMemberId()));

        for (Map.Entry<String, List<CashDataStatisticsDTO>> entry : memberGroupMap.entrySet()) {
            List<CashDataStatisticsDTO> memberDataList = entry.getValue();
            if (CollUtil.isEmpty(memberDataList)) {
                continue;
            }

            CashDataStatisticsDTO firstDto = memberDataList.get(0);
            Long merchantId = firstDto.getMerchantId();
            Long memberId = firstDto.getMemberId();

            // 计算该会员的总投注金额
            Long totalBetAmount = memberDataList.stream()
                    .mapToLong(CashDataStatisticsDTO::getValidBetAmount)
                    .sum();

            if (totalBetAmount <= 0) {
                // 如果没有有效投注金额，直接跳过
                continue;
            }

            try {
                // 先扣减过滤金额，返回未扣减完的金额（即可用于统计的投注金额）
                Long undeductedAmount = deductRebateFilterMoney(merchantId, memberId, totalBetAmount);

                // 计算被扣减的金额
                Long deductedAmount = totalBetAmount - undeductedAmount;

                log.debug("Member: {}, total bet: {}, deducted: {}, undeducted: {}",
                        memberId, totalBetAmount, deductedAmount, undeductedAmount);

                // 根据未扣减金额处理投注记录
                if (undeductedAmount > 0) {
                    // 有未扣减完的金额，按比例分配到各个游戏类型
                    processUndeductedAmount(memberDataList, undeductedAmount, totalBetAmount, processedList);
                } else {
                    // 全部被扣减，不进行统计
                    log.debug("All bet amount deducted for member: {}, total bet: {}", memberId, totalBetAmount);
                }

            } catch (Exception e) {
                log.error("Failed to process rebate filter deduction for member: {}, error: {}", memberId, e.getMessage(), e);
                // 发生异常时，保留原始数据以确保统计不中断
                processedList.addAll(memberDataList);
            }
        }

        return processedList;
    }

    /**
     * 处理未扣减完的金额，按顺序扣减，不进行平均分配
     * 按顺序扣减各个游戏类型的投注金额，直到某个游戏类型刚好扣减完或还有剩余
     */
    private void processUndeductedAmount(List<CashDataStatisticsDTO> memberDataList,
                                       Long undeductedAmount,
                                       Long totalBetAmount,
                                       List<CashDataStatisticsDTO> processedList) {

        // 计算总的被扣减金额
        Long totalDeductedAmount = totalBetAmount - undeductedAmount;
        Long remainingDeductAmount = totalDeductedAmount; // 剩余需要扣减的金额

        log.debug("Total bet: {}, undeducted: {}, need to deduct: {}",
                totalBetAmount, undeductedAmount, totalDeductedAmount);

        for (CashDataStatisticsDTO dto : memberDataList) {
            CashDataStatisticsDTO processedDto = new CashDataStatisticsDTO();
            BeanUtil.copyProperties(dto, processedDto);

            Long originalBetAmount = dto.getValidBetAmount();
            Long adjustedBetAmount;

            if (remainingDeductAmount <= 0) {
                // 已经扣减完毕，后续的投注记录保持原金额
                adjustedBetAmount = originalBetAmount;
            } else if (remainingDeductAmount >= originalBetAmount) {
                // 当前记录的金额全部被扣减
                remainingDeductAmount -= originalBetAmount;
                adjustedBetAmount = 0L;
            } else {
                // 当前记录部分被扣减，剩余部分保留
                adjustedBetAmount = originalBetAmount - remainingDeductAmount;
                remainingDeductAmount = 0L; // 扣减完毕
            }

            processedDto.setValidBetAmount(adjustedBetAmount);

            if (adjustedBetAmount > 0) {
                processedList.add(processedDto);
            }

            log.debug("Game type: {}, original: {}, remaining deduct: {}, adjusted: {}",
                    dto.getGameCategoryEnum(), originalBetAmount, remainingDeductAmount, adjustedBetAmount);
        }
    }

    private ActivityRebateStatistics buildRebateStatistics(List<CashDataStatisticsDTO> cashDataStatisticsDTOS) {
        ActivityRebateStatistics statistics = new ActivityRebateStatistics();
        statistics.setCreateTime(LocalDateTime.now());
        statistics.setUpdateTime(LocalDateTime.now());
        statistics.setCurrencyEnum(cashDataStatisticsDTOS.get(0).getCurrencyEnum());
        statistics.setMemberId(cashDataStatisticsDTOS.get(0).getMemberId());
        statistics.setMerchantId(cashDataStatisticsDTOS.get(0).getMerchantId());
        statistics.setMemberName(cashDataStatisticsDTOS.get(0).getMemberName());
        setBetMoney(statistics, cashDataStatisticsDTOS);
        buildDefaultValidBetAmount(statistics);
        return statistics;
    }

    private void buildDefaultValidBetAmount(ActivityRebateStatistics statistics) {
        if (Objects.isNull(statistics.getTotalValidBetMoney())) {
            statistics.setTotalValidBetMoney(0L);
        }
        if (Objects.isNull(statistics.getThreeValidBetMoney())) {
            statistics.setThreeValidBetMoney(0L);
        }
        if (Objects.isNull(statistics.getLotteryValidBetMoney())) {
            statistics.setLotteryValidBetMoney(0L);
        }
        if (Objects.isNull(statistics.getSlotValidBetMoney())) {
            statistics.setSlotValidBetMoney(0L);
        }
        if (Objects.isNull(statistics.getFishValidBetMoney())) {
            statistics.setFishValidBetMoney(0L);
        }
        if (Objects.isNull(statistics.getCasinoValidBetMoney())) {
            statistics.setCasinoValidBetMoney(0L);
        }
        if (Objects.isNull(statistics.getPokerValidBetMoney())) {
            statistics.setPokerValidBetMoney(0L);
        }
        if (Objects.isNull(statistics.getMiniValidBetMoney())) {
            statistics.setMiniValidBetMoney(0L);
        }
        if (Objects.isNull(statistics.getSportValidBetMoney())) {
            statistics.setSportValidBetMoney(0L);
        }
        if (Objects.isNull(statistics.getEsportValidBetMoney())) {
            statistics.setEsportValidBetMoney(0L);
        }
        if (Objects.isNull(statistics.getCockValidBetMoney())) {
            statistics.setCockValidBetMoney(0L);
        }
        if (Objects.isNull(statistics.getRebateFilterMoney())) {
            statistics.setRebateFilterMoney(0L);
        }
    }

    private void setBetMoney(ActivityRebateStatistics statistics, List<CashDataStatisticsDTO> cashDataStatisticsDTOS) {
        long totalValidBetAmount = 0L;
        for (Map.Entry<GameCategoryEnum, List<CashDataStatisticsDTO>> entry : cashDataStatisticsDTOS.stream().collect(Collectors.groupingBy(CashDataStatisticsDTO::getGameCategoryEnum)).entrySet()) {
            GameCategoryEnum gameCode = entry.getKey();
            List<CashDataStatisticsDTO> cashDataStatisticsDTOList = entry.getValue();
            if (CollUtil.isEmpty(cashDataStatisticsDTOList)) {
                continue;
            }
            //根据gameCode汇总validBetAmount
            long validBetAmount = cashDataStatisticsDTOList.stream().mapToLong(CashDataStatisticsDTO::getValidBetAmount).sum();
            switch (gameCode) {
                case THREE:
                    totalValidBetAmount += validBetAmount;
                    statistics.setThreeValidBetMoney(validBetAmount);
                    break;
                case LOTTERY:
                    totalValidBetAmount += validBetAmount;
                    statistics.setLotteryValidBetMoney(validBetAmount);
                    break;
                case SLOT:
                    totalValidBetAmount += validBetAmount;
                    statistics.setSlotValidBetMoney(validBetAmount);
                    break;
                case FISH:
                    totalValidBetAmount += validBetAmount;
                    statistics.setFishValidBetMoney(validBetAmount);
                    break;
                case CASINO:
                    totalValidBetAmount += validBetAmount;
                    statistics.setCasinoValidBetMoney(validBetAmount);
                    break;
                case POKER:
                    totalValidBetAmount += validBetAmount;
                    statistics.setPokerValidBetMoney(validBetAmount);
                    break;
                case MINI:
                    totalValidBetAmount += validBetAmount;
                    statistics.setMiniValidBetMoney(validBetAmount);
                    break;
                case SPORT:
                    totalValidBetAmount += validBetAmount;
                    statistics.setSportValidBetMoney(validBetAmount);
                    break;
                case E_SPORT:
                    totalValidBetAmount += validBetAmount;
                    statistics.setEsportValidBetMoney(validBetAmount);
                    break;
                case COCK:
                    totalValidBetAmount += validBetAmount;
                    statistics.setCockValidBetMoney(validBetAmount);
                    break;
                default:
                    log.warn("gameCode not found...{}", gameCode);
                    break;
            }
        }
        statistics.setTotalValidBetMoney(totalValidBetAmount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRebateFilterMoney(Long merchantId, Long memberId, String memberName, CurrencyEnum currencyEnum, Long filterAmount) {
        if (filterAmount == null || filterAmount <= 0) {
            return;
        }

        ActivityRebateStatistics statistics = new ActivityRebateStatistics();
        statistics.setCreateTime(LocalDateTime.now());
        statistics.setUpdateTime(LocalDateTime.now());
        statistics.setCurrencyEnum(currencyEnum);
        statistics.setMemberId(memberId);
        statistics.setMerchantId(merchantId);
        statistics.setMemberName(memberName);
        buildDefaultValidBetAmount(statistics);
        statistics.setRebateFilterMoney(filterAmount);
        activityRebateStatisticsMapper.saveOrUpdateBatchOnDuplicate(Collections.singletonList(statistics));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Retryable(value = NeedRetryException.class, maxAttempts = 3)
    public Long deductRebateFilterMoney(Long merchantId, Long memberId, Long deductAmount) {
        if (deductAmount == null || deductAmount <= 0) {
            return 0L;
        }

        // 查询当前的过滤金额
        ActivityRebateStatistics statistics = this.lambdaQuery()
                .eq(ActivityRebateStatistics::getMerchantId, merchantId)
                .eq(ActivityRebateStatistics::getMemberId, memberId)
                .one();

        if (statistics == null || statistics.getRebateFilterMoney() == null || statistics.getRebateFilterMoney() <= 0) {
            // 没有过滤金额，可以直接发放全部奖励
            return deductAmount;
        }

        Long currentFilterMoney = statistics.getRebateFilterMoney();
        Long actualDeductAmount = Math.min(currentFilterMoney, deductAmount);
        Long remainingReward = deductAmount - actualDeductAmount;

        // 使用原子性操作扣除过滤金额，此次就算因为其他情况未扣减为 0，那么下次发放奖励时会再次扣除
        String columnName = SqlUtil.columnToUnderline(ActivityRebateStatistics::getRebateFilterMoney);
        String sqlOperation = columnName + " = GREATEST(0, COALESCE(" + columnName + ", 0) - " + actualDeductAmount + ")";

        boolean updated = this.lambdaUpdate()
                .setSql(sqlOperation)
                .set(ActivityRebateStatistics::getUpdateTime, LocalDateTime.now())
                .eq(ActivityRebateStatistics::getMemberId, memberId)
                .eq(ActivityRebateStatistics::getMerchantId, merchantId)
                .eq(ActivityRebateStatistics::getRebateFilterMoney, currentFilterMoney)
                .update();

        if (!updated) {
            // 更新失败说明 rebateFilterMoney 条件不匹配，有其他线程进行了数据修改，抛出重试异常
            String errorMsg = String.format("Failed to deduct rebate filter money due to concurrent modification, memberId: %d, currentFilterMoney: %d, deductAmount: %d",
                    memberId, currentFilterMoney, actualDeductAmount);
            log.warn(errorMsg);
            throw new NeedRetryException(errorMsg);
        }

        log.debug("Deducted rebate filter money for memberId: {}, deductAmount: {}, remainingReward: {}",
                memberId, actualDeductAmount, remainingReward);

        return remainingReward; // 返回扣除过滤金额后可发放的奖励金额
    }
}
