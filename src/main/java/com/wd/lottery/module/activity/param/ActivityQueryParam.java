package com.wd.lottery.module.activity.param;

import com.wd.lottery.common.api.BaseParam;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.activity.constants.ActivityTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ActivityQueryParam extends BaseParam {

    private Long id;

    @Schema(description = "活动标题")
    private String title;

    @Schema(description = "活动内容")
    private String content;

    @Schema(description = "商户id", hidden = true)
    private Long merchantId;

    private ActivityTypeEnum activityTypeEnum;

    private EnableEnum enableEnum;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    @Schema(description = "货币类型", hidden = true)
    private CurrencyEnum currencyEnum;
}
