package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.GrapeApplication;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.util.Tup2;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.entity.ActivityAATeamProxyLowerLevelMember;
import com.wd.lottery.module.activity.entity.ActivityAATeamProxyMember;
import com.wd.lottery.module.activity.mapper.ActivityAATeamProxyLowerLevelMemberMapper;
import com.wd.lottery.module.activity.param.AAActivityTeamProxyStatQueryParam;
import com.wd.lottery.module.activity.param.ActivityTeamProxyLowerLevelUserQueryParam;
import com.wd.lottery.module.activity.param.ActivityTeamProxyRecentAddLowerLevelUserParam;
import com.wd.lottery.module.activity.service.ActivityAATeamProxyBonusDailySummaryService;
import com.wd.lottery.module.activity.service.ActivityAATeamProxyLowerLevelMemberService;
import com.wd.lottery.module.activity.service.ActivityAATeamProxyMemberService;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.activity.param.teamproxy.ActivityAATeamProxyParam;
import com.wd.lottery.module.activity.param.teamproxy.ActivityAATeamProxyConfigParam;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.constants.ActivityTypeEnum;
import com.wd.lottery.module.member.constants.MemberLevelQueryTypeEnum;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.entity.MemberProxy;
import com.wd.lottery.module.member.param.QueryMemberProxyParam;
import com.wd.lottery.module.member.service.MemberProxyService;
import com.wd.lottery.module.report.constans.DepositTypeEnum;

import com.wd.lottery.module.report.entity.ReportMemberDate;
import com.wd.lottery.module.report.entity.ReportMemberTotal;
import com.wd.lottery.module.report.service.ReportFirstDepositHisService;
import com.wd.lottery.module.report.service.ReportMemberActivityTotalService;
import com.wd.lottery.module.report.service.ReportMemberDateService;
import com.wd.lottery.module.report.service.ReportMemberTotalService;

import com.wd.lottery.common.constans.CurrencyEnum;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ActivityAATeamProxyLowerLevelMemberServiceImpl
        extends ServiceImpl<ActivityAATeamProxyLowerLevelMemberMapper, ActivityAATeamProxyLowerLevelMember>
        implements ActivityAATeamProxyLowerLevelMemberService {

    @Resource
    private ActivityAATeamProxyBonusDailySummaryService teamProxyBonusDailySummaryService;

    @Resource
    private ActivityAATeamProxyMemberService teamProxyMemberService;

    @Resource
    private MemberProxyService memberProxyService;

    @Resource
    private ReportMemberActivityTotalService reportMemberActivityTotalService;

    @Resource
    private ReportMemberTotalService reportMemberTotalService;
    @Resource
    private ReportFirstDepositHisService reportFirstDepositHisService;
    @Resource
    private ReportMemberDateService reportMemberDateService;
    @Resource
    private ActivityService activityService;

    @Override
    public TeamProxyMemberHomePageDataDTO getHomePageData(MemberTokenInfoDTO dto) {
        LocalDate yesterdayPeriods = LocalDateTimeUtil.of(DateUtil.yesterday()).toLocalDate();
        Long merchantId = dto.getMerchantId();
        Long memberId = dto.getId();
        TeamProxyMemberHomePageDataDTO bonusDTO = new TeamProxyMemberHomePageDataDTO();

        // 获取昨日佣金
        Long yesterdayBonusAmount = teamProxyBonusDailySummaryService.getTotalBonusAmount(merchantId, memberId, yesterdayPeriods, yesterdayPeriods);
        bonusDTO.setYesterdayTeamProxyBonus(yesterdayBonusAmount);

        // 获取历史累计数据
        ActivityAATeamProxyMember teamProxyMember = teamProxyMemberService.getById(merchantId, memberId);

        // 获取今日累计数据
        ActivityAATeamProxyLowerLevelMemberServiceImpl bean = GrapeApplication.getBean(ActivityAATeamProxyLowerLevelMemberServiceImpl.class);
        TeamProxyTodayRealTimeDataDTO todayData = bean.getTodayRealTimeDepositAndBetData(merchantId, memberId, dto.getCurrencyEnum(), teamProxyMember);
        if (teamProxyMember != null) {
            bonusDTO.setTotalTeamProxyBonus(teamProxyMember.getTotalTeamProxyBonus());
            bonusDTO.setTeamLevel(teamProxyMember.getTeamLevel());

            bonusDTO.setTotalRechargeAmount(teamProxyMember.getTotalRechargeAmount() + todayData.getTodayRechargeAmount());
            bonusDTO.setTotalRechargeUserCount(teamProxyMember.getTotalRechargeUserCount() + todayData.getTodayRechargeUserCount());
            bonusDTO.setTotalBetAmount(teamProxyMember.getTotalBetAmount() + todayData.getTodayBetAmount());
        } else {
            bonusDTO.setTotalRechargeAmount(todayData.getTodayRechargeAmount());
            bonusDTO.setTotalRechargeUserCount(todayData.getTodayRechargeUserCount());
            bonusDTO.setTotalBetAmount(todayData.getTodayBetAmount());
        }

        //使用缓存中重新计算的团队等级
        if (todayData.getRecalculatedTeamLevel() != null && todayData.getRecalculatedTeamLevel() > bonusDTO.getTeamLevel()) {
            bonusDTO.setTeamLevel(todayData.getRecalculatedTeamLevel());
        }

        return bonusDTO;
    }

    @Override
    public TeamProxyRecentAddLowerLevelUserStatDTO getTeamProxyLowerLevelUserStat(ActivityTeamProxyRecentAddLowerLevelUserParam param) {
        TeamProxyRecentAddLowerLevelUserStatDTO statDTO = new TeamProxyRecentAddLowerLevelUserStatDTO();
        Long totalUserProxyCount = memberProxyService.getUserProxyCount(param.getMerchantId(), param.getHighMemberId(), 3);
        statDTO.setTotalUserCount(totalUserProxyCount);

        //本月第一天是从昨天的首月开始算，结束为当前时间。 所以昨日数据是有的，不需要单独获取
        LocalDateTime monthStartTime = LocalDateTimeUtil.of(DateUtil.beginOfMonth(DateUtil.yesterday()));
        LocalDateTime monthEndTime = LocalDateTimeUtil.of(DateUtil.endOfDay(new Date()));
        Map<LocalDate, Long> userProxyCountGroupByDate = memberProxyService.getUserProxyCountGroupByDate(param.getMerchantId(), param.getHighMemberId(), 3, false, monthStartTime, monthEndTime);
        long monthUserProxyCount = userProxyCountGroupByDate.values().stream().mapToLong(Long::longValue).sum();
        statDTO.setMonthUserCount(monthUserProxyCount);
        Long todayUserProxyCount = userProxyCountGroupByDate.getOrDefault(LocalDate.now(), 0L);
        statDTO.setTodayUserCount(todayUserProxyCount);
        Long yesterdayUserProxyCount = userProxyCountGroupByDate.getOrDefault(LocalDate.now().minusDays(1), 0L);
        statDTO.setYesterdayUserCount(yesterdayUserProxyCount);

        Map<Integer, Long> allTierUserProxyCount = memberProxyService.getUserProxyCountGroupByLev(param.getMerchantId(), param.getHighMemberId(), 3, false, param.getStartTime(), param.getEndTime());
        Long tier1UserProxyCount = allTierUserProxyCount.getOrDefault(1, 0L);
        Long tier2UserProxyCount = allTierUserProxyCount.getOrDefault(2, 0L);
        Long tier3UserProxyCount = allTierUserProxyCount.getOrDefault(3, 0L);
        statDTO.setTier1(tier1UserProxyCount);
        statDTO.setTier2(tier2UserProxyCount);
        statDTO.setTier3(tier3UserProxyCount);

        return statDTO;
    }

    @Override
    public Page<TeamProxyRecentAddLowerLevelUserDTO> getTeamProxyRecentAddLowerLevelUserByPage(ActivityTeamProxyLowerLevelUserQueryParam param) {
        Long merchantId = param.getMerchantId();
        Page<MemberProxy> memberProxyPage = memberProxyService.getMemberProxyByHighNameForPage(QueryMemberProxyParam.builder()
                .merchantId(merchantId)
                .highMemberId(param.getHighMemberId())
                .memberId(param.getMemberId())
                .startTime(param.getStartTime())
                .endEnd(param.getEndTime())
                .level(param.getLevel())
                .levelQueryTypeEnum(MemberLevelQueryTypeEnum.MATCH_LEVEL)
                .current(param.getCurrent())
                .size(param.getSize())
                .build());

        if (memberProxyPage == null || CollectionUtil.isEmpty(memberProxyPage.getRecords())) {
            return new Page<>(param.getCurrent(), param.getSize(), 0);
        }
        List<TeamProxyRecentAddLowerLevelUserDTO> collect = memberProxyPage.getRecords().stream().map(v -> {
            TeamProxyRecentAddLowerLevelUserDTO dto = new TeamProxyRecentAddLowerLevelUserDTO();
            BeanUtil.copyProperties(v, dto);
            return dto;
        }).collect(Collectors.toList());

        Map<Long, Long> memberTotalMap = reportMemberTotalService.getListByMemberIdList(merchantId,
                        collect.stream().map(TeamProxyRecentAddLowerLevelUserDTO::getMemberId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(ReportMemberTotal::getMemberId, it -> it.getDepositAmount(), (k1, k2) -> k1));

        for (TeamProxyRecentAddLowerLevelUserDTO levelUserDTO : collect) {
            levelUserDTO.setRechargeAmount(memberTotalMap.getOrDefault(levelUserDTO.getMemberId(), 0L));
        }

        return new Page<TeamProxyRecentAddLowerLevelUserDTO>(memberProxyPage.getCurrent(),memberProxyPage.getSize(), memberProxyPage.getTotal())
                .setRecords(collect);
    }

    @Override
    public Page<TeamProxyLowerLevelMemberBonusDTO> getTeamProxyLowerLevelUserByPage(ActivityTeamProxyLowerLevelUserQueryParam param) {
        LocalDate startPeriods = param.getStartTime().toLocalDate();
        LocalDate endPeriods = param.getEndTime().toLocalDate();
        Page<ActivityAATeamProxyLowerLevelMember> p = new Page<>(param.getCurrent(), param.getSize());
        LambdaQueryWrapper<ActivityAATeamProxyLowerLevelMember> wrapper = new LambdaQueryWrapper<ActivityAATeamProxyLowerLevelMember>()
                .eq(ActivityAATeamProxyLowerLevelMember::getMerchantId, param.getMerchantId())
                .eq(ActivityAATeamProxyLowerLevelMember::getHighMemberId, param.getHighMemberId())
                .between(ActivityAATeamProxyLowerLevelMember::getPeriods, startPeriods, endPeriods)
                .eq(param.getMemberId() != null, ActivityAATeamProxyLowerLevelMember::getMemberId, param.getMemberId())
                .eq(param.getLevel() != null, ActivityAATeamProxyLowerLevelMember::getLevel, param.getLevel())
                .orderBy(true, param.getSortEnum().getIsAsc(), param.getSearchTypeEnum().getColumn());

        Page<ActivityAATeamProxyLowerLevelMember> page = baseMapper.selectPage(p, wrapper);

        List<TeamProxyLowerLevelMemberBonusDTO> list = page.getRecords().stream().map(v -> {
            TeamProxyLowerLevelMemberBonusDTO bonusDTO = new TeamProxyLowerLevelMemberBonusDTO();
            BeanUtil.copyProperties(v, bonusDTO);
            return bonusDTO;
        }).collect(Collectors.toList());

        Page<TeamProxyLowerLevelMemberBonusDTO> dtoPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        dtoPage.setRecords(list);
        return dtoPage;
    }

    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_LONG_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public AATeamProxyMemberStatDTO getTeamProxyLowerLevelMemberStat(AAActivityTeamProxyStatQueryParam param) {
        Long merchantId = param.getMerchantId();
        Long memberId = param.getMemberId();
        LocalDate startDate = param.getStartDate();
        LocalDate endDate = param.getEndDate();

        LocalDateTime startTime = LocalDateTimeUtil.beginOfDay(startDate);
        LocalDateTime endTime = LocalDateTimeUtil.endOfDay(endDate);

        AATeamProxyMemberStatDTO stat = new AATeamProxyMemberStatDTO();
        Long directRegisterUserCount = memberProxyService.getUserProxyCount(merchantId, memberId,
                1, true, startTime, endTime);
        stat.setDirectRegisterUserCount(Math.toIntExact(directRegisterUserCount));
        Long teamRegisterUserCount = memberProxyService.getUserProxyCount(merchantId, memberId,
                3, false, startTime, endTime);
        stat.setTeamRegisterUserCount(Math.toIntExact(teamRegisterUserCount));

        Tup2<Long, Long> directFirstDeposit = reportFirstDepositHisService.getFirstDepositTotalCountAndAmount(
                merchantId, memberId, 1, DepositTypeEnum.FIRST, startTime, endTime);
        stat.setDirectFirstRechargeUserCount(Math.toIntExact(directFirstDeposit.get_1()));
        stat.setDirectFirstRechargeAmount(directFirstDeposit.get_2());
        Tup2<Long, Long> teamFirstDeposit = reportFirstDepositHisService.getFirstDepositTotalCountAndAmount(
                merchantId, memberId, 3, DepositTypeEnum.FIRST, startTime, endTime);
        stat.setTeamFirstRechargeUserCount(Math.toIntExact(teamFirstDeposit.get_1()));
        stat.setTeamFirstRechargeAmount(teamFirstDeposit.get_2());

        ReportMemberDate directDepositAndWithdraw = reportMemberDateService.getTeamDepositAndWithdrawStatistics(merchantId, memberId, 1, startDate, endDate);
        stat.setDirectRechargeUserCount(Math.toIntExact(directDepositAndWithdraw.getDepositCount()));
        stat.setDirectRechargeAmount(directDepositAndWithdraw.getDepositAmount());
        stat.setDirectWithdrawUserCount(Math.toIntExact(directDepositAndWithdraw.getWithdrawCount()));
        stat.setDirectWithdrawAmount(directDepositAndWithdraw.getWithdrawAmount());

        ReportMemberDate teamDepositAndWithdraw = reportMemberDateService.getTeamDepositAndWithdrawStatistics(merchantId, memberId, 3, startDate, endDate);
        stat.setTeamRechargeUserCount(Math.toIntExact(teamDepositAndWithdraw.getDepositCount()));
        stat.setTeamRechargeAmount(teamDepositAndWithdraw.getDepositAmount());
        stat.setTeamWithdrawUserCount(Math.toIntExact(teamDepositAndWithdraw.getWithdrawCount()));
        stat.setTeamWithdrawAmount(teamDepositAndWithdraw.getWithdrawAmount());

        Long bonusAmount = teamProxyBonusDailySummaryService.getTotalBonusAmount(merchantId, memberId, startDate, endDate);
        stat.setTotalTeamProxyBonus(bonusAmount);

        return stat;
    }

    @Override
    public Set<Long> getHistoricalRechargeMembers(Long merchantId, Set<Long> memberIds) {
        if (CollectionUtil.isEmpty(memberIds)) {
            return Collections.emptySet();
        }

        // 查询历史上有过充值记录的用户（rechargeAmount > 0）
        LambdaQueryWrapper<ActivityAATeamProxyLowerLevelMember> wrapper = new LambdaQueryWrapper<ActivityAATeamProxyLowerLevelMember>()
                .eq(ActivityAATeamProxyLowerLevelMember::getMerchantId, merchantId)
                .in(ActivityAATeamProxyLowerLevelMember::getMemberId, memberIds)
                .gt(ActivityAATeamProxyLowerLevelMember::getRechargeAmount, 0L)
                .select(ActivityAATeamProxyLowerLevelMember::getMemberId);

        List<ActivityAATeamProxyLowerLevelMember> historicalRechargeRecords = this.list(wrapper);

        return historicalRechargeRecords.stream()
                .map(ActivityAATeamProxyLowerLevelMember::getMemberId)
                .collect(Collectors.toSet());
    }

    /**
     * 获取今日实时数据（带缓存优化）- 优化版本，避免重复查询
     * 默认缓存10分钟，减少数据库查询压力
     * 同时计算基于最新数据的团队等级
     */
    public TeamProxyTodayRealTimeDataDTO getTodayRealTimeDepositAndBetData(Long merchantId, Long memberId, CurrencyEnum currencyEnum, ActivityAATeamProxyMember teamProxyMember) {
        TeamProxyTodayRealTimeDataDTO result = new TeamProxyTodayRealTimeDataDTO();

        try {
            // 获取今日时间范围
            LocalDate today = LocalDate.now();

            // 直接调用报表服务获取团队实时统计数据
            ReportMemberDate todayStats = reportMemberDateService.getTeamRealTimeDepositAndBetStatistics(
                merchantId, memberId, 3, today, today);

            if (todayStats != null) {
                result.setTodayRechargeUserCount(Math.toIntExact(todayStats.getDepositCount()));
                result.setTodayRechargeAmount(todayStats.getDepositAmount());
                result.setTodayBetAmount(todayStats.getBetAmount());
            }

            // 如果今日有数据，则计算基于最新数据的团队等级
            if (result.getTodayRechargeUserCount() > 0 || result.getTodayRechargeAmount() > 0 || result.getTodayBetAmount() > 0) {
                Integer recalculatedLevel = computeTeamLevelWithTodayData(merchantId, memberId, currencyEnum, result, teamProxyMember);
                result.setRecalculatedTeamLevel(recalculatedLevel);
            }

        } catch (Exception e) {
            // 异常情况下返回默认值，避免影响主流程
            log.warn("获取今日实时数据失败, merchantId: {}, memberId: {}", merchantId, memberId, e);
        }

        return result;
    }

    /**
     * 根据今日数据重新计算团队等级
     *
     * @param merchantId 商户ID
     * @param memberId 会员ID
     * @param currencyEnum 币种
     * @param todayData 今日实时数据
     * @param teamProxyMember 历史累计数据（避免重复查询）
     * @return 新的团队等级，如果无法计算则返回null
     */
    private Integer computeTeamLevelWithTodayData(Long merchantId, Long memberId, CurrencyEnum currencyEnum, TeamProxyTodayRealTimeDataDTO todayData, ActivityAATeamProxyMember teamProxyMember) {
        try {
            // 计算最新的累计数据（历史 + 今日）
            // 先从历史数据开始，如果没有历史数据则从0开始
            Integer totalRechargeUserCount = teamProxyMember != null ? teamProxyMember.getTotalRechargeUserCount() : 0;
            Long totalRechargeAmount = teamProxyMember != null ? teamProxyMember.getTotalRechargeAmount() : 0L;
            Long totalBetAmount = teamProxyMember != null ? teamProxyMember.getTotalBetAmount() : 0L;

            // 再加上今日的实时数据
            totalRechargeUserCount += todayData.getTodayRechargeUserCount();
            totalRechargeAmount += todayData.getTodayRechargeAmount();
            totalBetAmount += todayData.getTodayBetAmount();

            // 获取活动配置
            Activity activity = activityService.getEnableOne(merchantId, currencyEnum, ActivityTypeEnum.AA_TEAM_PROXY);
            if (activity == null) {
                log.warn("未找到团队代理活动配置, merchantId: {}, currencyEnum: {}", merchantId, currencyEnum);
                return null;
            }

            // 解析活动参数
            ActivityAATeamProxyParam param = activity.activityParamToBean(ActivityAATeamProxyParam.class);
            if (param == null || CollectionUtil.isEmpty(param.getActivityAATeamProxyConfigParamList())) {
                log.warn("团队代理活动配置参数为空, merchantId: {}, currencyEnum: {}", merchantId, currencyEnum);
                return null;
            }

            // 创建临时的团队代理会员对象用于计算等级
            ActivityAATeamProxyMember tempMember = new ActivityAATeamProxyMember();
            tempMember.setId(memberId);
            tempMember.setMerchantId(merchantId);
            tempMember.setCurrencyEnum(currencyEnum);
            tempMember.setTotalRechargeUserCount(totalRechargeUserCount);
            tempMember.setTotalRechargeAmount(totalRechargeAmount);
            tempMember.setTotalBetAmount(totalBetAmount);

            // 计算团队等级
            return computeTeamLevel(tempMember, param.getActivityAATeamProxyConfigParamList());

        } catch (Exception e) {
            log.error("重新计算团队等级失败, merchantId: {}, memberId: {}", merchantId, memberId, e);
            return null;
        }
    }

    /**
     * 计算团队等级
     * 参照 ActivityAATeamProxyServiceImpl 中的 computeTeamLevel 方法
     */
    private Integer computeTeamLevel(ActivityAATeamProxyMember member, List<ActivityAATeamProxyConfigParam> teamLevelConfigList) {
        if (CollectionUtil.isEmpty(teamLevelConfigList)) {
            return 0;
        }

        // 从大到小排序
        teamLevelConfigList.sort((o1, o2) -> o2.getTeamLevel().compareTo(o1.getTeamLevel()));

        for (ActivityAATeamProxyConfigParam setting : teamLevelConfigList) {
            boolean matchRechargeUserCount = false;
            boolean matchTotalRechargeAmount = false;
            boolean matchTotalBetAmount = false;

            if (member.getTotalRechargeUserCount() >= setting.getTotalRechargeUserCount()) {
                matchRechargeUserCount = true;
            }
            if (member.getTotalRechargeAmount() >= (setting.getTotalRechargeAmount() * 10000)) {
                matchTotalRechargeAmount = true;
            }
            if (member.getTotalBetAmount() >= (setting.getTotalBetAmount() * 10000)) {
                matchTotalBetAmount = true;
            }

            if (matchRechargeUserCount && matchTotalRechargeAmount && matchTotalBetAmount) {
                return setting.getTeamLevel();
            }
        }
        return 0;
    }

}
