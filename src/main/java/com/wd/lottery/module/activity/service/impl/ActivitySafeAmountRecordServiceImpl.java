package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.SqlUtil;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.dto.ActivitySafeAmountRecordDTO;
import com.wd.lottery.module.activity.dto.ActivitySafeAmountRecordSummaryDTO;
import com.wd.lottery.module.activity.dto.ActivitySafeMonthRecordDTO;
import com.wd.lottery.module.activity.entity.ActivitySafeAmountRecord;
import com.wd.lottery.module.activity.mapper.ActivitySafeAmountRecordMapper;
import com.wd.lottery.module.activity.param.ActivitySafeRecordQueryParam;
import com.wd.lottery.module.activity.service.ActivitySafeAmountRecordService;
import com.wd.lottery.module.cash.constatns.TradeTypeEnum;
import com.wd.lottery.module.common.config.ExportExcelConfig;
import com.wd.lottery.module.common.constants.ExcelExportParamEnum;
import com.wd.lottery.module.common.constants.ExportTypeEnum;
import com.wd.lottery.module.common.export.ActivitySafeAmountRecordExportExcelStrategy;
import com.wd.lottery.module.common.param.AsyncExportParam;
import com.wd.lottery.module.common.service.ExportExcelService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivitySafeAmountRecordServiceImpl extends ServiceImpl<ActivitySafeAmountRecordMapper, ActivitySafeAmountRecord> implements ActivitySafeAmountRecordService {

    @Resource
    private ExportExcelService exportExcelService;

    @Resource
    private ExportExcelConfig exportExcelConfig;

    public List<ActivitySafeAmountRecordDTO> getLatestRecord(Long memberId, Long merchantId) {
        Assert.notNull(memberId);
        Assert.notNull(merchantId);
        List<ActivitySafeAmountRecord> list = this.lambdaQuery()
                .eq(ActivitySafeAmountRecord::getMemberId, memberId)
                .eq(ActivitySafeAmountRecord::getMerchantId, merchantId)
                .orderByDesc(ActivitySafeAmountRecord::getId)
                .last("limit " + 10)
                .list();

        return list.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public ActivitySafeMonthRecordDTO getHistoryRecord(Long memberId, Long merchantId, String date) {
        DateTime dateTime = null;
        try {
            dateTime = DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN);
        } catch (Exception e) {
            throw new ApiException(CommonCode.PARAM_INVALID, "日期格式错误，正确格式为yyyy-MM");
        }
        Date start = DateUtil.beginOfMonth(dateTime);
        Date end = DateUtil.endOfMonth(dateTime);

        LocalDateTime startDateTime = LocalDateTimeUtil.of(start);
        LocalDateTime endDateTime = LocalDateTimeUtil.of(end);

        List<ActivitySafeAmountRecord> list = this.lambdaQuery()
                .eq(ActivitySafeAmountRecord::getMemberId, memberId)
                .eq(ActivitySafeAmountRecord::getMerchantId, merchantId)
                .ge(ActivitySafeAmountRecord::getCreateTime, startDateTime)
                .le(ActivitySafeAmountRecord::getCreateTime, endDateTime)
                .orderByDesc(ActivitySafeAmountRecord::getCreateTime)
                .list();

        Long inAmount = 0L;
        Long outAmount = 0L;
        Long income = 0L;
        for (ActivitySafeAmountRecord activityRecord : list) {
            if (activityRecord.getTradeTypeEnum() == TradeTypeEnum.IN) {
                inAmount += activityRecord.getAmount();
            } else {
                outAmount += activityRecord.getAmount();
                income += activityRecord.getIncome();
            }
        }

        return ActivitySafeMonthRecordDTO.builder()
                .inAmount(inAmount)
                .outAmount(outAmount)
                .income(income)
                .records(list.stream().map(this::convertToDTO).collect(Collectors.toList()))
                .build();
    }

    @Override
    public Page<ActivitySafeAmountRecordDTO> bGetPage(ActivitySafeRecordQueryParam param) {
        Assert.notNull(param.getMerchantId());
        Assert.notNull(param.getStartTime());
        Assert.notNull(param.getEndTime());

        Page<ActivitySafeAmountRecord> page = this.lambdaQuery()
                .eq(ActivitySafeAmountRecord::getMerchantId, param.getMerchantId())
                .eq(param.getMemberId() != null, ActivitySafeAmountRecord::getMemberId, param.getMemberId())
                .ge(ActivitySafeAmountRecord::getCreateTime, param.getStartTime())
                .le(ActivitySafeAmountRecord::getCreateTime, param.getEndTime())
                .eq(param.getTradeTypeEnum() != null, ActivitySafeAmountRecord::getTradeTypeEnum, param.getTradeTypeEnum())
                .eq(param.getCurrencyEnum() != null, ActivitySafeAmountRecord::getCurrencyEnum, param.getCurrencyEnum())
                .eq(CollUtil.isNotEmpty(param.getChannelIdSet()),ActivitySafeAmountRecord::getIsDirect, BooleanEnum.TRUE)
                .in(CollUtil.isNotEmpty(param.getChannelIdSet()),ActivitySafeAmountRecord::getChannelId, param.getChannelIdSet())
                .orderByDesc(ActivitySafeAmountRecord::getId)
                .page(new Page<>(param.getCurrent(), param.getSize()));
        return new Page<ActivitySafeAmountRecordDTO>(page.getCurrent(), page.getSize(), page.getTotal())
                .setRecords(page.getRecords().stream().map(this::convertToDTO).collect(Collectors.toList()));
    }

    @Override
    public ActivitySafeAmountRecordSummaryDTO bGetPageSummary(ActivitySafeRecordQueryParam param) {
        Assert.notNull(param.getMerchantId());
        Assert.notNull(param.getStartTime());
        Assert.notNull(param.getEndTime());

        ActivitySafeAmountRecordSummaryDTO dto = new ActivitySafeAmountRecordSummaryDTO();
        dto.setCurrencyEnum(param.getCurrencyEnum());

        TradeTypeEnum tradeTypeEnum = param.getTradeTypeEnum();
        if (tradeTypeEnum == null) {
            ActivitySafeAmountRecord depositAmountRecord = this.baseMapper.selectOne(buildSummaryWrapper(param, TradeTypeEnum.IN));
            ActivitySafeAmountRecord withdrawAmountRecord = this.baseMapper.selectOne(buildSummaryWrapper(param, TradeTypeEnum.OUT));

            if (depositAmountRecord != null && depositAmountRecord.getAmount() != null) {
                dto.setDepositAmount(depositAmountRecord.getAmount());
            }
            if (withdrawAmountRecord != null && withdrawAmountRecord.getAmount() != null) {
                dto.setWithdrawAmount(withdrawAmountRecord.getAmount());
            }
            if (withdrawAmountRecord != null && withdrawAmountRecord.getIncome() != null) {
                dto.setIncome(withdrawAmountRecord.getIncome());
            }
        } else {
            ActivitySafeAmountRecord record = this.baseMapper.selectOne(buildSummaryWrapper(param, tradeTypeEnum));
            if (record != null && record.getAmount() != null && tradeTypeEnum == TradeTypeEnum.IN) {
                dto.setDepositAmount(record.getAmount());
            }
            if (record != null && record.getAmount() != null && tradeTypeEnum == TradeTypeEnum.OUT) {
                dto.setWithdrawAmount(record.getAmount());
            }
            if (record != null && record.getIncome() != null && tradeTypeEnum == TradeTypeEnum.OUT) {
                dto.setIncome(record.getIncome());
            }
        }

        return dto;
    }

    @Override
    public ApiResult bExport(ActivitySafeRecordQueryParam param) {
        Map<String,Object> paramMap = Maps.newHashMap();
        buildExportParam(paramMap,param);
        AsyncExportParam asyncExportParam = new AsyncExportParam();
        asyncExportParam.setMerchantId(param.getMerchantId());
        asyncExportParam.setLanguage(param.getLanguage());
        asyncExportParam.setCreateBy(param.getCreateBy());
        asyncExportParam.setExportType(ExportTypeEnum.ACTIVITY_SAFE_AMOUNT_RECORD);
        asyncExportParam.setFileName(ExportTypeEnum.ACTIVITY_SAFE_AMOUNT_RECORD.getDesc() + "_" + DateUtil.today() + ".xlsx");

        log.info("exportParam..{},AsyncExportParam...{},map...{}", param, asyncExportParam, JSONUtil.toJsonStr(paramMap));
        ActivitySafeAmountRecordExportExcelStrategy strategy =
                new ActivitySafeAmountRecordExportExcelStrategy(ExcelExportParamEnum.ACTIVITY_SAFE_RECORD.getMapperMethod(),
                        ExcelExportParamEnum.ACTIVITY_SAFE_RECORD.getNextSearchField(), paramMap, exportExcelConfig.getLimit(), param.getLanguage());
        exportExcelService.asyncExportExcel(asyncExportParam, strategy, param.getIsAll());
        return ApiResult.success();
    }

    private void buildExportParam(Map<String, Object> paramMap, ActivitySafeRecordQueryParam param) {
        paramMap.put("merchantId",param.getMerchantId());
        if (Objects.nonNull(param.getMemberId())) {
            paramMap.put("memberId",param.getMemberId());
        }

        paramMap.put("startTime",param.getStartTime());
        paramMap.put("endTime",param.getEndTime());
        if (Objects.nonNull(param.getTradeTypeEnum())) {
            paramMap.put("tradeTypeEnum",param.getTradeTypeEnum());
        }
        if (Objects.nonNull(param.getCurrencyEnum())) {
            paramMap.put("currencyEnum",param.getCurrencyEnum());
        }
        if (CollUtil.isNotEmpty(param.getChannelIdSet())) {
            paramMap.put("channelIdSet",param.getChannelIdSet());
        }
        paramMap.put("adminId",param.getAdminId());
        paramMap.put("roleId",param.getRoleId());
        if (param.getIsAll()) {
            //导出全部
            paramMap.put("limit", exportExcelConfig.getLimit());
            paramMap.put("id", 0);
        } else {
            //导出当前页
            paramMap.put("limit", Objects.nonNull(param.getSize()) ? param.getSize() : exportExcelConfig.getLimit());
        }
    }

    private LambdaQueryWrapper<ActivitySafeAmountRecord> buildSummaryWrapper(ActivitySafeRecordQueryParam param, TradeTypeEnum tradeTypeEnum) {
        QueryWrapper<ActivitySafeAmountRecord> queryWrapper = new QueryWrapper<>();
        if (tradeTypeEnum == TradeTypeEnum.IN) {
            queryWrapper
                    .select(SqlUtil.selectSum(ActivitySafeAmountRecord::getAmount));
        } else {
            queryWrapper
                    .select(SqlUtil.selectSum(ActivitySafeAmountRecord::getAmount),
                            SqlUtil.selectSum(ActivitySafeAmountRecord::getIncome));
        }

        return queryWrapper
                .lambda().eq(ActivitySafeAmountRecord::getMerchantId, param.getMerchantId())
                .eq(param.getMemberId() != null, ActivitySafeAmountRecord::getMemberId, param.getMemberId())
                .eq(CollUtil.isNotEmpty(param.getChannelIdSet()),ActivitySafeAmountRecord::getIsDirect, BooleanEnum.TRUE)
                .in(CollUtil.isNotEmpty(param.getChannelIdSet()), ActivitySafeAmountRecord::getChannelId, param.getChannelIdSet())
                .ge(ActivitySafeAmountRecord::getCreateTime, param.getStartTime())
                .le(ActivitySafeAmountRecord::getCreateTime, param.getEndTime())
                .eq(ActivitySafeAmountRecord::getTradeTypeEnum, tradeTypeEnum)
                .eq(param.getCurrencyEnum() != null, ActivitySafeAmountRecord::getCurrencyEnum, param.getCurrencyEnum());
    }

    private ActivitySafeAmountRecordDTO convertToDTO(ActivitySafeAmountRecord activitySafeAmountRecord) {
        return BeanUtil.toBean(activitySafeAmountRecord, ActivitySafeAmountRecordDTO.class);
    }
}
