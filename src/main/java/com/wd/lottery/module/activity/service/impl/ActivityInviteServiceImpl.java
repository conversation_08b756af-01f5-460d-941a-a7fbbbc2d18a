package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.annotation.MasterOnly;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.Md5Util;
import com.wd.lottery.module.activity.constants.ActivityStatusEnum;
import com.wd.lottery.module.activity.constants.ActivityTypeEnum;
import com.wd.lottery.module.activity.constants.SubActivityTypeEnum;
import com.wd.lottery.module.activity.dto.ActivityInviteSummaryDTO;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.entity.ActivityRecord;
import com.wd.lottery.module.activity.param.ActivityInviteMemberRecordQueryParam;
import com.wd.lottery.module.activity.param.ActivityParam;
import com.wd.lottery.module.activity.param.ActivityRewardParam;
import com.wd.lottery.module.activity.param.invite.ActivityInviteConfigParam;
import com.wd.lottery.module.activity.param.invite.ActivityInviteParam;
import com.wd.lottery.module.activity.param.invite.ActivityInviteRecordParam;
import com.wd.lottery.module.activity.param.invite.ActivityInviteSingleRecordParam;
import com.wd.lottery.module.activity.service.ActivityInviteService;
import com.wd.lottery.module.activity.service.ActivityRecordService;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.activity.service.ActivitySingleInviteRecordService;
import com.wd.lottery.module.activity.vo.ActivityInviteMemberRecordVO;
import com.wd.lottery.module.cash.constatns.SubTradeTypeEnum;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.service.MemberProxyService;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.report.entity.ReportMemberActivityTotal;
import com.wd.lottery.module.report.entity.ReportMemberTotal;
import com.wd.lottery.module.report.service.ReportMemberActivityTotalService;
import com.wd.lottery.module.report.service.ReportMemberTotalService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class ActivityInviteServiceImpl implements ActivityInviteService {
    private final ActivityService activityService;

    private final ActivityRecordService activityRecordService;

    private final ReportMemberTotalService reportMemberTotalService;

    private final MemberService memberService;

    private final ActivitySingleInviteRecordService activitySingleInviteRecordService;

    private final ReportMemberActivityTotalService reportMemberActivityTotalService;

    private final MemberProxyService memberProxyService;

    @Override
    public void checkParam(ActivityParam activityParam) {
        ActivityInviteParam activityInviteParam = activityParam.getActivityParam(ActivityInviteParam.class);
        Assert.notNull(activityInviteParam.getInviteReward());
        Assert.notNull(activityInviteParam.getBeInviteReward());
        Assert.notNull(activityInviteParam.getDepositAmount());
        List<ActivityInviteConfigParam> activityInviteConfigParamList = activityInviteParam.getActivityInviteConfigParamList();
        Assert.notEmpty(activityInviteConfigParamList);
        for (ActivityInviteConfigParam activityInviteConfigParam : activityInviteConfigParamList) {
            Assert.notNull(activityInviteConfigParam.getRewardLevel());
            Assert.notNull(activityInviteConfigParam.getCondition());
            Assert.notNull(activityInviteConfigParam.getReward());
            Assert.notNull(activityInviteConfigParam.getInviteCount());
        }
    }

    @Override
    public List<ActivityRecord> getRewardList(ActivityRewardParam activityRewardParam) {
        ActivityRecord activityInviteRecord = getActivityInviteRecord(activityRewardParam);
        ActivityRecord activityRecordInviteSingleReward = getActivityInviteSingleRecord(activityRewardParam, false);

        return Arrays.asList(activityInviteRecord, activityRecordInviteSingleReward);
    }

    @NotNull
    private ActivityRecord getActivityInviteSingleRecord(ActivityRewardParam activityRewardParam, boolean updateMark) {
        MemberTokenInfoDTO memberTokenInfoDTO = activityRewardParam.getMemberTokenInfoDTO();
        Long merchantId = memberTokenInfoDTO.getMerchantId();
        Long memberId = memberTokenInfoDTO.getId();
        LocalDateTime localDateTimeNow = LocalDateTime.now();
        CurrencyEnum currencyEnum = memberTokenInfoDTO.getCurrencyEnum();

        Activity activity = activityService.getEnableAndStartOneNotNull(merchantId, currencyEnum, this.getActivityTypeEnum(), activityRewardParam.getActivityId());

        Long beInviteRewardAmount = activitySingleInviteRecordService.getBeInviteRewardAmount(merchantId, memberId, updateMark);
        Long inviteRewardAmount = activitySingleInviteRecordService.getInviteRewardAmount(merchantId, memberId, updateMark);
        ActivityRecord activityRecordSave = activityRecordService.initActivityRecord(memberTokenInfoDTO, activity, SubActivityTypeEnum.INVITE_SINGLE_REWARD, localDateTimeNow, activityRewardParam.getRequestIp());
        activityRecordSave.setAmount(beInviteRewardAmount + inviteRewardAmount);
        activityRecordSave.setActivityStatusEnum(ActivityStatusEnum.WAIT_RECEIVE);
        activityRecordSave.setUid(getUid(activityRecordSave, localDateTimeNow));

        ActivityInviteSingleRecordParam activityInviteSingleRecordParam = new ActivityInviteSingleRecordParam();
        activityInviteSingleRecordParam.setBeInviteSingleReward(beInviteRewardAmount);
        activityInviteSingleRecordParam.setInviteSingleReward(inviteRewardAmount);
        activityRecordSave.setRecordJson(JSONUtil.toJsonStr(activityInviteSingleRecordParam));


        return activityRecordSave;
    }

    private ActivityRecord getActivityInviteRecord(ActivityRewardParam activityRewardParam) {
        DateTime now = DateUtil.date();
        LocalDateTime nowDateTime = now.toLocalDateTime();
        MemberTokenInfoDTO memberTokenInfoDTO = activityRewardParam.getMemberTokenInfoDTO();
        Long merchantId = memberTokenInfoDTO.getMerchantId();
        Long memberId = memberTokenInfoDTO.getId();

        Activity activity = activityService.getEnableAndStartOneNotNull(merchantId, memberTokenInfoDTO.getCurrencyEnum(), this.getActivityTypeEnum(), activityRewardParam.getActivityId());

        ActivityRecord activityRecord = activityRecordService.initActivityRecord(memberTokenInfoDTO, activity, SubActivityTypeEnum.INVITE_REWARD, nowDateTime, activityRewardParam.getRequestIp());

        List<ActivityRecord> activityRecordList = activityRecordService.getList(memberId, merchantId, this.getActivityTypeEnum(), SubActivityTypeEnum.INVITE_REWARD, activityRewardParam.getActivityId(), activity.getStartTime());
        checkComplete(activityRecordList, activity, activityRecord, memberTokenInfoDTO);
        return activityRecord;
    }

    private void checkComplete(List<ActivityRecord> activityRecordList, Activity activity, ActivityRecord activityRecord, MemberTokenInfoDTO memberTokenInfoDTO) {
        ActivityInviteParam activityInviteParam = activity.activityParamToBean(ActivityInviteParam.class);
        List<ActivityInviteConfigParam> activityInviteConfigParamList = activityInviteParam.getActivityInviteConfigParamList()
                .stream()
                .sorted(Comparator.comparingInt(ActivityInviteConfigParam::getRewardLevel))
                .collect(Collectors.toList());
        ActivityInviteConfigParam nextRewarLevleActivityInviteConfigParam;
        if(CollUtil.isEmpty(activityRecordList)){
            // 未领取过奖励则从第一级开始判断
            nextRewarLevleActivityInviteConfigParam = activityInviteConfigParamList.get(0);
        }else{
            nextRewarLevleActivityInviteConfigParam = getNextRewarLevelActivityInviteConfigParam(activityRecordList, activityRecord, activityInviteConfigParamList);
            if (nextRewarLevleActivityInviteConfigParam == null) {
                return;
            }
        }

        Long completeInviteCount = getCompleteInviteCount(activity.getStartTime(), nextRewarLevleActivityInviteConfigParam.getCondition(), memberTokenInfoDTO.getId(), memberTokenInfoDTO.getMerchantId());

        assembleRecordJson(activityRecord, nextRewarLevleActivityInviteConfigParam, completeInviteCount, null);

        if(completeInviteCount < nextRewarLevleActivityInviteConfigParam.getInviteCount()) {
            return;
        }
        //当下一条件满足的时候还需要查下下条件的满足情况
        ActivityInviteConfigParam nextNextRewarLevleActivityInviteConfigParam = activityInviteConfigParamList.stream()
                .filter(param -> param.getRewardLevel().equals(nextRewarLevleActivityInviteConfigParam.getRewardLevel() + Constants.ONE_INTEGER))
                .findFirst()
                .orElse(null);

        // 如果存在下下级奖励配置，计算下下级的完成邀请人数并设置到记录参数中
        if (nextNextRewarLevleActivityInviteConfigParam != null) {
            Long nextLevelCompleteInviteCount = getCompleteInviteCount(activity.getStartTime(),
                    nextNextRewarLevleActivityInviteConfigParam.getCondition(),
                    memberTokenInfoDTO.getId(),
                    memberTokenInfoDTO.getMerchantId());

            // 更新记录参数，添加下一级别的完成邀请人数信息
            assembleRecordJson(activityRecord, nextRewarLevleActivityInviteConfigParam, completeInviteCount, nextLevelCompleteInviteCount);
        }

        assembleActivityRecord(activityRecord, nextRewarLevleActivityInviteConfigParam);
    }

    private static void assembleRecordJson(ActivityRecord activityRecord, ActivityInviteConfigParam nextRewarLevleActivityInviteConfigParam, Long completeInviteCount, Long nextLevelCompleteInviteCount) {
        ActivityInviteRecordParam activityInviteRecordParam = new ActivityInviteRecordParam();
        activityInviteRecordParam.setRewardLevel(nextRewarLevleActivityInviteConfigParam.getRewardLevel());
        activityInviteRecordParam.setCondition(nextRewarLevleActivityInviteConfigParam.getCondition());
        activityInviteRecordParam.setCompleteInviteCount(completeInviteCount);
        activityInviteRecordParam.setNextLevelCompleteInviteCount(nextLevelCompleteInviteCount);
        activityRecord.setRecordJson(JSONUtil.toJsonStr(activityInviteRecordParam));
    }

    private void assembleActivityRecord(ActivityRecord activityRecord, ActivityInviteConfigParam nextRewarLevleActivityInviteConfigParam) {
        activityRecord.setAmount(nextRewarLevleActivityInviteConfigParam.getReward());
        activityRecord.setActivityStatusEnum(ActivityStatusEnum.WAIT_RECEIVE);
        String uid = this.getUid(activityRecord, nextRewarLevleActivityInviteConfigParam.getRewardLevel());
        activityRecord.setUid(uid);
    }

    @Nullable
    private static ActivityInviteConfigParam getNextRewarLevelActivityInviteConfigParam(List<ActivityRecord> activityRecordList, ActivityRecord activityRecord, List<ActivityInviteConfigParam> activityInviteConfigParamList) {
        ActivityInviteConfigParam activityInviteConfigParam;
        Optional<ActivityRecord> maxActivityRecordOptional = activityRecordList.stream().max(Comparator.comparingInt(o -> o.getRecordParam(ActivityInviteRecordParam.class).getRewardLevel()));
        if (!maxActivityRecordOptional.isPresent()) {
            log.error("maxActivityRecordOptional is not present, activityRecordList:{}", activityRecordList);
            return null;
        }
        ActivityRecord maxActivityRecord = maxActivityRecordOptional.get();
        ActivityInviteRecordParam recordParam = maxActivityRecord.getRecordParam(ActivityInviteRecordParam.class);
        //前端需要知道当前已领取等级 方便展示
        recordParam.setReceivedRewardLevel(recordParam.getRewardLevel());
        activityRecord.setRecordJson(JSONUtil.toJsonStr(recordParam));
        Integer rewardLevel = recordParam.getRewardLevel();
        Integer nexRewardLevel = rewardLevel + Constants.ONE_INTEGER;
        Optional<ActivityInviteConfigParam> nexRewardLevelOptional = activityInviteConfigParamList
                .stream()
                .filter(o -> o.getRewardLevel().equals(nexRewardLevel))
                .findFirst();
        if (!nexRewardLevelOptional.isPresent()) {
            return null;
        }
        activityInviteConfigParam = nexRewardLevelOptional.get();
        return activityInviteConfigParam;
    }

    public Long getCompleteInviteCount(LocalDateTime startTime, Long condition, Long inviteMemberId, Long merchantId){
        Assert.notNull(condition);
        List<Long> memberIdListByInviteMemberId = memberService.getMemberIdListByInviteMemberId(inviteMemberId, merchantId, startTime);
        if(CollUtil.isEmpty(memberIdListByInviteMemberId)){
            return Constants.ZERO_LONG;
        }
        if (condition == 0) {
            return Long.valueOf(memberIdListByInviteMemberId.size());
        }
        return reportMemberTotalService.countMembersWithDepositAmountGreaterThan(condition, memberIdListByInviteMemberId, merchantId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ActivityRecord receiveReward(ActivityRewardParam activityRewardParam) {
        switch (activityRewardParam.getSubActivityTypeEnum()) {
            case INVITE_REWARD:
                return activityRecordService.receiveReward(Collections.singletonList(
                        this.getActivityInviteRecord(activityRewardParam)));
            case INVITE_SINGLE_REWARD:
                return activityRecordService.receiveReward(Collections.singletonList(
                        this.getActivityInviteSingleRecord(activityRewardParam, true)));
            default:
                throw new ApiException(CommonCode.PARAM_INVALID);
        }
    }

    @Override
    public ActivityTypeEnum getActivityTypeEnum() {
        return ActivityTypeEnum.INVITE;
    }

    @Override
    public String getUid(ActivityRecord activityRecord, Object... others) {
        String uidStr = activityRecord.getMemberId().toString() +  StrPool.COLON + activityRecord.getActivityTypeEnum() + StrPool.COLON + activityRecord.getSubActivityTypeEnum() + StrPool.COLON + activityRecord.getActivityId() + others[0];
        return Md5Util.getMd5HexLowerCase(uidStr);
    }

    @Override
    public Boolean getUniqueActivity() {
        return true;
    }

    @Override
    public List<ActivityInviteMemberRecordVO> getActivityInviteMemberRecordList(ActivityInviteMemberRecordQueryParam activityInviteMemberRecordQueryParam) {
        Assert.notNull(activityInviteMemberRecordQueryParam);
        Assert.notNull(activityInviteMemberRecordQueryParam.getActivityId());
        Assert.notNull(activityInviteMemberRecordQueryParam.getMerchantId());
        Assert.notNull(activityInviteMemberRecordQueryParam.getMemberId());
        Assert.notNull(activityInviteMemberRecordQueryParam.getCurrencyEnum());
        Assert.notNull(activityInviteMemberRecordQueryParam.getCurrent());
        Assert.notNull(activityInviteMemberRecordQueryParam.getSize());

        Activity activity = activityService.getEnableAndStartOneNotNull(activityInviteMemberRecordQueryParam.getMerchantId(), activityInviteMemberRecordQueryParam.getCurrencyEnum(), this.getActivityTypeEnum(), activityInviteMemberRecordQueryParam.getActivityId());
        List<Member> memberList = memberService.getMemberListByInviteMemberId(activityInviteMemberRecordQueryParam.getMemberId(), activityInviteMemberRecordQueryParam.getMerchantId(), activity.getStartTime(), activityInviteMemberRecordQueryParam.getCurrent(), activityInviteMemberRecordQueryParam.getSize());
        if (CollUtil.isEmpty(memberList)) {
            return new ArrayList<>();
        }
        List<Long> memberIdList = memberList.stream().map(Member::getId).collect(Collectors.toList());
        List<ReportMemberTotal> reportMemberTotalList = reportMemberTotalService.getListByMemberIdList(memberIdList);
        List<ActivityInviteMemberRecordVO> activityInviteMemberRecordVOList = BeanUtil.copyToList(memberList, ActivityInviteMemberRecordVO.class);
        if (CollUtil.isEmpty(reportMemberTotalList)) {
            return activityInviteMemberRecordVOList;
        }
        Map<Long, Long> memberDepositAmountMap = reportMemberTotalList.stream().collect(Collectors.toMap(ReportMemberTotal::getMemberId, ReportMemberTotal::getDepositAmount, (k1, k2) -> k1));
        for (ActivityInviteMemberRecordVO activityInviteMemberRecordVO : activityInviteMemberRecordVOList) {
            activityInviteMemberRecordVO.setDepositAmount(memberDepositAmountMap.getOrDefault(activityInviteMemberRecordVO.getId(), Constants.ZERO_LONG));
        }
        return activityInviteMemberRecordVOList;
    }

    @MasterOnly
    @Override
    public ActivityInviteSummaryDTO getActivityInviteSummary(Long merchantId, Long memberId) {
        ReportMemberActivityTotal activityTotal = reportMemberActivityTotalService.getByMemberIdAndMerchantIdAndSubTradeTypeEnum(memberId, merchantId, SubTradeTypeEnum.ACTIVITY_INVITE_REWARD);
        Long userProxyCount = memberProxyService.getUserProxyCount(merchantId, memberId, 1);

        ActivityInviteSummaryDTO dto = new ActivityInviteSummaryDTO();
        dto.setTotalRewards(activityTotal == null ? 0L : activityTotal.getActivityAmount());
        dto.setTotalInvites(userProxyCount == null ? 0L : userProxyCount);

        return dto;
    }

    @Override
    public void extraOperateByUpdate(ActivityParam param, Activity beforeUpdated) {
        this.activitySingleInviteRecordService.checkAndRemoveActivitySingleInviteRecord(param, beforeUpdated);
    }
}
