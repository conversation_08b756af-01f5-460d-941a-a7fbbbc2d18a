package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.multi.RowKeyTable;
import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.batch.MybatisBatch;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.MybatisBatchUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.wd.lottery.common.annotation.MasterOnly;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.constans.RabbitMQConstants;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.LocalUtil;
import com.wd.lottery.common.util.Md5Util;
import com.wd.lottery.common.util.SqlUtil;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.dto.ActivityVipLevelChangeDTO;
import com.wd.lottery.module.activity.dto.ActivityVipMemberRewardDTO;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.entity.ActivityRecord;
import com.wd.lottery.module.activity.entity.ActivityVipMember;
import com.wd.lottery.module.activity.entity.ActivityVipRecord;
import com.wd.lottery.module.activity.mapper.ActivityVipMemberMapper;
import com.wd.lottery.module.activity.param.ActivityParam;
import com.wd.lottery.module.activity.param.ActivityRewardParam;
import com.wd.lottery.module.activity.param.vip.ActivityVipConfigParam;
import com.wd.lottery.module.activity.param.vip.ActivityVipLevelDTO;
import com.wd.lottery.module.activity.param.vip.ActivityVipParam;
import com.wd.lottery.module.activity.param.vip.ActivityVipRecordParam;
import com.wd.lottery.module.activity.service.*;
import com.wd.lottery.module.activity.service.*;
import com.wd.lottery.module.cash.dto.CashDataStatisticsDTO;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.merchant.service.MerchantService;
import com.wd.lottery.module.report.constans.ReportMemberGroupEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.SqlSessionFactory;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import jakarta.validation.Validator;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityVipServiceImpl implements ActivityVipService {
    @Resource
    private ActivityVipMemberService activityVipMemberService;

    @Resource
    private ActivityVipRecordService activityVipRecordService;

    @Resource
    private ActivityService activityService;

    @Resource
    private ActivityRecordService activityRecordService;

    @Resource
    private MerchantService merchantService;

    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private Validator validator;

    @Resource
    private AmqpTemplate amqpTemplate;

    @Override
    public void checkParam(ActivityParam activityParam) {
        ActivityVipParam param = activityParam.getActivityParam(ActivityVipParam.class);
        Assert.notNull(param, "configJson not null");
        Assert.notNull(param.getExpRate(), "expRate is null");
        Assert.notEmpty(param.getActivityVipConfigParamList(), "activityVipConfigParamList is empty");
        Assert.notNull(param.getExpType(), "expType is null");
        for (ActivityVipConfigParam configParam : param.getActivityVipConfigParamList()) {
            Assert.notNull(configParam.getVipLevel(), "vipLevel is null");
            Assert.notNull(configParam.getUpgradeCondition(), "upgradeCondition is null");
            Assert.notNull(configParam.getMonthlyReward(), "monthlyReward is null");
            Assert.notNull(configParam.getMaintainCondition(), "maintainCondition is null");
            Assert.notNull(configParam.getIncreaseSafeBoxRate(), "increaseSafeBoxRate is null");
            Assert.notNull(configParam.getIncreaseThreeRebateRate(), "increaseThreeRebateRate is null");
            Assert.notNull(configParam.getIncreaseLotteryRebateRate(), "increaseLotteryRebateRate is null");
            Assert.notNull(configParam.getIncreaseSlotRebateRate(), "increaseSlotRebateRate is null");
            Assert.notNull(configParam.getIncreaseFishRebateRate(), "increaseFishRebateRate is null");
            Assert.notNull(configParam.getIncreaseCasinoRebateRate(), "increaseCasinoRebateRate is null");
            Assert.notNull(configParam.getIncreasePokerRebateRate(), "increasePokerRebateRate is null");
            Assert.notNull(configParam.getIncreaseMiniRebateRate(), "increaseMiniRebateRate is null");
            Assert.notNull(configParam.getIncreaseSportRebateRate(), "increaseSportRebateRate is null");
            Assert.notNull(configParam.getIncreaseESportRebateRate(), "increaseESportRebateRate is null");
            Assert.notNull(configParam.getIncreaseCockRebateRate(), "increaseCockRebateRate is null");
            if (param.getIsWithdrawLimit() == EnableEnum.TRUE) {
                Assert.notNull(configParam.getWithdrawLimitAmount(), "withdrawLimitAmount is null");
                Assert.notNull(configParam.getFreeWithdrawCount(), "freeWithdrawCount is null");
            }
        }
    }

    @Override
    public List<ActivityRecord> getRewardList(ActivityRewardParam activityRewardParam) {
        ActivityVipMemberRewardDTO vipMemberRewardOne = getVipMemberRewardOne(activityRewardParam.getMemberTokenInfoDTO());

        ActivityRecord activityRecordUpgradeReward = new ActivityRecord();
        activityRecordUpgradeReward.setActivityTypeEnum(this.getActivityTypeEnum());
        activityRecordUpgradeReward.setSubActivityTypeEnum(SubActivityTypeEnum.VIP_UPGRADE_REWARD);
        activityRecordUpgradeReward.setAmount(vipMemberRewardOne.getUpgradeReward());
        //VIP已领取的等级
        activityRecordUpgradeReward.setRecordJson(JSONUtil.toJsonStr(vipMemberRewardOne.getReceiveVipLevel()));

        ActivityRecord activityRecordMonthlyReward = new ActivityRecord();
        activityRecordMonthlyReward.setActivityTypeEnum(this.getActivityTypeEnum());
        activityRecordMonthlyReward.setSubActivityTypeEnum(SubActivityTypeEnum.VIP_MONTHLY_REWARD);
        activityRecordMonthlyReward.setAmount(vipMemberRewardOne.getMonthlyReward());

        return Arrays.asList(activityRecordUpgradeReward, activityRecordMonthlyReward);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivityRecord receiveReward(ActivityRewardParam activityRewardParam) {
        switch (activityRewardParam.getSubActivityTypeEnum()) {
            case VIP_UPGRADE_REWARD:
                return receiveUpgradeAward(activityRewardParam);
            case VIP_MONTHLY_REWARD:
                return receiveMonthlyReward(activityRewardParam.getMemberTokenInfoDTO(), activityRewardParam);
        }
        throw new ApiException(CommonCode.PARAM_INVALID);
    }

    @Override
    public ActivityTypeEnum getActivityTypeEnum() {
        return ActivityTypeEnum.VIP;
    }

    @Override
    public String getUid(ActivityRecord activityRecord, Object... others) {
        ActivityVipRecordParam activityVipRecordParam = activityRecord.getRecordParam(ActivityVipRecordParam.class);
        if(SubActivityTypeEnum.VIP_UPGRADE_REWARD.equals(activityRecord.getSubActivityTypeEnum())) {
            return getUpgradeRewardUid(activityRecord.getMemberId(), activityVipRecordParam.getVipLevel());
        }
        if(SubActivityTypeEnum.VIP_MONTHLY_REWARD.equals(activityRecord.getSubActivityTypeEnum())) {
            return getMonthlyRewardUid(activityRecord.getMemberId(), activityVipRecordParam.getVipLevel(), activityRecord.getCreateTime().toLocalDate());
        }
        throw new ApiException(CommonCode.PARAM_INVALID);
    }

    @Override
    public Boolean getUniqueActivity() {
        return true;
    }

    @Override
    // 禁止加事物注解，避免长事物，内部已经有编程式事物
    public void vipExpSettle(List<CashDataStatisticsDTO> cashDataStatisticsDTOList) {
        List<Long> merchantIdList = cashDataStatisticsDTOList.stream()
                .map(CashDataStatisticsDTO::getMerchantId)
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(merchantIdList)) {
            log.warn("merchantIdList is empty, cashDataStatisticsDTOList:{}", cashDataStatisticsDTOList);
            return;
        }
        RowKeyTable<Long, CurrencyEnum, Activity> activityRowKeyTable = getActivityRowKeyTable(merchantIdList);
        if (activityRowKeyTable == null) {
            return;
        }

        Map<Long, ActivityVipRecord> activityVipRecordMap = getHasExpVipRecord(cashDataStatisticsDTOList, activityRowKeyTable);

        if (CollUtil.isEmpty(activityVipRecordMap)) {
            return;
        }

        Set<Long> memberIdSet = activityVipRecordMap.values().stream().map(ActivityVipRecord::getMemberId).collect(Collectors.toSet());
        Set<Long> hasExpMerchantIdSet = activityVipRecordMap.values().stream().map(ActivityVipRecord::getMerchantId).collect(Collectors.toSet());
        List<ActivityVipMember> activityVipMemberList = activityVipMemberService.lambdaQuery()
                .in(ActivityVipMember::getId, memberIdSet)
                .in(ActivityVipMember::getMerchantId, hasExpMerchantIdSet)
                .list();
        if (CollUtil.isEmpty(activityVipMemberList)) {
            log.warn("activityVipMemberList is empty, cashDataStatisticsDTOList:{}", cashDataStatisticsDTOList);
            return;
        }

        List<ActivityVipRecord> activityVipRecordUpgradeList = new ArrayList<>();
        for (ActivityVipMember activityVipMember : activityVipMemberList) {
            upgradeCheck(activityVipMember, activityVipRecordMap, activityRowKeyTable, activityVipRecordUpgradeList);
        }

        transactionTemplate.execute(action -> {
            updateVipMemberAndSaveVipRecordForExpInc(activityVipRecordMap, activityVipRecordUpgradeList, activityVipMemberList);
            return true;
        });
    }

    private Map<Long, ActivityVipRecord> getHasExpVipRecord(List<CashDataStatisticsDTO> cashDataStatisticsDTOList, RowKeyTable<Long, CurrencyEnum, Activity> activityRowKeyTable) {
        Map<Long, ActivityVipRecord> activityVipRecordMap = new HashMap<>();
        for (CashDataStatisticsDTO cashDataStatisticsDTO : cashDataStatisticsDTOList) {
            Activity activity = activityRowKeyTable.get(cashDataStatisticsDTO.getMerchantId(), cashDataStatisticsDTO.getCurrencyEnum());
            if (activity == null) {
                log.debug("Merchant currency not config vip activity, cashDataStatisticsDTO:{}", cashDataStatisticsDTO);
                continue;
            }

            ActivityVipParam activityVipParam = activity.activityParamToBean(ActivityVipParam.class);
            if (activityVipParam == null) {
                log.error("vipLevel is null, cashDataStatisticsDTO:{}, activity:{}", cashDataStatisticsDTO, activity);
                continue;
            }

            VipExpTypeEnum expType = activityVipParam.getExpType();
            if (!isValidStatisticData(cashDataStatisticsDTO, expType)) {
                continue;
            }

            // 計算經驗值
            boolean isDepositType = VipExpTypeEnum.DEPOSIT == expType;
            long baseAmount = isDepositType ? cashDataStatisticsDTO.getAmount() : cashDataStatisticsDTO.getValidBetAmount();
            long exp = baseAmount * activityVipParam.getExpRate() / Constants.TEN_THOUSAND;

            if (exp <= 0) {
                log.debug("exp le 0, cashDataStatisticsDTO:{}, activity:{}", cashDataStatisticsDTO, activity);
                continue;
            }

            ActivityVipRecord activityVipRecord = activityVipRecordMap.get(cashDataStatisticsDTO.getMemberId());
            if (activityVipRecord == null) {
                activityVipRecord = new ActivityVipRecord();
                activityVipRecord.setMerchantId(cashDataStatisticsDTO.getMerchantId());
                activityVipRecord.setMemberId(cashDataStatisticsDTO.getMemberId());
                activityVipRecord.setMemberName(cashDataStatisticsDTO.getMemberName());
                activityVipRecord.setExp(0L);
                activityVipRecord.setVipRecordTypeEnum(VipRecordTypeEnum.EXP_REWARD);
                activityVipRecord.setCurrencyEnum(cashDataStatisticsDTO.getCurrencyEnum());
                activityVipRecord.setCreateTime(LocalDateTime.now());
                activityVipRecordMap.put(activityVipRecord.getMemberId(), activityVipRecord);
            }
            activityVipRecord.setExp(activityVipRecord.getExp() + exp);
        }
        return activityVipRecordMap;
    }

    private boolean isValidStatisticData(CashDataStatisticsDTO cashDataStatisticsDTO, VipExpTypeEnum expType) {
        if (expType == null) {
            return false;
        } else if (VipExpTypeEnum.DEPOSIT == expType) {
            if (cashDataStatisticsDTO.getSubTradeTypeEnum() == null || ReportMemberGroupEnum.DEPOSIT_AMOUNT != cashDataStatisticsDTO.getSubTradeTypeEnum().getReportMemberGroupEnum()) {
                return false;
            }
            if (cashDataStatisticsDTO.getAmount() <= 0) {
                log.debug("depositAmount le 0, cashDataStatisticsDTO:{}", cashDataStatisticsDTO);
                return false;
            }
        } else if (VipExpTypeEnum.VALID_BET == expType) {
            if (cashDataStatisticsDTO.getValidBetAmount() <= 0) {
                log.debug("validBetAmount le 0, cashDataStatisticsDTO:{}", cashDataStatisticsDTO);
                return false;
            }
        }
        return true;
    }

    private void updateVipMemberAndSaveVipRecordForExpInc(Map<Long, ActivityVipRecord> activityVipRecordMap, List<ActivityVipRecord> activityVipRecordUpgradeList, List<ActivityVipMember> activityVipMemberList) {
        try {
            List<ActivityVipRecord> activityVipRecordList = new ArrayList<>(activityVipRecordMap.values());
            Map<Long, ActivityVipRecord> allActivityVipRecordMap = activityVipRecordList.stream().collect(Collectors.toMap(ActivityVipRecord::getMemberId, Function.identity()));
            Map<Long, ActivityVipRecord> upgradeActivityVipRecordMap = activityVipRecordUpgradeList.stream().collect(Collectors.toMap(ActivityVipRecord::getMemberId, Function.identity()));
            MybatisBatch.Method<ActivityVipMember> method = new MybatisBatch.Method<>(ActivityVipMemberMapper.class);
            LocalDate now = LocalDate.now();
            String currencyExpColumn = SqlUtil.columnToUnderline(ActivityVipMember::getCurrentExp);
            List<BatchResult> batchResultList = MybatisBatchUtils.execute(sqlSessionFactory,
                    activityVipMemberList,
                    method.update(activityVipMember -> {
                        LambdaUpdateWrapper<ActivityVipMember> activityVipMemberLambdaUpdateWrapper = new LambdaUpdateWrapper<ActivityVipMember>()
                                .setSql(currencyExpColumn + StringPool.EQUALS + currencyExpColumn + StringPool.PLUS + allActivityVipRecordMap.get(activityVipMember.getId()).getExp())
                                .eq(ActivityVipMember::getId, activityVipMember.getId())
                                .eq(ActivityVipMember::getMerchantId, activityVipMember.getMerchantId());
                        ActivityVipRecord upgradeActivityVipUpRecord = upgradeActivityVipRecordMap.get(activityVipMember.getId());
                        if (Objects.nonNull(upgradeActivityVipUpRecord)) {
                            activityVipMemberLambdaUpdateWrapper
                                    .set(ActivityVipMember::getVipLevel, upgradeActivityVipUpRecord.getUpgradeLeve())
                                    .set(ActivityVipMember::getRelegationExp, upgradeActivityVipUpRecord.getUpgradeCondition())
                                    .set(ActivityVipMember::getRelegationDate, now)
                                    .set(ActivityVipMember::getUpgradeDate, now)
                                    .eq(ActivityVipMember::getVipLevel, activityVipMember.getVipLevel());
                        }
                        return activityVipMemberLambdaUpdateWrapper;
                        }
                    )
            );

            int updateSuccessTotal = 0;
            for (BatchResult batchResult : batchResultList) {
                int[] updateCounts = batchResult.getUpdateCounts();
                for (int updateCount : updateCounts) {
                    if (updateCount == 1) {
                        updateSuccessTotal++;
                    }
                }
            }

            if (updateSuccessTotal != activityVipMemberList.size()) {
                log.warn("updateVipMemberAndSaveVipRecordForExpInc fail, same activityVipMember already update by other thread, activityVipMemberList:{}", activityVipMemberList);
                throw new RuntimeException("updateVipMemberAndSaveVipRecordForExpInc fail, same activityVipMember already update by other thread");
            }

            if (CollUtil.isNotEmpty(activityVipRecordUpgradeList)) {
                activityVipRecordList.addAll(activityVipRecordUpgradeList);
            }
            saveBatchAndSendVipLevelChangeMessage(activityVipRecordList);
        } catch (Exception e) {
            log.warn("updateVipMemberAndSaveVipRecordForExpInc fail, activityVipRecordMap:{}, activityVipRecordUpgradeList:{}, activityVipMemberList:{}", activityVipRecordMap, activityVipRecordUpgradeList, activityVipMemberList);
            throw e;
        }
    }

    private void saveBatchAndSendVipLevelChangeMessage(List<ActivityVipRecord> activityVipRecordList) {
        if(CollUtil.isEmpty(activityVipRecordList)){
            return;
        }

        sendVipLevelChangeMessage(activityVipRecordList);

        activityVipRecordService.saveBatch(activityVipRecordList);
    }

    private void sendVipLevelChangeMessage(List<ActivityVipRecord> activityVipRecordList) {
        List<ActivityVipLevelChangeDTO> activityVipLevelChangeDTOList = activityVipRecordList.stream()
                .filter(o -> VipRecordTypeEnum.getVipLevelChangeVipRecordTypeEnumSet().contains(o.getVipRecordTypeEnum()))
                .map(o -> new ActivityVipLevelChangeDTO(o.getMemberId(), o.getMerchantId(), Integer.valueOf(o.getRemark())))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(activityVipLevelChangeDTOList)) {
            return;
        }

        amqpTemplate.convertAndSend(RabbitMQConstants.ACTIVITY_VIP_LEVEL_CHANGE_EXCHANGE, "", JSONUtil.toJsonStr(activityVipLevelChangeDTOList));
    }

    @Nullable
    private RowKeyTable<Long, CurrencyEnum, Activity> getActivityRowKeyTable(List<Long> merchantIdList) {
        Assert.notEmpty(merchantIdList);

        List<Activity> listByMerchantListAndActivityTypeEnumCache = activityService.getListByMerchantListAndActivityTypeEnumCache(merchantIdList, this.getActivityTypeEnum());
        if (CollUtil.isEmpty(listByMerchantListAndActivityTypeEnumCache)) {
            log.debug("listByMerchantListAndActivityTypeEnumCache is empty");
            return null;
        }

        RowKeyTable<Long, CurrencyEnum, Activity> activityRowKeyTable = new RowKeyTable<>();
        for (Activity activity : listByMerchantListAndActivityTypeEnumCache) {
            activityRowKeyTable.put(activity.getMerchantId(), activity.getCurrencyEnum(), activity);
        }
        return activityRowKeyTable;
    }

    private void upgradeCheck(ActivityVipMember activityVipMember, Map<Long, ActivityVipRecord> activityVipRecordMap, RowKeyTable<Long, CurrencyEnum, Activity> activityRowKeyTable, List<ActivityVipRecord> activityVipRecordUpgradeList) {
        ActivityVipRecord activityVipRecord = activityVipRecordMap.get(activityVipMember.getId());

        Activity activity = activityRowKeyTable.get(activityVipMember.getMerchantId(), activityVipMember.getCurrencyEnum());
        if (activity == null) {
            log.debug("activity is null, activityVipMember:{}, activityRowKeyTable:{}", activityVipMember, JSONUtil.toJsonStr(activityRowKeyTable));
            return;
        }

        long totalExp = activityVipMember.getCurrentExp() + activityVipRecord.getExp();
        ActivityVipConfigParam nextUpgradeVipLevelParam = getNextUpgradeVipLevelParam(activity, activityVipMember.getVipLevel(), totalExp);
        if (nextUpgradeVipLevelParam == null) {
            log.debug("totalExp can't upgrade, activityVipMember:{}, totalExp:{}", activityVipMember, totalExp);
            return;
        }
        ActivityVipRecord activityVipRecordUpgrade = getActivityVipRecordUpgrade(activityVipRecord, nextUpgradeVipLevelParam);
        activityVipRecordUpgradeList.add(activityVipRecordUpgrade);
    }

    private ActivityVipConfigParam getNextUpgradeVipLevelParam(Activity activity, Integer currentVipLevel, Long totalExp) {
        ActivityVipParam activityVipParam = activity.activityParamToBean(ActivityVipParam.class);
        if (activityVipParam == null) {
            log.error("activityVipParam is null, activity:{}", activity);
            return null;
        }
        List<ActivityVipConfigParam> activityVipConfigParamList = activityVipParam.getActivityVipConfigParamList();
        Assert.notEmpty(activityVipConfigParamList);
        if (CollUtil.isEmpty(activityVipConfigParamList)) {
            log.error("activityVipConfigParamList is empty,activity:{}", activity);
            return null;
        }
        ActivityVipConfigParam activityVipConfigParam = activityVipConfigParamList.stream()
                .sorted(Comparator.comparing(ActivityVipConfigParam::getUpgradeCondition).reversed())
                .filter(o -> o.getUpgradeCondition() <= totalExp)
                .findFirst()
                .orElse(null);
        if (activityVipConfigParam == null) {
            return null;
        }
        if (currentVipLevel >= activityVipConfigParam.getVipLevel()) {
            return null;
        }
        return activityVipConfigParam;
    }

    @NotNull
    private static ActivityVipRecord getActivityVipRecordUpgrade(ActivityVipRecord activityVipRecord, ActivityVipConfigParam activityVipConfigParam) {
        ActivityVipRecord activityVipRecordUpgrade = new ActivityVipRecord();
        activityVipRecordUpgrade.setExp(Constants.ZERO_LONG);
        activityVipRecordUpgrade.setMemberId(activityVipRecord.getMemberId());
        activityVipRecordUpgrade.setMemberName(activityVipRecord.getMemberName());
        activityVipRecordUpgrade.setMerchantId(activityVipRecord.getMerchantId());
        activityVipRecordUpgrade.setCreateTime(activityVipRecord.getCreateTime());
        activityVipRecordUpgrade.setVipRecordTypeEnum(VipRecordTypeEnum.VIP_UPGRADE);
        activityVipRecordUpgrade.setCurrencyEnum(activityVipRecord.getCurrencyEnum());
        activityVipRecordUpgrade.setRemark(activityVipConfigParam.getVipLevel().toString());
        activityVipRecordUpgrade.setUpgradeLeve(activityVipConfigParam.getVipLevel());
        activityVipRecordUpgrade.setUpgradeCondition(activityVipConfigParam.getUpgradeCondition());
        return activityVipRecordUpgrade;
    }


    @Override
    @MasterOnly
    // 禁止加事物注解，避免长事物，内部已经有编程式事物
    public void maintainVipLevel() {
        LocalDateTime now = LocalDateTime.now();
        LocalDate nowDate = now.toLocalDate();
        List<Long> merchantIdList = merchantService.getEnableMerchantIdList();
        if (CollUtil.isEmpty(merchantIdList)) {
            return;
        }

        RowKeyTable<Long, CurrencyEnum, Activity> activityRowKeyTable = getActivityRowKeyTable(merchantIdList);
        if (CollUtil.isEmpty(activityRowKeyTable)) {
            return;
        }

        for (Long merchantId : merchantIdList) {
            maintainVipLevelOneMerchant(merchantId, nowDate, activityRowKeyTable);
        }
    }

    private void maintainVipLevelOneMerchant(Long merchantId, LocalDate nowDate, RowKeyTable<Long, CurrencyEnum, Activity> activityRowKeyTable) {
        try {
            List<ActivityVipMember> activityVipMemberList = getNeedMaintainActivityVipMemberList(merchantId, nowDate);
            if (CollUtil.isEmpty(activityVipMemberList)) {
                return;
            }

            List<ActivityVipRecord> activityVipRecordDownGradeList = new ArrayList<>();
            List<ActivityVipRecord> activityVipRecordMaintainFailList = new ArrayList<>();
            List<ActivityVipMember> activityVipMemberMaintainFailList = new ArrayList<>();
            List<ActivityVipMember> activityVipMemberMaintainSuccessList = new ArrayList<>();
            maintainCheck(activityRowKeyTable,
                    activityVipMemberList,
                    activityVipRecordMaintainFailList,
                    activityVipRecordDownGradeList,
                    activityVipMemberMaintainFailList,
                    activityVipMemberMaintainSuccessList);

            updateMaintainSuccess(nowDate, activityVipMemberMaintainSuccessList);

            updateMaintainFail(nowDate, activityVipRecordMaintainFailList, activityVipRecordDownGradeList, activityVipMemberMaintainFailList);
        } catch (Exception e) {
            log.error("maintainVipLevelOneMerchant fail, merchantId:{}", merchantId, e);
        }
    }

    private void maintainCheck(RowKeyTable<Long, CurrencyEnum, Activity> activityRowKeyTable, List<ActivityVipMember> activityVipMemberList, List<ActivityVipRecord> activityVipRecordMaintainFailList, List<ActivityVipRecord> activityVipRecordDownGradeList, List<ActivityVipMember> activityVipMemberMaintainFailList, List<ActivityVipMember> activityVipMemberMaintainSuccessList) {
        for (ActivityVipMember activityVipMember : activityVipMemberList) {
            maintainCheckOne(activityVipMember,
                    activityRowKeyTable,
                    activityVipRecordMaintainFailList,
                    activityVipRecordDownGradeList,
                    activityVipMemberMaintainFailList,
                    activityVipMemberMaintainSuccessList
            );
        }
    }

    private List<ActivityVipMember> getNeedMaintainActivityVipMemberList(Long merchantId, LocalDate nowDate) {
        return activityVipMemberService.lambdaQuery()
                .eq(ActivityVipMember::getMerchantId, merchantId)
                .ne(ActivityVipMember::getVipLevel, Constants.ZERO_INTEGER)
                .le(ActivityVipMember::getRelegationDate, nowDate.minusDays(ActivityConstants.ACTIVITY_VIP_MAINTAIN_DAYS))
                .list();
    }

    private void updateMaintainFail(LocalDate nowDate, List<ActivityVipRecord> activityVipRecordMaintainFailList, List<ActivityVipRecord> activityVipRecordDownGradeList, List<ActivityVipMember> activityVipMemberMaintainFailList) {
        if (CollUtil.isEmpty(activityVipRecordMaintainFailList) && CollUtil.isEmpty(activityVipRecordMaintainFailList)) {
            return;
        }

        Map<Long, ActivityVipRecord> activityVipRecordMaintainFailMap = activityVipRecordMaintainFailList.stream().collect(Collectors.toMap(ActivityVipRecord::getMemberId, Function.identity()));
        Map<Long, ActivityVipRecord> activityVipRecordDownGradeMap = activityVipRecordDownGradeList.stream().collect(Collectors.toMap(ActivityVipRecord::getMemberId, Function.identity()));

        transactionTemplate.execute(action -> {
            updateVipMemberAndSaveVipRecordForMaintain(nowDate, activityVipMemberMaintainFailList, activityVipRecordMaintainFailMap, activityVipRecordDownGradeMap);
            return true;
        });
    }

    private void updateVipMemberAndSaveVipRecordForMaintain(LocalDate nowDate, List<ActivityVipMember> activityVipMemberMaintainFailList, Map<Long, ActivityVipRecord> activityVipRecordMaintainFailMap, Map<Long, ActivityVipRecord> activityVipRecordDownGradeMap) {
        // 更新会员VIP经验如果失败则放弃更新，等下一轮再更新,这种情况在降级的时候并发升级才会出现
        MybatisBatch.Method<ActivityVipMember> method = new MybatisBatch.Method<>(ActivityVipMemberMapper.class);
        String currencyExpColumn = SqlUtil.columnToUnderline(ActivityVipMember::getCurrentExp);
        String relegationExpColum = SqlUtil.columnToUnderline(ActivityVipMember::getRelegationExp);
        List<BatchResult> batchResultUpdateMaininFailList = MybatisBatchUtils.execute(sqlSessionFactory,
                activityVipMemberMaintainFailList,
                method.update(activityVipMember -> new LambdaUpdateWrapper<ActivityVipMember>()
                        .setSql(currencyExpColumn + StringPool.EQUALS + StringPool.LEFT_BRACKET + "@new_current_exp := " + currencyExpColumn + StringPool.DASH + activityVipRecordMaintainFailMap.get(activityVipMember.getId()).getExp() + StringPool.RIGHT_BRACKET)
                        .setSql(relegationExpColum + StringPool.EQUALS + "@new_current_exp")
                        .set(activityVipRecordDownGradeMap.containsKey(activityVipMember.getId()), ActivityVipMember::getVipLevel, activityVipMember.getVipLevel() - 1)
                        .set(ActivityVipMember::getRelegationDate, nowDate)
                        .eq(ActivityVipMember::getId, activityVipMember.getId())
                        .eq(ActivityVipMember::getMerchantId, activityVipMember.getMerchantId())
                        .eq(activityVipRecordDownGradeMap.containsKey(activityVipMember.getId()), ActivityVipMember::getVipLevel, activityVipMember.getVipLevel())
                )
        );

        List<ActivityVipRecord> saveActivityVipRecordList = new ArrayList<>();
        int updateIndex = -1;
        for (BatchResult batchResult : batchResultUpdateMaininFailList) {
            int[] updateCounts = batchResult.getUpdateCounts();
            for (int updateCount : updateCounts) {
                updateIndex++;
                if (updateCount == 0) {
                    continue;
                }
                ActivityVipMember activityVipMember = activityVipMemberMaintainFailList.get(updateIndex);
                if (activityVipRecordMaintainFailMap.containsKey(activityVipMember.getId())) {
                    saveActivityVipRecordList.add(activityVipRecordMaintainFailMap.get(activityVipMember.getId()));
                }
                if (activityVipRecordDownGradeMap.containsKey(activityVipMember.getId())) {
                    saveActivityVipRecordList.add(activityVipRecordDownGradeMap.get(activityVipMember.getId()));
                }
            }
        }

        if (CollUtil.isEmpty(saveActivityVipRecordList)) {
            return;
        }
        saveBatchAndSendVipLevelChangeMessage(saveActivityVipRecordList);
    }

    private void updateMaintainSuccess(LocalDate nowDate, List<ActivityVipMember> activityVipMemberMaintainSuccessList) {
        if (CollUtil.isEmpty(activityVipMemberMaintainSuccessList)) {
            return;
        }
        MybatisBatch.Method<ActivityVipMember> method = new MybatisBatch.Method<>(ActivityVipMemberMapper.class);
        MybatisBatchUtils.execute(sqlSessionFactory,
                activityVipMemberMaintainSuccessList,
                method.update(activityVipMember -> new LambdaUpdateWrapper<ActivityVipMember>()
                        .setSql(SqlUtil.columnToUnderline(ActivityVipMember::getRelegationExp) + "=" + SqlUtil.columnToUnderline(ActivityVipMember::getCurrentExp))
                        .set(ActivityVipMember::getRelegationDate, nowDate)
                        .eq(ActivityVipMember::getId, activityVipMember.getId())
                        .eq(ActivityVipMember::getMerchantId, activityVipMember.getMerchantId())
                )
        );
    }

    private void maintainCheckOne(ActivityVipMember activityVipMember,
                                  RowKeyTable<Long, CurrencyEnum, Activity> activityRowKeyTable,
                                  List<ActivityVipRecord> activityVipRecordMaintainFailList,
                                  List<ActivityVipRecord> activityVipRecordDownGradeList,
                                  List<ActivityVipMember> activityVipMemberMaintainFailList,
                                  List<ActivityVipMember> activityVipMemberMaintainSuccessList
    ) {
        try {
            Activity activity = activityRowKeyTable.get(activityVipMember.getMerchantId(), activityVipMember.getCurrencyEnum());
            if (Objects.isNull(activity)) {
                log.debug("activity is null, merchantId:{}, currencyEnum:{}", activityVipMember.getMerchantId(), activityVipMember.getCurrencyEnum());
                return;
            }
            ActivityVipConfigParam vipLevelConfigByLevel = activity.activityParamToVipLevelConfigByVipLevel(activityVipMember.getVipLevel());
            Assert.notNull(vipLevelConfigByLevel);

            Long maintainCondition = Optional.ofNullable(vipLevelConfigByLevel.getMaintainCondition()).orElse(0L);
            // 排除保级经验小于等于0
            if (maintainCondition <= Constants.ZERO_LONG) {
                activityVipMemberMaintainSuccessList.add(activityVipMember);
                return;
            }
            // 排除保级成功的
            if ((activityVipMember.getCurrentExp() - activityVipMember.getRelegationExp()) >= maintainCondition) {
                activityVipMemberMaintainSuccessList.add(activityVipMember);
                return;
            }
            Long maintainDeductExp = Optional.ofNullable(vipLevelConfigByLevel.getMaintainDeductExp()).orElse(Constants.ZERO_LONG);
            // 排除扣除经验小于等于0
            if (maintainDeductExp <= Constants.ZERO_LONG) {
                activityVipMemberMaintainSuccessList.add(activityVipMember);
                return;
            }

            activityVipMemberMaintainFailList.add(activityVipMember);

            ActivityVipRecord activityVipRecordExpDeduct = new ActivityVipRecord();
            activityVipRecordExpDeduct.setVipRecordTypeEnum(VipRecordTypeEnum.EXP_DEDUCT);
            activityVipRecordExpDeduct.setCreateTime(LocalDateTime.now());
            activityVipRecordExpDeduct.setExp(maintainDeductExp);
            activityVipRecordExpDeduct.setMemberId(activityVipMember.getId());
            activityVipRecordExpDeduct.setMemberName(activityVipMember.getMemberName());
            activityVipRecordExpDeduct.setMerchantId(activityVipMember.getMerchantId());
            activityVipRecordExpDeduct.setCurrencyEnum(activityVipMember.getCurrencyEnum());
            activityVipRecordMaintainFailList.add(activityVipRecordExpDeduct);

            Long upgradeCondition = Optional.ofNullable(vipLevelConfigByLevel.getUpgradeCondition()).orElse(Constants.ZERO_LONG);
            // 保级不成功且扣除经验后无法维持当前等级需要降级
            if ((activityVipMember.getCurrentExp() - maintainDeductExp) < upgradeCondition) {
                ActivityVipRecord activityVipRecordDownGrade = new ActivityVipRecord();
                activityVipRecordDownGrade.setVipRecordTypeEnum(VipRecordTypeEnum.VIP_DOWNGRADE);
                activityVipRecordDownGrade.setCreateTime(LocalDateTime.now());
                activityVipRecordDownGrade.setExp(Constants.ZERO_LONG);
                activityVipRecordDownGrade.setMemberId(activityVipMember.getId());
                activityVipRecordDownGrade.setMemberName(activityVipMember.getMemberName());
                activityVipRecordDownGrade.setMerchantId(activityVipMember.getMerchantId());
                activityVipRecordDownGrade.setCurrencyEnum(activityVipMember.getCurrencyEnum());
                activityVipRecordDownGrade.setRemark(Integer.toString(activityVipMember.getVipLevel() - 1));
                activityVipRecordDownGradeList.add(activityVipRecordDownGrade);
            }
        } catch (Exception e) {
            log.error("getMaintainFailList fail, activityVipMember:{}, activityRowKeyTable:{}", activityVipMember, activityRowKeyTable, e);
        }
    }

    public ActivityVipMemberRewardDTO getVipMemberRewardOne(MemberTokenInfoDTO memberTokenInfoDTO){
        Assert.notNull(memberTokenInfoDTO);
        Assert.notNull(memberTokenInfoDTO.getId());
        Assert.notNull(memberTokenInfoDTO.getMerchantId());
        Assert.notNull(memberTokenInfoDTO.getCurrencyEnum());

        Activity activity = activityService.getEnableOneNotNull(memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getCurrencyEnum(), this.getActivityTypeEnum());

        ActivityVipMember activityVipMember = activityVipMemberService.getOneByMemberIdAndMerchantIdNotNull(memberTokenInfoDTO.getId(), memberTokenInfoDTO.getMerchantId());

        ActivityVipMemberRewardDTO activityVipMemberRewardDTO = new ActivityVipMemberRewardDTO();

        activityVipMemberRewardDTO.setVipLevel(activityVipMember.getVipLevel());
        //vip已领取的等级
        activityVipMemberRewardDTO.setReceiveVipLevel(strToSet(activityVipMember.getReceivedLevelSetJson()));

        long diffMonthlyReward = getDiffMonthlyReward(activityVipMember, activity, LocalDate.now());
        activityVipMemberRewardDTO.setMonthlyReward(diffMonthlyReward);

        Long upgradeReward = getMemberUpgradeReward(activityVipMember, activity);
        activityVipMemberRewardDTO.setUpgradeReward(upgradeReward);

        return activityVipMemberRewardDTO;
    }

    private Long getMemberUpgradeReward(ActivityVipMember activityVipMember, Activity activity) {
        Assert.notNull(activityVipMember, "ActivityVipMember must not be null.");
        Assert.notNull(activity, "Activity must not be null.");
        Assert.notNull(activityVipMember.getVipLevel(), "activityVipMember vipLevel must not be null.");

        List<ActivityVipConfigParam> activityVipConfigParamList = activity.activityParamToActivityVipConfigParamListNotNull();
        return activityVipConfigParamList.stream()
                .filter(param -> param.getVipLevel() <= activityVipMember.getVipLevel())
                .filter(param -> !activityVipMember.getReceivedLevelSet().contains(param.getVipLevel()))
                .mapToLong(ActivityVipConfigParam::getUpgradeReward)
                .sum();
    }
    private ActivityRecord receiveUpgradeAward(ActivityRewardParam activityRewardParam) {
        Assert.notNull(activityRewardParam.getOptional(), " receive level must not be null.");
        Activity vipActivity = activityService.getEnableOneNotNull(activityRewardParam.getMemberTokenInfoDTO().getMerchantId(), activityRewardParam.getMemberTokenInfoDTO().getCurrencyEnum(), this.getActivityTypeEnum());
        ActivityVipMember activityVipMember = activityVipMemberService.getOneByMemberIdAndMerchantIdNotNull(activityRewardParam.getMemberTokenInfoDTO().getId(), activityRewardParam.getMemberTokenInfoDTO().getMerchantId());

        ActivityVipLevelDTO activityVipLevelDto = getSingleVip(vipActivity, activityVipMember, activityRewardParam);
        if (Objects.isNull(activityVipLevelDto.getMemberUpgradeReward())|| activityVipLevelDto.getMemberUpgradeReward() <= Constants.ZERO_LONG) {
            throw new ApiException(CommonCode.ACTIVITY_CONDITION_NOT_MEET);
        }
        boolean update = activityVipMemberService.lambdaUpdate()
                .set(ActivityVipMember::getReceivedLevelSetJson, JSONUtil.toJsonStr(activityVipLevelDto.getReceivedUpgradeRewardLeveLSet()))
                .eq(ActivityVipMember::getId, activityVipMember.getId())
                .eq(ActivityVipMember::getMerchantId, activityVipMember.getMerchantId())
                .eq(ActivityVipMember::getReceivedLevelSetJson, activityVipMember.getReceivedLevelSetJson())
                .update();

        if(!update){
            throw new ApiException(CommonCode.SYSTEM_BUSY_TRY_LATER);
        }

        LocalDateTime localDateTimeNow = LocalDateTime.now();
        //将领取的等级传递到后面的生成uid使用
        activityVipMember.setVipLevel(Integer.valueOf(activityRewardParam.getOptional()));

        return saveVipRecordAndOperateWallet(vipActivity, activityVipLevelDto.getMemberUpgradeReward(), localDateTimeNow, activityVipMember, SubActivityTypeEnum.VIP_UPGRADE_REWARD, activityRewardParam);
    }

    private ActivityVipLevelDTO getSingleVip(Activity vipActivity , ActivityVipMember activityVipMember , ActivityRewardParam activityRewardParam ){
        Assert.notNull(activityRewardParam.getOptional(), "receiveVipLevel must not be null.");
        ActivityVipLevelDTO activityVipLevelDto = new ActivityVipLevelDTO();
        Integer level = Integer.valueOf(activityRewardParam.getOptional()) ;
        //已经领取的等级
        Set<Integer> levelSet = strToSet(activityVipMember.getReceivedLevelSetJson());
        if(levelSet.contains(level)){
            throw new ApiException(CommonCode.ACTIVITY_CONDITION_NOT_MEET);
        }
        //添加未领取的等级
        levelSet.add(level);
        ActivityVipConfigParam vipLevelConfigByLevel = vipActivity.activityParamToVipLevelConfigByVipLevel(level);
        if(Objects.nonNull(vipLevelConfigByLevel)){
            activityVipLevelDto.setMemberUpgradeReward(vipLevelConfigByLevel.getUpgradeReward());
        }else{
            throw new ApiException(CommonCode.ACTIVITY_CONDITION_NOT_MEET);
        }
        activityVipLevelDto.setReceivedUpgradeRewardLeveLSet(levelSet);
        return activityVipLevelDto ;
    }

    private static Set<Integer> strToSet(String setStr) {
        Set<Integer> hashSet = new TreeSet<>();
        String[] strArray = setStr.substring(1, setStr.length() - 1).split(",");
        // 将分割后的字符串数组转换为整数，并添加到HashSet中
        for (String s : strArray) {
            if(StringUtils.isNotBlank(s)){
                hashSet.add(Integer.parseInt(s.trim()));
            }
        }
        return hashSet;
    }
    private ActivityRecord saveVipRecordAndOperateWallet(Activity vipActivity, Long reward, LocalDateTime localDateTimeNow, ActivityVipMember activityVipMember, SubActivityTypeEnum subActivityTypeEnum, ActivityRewardParam activityRewardParam) {
        ActivityRecord activityRecordSave = getActivityRecord(vipActivity, reward, localDateTimeNow, activityVipMember, subActivityTypeEnum, activityRewardParam);
        activityRecordService.saveAndOperateWallet(activityRecordSave);
        return activityRecordSave;
    }

    @NotNull
    private  ActivityRecord getActivityRecord(Activity vipActivity, Long reward, LocalDateTime localDateTimeNow, ActivityVipMember activityVipMember, SubActivityTypeEnum subActivityTypeEnum, ActivityRewardParam activityRewardParam) {
        ActivityRecord activityRecordSave = new ActivityRecord();
        activityRecordSave.setActivityId(vipActivity.getId());
        activityRecordSave.setAmount(reward);
        activityRecordSave.setActivityTypeEnum(vipActivity.getActivityTypeEnum());
        activityRecordSave.setSubActivityTypeEnum(subActivityTypeEnum);
        activityRecordSave.setCreateTime(localDateTimeNow);
        activityRecordSave.setUpdateTime(localDateTimeNow);
        activityRecordSave.setMarkDate(localDateTimeNow.toLocalDate());

        activityRecordSave.setMemberId(activityVipMember.getId());
        activityRecordSave.setMemberName(activityVipMember.getMemberName());
        activityRecordSave.setMerchantId(activityVipMember.getMerchantId());
        activityRecordSave.setCurrencyEnum(activityVipMember.getCurrencyEnum());

        activityRecordSave.setRequestIp(activityRewardParam.getRequestIp());
        activityRecordSave.setChannelId(activityRewardParam.getMemberTokenInfoDTO().getChannelId());
        activityRecordSave.setIsDirect(activityRewardParam.getMemberTokenInfoDTO().getIsDirect());

        ActivityVipRecordParam activityVipRecordParam = new ActivityVipRecordParam();
        activityVipRecordParam.setVipLevel(activityVipMember.getVipLevel());
        activityRecordSave.setRecordJson(JSONUtil.toJsonStr(activityVipRecordParam));

        activityRecordSave.setUid(getUid(activityRecordSave));
        return activityRecordSave;
    }

    private  ActivityRecord receiveMonthlyReward(MemberTokenInfoDTO memberTokenInfoDTO, ActivityRewardParam activityRewardParam) {
        LocalDateTime nowLocalDateTime = LocalDateTime.now();

        Activity vipActivity = activityService.getEnableOneNotNull(memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getCurrencyEnum(), this.getActivityTypeEnum());

        ActivityVipMember activityVipMember = activityVipMemberService.getOneByMemberIdAndMerchantIdNotNull(memberTokenInfoDTO.getId(), memberTokenInfoDTO.getMerchantId());

        long diffMonthlyReward = getDiffMonthlyReward(activityVipMember, vipActivity, nowLocalDateTime.toLocalDate());

        // 发放月奖励
        if (diffMonthlyReward <= 0) {
            throw new ApiException(CommonCode.ACTIVITY_CONDITION_NOT_MEET);
        }

        return saveVipRecordAndOperateWallet(vipActivity, diffMonthlyReward, nowLocalDateTime, activityVipMember, SubActivityTypeEnum.VIP_MONTHLY_REWARD, activityRewardParam);
    }

    private String getUpgradeRewardUid(Long memberId, Integer level) {
        String uidStr = memberId.toString() +  StrPool.COLON + this.getActivityTypeEnum() +  StrPool.COLON + SubActivityTypeEnum.VIP_UPGRADE_REWARD +  StrPool.COLON + level.toString();
        return Md5Util.getMd5HexLowerCase(uidStr);
    }

    private String getMonthlyRewardUid(Long memberId, Integer level, LocalDate localDate) {
        String uidStr = memberId.toString() + StrPool.COLON + this.getActivityTypeEnum() + StrPool.COLON + SubActivityTypeEnum.VIP_MONTHLY_REWARD +  StrPool.COLON +level.toString() + StrPool.COLON + localDate.getYear() + StrPool.COLON +localDate.getMonth();
        return Md5Util.getMd5HexLowerCase(uidStr);
    }

    private long getDiffMonthlyReward(ActivityVipMember activityVipMember, Activity vipActivity, LocalDate nowLocalDate) {
        Integer level = activityVipMember.getVipLevel();
        ActivityVipConfigParam vipLevelConfigByLevel = vipActivity.activityParamToVipLevelConfigByVipLevel(level);
        long levelMonthlyReward = Optional.ofNullable(vipLevelConfigByLevel.getMonthlyReward()).orElse(Constants.ZERO_LONG);
        if (levelMonthlyReward <= Constants.ZERO_LONG) {
            return Constants.ZERO_LONG;
        }
        List<ActivityRecord> activityRecordList = activityRecordService.lambdaQuery()
                .eq(ActivityRecord::getMemberId, activityVipMember.getId())
                .eq(ActivityRecord::getMerchantId, activityVipMember.getMerchantId())
                .eq(ActivityRecord::getActivityTypeEnum, this.getActivityTypeEnum())
                .eq(ActivityRecord::getSubActivityTypeEnum, SubActivityTypeEnum.VIP_MONTHLY_REWARD)
                .between(ActivityRecord::getMarkDate, LocalUtil.beginOfMonth(nowLocalDate), LocalUtil.endOfMonth(nowLocalDate))
                .list();
        if (CollUtil.isEmpty(activityRecordList)) {
            return levelMonthlyReward;
        }

        boolean currentVipLevelMonthlyRewardAlreadyReceiveResult = currentVipLevelMonthlyRewardAlreadyReceive(activityVipMember, activityRecordList);
        if (currentVipLevelMonthlyRewardAlreadyReceiveResult) {
            return Constants.ZERO_LONG;
        }

        // 本月未领取当前等级每月奖励，计算下是否有多次领取，多次领取则扣除前面已经领取过的每月奖励总和
        long monthlyTotalReward = activityRecordList.stream().mapToLong(ActivityRecord::getAmount).sum();
        long diffMonthlyReward = levelMonthlyReward - monthlyTotalReward;
        return Math.max(diffMonthlyReward, Constants.ZERO_LONG);
    }

    private boolean currentVipLevelMonthlyRewardAlreadyReceive(ActivityVipMember activityVipMember, List<ActivityRecord> activityRecordList) {
        for (ActivityRecord activityRecord : activityRecordList) {
            ActivityVipRecordParam recordParam = activityRecord.getRecordParam(ActivityVipRecordParam.class);
            if (Objects.isNull(recordParam)) {
                continue;
            }
            if (activityVipMember.getVipLevel().equals(recordParam.getVipLevel())) {
                return true;
            }
        }
        return false;
    }

}
