package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.JacksonUtil;
import com.wd.lottery.common.util.Md5Util;
import com.wd.lottery.module.activity.constants.ActivityTypeEnum;
import com.wd.lottery.module.activity.constants.SubActivityTypeEnum;
import com.wd.lottery.module.activity.dto.TeamProxyPlatformBonusDTO;
import com.wd.lottery.module.activity.entity.*;
import com.wd.lottery.module.activity.param.ActivityParam;
import com.wd.lottery.module.activity.param.ActivityRewardParam;
import com.wd.lottery.module.activity.param.teamproxy.ActivityAATeamProxyConfigParam;
import com.wd.lottery.module.activity.param.teamproxy.ActivityAATeamProxyMemberParam;
import com.wd.lottery.module.activity.param.teamproxy.ActivityAATeamProxyParam;
import com.wd.lottery.module.activity.service.*;
import com.wd.lottery.module.cash.dto.GetMemberIdAndMoneyDTO;
import com.wd.lottery.module.cash.entity.CashDepositOrder;
import com.wd.lottery.module.cash.service.CashDepositOrderService;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.entity.MemberProxy;
import com.wd.lottery.module.member.service.MemberProxyService;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.merchant.entity.Merchant;
import com.wd.lottery.module.merchant.service.MerchantService;
import com.wd.lottery.module.report.dto.ListGroupGameCategoryByDateDTO;
import com.wd.lottery.module.report.entity.ReportFirstDepositHis;
import com.wd.lottery.module.report.entity.ReportMemberGameDate;
import com.wd.lottery.module.report.service.ReportFirstDepositHisService;
import com.wd.lottery.module.report.service.ReportMemberGameDateService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ActivityAATeamProxyServiceImpl implements ActivityAATeamProxyService {

    private static final String TAG = "ActivityTeamProxyService-";

    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private TransactionDefinition transactionDefinition;

    @Resource
    private ReportMemberGameDateService reportMemberGameDateService;
    @Resource
    private CashDepositOrderService cashDepositOrderService;
    @Resource
    private ReportFirstDepositHisService reportFirstDepositHisService;

    @Resource
    private MemberProxyService memberProxyService;
    @Resource
    private MemberService memberService;

    @Resource
    private ActivityService activityService;

    @Resource
    private ActivityRecordService activityRecordService;

    @Resource
    private MerchantService merchantService;

    @Resource
    private ActivityAATeamProxyBonusService activityAATeamProxyBonusService;
    @Resource
    private ActivityAATeamProxyBonusDailySummaryService activityAATeamProxyBonusDailySummaryService;
    @Resource
    private ActivityAATeamProxyLowerLevelMemberService activityAATeamProxyLowerLevelMemberService;
    @Resource
    private ActivityAATeamProxyMemberService activityAATeamProxyMemberService;

    @Override
    public List<ActivityRecord> getRewardList(ActivityRewardParam activityRewardParam) {
        MemberTokenInfoDTO dto = activityRewardParam.getMemberTokenInfoDTO();
        Activity activity = getActivityByMerchantAndCurrencyNotNull(dto.getMerchantId(), dto.getCurrencyEnum());
        Long settleBonusAmount = activityAATeamProxyBonusDailySummaryService.getSettleBonusAmount(dto, activity.getId());

        ActivityRecord activityRecordTeamProxyReward = new ActivityRecord();
        activityRecordTeamProxyReward.setActivityTypeEnum(this.getActivityTypeEnum());
        activityRecordTeamProxyReward.setSubActivityTypeEnum(SubActivityTypeEnum.AA_TEAM_PROXY_REWARD);
        activityRecordTeamProxyReward.setAmount(settleBonusAmount);

        return Arrays.asList(activityRecordTeamProxyReward);
    }

    @Transactional
    @Override
    public ActivityRecord receiveReward(ActivityRewardParam activityRewardParam) {
        MemberTokenInfoDTO dto = activityRewardParam.getMemberTokenInfoDTO();
        BigInteger requestIp = activityRewardParam.getRequestIp();
        Activity activity = getActivityByMerchantAndCurrencyNotNull(dto.getMerchantId(), dto.getCurrencyEnum());
        List<ActivityAATeamProxyBonusDailySummary> waitSettleBonusList = activityAATeamProxyBonusDailySummaryService.getWaitSettleBonusList(dto, activity.getId());

        ActivityRecord activityRecord = activityRecordService.initActivityRecord(dto, activity, SubActivityTypeEnum.AA_TEAM_PROXY_REWARD, LocalDateTime.now(), requestIp);
        activityRecord.setUid(getUid(activityRecord));
        activityRecord.setAmount(0L);
        Set<Long> ids = waitSettleBonusList.stream().distinct().map(ActivityAATeamProxyBonusDailySummary::getId).collect(Collectors.toSet());
        Integer batchUpdateSize = activityAATeamProxyBonusDailySummaryService.batchUpdateTeamProxyBonus(activityRewardParam.getMemberTokenInfoDTO().getMerchantId(),ids);
        if (ObjectUtil.notEqual(batchUpdateSize,ids.size())) {
            throw new ApiException(CommonCode.FREQUENT_REQUESTS);
        }

        for (ActivityAATeamProxyBonusDailySummary dailySummary : waitSettleBonusList) {
            activityRecord.setAmount(activityRecord.getAmount() + dailySummary.getTeamProxyBonus());
        }
        activityRecordService.saveAndOperateWallet(activityRecord);
        return activityRecord;
    }

    @Override
    public void settleTeamProxyBonus(LocalDate yesterdayDate) {
        //查询所有Merchant
        List<Merchant> allMerchant = merchantService.list();

        for (Merchant merchant : allMerchant) {
            TransactionStatus status = transactionManager.getTransaction(transactionDefinition);
            try {
                settleTeamProxyBonusImpl(merchant, yesterdayDate);
                transactionManager.commit(status);
            } catch (Exception e) {
                transactionManager.rollback(status);
                log.error(String.format(TAG + "settleTeamProxyBonus error, merchantId:{}", merchant.getCode()), e);
            }
        }
    }

    private void settleTeamProxyBonusImpl(Merchant merchant, LocalDate yesterdayDate) {
        final int maxLevel = 3;
        final int conditionCount = 3;
        //1. 首先查询该商户的所有对应配置
        Map<CurrencyEnum, Activity> currencyConfigMap = getMerchantActivityConfigCurrencyConfigMap(merchant);
        Long merchantId = merchant.getId();
        if (currencyConfigMap.isEmpty()) {
            log.warn(TAG + "settleTeamProxyBonusImpl currencyConfigMap is empty, merchantId:{}", merchantId);
            return;
        }

        Map<Long, CurrencyEnum> memberIdCurrencyMap = new HashMap<>();

        Map<Long, CurrencyEnum> highMemberIdCurrencyMap = new HashMap<>();

        Map<Long, Integer> highMemberIdTeamLevelMap = new HashMap<>();

        //货币->team proxy配置列表
        Map<CurrencyEnum, List<ActivityAATeamProxyConfigParam>> currencyConfigListMap = new HashMap<>();
        //货币->teamLevel->level-> (gameCategory -> proxyRatio)
        Map<CurrencyEnum, Map<Integer, Map<Integer, Map<String, Long>>>> currencyTeamLevelGameCategoryRatioMap = new HashMap<>();

        for (Map.Entry<CurrencyEnum, Activity> entry : currencyConfigMap.entrySet()) {
            CurrencyEnum currencyEnum = entry.getKey();
            Activity activity = entry.getValue();
            ActivityAATeamProxyParam param = activity.activityParamToBean(ActivityAATeamProxyParam.class);
            currencyConfigListMap.put(currencyEnum, param.getActivityAATeamProxyConfigParamList());
            Map<Integer, Map<Integer, Map<String, Long>>> levelGameCategoryRatioMap = new HashMap<>();
            for (ActivityAATeamProxyConfigParam activityAATeamProxyConfigParam : param.getActivityAATeamProxyConfigParamList()) {
                levelGameCategoryRatioMap.put(activityAATeamProxyConfigParam.getTeamLevel(), activityAATeamProxyConfigParam.getLevelGameCategoryProxyRatioMap());
            }
            currencyTeamLevelGameCategoryRatioMap.put(currencyEnum, levelGameCategoryRatioMap);
        }

        LocalDateTime yesterdayStartDateTime = LocalDateTimeUtil.beginOfDay(LocalDateTime.of(yesterdayDate, LocalTime.now()));
        LocalDateTime yesterdayEndDateTime = LocalDateTimeUtil.endOfDay(LocalDateTime.of(yesterdayDate, LocalTime.now()));

        //查询昨日投注会员Id列表
        ListGroupGameCategoryByDateDTO searchDateMemberGameReportParam = new ListGroupGameCategoryByDateDTO(merchantId, yesterdayDate, yesterdayDate);
        List<ReportMemberGameDate> yesterdayBetMemberList = reportMemberGameDateService.getListGroupGameCategoryByDate(
                searchDateMemberGameReportParam);

        //统计用户在不同分类下的投注金额
        //memberId -> GameCategoryEnum -> validBetMoney
        Map<Long, Map<GameCategoryEnum, Long>> memberIdgameCategoryBetMoneyMap = new HashMap<>();
        //memberId -> validBetMoney
        Map<Long, Long> memberIdBetMoneyMap = new HashMap<>();
        for (ReportMemberGameDate reportMemberGameDate : yesterdayBetMemberList) {
            Long memberId = reportMemberGameDate.getMemberId();
            GameCategoryEnum gameCategoryEnum = reportMemberGameDate.getGameCategoryEnum();
            Long validBetAmount = reportMemberGameDate.getValidBetAmount();

            //不存在的货币不统计
            if (!currencyConfigMap.containsKey(reportMemberGameDate.getCurrencyEnum())) {
                continue;
            }

            memberIdCurrencyMap.put(memberId, reportMemberGameDate.getCurrencyEnum());

            Map<GameCategoryEnum, Long> gameCategoryBetMoneyMap = memberIdgameCategoryBetMoneyMap.computeIfAbsent(memberId, k -> new HashMap<>());
            gameCategoryBetMoneyMap.put(gameCategoryEnum,
                    gameCategoryBetMoneyMap.getOrDefault(gameCategoryEnum, 0L) + validBetAmount);

            memberIdBetMoneyMap.put(memberId,
                    memberIdBetMoneyMap.getOrDefault(memberId, 0L) + validBetAmount);
        }

        //查询昨日充值会员Id列表
        List<CashDepositOrder> memberDepositList = cashDepositOrderService.getMemberIdAndDepositMoneyByDate(
                new GetMemberIdAndMoneyDTO(merchantId, yesterdayStartDateTime, yesterdayEndDateTime));

        //memberId -> depositMoney
        Map<Long, Long> memberIdDepositMoneyMap = new HashMap<>();
        for (CashDepositOrder cashDepositOrder : memberDepositList) {
            Long memberId = cashDepositOrder.getMemberId();
            Long depositMoney = cashDepositOrder.getRealMoney();
            CurrencyEnum currencyEnum = cashDepositOrder.getCurrencyEnum();

            //不存在的货币不统计
            if (!currencyConfigMap.containsKey(currencyEnum)) {
                continue;
            }
            memberIdCurrencyMap.put(memberId, currencyEnum);
            memberIdDepositMoneyMap.put(memberId, depositMoney);
        }

        //查询首充数据
        List<ReportFirstDepositHis> reportFirstDepositHis = reportFirstDepositHisService.searchFirstDepositByDate(merchantId, yesterdayStartDateTime, yesterdayEndDateTime, merchant.getCurrencyEnumList());
        //memberId -> 首充金额
        Map<Long, Long> memberIdFirstDepositMoneyMap = new HashMap<>();
        for (ReportFirstDepositHis firstDepositHis : reportFirstDepositHis) {
            Long memberId = firstDepositHis.getMemberId();
            Long depositMoney = firstDepositHis.getMoney();

            memberIdFirstDepositMoneyMap.put(memberId, depositMoney);
        }

        LocalDateTime optDateTime = LocalDateTime.now();

        // 只对当天有充值的用户进行历史记录查询，用于去重
        Set<Long> todayRechargeMembers = new HashSet<>(memberIdDepositMoneyMap.keySet());
        Set<Long> historicalRechargeMembers = Collections.emptySet();
        if (!todayRechargeMembers.isEmpty()) {
            historicalRechargeMembers = activityAATeamProxyLowerLevelMemberService.getHistoricalRechargeMembers(merchantId, todayRechargeMembers);
        }

        // 查询现有的 ActivityAATeamProxyMember 数据，用于内存操作
        Map<Long, ActivityAATeamProxyMember> existingMemberMap = new HashMap<>();

        //开始进行结算数据生成
        //memberId  -> dailySummary
        Map<Long, ActivityAATeamProxyBonusDailySummary> bonusDailySummaryMap = new HashMap<>();
        //memberId -> level(用户的代理层级) -> gameCategory - bonus
        Map<Long, Map<Integer, Map<GameCategoryEnum, ActivityAATeamProxyBonus>>> bonusMap = new HashMap<>();
        //highMemberId -> level(用户的代理层级) -> lowerUser
        Map<Long, Map<Integer, List<ActivityAATeamProxyLowerLevelMember>>> lowerLevelMemberDataEntityMap = new HashMap<>();
        //highMemberId -> AATeamProxyMember
        Map<Long, ActivityAATeamProxyMemberParam> highMemberIdTeamProxyMemberParamMap = new HashMap<>();
        for (Map.Entry<Long, CurrencyEnum> entry : memberIdCurrencyMap.entrySet()) {
            Long memberId = entry.getKey();
            CurrencyEnum currencyEnum = entry.getValue();

            List<MemberProxy> allHighMember = memberProxyService.getAllHighMemberByLevel(merchantId, memberId, maxLevel);
            if (allHighMember == null || allHighMember.isEmpty()) {
                continue;
            }

            Member currentMember = memberService.getMemberById(memberId, merchantId);
            //给当前会员的每个上级进行数据统计
            //上级根据下级的数据统计自己的数据， 即 这里的 highMember 统计 外层循环 memberId 的数据
            for (MemberProxy highMember : allHighMember) {
                Integer currentLevel = highMember.getLev();

                Map<Integer, Map<GameCategoryEnum, ActivityAATeamProxyBonus>> bonusEntityMap = bonusMap.computeIfAbsent(
                        highMember.getHighMemberId(),
                        highMemberId -> new HashMap<>());

                highMemberIdCurrencyMap.put(highMember.getHighMemberId(), highMember.getCurrencyEnum());

                //根据投注数据统计上级数据
                Map<GameCategoryEnum, Long> gameCategoryBetMoneyMap = memberIdgameCategoryBetMoneyMap.get(memberId);
                if (gameCategoryBetMoneyMap != null) {
                    for (Map.Entry<GameCategoryEnum, Long> gameCategoryBetMoneyEntry : gameCategoryBetMoneyMap.entrySet()) {
                        Map<GameCategoryEnum, ActivityAATeamProxyBonus> gameCategoryBonusMap = bonusEntityMap
                                .computeIfAbsent(currentLevel, level -> new HashMap<>());
                        ActivityAATeamProxyBonus bonus = gameCategoryBonusMap.computeIfAbsent(gameCategoryBetMoneyEntry.getKey(), gameCategoryEnum -> new ActivityAATeamProxyBonus());
                        computeBonusBaseData(yesterdayDate, highMember, gameCategoryBetMoneyEntry, bonus, merchantId, currentLevel, currencyEnum, optDateTime);
                    }
                }

                boolean isDeposit = memberIdDepositMoneyMap.containsKey(memberId);
                boolean isBet = memberIdBetMoneyMap.containsKey(memberId);
                boolean isFirstDeposit = memberIdFirstDepositMoneyMap.containsKey(memberId);
                boolean isYesterdayRegister = LocalDateTimeUtil.isSameDay(currentMember.getCreateTime(), yesterdayStartDateTime);
                LocalDateTime memberCreateTime = currentMember.getCreateTime();

                Long rechargeAmount = memberIdDepositMoneyMap.getOrDefault(memberId, 0L);
                Long betAmount = memberIdBetMoneyMap.getOrDefault(memberId, 0L);

                // 判断该用户是否为新充值用户（历史上从未充值过且当前有充值）
                boolean isNewRechargeUser = isDeposit && !historicalRechargeMembers.contains(memberId);

                //计算 highMember 对应团队等级
                if (!highMemberIdTeamProxyMemberParamMap.containsKey(highMember.getHighMemberId())) {
                    ActivityAATeamProxyMemberParam memberParam = new ActivityAATeamProxyMemberParam();
                    memberParam.setId(highMember.getHighMemberId());
                    memberParam.setMerchantId(highMember.getMerchantId());
                    memberParam.setMemberName(highMember.getHighMemberName());
                    memberParam.setCurrencyEnum(highMember.getCurrencyEnum());
                    // 只有新充值用户才计入充值人数
                    memberParam.setTotalRechargeUserCount(isNewRechargeUser ? 1 : 0);
                    memberParam.setTotalRechargeAmount(rechargeAmount);
                    memberParam.setTotalBetAmount(betAmount);
                    highMemberIdTeamProxyMemberParamMap.put(highMember.getHighMemberId(), memberParam);
                } else {
                    ActivityAATeamProxyMemberParam memberParam = highMemberIdTeamProxyMemberParamMap.get(highMember.getHighMemberId());
                    // 只有新充值用户才计入充值人数
                    memberParam.setTotalRechargeUserCount((isNewRechargeUser ? 1 : 0) + memberParam.getTotalRechargeUserCount());
                    memberParam.setTotalRechargeAmount(rechargeAmount + memberParam.getTotalRechargeAmount());
                    memberParam.setTotalBetAmount(betAmount + memberParam.getTotalBetAmount());
                }

                //根据投注和支付信息统计整体汇总数据
                ActivityAATeamProxyBonusDailySummary bonusDailySummary = bonusDailySummaryMap.computeIfAbsent(highMember.getHighMemberId(), k -> {
                    Activity activity = currencyConfigMap.get(currencyEnum);
                    ActivityAATeamProxyBonusDailySummary dailySummary = new ActivityAATeamProxyBonusDailySummary();
                    if (activity != null) {
                        dailySummary.setActivityId(activity.getId());
                    }
                    return dailySummary;
                });
                computeDailySummaryBaseData(yesterdayDate, highMember, bonusDailySummary, merchantId, currencyEnum, optDateTime, isDeposit, memberIdDepositMoneyMap, memberId, isBet, memberIdBetMoneyMap, isYesterdayRegister, memberIdFirstDepositMoneyMap, isFirstDeposit);

                //根据投注和支付信息统计下级用户数据
                ActivityAATeamProxyLowerLevelMember lowerLevelUserDataEntity = new ActivityAATeamProxyLowerLevelMember();
                computeLowerMemberBaseData(yesterdayDate, highMember, lowerLevelUserDataEntity, merchantId, memberId, currentMember, currentLevel, currencyEnum, memberIdDepositMoneyMap, memberIdBetMoneyMap, optDateTime, isYesterdayRegister, memberIdFirstDepositMoneyMap.getOrDefault(memberId, 0L), memberCreateTime);

                Map<Integer, List<ActivityAATeamProxyLowerLevelMember>> lowerUserListMap = lowerLevelMemberDataEntityMap.computeIfAbsent(lowerLevelUserDataEntity.getHighMemberId(), v -> new HashMap<>());
                List<ActivityAATeamProxyLowerLevelMember> lowerUserList = lowerUserListMap.computeIfAbsent(highMember.getLev(), v -> new ArrayList<>());
                lowerUserList.add(lowerLevelUserDataEntity);
            }
        }

        // 预先查询需要的 ActivityAATeamProxyMember 数据
        Set<Long> memberIds = highMemberIdTeamProxyMemberParamMap.keySet();
        if (!memberIds.isEmpty()) {
            List<ActivityAATeamProxyMember> existingMembers = activityAATeamProxyMemberService.lambdaQuery()
                    .eq(ActivityAATeamProxyMember::getMerchantId, merchantId)
                    .in(ActivityAATeamProxyMember::getId, memberIds)
                    .list();
            for (ActivityAATeamProxyMember member : existingMembers) {
                existingMemberMap.put(member.getId(), member);
            }
        }

        //更新用户等级（仅内存操作，不保存到数据库）
        highMemberIdTeamProxyMemberParamMap.values().forEach(it -> {
            ActivityAATeamProxyMember member = updateMemberInMemory(
                    it, currencyConfigListMap.get(it.getCurrencyEnum()), existingMemberMap);
            highMemberIdTeamLevelMap.put(it.getId(), member.getTeamLevel());
        });

        //根据上述数据匹配配置进行结算
        for (Map.Entry<Long, Map<Integer, Map<GameCategoryEnum, ActivityAATeamProxyBonus>>> bonusEntry : bonusMap.entrySet()) {
            Long memberId = bonusEntry.getKey();
            Map<Integer, Map<GameCategoryEnum, ActivityAATeamProxyBonus>> levelGameCategoryBonusMap = bonusEntry.getValue();
            CurrencyEnum currencyEnum = highMemberIdCurrencyMap.get(memberId);

            //开始计算佣金
            for (Map.Entry<Integer, Map<GameCategoryEnum, ActivityAATeamProxyBonus>> levelGameCategoryBonusEntry : levelGameCategoryBonusMap.entrySet()) {
                Integer level = levelGameCategoryBonusEntry.getKey();
                Map<GameCategoryEnum, ActivityAATeamProxyBonus> gameCategoryBonusMap = levelGameCategoryBonusEntry.getValue();

                //获取对应团队等级-层级-游戏类型 返佣比率
                int teamLevel = highMemberIdTeamLevelMap.getOrDefault(memberId, 0);
                if (teamLevel == 0) {
                    log.debug("");
                    continue;
                }

                //货币->teamLevel->level-> (gameCategory -> proxyRatio)
                Map<String, Long> gameCategoryRateMap = currencyTeamLevelGameCategoryRatioMap.getOrDefault(currencyEnum, Collections.emptyMap())
                        .getOrDefault(teamLevel, Collections.emptyMap())
                        .getOrDefault(level, Collections.emptyMap());

                if (gameCategoryRateMap.isEmpty()) {
                    continue;
                }

                for (Map.Entry<GameCategoryEnum, ActivityAATeamProxyBonus> gameCategoryBonusEntry : gameCategoryBonusMap.entrySet()) {
                    GameCategoryEnum gameCategoryEnum = gameCategoryBonusEntry.getKey();
                    ActivityAATeamProxyBonus bonus = gameCategoryBonusEntry.getValue();


                    Long gameCategoryRate = gameCategoryRateMap.getOrDefault(gameCategoryEnum.name(), 0L);
                    BigDecimal ratio = computeRatio(gameCategoryRate);
                    bonus.setTeamProxyBonus(BigDecimal.valueOf(bonus.getBetAmount()).multiply(ratio).longValue());
                    bonus.setRate(gameCategoryRate.intValue());
                    bonus.setTeamLevel(teamLevel);

                    //计算下级用户
                    Map<Integer, List<ActivityAATeamProxyLowerLevelMember>> lowerUserListMap = lowerLevelMemberDataEntityMap.getOrDefault(memberId, Collections.emptyMap());
                    List<ActivityAATeamProxyLowerLevelMember> lowerMemberList = lowerUserListMap.getOrDefault(level, Collections.emptyList());
                    for (ActivityAATeamProxyLowerLevelMember lowerUser : lowerMemberList) {
                        Long betMoney = memberIdgameCategoryBetMoneyMap.getOrDefault(lowerUser.getMemberId(), Collections.emptyMap())
                                .getOrDefault(gameCategoryEnum, 0L);
                        lowerUser.setTeamProxyBonus(lowerUser.getTeamProxyBonus() + BigDecimal.valueOf(betMoney).multiply(ratio).longValue());
                    }

                }
            }
        }

        //插入统计数据
        List<ActivityAATeamProxyBonus> bonusList = bonusMap.values().stream()
                .map(v -> v.values()).flatMap(Collection::stream)
                .map(v -> v.values()).flatMap(Collection::stream)
                .collect(Collectors.toList());

        for (Map.Entry<Long, ActivityAATeamProxyBonusDailySummary> memberIdDailySummaryEntry : bonusDailySummaryMap.entrySet()) {
            Long memberId = memberIdDailySummaryEntry.getKey();
            ActivityAATeamProxyBonusDailySummary dailySummary = memberIdDailySummaryEntry.getValue();

            Map<GameCategoryEnum, Long> gameCategoryTeamProxyBonusAmountMap = new HashMap<>();
            bonusMap.values().stream().map(v -> v.values()).flatMap(Collection::stream).forEach(v -> v.values().stream()
                    .filter(t -> t.getMemberId().equals(memberId)).forEach(bonus -> {
                        gameCategoryTeamProxyBonusAmountMap.put(bonus.getGameCategoryEnum(),
                                gameCategoryTeamProxyBonusAmountMap.getOrDefault(bonus.getGameCategoryEnum(), 0L)
                                        + bonus.getTeamProxyBonus());
                    }));
            dailySummary.setThreeTeamProxyBonus(gameCategoryTeamProxyBonusAmountMap.getOrDefault(GameCategoryEnum.THREE, 0L));
            dailySummary.setLotteryTeamProxyBonus(gameCategoryTeamProxyBonusAmountMap.getOrDefault(GameCategoryEnum.LOTTERY, 0L));
            dailySummary.setSlotTeamProxyBonus(gameCategoryTeamProxyBonusAmountMap.getOrDefault(GameCategoryEnum.SLOT, 0L));
            dailySummary.setFishTeamProxyBonus(gameCategoryTeamProxyBonusAmountMap.getOrDefault(GameCategoryEnum.FISH, 0L));
            dailySummary.setCasinoTeamProxyBonus(gameCategoryTeamProxyBonusAmountMap.getOrDefault(GameCategoryEnum.CASINO, 0L));
            dailySummary.setPokerTeamProxyBonus(gameCategoryTeamProxyBonusAmountMap.getOrDefault(GameCategoryEnum.POKER, 0L));
            dailySummary.setMiniTeamProxyBonus(gameCategoryTeamProxyBonusAmountMap.getOrDefault(GameCategoryEnum.MINI, 0L));
            dailySummary.setSportTeamProxyBonus(gameCategoryTeamProxyBonusAmountMap.getOrDefault(GameCategoryEnum.SPORT, 0L));
            dailySummary.setESportTeamProxyBonus(gameCategoryTeamProxyBonusAmountMap.getOrDefault(GameCategoryEnum.E_SPORT, 0L));
            dailySummary.setCockTeamProxyBonus(gameCategoryTeamProxyBonusAmountMap.getOrDefault(GameCategoryEnum.COCK, 0L));
            dailySummary.setTeamProxyBonus(gameCategoryTeamProxyBonusAmountMap.values().stream().mapToLong(v -> v).sum());

            //设置该用户平台结算信息
            List<TeamProxyPlatformBonusDTO> memberBonusList = bonusList.stream()
                    .filter(it -> Objects.equals(memberId, it.getMemberId()))
                    .collect(Collectors.groupingBy(ActivityAATeamProxyBonus::getGameCategoryEnum))
                    .entrySet().stream()
                    .map(it -> {
                        //不同层级是分开统计的，这里记录需要合并数据
                        TeamProxyPlatformBonusDTO dto = new TeamProxyPlatformBonusDTO();
                        int betUserCount = it.getValue().stream().mapToInt(ActivityAATeamProxyBonus::getBetUserCount).sum();
                        long betAmount = it.getValue().stream().mapToLong(ActivityAATeamProxyBonus::getBetAmount).sum();
                        long teamProxyBonus = it.getValue().stream().mapToLong(ActivityAATeamProxyBonus::getTeamProxyBonus).sum();
                        BeanUtil.copyProperties(it.getValue().get(0), dto);
                        dto.setBetUserCount(betUserCount);
                        dto.setBetAmount(betAmount);
                        dto.setTeamProxyBonus(teamProxyBonus);
                        return dto;
                    }).collect(Collectors.toList());
            dailySummary.setPlatformInfoJson(JacksonUtil.toJSONString(memberBonusList));
        }

        // 计算每个会员的总佣金并更新到 ActivityAATeamProxyMember 中
        for (ActivityAATeamProxyBonusDailySummary bonus : bonusDailySummaryMap.values()) {
            Long memberId = bonus.getMemberId();
            Long teamProxyBonus = bonus.getTeamProxyBonus();
            ActivityAATeamProxyMember member = existingMemberMap.get(memberId);
            if (member != null) {
                member.setTotalTeamProxyBonus(member.getTotalTeamProxyBonus() + teamProxyBonus);
            }
        }

        // 批量更新 ActivityAATeamProxyMember 到数据库
        List<ActivityAATeamProxyMember> membersToUpdate = new ArrayList<>(existingMemberMap.values());
        activityAATeamProxyMemberService.batchUpdateMembers(membersToUpdate);

        activityAATeamProxyBonusService.saveBatch(bonusList);
        List<ActivityAATeamProxyBonusDailySummary> dailySummaryList = bonusDailySummaryMap.values().stream().collect(Collectors.toList());
        activityAATeamProxyBonusDailySummaryService.saveBatch(dailySummaryList);
        List<ActivityAATeamProxyLowerLevelMember> lowerLevelMemberList = lowerLevelMemberDataEntityMap.values().stream().map(v -> v.values()).flatMap(Collection::stream).flatMap(Collection::stream).collect(Collectors.toList());
        activityAATeamProxyLowerLevelMemberService.saveBatch(lowerLevelMemberList);
    }

    public static BigDecimal computeRatio(Long platformRatio) {
        double v = (platformRatio / 10000D);
        return BigDecimal.valueOf(v).setScale(4, RoundingMode.HALF_UP);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class MatchSetting {
        private ActivityAATeamProxyConfigParam setting;
        private boolean matchRechargeUserCount;
        private boolean matchTotalRechargeAmount;
        private boolean matchTotalBetAmount;
    }


    private static void computeLowerMemberBaseData(LocalDate yesterdayDate, MemberProxy highMember, ActivityAATeamProxyLowerLevelMember lowerLevelUserDataEntity, Long merchantId, Long memberId, Member currentMember, Integer currentLevel, CurrencyEnum currencyEnum, Map<Long, Long> memberIdDepositMoneyMap, Map<Long, Long> memberIdBetMoneyMap, LocalDateTime optDateTime, boolean isRegister, Long firstRechargeAmount, LocalDateTime memberCreateTime) {
        lowerLevelUserDataEntity.setMerchantId(merchantId);
        lowerLevelUserDataEntity.setHighMemberId(highMember.getHighMemberId());
        lowerLevelUserDataEntity.setHighMemberName(highMember.getHighMemberName());
        lowerLevelUserDataEntity.setMemberId(memberId);
        lowerLevelUserDataEntity.setMemberName(currentMember.getMemberName());
        lowerLevelUserDataEntity.setLevel(currentLevel);
        lowerLevelUserDataEntity.setPeriods(yesterdayDate);
        lowerLevelUserDataEntity.setCurrencyEnum(currencyEnum);
        lowerLevelUserDataEntity.setRechargeAmount(memberIdDepositMoneyMap.getOrDefault(memberId, 0L));
        lowerLevelUserDataEntity.setBetAmount(memberIdBetMoneyMap.getOrDefault(memberId, 0L));
        lowerLevelUserDataEntity.setCurrencyEnum(currencyEnum);
        lowerLevelUserDataEntity.setCreateTime(memberCreateTime);
        lowerLevelUserDataEntity.setFirstRegister(isRegister ? 1 : 0);
        lowerLevelUserDataEntity.setFirstRechargeAmount(firstRechargeAmount);

    }

    private static void computeDailySummaryBaseData(LocalDate yesterdayDate, MemberProxy highMember, ActivityAATeamProxyBonusDailySummary dailySummary, Long merchantId, CurrencyEnum currencyEnum, LocalDateTime optDateTime, boolean isDeposit, Map<Long, Long> memberIdDepositMoneyMap, Long memberId, boolean isBet, Map<Long, Long> memberIdBetMoneyMap, boolean isYesterdayRegister, Map<Long, Long> memberIdFirstDepositMoneyMap, boolean isFirstDeposit) {
        dailySummary.setMerchantId(merchantId);
        dailySummary.setMemberId(highMember.getHighMemberId());
        dailySummary.setMemberName(highMember.getHighMemberName());
        dailySummary.setCurrencyEnum(currencyEnum);
        dailySummary.setPeriods(yesterdayDate);
        dailySummary.setCreateTime(optDateTime);

        Integer rechargeUserCount = dailySummary.getRechargeUserCount() + (isDeposit ? 1 : 0);
        Long rechargeAmount = dailySummary.getRechargeAmount() + memberIdDepositMoneyMap.getOrDefault(memberId, 0L);
        Integer betUserCount = dailySummary.getBetUserCount() + (isBet ? 1 : 0);
        Long betAmount = dailySummary.getBetAmount() + memberIdBetMoneyMap.getOrDefault(memberId, 0L);
        Integer registerUserCount = dailySummary.getRegisterUserCount() + (isYesterdayRegister ? 1 : 0);
        Long firstRechargeAmount = dailySummary.getFirstRechargeAmount() + memberIdFirstDepositMoneyMap.getOrDefault(memberId, 0L);
        Integer firstRechargeUserCount = dailySummary.getFirstRechargeUserCount() + (isFirstDeposit ? 1 : 0);
        dailySummary.setRechargeUserCount(rechargeUserCount);
        dailySummary.setRechargeAmount(rechargeAmount);
        dailySummary.setBetUserCount(betUserCount);
        dailySummary.setBetAmount(betAmount);
        dailySummary.setRegisterUserCount(registerUserCount);
        dailySummary.setFirstRechargeAmount(firstRechargeAmount);
        dailySummary.setFirstRechargeUserCount(firstRechargeUserCount);
    }

    private static void computeBonusBaseData(LocalDate yesterdayDate, MemberProxy highMember, Map.Entry<GameCategoryEnum, Long> gameCategoryBetMoneyEntry, ActivityAATeamProxyBonus bonus, Long merchantId, Integer currentLevel, CurrencyEnum currencyEnum, LocalDateTime optDateTime) {
        GameCategoryEnum gameCategoryEnum = gameCategoryBetMoneyEntry.getKey();
        Long betMoney = gameCategoryBetMoneyEntry.getValue();
        bonus.setGameCategoryEnum(gameCategoryEnum);
        bonus.setBetUserCount(bonus.getBetUserCount() + 1);
        bonus.setBetAmount(bonus.getBetAmount() + betMoney);

        bonus.setMerchantId(merchantId);
        bonus.setMemberId(highMember.getHighMemberId());
        bonus.setMemberName(highMember.getHighMemberName());
        bonus.setLevel(currentLevel);
        bonus.setPeriods(yesterdayDate);
        bonus.setCurrencyEnum(currencyEnum);
        bonus.setCreateTime(optDateTime);
    }

    private Map<CurrencyEnum, Activity> getMerchantActivityConfigCurrencyConfigMap(Merchant merchant) {
        List<Activity> activityList = activityService.getAllCurrencyEnableActivityList(merchant.getId(), ActivityTypeEnum.AA_TEAM_PROXY);
        activityList = activityList == null ? Collections.emptyList() : activityList;
        Map<CurrencyEnum, Activity> currencyTeamProxyConfigMap = new HashMap<>();
        for (Activity activity : activityList) {
            ActivityAATeamProxyParam activityParam = activity.activityParamToBean(ActivityAATeamProxyParam.class);
            if (activityParam == null || !activityParam.checkActivityAATeamProxyConfigParamList()) {
                continue;
            }
            currencyTeamProxyConfigMap.put(activity.getCurrencyEnum(), activity);
        }
        return currencyTeamProxyConfigMap;
    }

    private Activity getActivityByMerchantAndCurrencyNotNull(Long merchantId, CurrencyEnum currencyEnum) {
        return activityService.getEnableOneNotNull(merchantId, currencyEnum, ActivityTypeEnum.AA_TEAM_PROXY);
    }

    @Override
    public void checkParam(ActivityParam activityParam) {
        ActivityAATeamProxyParam param = activityParam.getActivityParam(ActivityAATeamProxyParam.class);
        Assert.notNull(param, "configJson not null");
        Assert.notEmpty(param.getActivityAATeamProxyConfigParamList(), "activityTeamProxyConfigParamList is empty");
        Assert.isTrue(param.checkActivityAATeamProxyConfigParamList(), "activityTeamProxyConfigParamList param is error");
    }

    @Override
    public ActivityTypeEnum getActivityTypeEnum() {
        return ActivityTypeEnum.AA_TEAM_PROXY;
    }

    @Override
    public String getUid(ActivityRecord activityRecord, Object... others) {
        Assert.notNull(activityRecord.getMerchantId());
        Assert.notNull(activityRecord.getActivityTypeEnum());
        Assert.notNull(activityRecord.getSubActivityTypeEnum());
        Assert.notNull(activityRecord.getActivityId());
        Assert.notNull(activityRecord.getMarkDate());
        String uidStr = activityRecord.getMemberId().toString() + activityRecord.getActivityTypeEnum() +
                activityRecord.getSubActivityTypeEnum() + activityRecord.getActivityId() + activityRecord.getCreateTime() + (others.length > 0 ? others[0] : "");
        return Md5Util.getMd5HexLowerCase(uidStr);
    }

    @Override
    public Boolean getUniqueActivity() {
        return true;
    }

    private ActivityAATeamProxyMember updateMemberInMemory(ActivityAATeamProxyMemberParam param,
                                                          List<ActivityAATeamProxyConfigParam> teamLevelConfigList,
                                                          Map<Long, ActivityAATeamProxyMember> existingMemberMap) {
        ActivityAATeamProxyMember member = existingMemberMap.get(param.getId());
        LocalDateTime now = LocalDateTime.now();

        if (member == null) {
            member = new ActivityAATeamProxyMember();
            BeanUtil.copyProperties(param, member);
            member.setCreateTime(now);
            member.setUpdateTime(now);
            Integer teamLevel = computeTeamLevel(member, teamLevelConfigList);
            member.setTeamLevel(teamLevel);
            existingMemberMap.put(param.getId(), member);
        } else {
            member.setTotalBetAmount(param.getTotalBetAmount() + member.getTotalBetAmount());
            member.setTotalRechargeAmount(param.getTotalRechargeAmount() + member.getTotalRechargeAmount());
            member.setTotalRechargeUserCount(param.getTotalRechargeUserCount() + member.getTotalRechargeUserCount());
            member.setUpdateTime(now);
            Integer teamLevel = computeTeamLevel(member, teamLevelConfigList);
            member.setTeamLevel(teamLevel);
        }

        return member;
    }

    private Integer computeTeamLevel(ActivityAATeamProxyMember member, List<ActivityAATeamProxyConfigParam> teamLevelConfigList) {
        if (CollectionUtil.isEmpty(teamLevelConfigList))
            return 0;
        //从大到小排序
        teamLevelConfigList.sort((o1, o2) -> o2.getTeamLevel().compareTo(o1.getTeamLevel()));

        for (ActivityAATeamProxyConfigParam setting : teamLevelConfigList) {
            boolean matchRechargeUserCount = false;
            boolean matchTotalRechargeAmount = false;
            boolean matchTotalBetAmount = false;
            if (member.getTotalRechargeUserCount() >= setting.getTotalRechargeUserCount()) {
                matchRechargeUserCount = true;
            }
            if (member.getTotalRechargeAmount() >= (setting.getTotalRechargeAmount() * 10000)) {
                matchTotalRechargeAmount = true;
            }
            if (member.getTotalBetAmount() >= (setting.getTotalBetAmount() * 10000)) {
                matchTotalBetAmount = true;
            }
            if (matchRechargeUserCount && matchTotalRechargeAmount && matchTotalBetAmount) {
                return setting.getTeamLevel();
            }
        }
        return 0;
    }

}
