package com.wd.lottery.module.activity.controller.consumer;

import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.util.MemberTokenInfoUtil;
import com.wd.lottery.common.util.RequestUtil;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.param.safe.ActivitySafeWalletParam;
import com.wd.lottery.module.activity.service.*;
import com.wd.lottery.module.activity.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("${consumer-path}/${module-path.activity}/safeMember")
@Tag(name = "保险箱活动模块")
public class CActivitySafeMemberController {
    @Resource
    private ActivitySafeMemberWalletService safeMemberWalletService;

    @Resource
    private ActivityService activityService;

    @Resource
    private ActivityRecordService activityRecordService;

    @Resource
    private ActivitySafeAmountRecordService activitySafeAmountRecordService;

    @Resource
    private ActivitySafeService activitySafeService;

    @GetMapping("getBalance")
    @Operation(summary = "查询会员保险箱余额")
    public ApiResult<Long> getBalance() {
        ActivitySafeMemberWalletDTO safeMemberWalletDTO = safeMemberWalletService.getByMemberId(MemberTokenInfoUtil.getMerchantId(), MemberTokenInfoUtil.getMemberId());
        return ApiResult.success(safeMemberWalletDTO == null ? 0 : safeMemberWalletDTO.getCashBalance());
    }

    @PostMapping("getRevenueAnalysis")
    @Operation(summary = "获取当前收益&利率分析")
    public ApiResult<ActivitySafeRevenueAnalysisDTO> getRevenueAnalysis() {
        return ApiResult.success(activitySafeService.getRevenueAnalysis(MemberTokenInfoUtil.getMerchantId(),
                MemberTokenInfoUtil.getMemberId(), MemberTokenInfoUtil.getCurrencyEnum()));
    }


    @PostMapping("getPreTransferOutInfo")
    @Operation(summary = "获取转出金额时的预处理数据")
    public ApiResult<ActivitySafePerTransferOutDTO> getLatestRecord(@RequestBody ActivitySafeWalletParam param) {
        param.setMemberTokenInfoDTO(MemberTokenInfoUtil.getMemberTokenInfo());
        param.setRequestIp(RequestUtil.getBigIntegerIpFromRequest());
        ActivitySafePerTransferOutDTO preTransferOutInfo = activitySafeService.getPreTransferOutInfo(param);
        return ApiResult.success(preTransferOutInfo);
    }

    @PostMapping("operateSafeWallet")
    @Operation(summary = "操作保险箱钱包")
    public ApiResult<?> operateSafeWallet(@RequestBody ActivitySafeWalletParam param) {
        param.setMemberTokenInfoDTO(MemberTokenInfoUtil.getMemberTokenInfo());
        param.setRequestIp(RequestUtil.getBigIntegerIpFromRequest());
        activitySafeService.operateSafeWallet(param);
        return ApiResult.success();
    }

    @GetMapping("getLatestRecord")
    @Operation(summary = "获取最近保险箱活动记录")
    public ApiResult<List<ActivitySafeAmountRecordDTO>> getLatestRecord() {
        return ApiResult.success(activitySafeAmountRecordService.
                getLatestRecord(MemberTokenInfoUtil.getMemberId(), MemberTokenInfoUtil.getMerchantId()));
    }

    @GetMapping("getHistoryRecord")
    @Operation(summary = "获取保险箱活动记录")
    public ApiResult<ActivitySafeMonthRecordDTO> getHistoryRecord(@RequestParam String date) {
        return ApiResult.success(activitySafeAmountRecordService.
                getHistoryRecord(MemberTokenInfoUtil.getMemberId(), MemberTokenInfoUtil.getMerchantId(), date));
    }



}
