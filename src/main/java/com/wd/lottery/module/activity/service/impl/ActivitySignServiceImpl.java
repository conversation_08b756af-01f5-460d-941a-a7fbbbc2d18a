package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.LocalUtil;
import com.wd.lottery.common.util.Md5Util;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.entity.ActivityRecord;
import com.wd.lottery.module.activity.param.ActivityParam;
import com.wd.lottery.module.activity.param.ActivityRewardParam;
import com.wd.lottery.module.activity.param.sign.ActivitySignConfigParam;
import com.wd.lottery.module.activity.param.sign.ActivitySignParam;
import com.wd.lottery.module.activity.param.sign.ActivitySignRecordParam;
import com.wd.lottery.module.activity.service.ActivityRecordService;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.activity.service.ActivitySignService;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.report.dto.ReportMemberDateDTO;
import com.wd.lottery.module.report.entity.ReportMemberDate;
import com.wd.lottery.module.report.service.ReportMemberDateService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Slf4j
@Service
public class ActivitySignServiceImpl implements ActivitySignService {

    @Resource
    private ActivityRecordService activityRecordService;

    @Resource
    private ActivityService activityService;

    @Resource
    private ReportMemberDateService reportMemberDateService;

    @Override
    public void checkParam(ActivityParam activityParam) {
        Assert.notNull(activityParam.getStartTime());
        Assert.notNull(activityParam.getEndTime());
        Assert.isFalse(activityParam.getEndTime().isBefore(activityParam.getStartTime()), "Activity endTime is before StartTime");
        ActivitySignParam param = activityParam.getActivityParam(ActivitySignParam.class);
        Assert.notNull(param);
        Assert.notNull(param.getSignPeriodEnum());
        Assert.notEmpty(param.getActivitySignConfigParamList());
        for (ActivitySignConfigParam activitySignConfigParam : param.getActivitySignConfigParamList()) {
            Assert.notNull(activitySignConfigParam.getSignDays());
            Assert.notNull(activitySignConfigParam.getDepositAmount());
            Assert.notNull(activitySignConfigParam.getValidBetAmount());
        }
    }

    @Override
    public List<ActivityRecord> getRewardList(ActivityRewardParam activityRewardParam) {
        MemberTokenInfoDTO memberTokenInfoDTO = activityRewardParam.getMemberTokenInfoDTO();
        LocalDateTime localDateTimeNow = LocalDateTime.now();
        //查询签到的规则配置
        Activity activity = activityService.getEnableAndStartOneNotNull(memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getCurrencyEnum(), this.getActivityTypeEnum(), activityRewardParam.getActivityId());

        ActivityRecord lastSignRecord = activityRecordService.getOneOrderByCreateTimeDesc(memberTokenInfoDTO.getId(), memberTokenInfoDTO.getMerchantId(), activityRewardParam.getActivityTypeEnum(), activityRewardParam.getActivityId());

        ActivityRecord hasAlreadySignActivityRecord = checkAlreadySign(lastSignRecord, localDateTimeNow);
        if(hasAlreadySignActivityRecord != null){
            return Collections.singletonList(hasAlreadySignActivityRecord);
        }

        ReportMemberDate reportMemberDate = getReportMemberDate(memberTokenInfoDTO);

        ActivityRecord activityRecord = activityRecordService.initActivityRecord(memberTokenInfoDTO, activity, SubActivityTypeEnum.SIGN_REWARD, localDateTimeNow, activityRewardParam.getRequestIp());

        ActivitySignRecordParam activitySignRecordParam = getActivitySignRecordParam(lastSignRecord, localDateTimeNow);

        ActivitySignParam activitySignParam = activity.activityParamToBean(ActivitySignParam.class);

        int nextSignDays = getNextSignDays(activitySignRecordParam, activitySignParam);

        ActivitySignConfigParam activitySignConfigParam = getNextSignConfigParam(activitySignParam, nextSignDays);

        checkComplete(activityRecord, reportMemberDate, activitySignConfigParam, activitySignRecordParam, nextSignDays, activityRewardParam.isReceiveRequest());
        return Collections.singletonList(activityRecord);
    }

    @Override
    public ActivityRecord receiveReward(ActivityRewardParam activityRewardParam) {
        return activityRecordService.receiveReward(this.getRewardList(activityRewardParam));
    }

    private static ActivitySignConfigParam getNextSignConfigParam(ActivitySignParam activitySignParam, int nextSignDays) {
        return activitySignParam.getActivitySignConfigParamList().stream()
                .sorted(Comparator.comparingInt(ActivitySignConfigParam::getSignDays).reversed())
                .filter(o -> o.getSignDays() <= nextSignDays)
                .findFirst()
                .orElseThrow(() -> new ApiException(CommonCode.ACTIVITY_CONDITION_NOT_MEET));
    }

    private void checkComplete(ActivityRecord activityRecord, ReportMemberDate reportMemberDate, ActivitySignConfigParam activitySignConfigParam, ActivitySignRecordParam activitySignRecordParam, int nextSignDays, boolean isReceiveRequest) {
        activitySignRecordParam.setDepositAmount(reportMemberDate.getDepositAmount());
        activitySignRecordParam.setValidBetAmount(reportMemberDate.getValidBetAmount());
        activityRecord.setRecordJson(JSONUtil.toJsonStr(activitySignRecordParam));
        if (activitySignRecordParam.getDepositAmount() < activitySignConfigParam.getDepositAmount()){
            return;
        }

        if(activitySignRecordParam.getValidBetAmount() < activitySignConfigParam.getValidBetAmount()){
            return;
        }
        activityRecord.setAmount(activitySignConfigParam.getSignReward());
        activityRecord.setActivityStatusEnum(ActivityStatusEnum.WAIT_RECEIVE);
        activityRecord.setUid(this.getUid(activityRecord));
        receiveUpdateRecordJson(activityRecord, activitySignConfigParam, activitySignRecordParam, nextSignDays, isReceiveRequest);
    }

    private static void receiveUpdateRecordJson(ActivityRecord activityRecord, ActivitySignConfigParam activitySignConfigParam, ActivitySignRecordParam activitySignRecordParam, int nextSignDays, boolean receiveRequest) {
        if (!receiveRequest) {
            return;
        }
        activitySignRecordParam.setSignDays(nextSignDays);
        activitySignRecordParam.setAccumulatedReward(activitySignRecordParam.getAccumulatedReward() + activitySignConfigParam.getSignReward());
        activityRecord.setRecordJson(JSONUtil.toJsonStr(activitySignRecordParam));
    }

    private static int getNextSignDays(ActivitySignRecordParam activitySignRecordParam, ActivitySignParam activitySignParam) {
        int signDays = activitySignRecordParam.getSignDays() + 1;
        if (activitySignParam.getSignPeriodEnum().equals(SignPeriodEnum.CYCLE)
                && signDays > ActivityConstants.ACTIVITY_SIGN_PERIOD_DAYS) {
            signDays = 1;
        }
        return signDays;
    }

    @NotNull
    private static ActivitySignRecordParam getActivitySignRecordParam(ActivityRecord lastSignRecord, LocalDateTime localDateTimeNow) {
        if (lastSignRecord != null) {
            LocalDate yesterday = LocalUtil.yesterday(localDateTimeNow.toLocalDate());
            ActivitySignRecordParam recordParam = lastSignRecord.getRecordParam(ActivitySignRecordParam.class);
            // 昨天未签到表明签到中断，重新开始签到
            if(!yesterday.equals(lastSignRecord.getMarkDate())) {
                recordParam.setSignDays(Constants.ZERO_INTEGER);
            }
            return recordParam;
        }else{
            ActivitySignRecordParam activitySignRecordParam = new ActivitySignRecordParam();
            activitySignRecordParam.setSignDays(Constants.ZERO_INTEGER);
            activitySignRecordParam.setAccumulatedReward(Constants.ZERO_LONG);
            return activitySignRecordParam;
        }
    }

    private ActivityRecord getLastActivityRecord(ActivityRewardParam activityRewardParam, MemberTokenInfoDTO memberTokenInfoDTO) {
        return activityRecordService.lambdaQuery()
                .eq(ActivityRecord::getMerchantId, memberTokenInfoDTO.getMerchantId())
                .eq(ActivityRecord::getActivityTypeEnum, activityRewardParam.getActivityTypeEnum())
                .eq(ActivityRecord::getActivityId, activityRewardParam.getActivityId())
                .eq(ActivityRecord::getMemberId, memberTokenInfoDTO.getId())
                .orderByDesc(ActivityRecord::getCreateTime)
                .last(Constants.SQL_LIMIT_1)
                .one();
    }

    private static void checkStart(Activity activity) {
        if (LocalDateTime.now().isBefore(activity.getStartTime()) || LocalDateTime.now().isAfter(activity.getEndTime())) {
            throw new ApiException(CommonCode.ACTIVITY_NOT_START);
        }
    }

    @Override
    public ActivityTypeEnum getActivityTypeEnum() {
        return ActivityTypeEnum.SIGN;
    }

    @Override
    public String getUid(ActivityRecord activityRecord, Object ... others) {
        Assert.notNull(activityRecord.getMerchantId());
        Assert.notNull(activityRecord.getActivityTypeEnum());
        Assert.notNull(activityRecord.getSubActivityTypeEnum());
        Assert.notNull(activityRecord.getActivityId());
        Assert.notNull(activityRecord.getMarkDate());
        String uidStr = activityRecord.getMemberId().toString() + StrPool.COLON + activityRecord.getActivityTypeEnum() + StrPool.COLON + activityRecord.getSubActivityTypeEnum() + StrPool.COLON + activityRecord.getActivityId() + StrPool.COLON + activityRecord.getMarkDate();
        return Md5Util.getMd5HexLowerCase(uidStr);
    }

    @Override
    public Boolean getUniqueActivity() {
        return null;
    }

    private ActivityRecord checkAlreadySign(ActivityRecord lastSignRecord, LocalDateTime localDateTimeNow) {
        if (lastSignRecord == null) {
            return null;
        }

        if (!lastSignRecord.getMarkDate().equals(localDateTimeNow.toLocalDate())) {
            return null;
        }
        lastSignRecord.setAmount(Constants.ZERO_LONG);
        return lastSignRecord;
    }

    private ReportMemberDate getReportMemberDate(MemberTokenInfoDTO memberTokenInfo) {
        ReportMemberDateDTO dto = new ReportMemberDateDTO();
        dto.setMerchantId(memberTokenInfo.getMerchantId());
        dto.setStartDate(LocalDate.now());
        dto.setEndDate(LocalDate.now());
        dto.setMemberId(memberTokenInfo.getId());
        return reportMemberDateService.getOneGroupByMemberIdOrElseNew(dto);
    }

}
