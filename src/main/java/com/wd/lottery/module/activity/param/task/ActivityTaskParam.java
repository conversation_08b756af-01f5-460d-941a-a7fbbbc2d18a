package com.wd.lottery.module.activity.param.task;

import com.wd.lottery.module.activity.constants.TaskPeriodEnum;
import com.wd.lottery.module.activity.constants.TaskRewardEnum;
import com.wd.lottery.module.activity.constants.TaskTypeEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class ActivityTaskParam implements Serializable {
    private static final long serialVersionUID = 1L;

    private TaskPeriodEnum taskPeriodEnum;

    private TaskTypeEnum taskTypeEnum;

    private TaskRewardEnum taskRewardEnum;

    private Set<GameCategoryEnum> gameCategoryEnumSet;

    private List<ActivityTaskConfigParam> activityTaskConfigParamList;

}
