package com.wd.lottery.module.activity.controller.consumer;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.QuerySortEnum;
import com.wd.lottery.common.util.MemberTokenInfoUtil;
import com.wd.lottery.module.activity.constants.TeamProxyLowerLevelUserSearchTypeEnum;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.param.AAActivityTeamProxyStatQueryParam;
import com.wd.lottery.module.activity.param.ActivityTeamProxyLowerLevelUserQueryParam;
import com.wd.lottery.module.activity.param.ActivityTeamProxyRecentAddLowerLevelUserParam;
import com.wd.lottery.module.activity.service.*;
import com.wd.lottery.module.activity.service.ActivityAATeamProxyBonusService;
import com.wd.lottery.module.activity.service.ActivityAATeamProxyLowerLevelMemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;

@RestController
@RequestMapping("${consumer-path}/${module-path.activity}/aateamProxy")
@Tag(name = "AA团队代理模块")
public class CActivityAATeamProxyMemberController {

    @Resource
    private ActivityAATeamProxyBonusService activityTeamProxyBonusService;
    @Resource
    private ActivityAATeamProxyLowerLevelMemberService activityTeamProxyLowerLevelMemberService;


    @Operation(summary = "查询会员团队代理首页信息")
    @GetMapping(value = "getHomePageData")
    public ApiResult<TeamProxyMemberHomePageDataDTO> getHomePageData() {
        return ApiResult.success(
                activityTeamProxyLowerLevelMemberService
                        .getHomePageData(MemberTokenInfoUtil.getMemberTokenInfo()));
    }

    @Operation(summary = "查询会员团队代理团队信息")
    @GetMapping(value = "getTeamProxyBonusStat")
    public ApiResult<AATeamProxyMemberStatDTO> getTeamProxyBonusStat(@RequestParam LocalDate startDate,
                                                                     @RequestParam LocalDate endDate) {

        AAActivityTeamProxyStatQueryParam queryParam = new AAActivityTeamProxyStatQueryParam();
        queryParam.setStartDate(startDate);
        queryParam.setEndDate(endDate);
        queryParam.setMemberId(MemberTokenInfoUtil.getMemberId());
        queryParam.setMerchantId(MemberTokenInfoUtil.getMerchantId());

        return ApiResult.success(
                activityTeamProxyLowerLevelMemberService
                        .getTeamProxyLowerLevelMemberStat(queryParam));
    }

    @Operation(summary = "查询会员团队代理下级用户统计数据")
    @GetMapping(value = "getTeamProxyLowerLevelUserStat")
    public ApiResult<TeamProxyRecentAddLowerLevelUserStatDTO> getTeamProxyLowerLevelUserStat(@RequestParam(required = false) LocalDateTime startTime,
                                                                                             @RequestParam(required = false) LocalDateTime endTime
    ) {
        ActivityTeamProxyRecentAddLowerLevelUserParam param = new ActivityTeamProxyRecentAddLowerLevelUserParam();
        param.setHighMemberId(MemberTokenInfoUtil.getMemberId());
        param.setMerchantId(MemberTokenInfoUtil.getMerchantId());
        param.setStartTime(startTime);
        param.setEndTime(endTime);
        return ApiResult.success(activityTeamProxyLowerLevelMemberService.getTeamProxyLowerLevelUserStat(param));
    }

    @Operation(summary = "查询会员团队代理下级分页信息")
    @GetMapping(value = "getTeamProxyLowerLevelUserBonusPage")
    public ApiResult<Page<TeamProxyLowerLevelMemberBonusDTO>> getTeamProxyLowerLevelUserBonusPage(@RequestParam LocalDateTime startTime,
                                                                                                  @RequestParam LocalDateTime endTime,
                                                                                                  @RequestParam Integer level,
                                                                                                  @RequestParam(required = false) Long memberId,
                                                                                                  @RequestParam(required = false) TeamProxyLowerLevelUserSearchTypeEnum searchTypeEnum,
                                                                                                  @RequestParam(required = false) QuerySortEnum sortEnum,
                                                                                                  @RequestParam Long current,
                                                                                                  @RequestParam Long size
    ) {
        ActivityTeamProxyLowerLevelUserQueryParam param = new ActivityTeamProxyLowerLevelUserQueryParam();
        param.setHighMemberId(MemberTokenInfoUtil.getMemberId());
        param.setMerchantId(MemberTokenInfoUtil.getMerchantId());
        param.setLevel(level);
        param.setStartTime(startTime);
        param.setEndTime(endTime);
        param.setMemberId(memberId);
        param.setCurrent(current);
        param.setSize(size);

        if (searchTypeEnum != null)
            param.setSearchTypeEnum(searchTypeEnum);

        if (sortEnum != null)
            param.setSortEnum(sortEnum);

        return ApiResult.success(activityTeamProxyLowerLevelMemberService.getTeamProxyLowerLevelUserByPage(param));
    }


    @Operation(summary = "查询会员团队代理近期新增用户")
    @GetMapping(value = "getTeamProxyLowerLevelUserPage")
    public ApiResult<Page<TeamProxyRecentAddLowerLevelUserDTO>> getTeamProxyLowerLevelUserPage(@RequestParam LocalDateTime startTime,
                                                                                               @RequestParam LocalDateTime endTime,
                                                                                               @RequestParam(required = false) Long memberId,
                                                                                               @RequestParam Integer level,
                                                                                               @RequestParam Long current,
                                                                                               @RequestParam Long size
    ) {
        ActivityTeamProxyLowerLevelUserQueryParam param = new ActivityTeamProxyLowerLevelUserQueryParam();
        param.setHighMemberId(MemberTokenInfoUtil.getMemberId());
        param.setMemberId(memberId);
        param.setMerchantId(MemberTokenInfoUtil.getMerchantId());
        param.setCurrent(current);
        param.setSize(size);
        param.setStartTime(startTime);
        param.setEndTime(endTime);
        param.setLevel(level);

        return ApiResult.success(activityTeamProxyLowerLevelMemberService.getTeamProxyRecentAddLowerLevelUserByPage(param));
    }
}
