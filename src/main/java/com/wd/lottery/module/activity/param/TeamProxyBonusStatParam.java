package com.wd.lottery.module.activity.param;

import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class TeamProxyBonusStatParam {

    @Schema(hidden = true)
    private CurrencyEnum currencyEnum;

    @Schema(hidden = true)
    private Long merchantId;

    @Schema(hidden = true)
    private Long memberId;

    private Integer level; //对应下级层级

    private LocalDate periods; // yyyyMMdd 20220111

    private GameCategoryEnum gameCategoryEnum;

}
