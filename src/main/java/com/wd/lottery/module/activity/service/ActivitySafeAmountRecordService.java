package com.wd.lottery.module.activity.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.module.activity.dto.*;
import com.wd.lottery.module.activity.dto.ActivitySafeAmountRecordDTO;
import com.wd.lottery.module.activity.dto.ActivitySafeAmountRecordSummaryDTO;
import com.wd.lottery.module.activity.dto.ActivitySafeMonthRecordDTO;
import com.wd.lottery.module.activity.entity.ActivitySafeAmountRecord;
import com.wd.lottery.module.activity.param.ActivitySafeRecordQueryParam;

import java.util.List;

/**
 * <p>
 * 會員保险箱錢包 服务类
 * </p>
 *
 */
public interface ActivitySafeAmountRecordService extends IService<ActivitySafeAmountRecord> {

    List<ActivitySafeAmountRecordDTO> getLatestRecord(Long memberId, Long merchantId);

    ActivitySafeMonthRecordDTO getHistoryRecord(Long memberId, Long merchantId, String yearMonth);

    Page<ActivitySafeAmountRecordDTO> bGetPage(ActivitySafeRecordQueryParam param);

    ActivitySafeAmountRecordSummaryDTO bGetPageSummary(ActivitySafeRecordQueryParam param);


    ApiResult bExport(ActivitySafeRecordQueryParam activitySafeRecordQueryParam);
}
