package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.annotation.MasterOnly;
import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.CommonUtil;
import com.wd.lottery.common.util.Md5Util;
import com.wd.lottery.common.util.RedPacketUtil;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.dto.InviteRouletteLotteryProgressDTO;
import com.wd.lottery.module.activity.dto.InviteRouletteLotteryRecordDTO;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.entity.ActivityInviteRouletteProgress;
import com.wd.lottery.module.activity.entity.ActivityInviteRouletteRecord;
import com.wd.lottery.module.activity.entity.ActivityRecord;
import com.wd.lottery.module.activity.mapper.ActivityInviteRouletteProgressMapper;
import com.wd.lottery.module.activity.mapper.ActivityInviteRouletteRecordMapper;
import com.wd.lottery.module.activity.param.ActivityParam;
import com.wd.lottery.module.activity.param.ActivityRewardParam;
import com.wd.lottery.module.activity.param.inviteroulette.ActivityInviteRouletteConfigParam;
import com.wd.lottery.module.activity.param.inviteroulette.ActivityInviteRouletteQueryParam;
import com.wd.lottery.module.activity.param.inviteroulette.ActivityInviteRouletteRecordParam;
import com.wd.lottery.module.activity.service.ActivityInviteRouletteService;
import com.wd.lottery.module.activity.service.ActivityRecordService;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.common.config.ExportExcelConfig;
import com.wd.lottery.module.common.constants.ExcelExportParamEnum;
import com.wd.lottery.module.common.constants.ExportTypeEnum;
import com.wd.lottery.module.common.export.ActivityInviteRouletteRecordExportExcelStrategy;
import com.wd.lottery.module.common.param.AsyncExportParam;
import com.wd.lottery.module.common.service.ExportExcelService;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.service.MemberService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityInviteRouletteServiceImpl implements ActivityInviteRouletteService {

    @Resource
    private ActivityService activityService;

    @Resource
    private ActivityInviteRouletteProgressMapper activityInviteRouletteProgressMapper;

    @Resource
    private ActivityInviteRouletteRecordMapper recordMapper;

    @Resource
    private ExportExcelService exportExcelService;

    @Resource
    private ActivityRecordService activityRecordService;

    @Resource
    private ExportExcelConfig exportExcelConfig;

    @Resource
    private MemberService memberService;

    @Override
    public void checkParam(ActivityParam activityParam) {
        Assert.notNull(activityParam.getStartTime(),"邀请转盘活动开始时间不能为空");
        Assert.notNull(activityParam.getEndTime(),"邀请转盘活动结束时间不能为空");
        Assert.notNull(activityParam.getBannerImg(),"banner图片不能为空");
        ActivityInviteRouletteConfigParam param = activityParam.getActivityParam(ActivityInviteRouletteConfigParam.class);
        Assert.notNull(param, "configJson not null");
        Assert.notNull(param.getInitialBonusMinRatio(),"首次参加活动奖金范围百分比最小值不能为空");
        Assert.notNull(param.getInitialBonusMaxRatio(),"首次参加活动奖金范围百分比最大值不能为空");
        Assert.notNull(param.getFirstDrawBonusMinRatio(),"第一次抽奖奖金范围百分比最小值不能为空");
        Assert.notNull(param.getFirstDrawBonusMaxRatio(),"第一次抽奖奖金范围百分比最大值不能为空");
        Assert.notNull(param.getSecondDrawBonusMinRatio(),"第二次抽奖奖金范围百分比最小值不能为空");
        Assert.notNull(param.getSecondDrawBonusMaxRatio(),"第二次抽奖奖金范围百分比最大值不能为空");
        Assert.notNull(param.getThirdDrawBonusMinRatio(),"第三次抽奖奖金范围百分比最小值不能为空");
        Assert.notNull(param.getThirdDrawBonusMaxRatio(),"第三次抽奖奖金范围百分比最大值不能为空");
        Assert.notNull(param.getInvitationRewardsBonusMinRatio(),"邀请奖励奖金范围百分比最小值不能为空");
        Assert.notNull(param.getInvitationRewardsBonusMaxRatio(),"邀请奖励奖金范围百分比最大值不能为空");
        Assert.notNull(param.getFirstChargeBonusMinRatio(),"首充邀请奖励奖金范围百分比最小值不能为空");
        Assert.notNull(param.getFirstChargeBonusMaxRatio(),"首充邀请奖励奖金范围百分比最大值不能为空");
        Assert.notNull(param.getInviteRewardIpLimit(), "邀请奖励ip限制不能为空");
        Assert.notNull(param.getInviteRewardNumLimit(), "邀请奖励次数限制不能为空");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ActivityRecord> getRewardList(ActivityRewardParam activityRewardParam) {
        Activity inviteRouletteActivity = checkConfig(activityRewardParam);
        //判断是首次领奖一学是活动期间领奖
        ActivityInviteRouletteProgress activityInviteRouletteProgress = checkIsFirst(activityRewardParam.getMemberTokenInfoDTO());
        ActivityInviteRouletteConfigParam configParam = inviteRouletteActivity.activityParamToBean(ActivityInviteRouletteConfigParam.class);
        ActivityInviteRouletteRecord activityInviteRouletteRecord = null;
        if (Objects.isNull(activityInviteRouletteProgress)) {
            activityInviteRouletteRecord = firstLottery(activityRewardParam, configParam, inviteRouletteActivity);
        } else {
            activityInviteRouletteRecord = notFirstLottery(activityRewardParam, activityInviteRouletteProgress, configParam, inviteRouletteActivity);

        }
        ActivityRecord record = new ActivityRecord();
        record.setRecordJson(JSONUtil.toJsonStr(activityInviteRouletteRecord));
        return Collections.singletonList(record);
    }

    private ActivityInviteRouletteRecord firstLottery(ActivityRewardParam activityRewardParam, ActivityInviteRouletteConfigParam configParam, Activity inviteRouletteActivity) {
        ActivityInviteRouletteRecord activityInviteRouletteRecord;
        //首次领奖
        long randomMoney = RedPacketUtil.getRandomMoney(CommonUtil.multiplyDoubleAndLong(configParam.getInitialBonusMinRatio(), configParam.getWithdrawalConditions()),
                CommonUtil.multiplyDoubleAndLong(configParam.getInitialBonusMaxRatio(), configParam.getWithdrawalConditions()));
        log.info("randomMoney...{},minMoney...{},maxMoney...{},conditions..{}",randomMoney,configParam.getInitialBonusMinRatio(),configParam.getInitialBonusMaxRatio(), configParam.getWithdrawalConditions());
        //保存进度记录
        ActivityInviteRouletteProgress progress = saveProgress(activityRewardParam.getMemberTokenInfoDTO(), randomMoney, inviteRouletteActivity, configParam);

        //保存抽奖流水记录
        activityInviteRouletteRecord = saveLotteryRecord(activityRewardParam, progress, InviteRouletteRewardTypeEnum.FIRST_REWARD, randomMoney, inviteRouletteActivity);
        return activityInviteRouletteRecord;
    }

    private ActivityInviteRouletteRecord notFirstLottery(ActivityRewardParam activityRewardParam, ActivityInviteRouletteProgress activityInviteRouletteProgress, ActivityInviteRouletteConfigParam configParam, Activity inviteRouletteActivity) {
        ActivityInviteRouletteRecord activityInviteRouletteRecord;
        //活动中领奖
        //判断用户当天是否能抽奖
        List<ActivityInviteRouletteRecord> activityInviteRouletteRecords = checkCanLottery(activityRewardParam, activityInviteRouletteProgress, configParam);
        //计算获奖金额
        Long bonus = calculateLotteryBonus(activityInviteRouletteRecords.size() + 1, configParam, activityInviteRouletteProgress);
        //保存抽奖流水记录
        activityInviteRouletteRecord = saveLotteryRecord(activityRewardParam, activityInviteRouletteProgress, InviteRouletteRewardTypeEnum.LOTTERY_REWARD, bonus, inviteRouletteActivity);
        //更新领奖进度
        ActivityInviteRouletteProgress newProgress = new ActivityInviteRouletteProgress();
        newProgress.setTotalWinAmount(activityInviteRouletteProgress.getTotalWinAmount() + bonus);
        newProgress.setUpdateTime(LocalDateTime.now());
        if (activityInviteRouletteProgress.getTotalWinAmount() + bonus >= configParam.getWithdrawalConditions()) {
            newProgress.setProgressEnum(InviteRouletteRewardProgressEnum.WAIT_RECEIVED);
        } else {
            newProgress.setProgressEnum(InviteRouletteRewardProgressEnum.NOT_MEET_CONDITION);
        }
        int update = activityInviteRouletteProgressMapper.update(newProgress, new LambdaUpdateWrapper<ActivityInviteRouletteProgress>()
                .eq(ActivityInviteRouletteProgress::getId, activityInviteRouletteProgress.getId())
                .eq(ActivityInviteRouletteProgress::getTotalWinAmount, activityInviteRouletteProgress.getTotalWinAmount()));
        if (update <= 0) {
            throw new ApiException(CommonCode.ACTIVITY_INVITE_ROULETTE_BUSY);
        }
        return activityInviteRouletteRecord;
    }

    private Long calculateLotteryBonus(Integer lotteryNum,ActivityInviteRouletteConfigParam configParam,ActivityInviteRouletteProgress activityInviteRouletteProgress) {
        InviteRouletteLotteryNumEnum enumByCode = InviteRouletteLotteryNumEnum.getEnumByCode(lotteryNum);
        if (Objects.isNull(enumByCode)) {
            throw new ApiException(CommonCode.ACTIVITY_INVITE_ROULETTE_LOTTERY_TIMES_OVER_LIMIT);
        }
        return getBonus(configParam, enumByCode,activityInviteRouletteProgress);
    }


    private Long getBonus(ActivityInviteRouletteConfigParam configParam, InviteRouletteLotteryNumEnum enumByCode,ActivityInviteRouletteProgress activityInviteRouletteProgress) {
        Long bonus;
        if (ObjectUtil.equals(enumByCode, InviteRouletteLotteryNumEnum.FIRST)) {
            bonus = RedPacketUtil.getRandomMoney(CommonUtil.multiplyDoubleAndLong(configParam.getFirstDrawBonusMinRatio(), configParam.getWithdrawalConditions()),
                    CommonUtil.multiplyDoubleAndLong(configParam.getFirstDrawBonusMaxRatio(), configParam.getWithdrawalConditions()));
        } else if (ObjectUtil.equals(enumByCode,InviteRouletteLotteryNumEnum.SECOND)) {
            bonus = RedPacketUtil.getRandomMoney(CommonUtil.multiplyDoubleAndLong(configParam.getSecondDrawBonusMinRatio(), configParam.getWithdrawalConditions()),
                    CommonUtil.multiplyDoubleAndLong(configParam.getSecondDrawBonusMaxRatio(), configParam.getWithdrawalConditions()));
        } else if (ObjectUtil.equals(enumByCode,InviteRouletteLotteryNumEnum.THIRD)) {
            bonus = RedPacketUtil.getRandomMoney(CommonUtil.multiplyDoubleAndLong(configParam.getThirdDrawBonusMinRatio(), configParam.getWithdrawalConditions()),
                    CommonUtil.multiplyDoubleAndLong(configParam.getThirdDrawBonusMaxRatio(), configParam.getWithdrawalConditions()));
        } else {
            bonus = RedPacketUtil.getRandomMoney(CommonUtil.multiplyDoubleAndLong(configParam.getThirdDrawBonusMinRatio(), configParam.getWithdrawalConditions()),
                    CommonUtil.multiplyDoubleAndLong(configParam.getThirdDrawBonusMaxRatio(), configParam.getWithdrawalConditions()));
        }
        if ((bonus + activityInviteRouletteProgress.getTotalWinAmount()) > configParam.getWithdrawalConditions()) {
            //如果 新获得奖金加上已中奖总金额大于限制 提现条件,那么此次奖金就是提现条件-已中奖总金额
            bonus = configParam.getWithdrawalConditions() - activityInviteRouletteProgress.getTotalWinAmount();
        }
        return bonus;
    }

    private List<ActivityInviteRouletteRecord> checkCanLottery(ActivityRewardParam activityRewardParam, ActivityInviteRouletteProgress activityInviteRouletteProgress,ActivityInviteRouletteConfigParam configParam) {
        //先判断当天是否已经抽过奖
        ActivityInviteRouletteRecord activityInviteRouletteRecord = recordMapper.selectOne(new LambdaQueryWrapper<ActivityInviteRouletteRecord>()
                .eq(ActivityInviteRouletteRecord::getMerchantId, activityRewardParam.getMemberTokenInfoDTO().getMerchantId())
                .eq(ActivityInviteRouletteRecord::getMemberId, activityRewardParam.getMemberTokenInfoDTO().getId())
                .eq(ActivityInviteRouletteRecord::getTypeEnum, InviteRouletteRewardTypeEnum.LOTTERY_REWARD)
                .eq(ActivityInviteRouletteRecord::getProgressId,activityInviteRouletteProgress.getId())
                .eq(ActivityInviteRouletteRecord::getReceiveDate, DateTime.now().toDateStr()));
        if (Objects.nonNull(activityInviteRouletteRecord)) {
            throw new ApiException(CommonCode.ACTIVITY_INVITE_ROULETTE_ALREADY_LOTTERY);
        }
        //activityInviteRouletteRecord为null,说明当天未抽奖,可以抽奖
        //判断已经抽过几次
        List<ActivityInviteRouletteRecord> activityInviteRouletteRecords = recordMapper.selectList(new LambdaQueryWrapper<ActivityInviteRouletteRecord>()
                .eq(ActivityInviteRouletteRecord::getMerchantId, activityRewardParam.getMemberTokenInfoDTO().getMerchantId())
                .eq(ActivityInviteRouletteRecord::getTypeEnum, InviteRouletteRewardTypeEnum.LOTTERY_REWARD)
                .eq(ActivityInviteRouletteRecord::getMemberId, activityRewardParam.getMemberTokenInfoDTO().getId())
                .eq(ActivityInviteRouletteRecord::getProgressId,activityInviteRouletteProgress.getId()));
        //判断抽奖次数是否大于活动周期
        if (activityInviteRouletteRecords.size() >= configParam.getActivityCycle()) {
            throw new ApiException(CommonCode.ACTIVITY_INVITE_ROULETTE_LOTTERY_TIMES_OVER_LIMIT);
        }
        return activityInviteRouletteRecords;
    }

    private ActivityInviteRouletteRecord saveLotteryRecord(ActivityRewardParam activityRewardParam, ActivityInviteRouletteProgress progress,
                                                           InviteRouletteRewardTypeEnum inviteRouletteRewardTypeEnum, Long randomMoney, Activity inviteRouletteActivity) {
        ActivityInviteRouletteRecord record = new ActivityInviteRouletteRecord();
        record.setMerchantId(activityRewardParam.getMemberTokenInfoDTO().getMerchantId());
        record.setMemberId(activityRewardParam.getMemberTokenInfoDTO().getId());
        record.setProgressId(progress.getId());
        record.setTypeEnum(inviteRouletteRewardTypeEnum);

        buildHelpCount(activityRewardParam, progress, inviteRouletteRewardTypeEnum, record);

        record.setRegisterIp(activityRewardParam.getRequestIp());
        if (Objects.nonNull(randomMoney)) {
            record.setAmount(randomMoney);
        } else {
            record.setAmount(progress.getTotalWinAmount());
        }
        record.setRouletteId(inviteRouletteActivity.getId());
        record.setCurrencyEnum(activityRewardParam.getMemberTokenInfoDTO().getCurrencyEnum());
        record.setReceiveDate(DateTime.now().toDateStr());
        record.setCreateTime(LocalDateTime.now());
        record.setMemberName(activityRewardParam.getMemberTokenInfoDTO().getMemberName());
        recordMapper.insert(record);
        return record;
    }

    private void buildHelpCount(ActivityRewardParam activityRewardParam, ActivityInviteRouletteProgress progress, InviteRouletteRewardTypeEnum inviteRouletteRewardTypeEnum, ActivityInviteRouletteRecord record) {
        if (Objects.equals(inviteRouletteRewardTypeEnum, InviteRouletteRewardTypeEnum.WITHDRAW_REWARD)) {
            //查询此活动周期内的有效助力,查询条件为活动周期id,邀请奖励金额>0,
            Long validCount = recordMapper.selectCount(new LambdaQueryWrapper<ActivityInviteRouletteRecord>()
                    .eq(ActivityInviteRouletteRecord::getProgressId, progress.getId())
                    .eq(ActivityInviteRouletteRecord::getTypeEnum, InviteRouletteRewardTypeEnum.INVITE_REGISTER_REWARD)
                    .eq(ActivityInviteRouletteRecord::getMemberId, activityRewardParam.getMemberTokenInfoDTO().getId())
                    .gt(ActivityInviteRouletteRecord::getAmount, 0));
            record.setValidInviteNum(validCount);
            //查询此活动周期内的无效助力,查询条件为活动周期id,邀请奖励金额=0
            Long invalidCount = recordMapper.selectCount(new LambdaQueryWrapper<ActivityInviteRouletteRecord>()
                    .eq(ActivityInviteRouletteRecord::getProgressId, progress.getId())
                    .eq(ActivityInviteRouletteRecord::getTypeEnum, InviteRouletteRewardTypeEnum.INVITE_REGISTER_REWARD)
                    .eq(ActivityInviteRouletteRecord::getMemberId, activityRewardParam.getMemberTokenInfoDTO().getId())
                    .eq(ActivityInviteRouletteRecord::getAmount, 0));
            record.setInvalidInviteNum(invalidCount);
            record.setCycleStartTime(progress.getStartTime());
        }
    }

    private ActivityInviteRouletteProgress saveProgress(MemberTokenInfoDTO memberTokenInfoDTO, long randomMoney, Activity inviteRouletteActivity,ActivityInviteRouletteConfigParam configParam) {
        ActivityInviteRouletteProgress progress = new ActivityInviteRouletteProgress();
        progress.setMerchantId(memberTokenInfoDTO.getMerchantId());
        progress.setMemberId(memberTokenInfoDTO.getId());
        progress.setRouletteId(inviteRouletteActivity.getId());
        progress.setCurrencyEnum(memberTokenInfoDTO.getCurrencyEnum());
        progress.setTotalWinAmount(randomMoney);
        if (randomMoney >= configParam.getWithdrawalConditions()) {
            progress.setTotalWinAmount(configParam.getWithdrawalConditions());
            progress.setProgressEnum(InviteRouletteRewardProgressEnum.WAIT_RECEIVED);
        } else {
            progress.setProgressEnum(InviteRouletteRewardProgressEnum.NOT_MEET_CONDITION);
        }
        LocalDateTime now = LocalDateTime.now();
        progress.setStartTime(now);
        progress.setEndTime(now.plusDays(configParam.getActivityCycle()));
        progress.setCreateTime(now);
        activityInviteRouletteProgressMapper.insert(progress);
        return progress;
    }

    private ActivityInviteRouletteProgress checkIsFirst(MemberTokenInfoDTO memberTokenInfoDTO) {
        return activityInviteRouletteProgressMapper.selectOne(new LambdaQueryWrapper<ActivityInviteRouletteProgress>()
                .eq(ActivityInviteRouletteProgress::getMerchantId, memberTokenInfoDTO.getMerchantId())
                .eq(ActivityInviteRouletteProgress::getMemberId, memberTokenInfoDTO.getId())
                .le(ActivityInviteRouletteProgress::getStartTime, LocalDateTime.now())
                .ge(ActivityInviteRouletteProgress::getEndTime, LocalDateTime.now()));
    }

    private Activity checkInviteRouletteActivity(ActivityRewardParam activityRewardParam) {
        return activityService.getEnableOne(activityRewardParam.getMemberTokenInfoDTO().getMerchantId(), activityRewardParam.getMemberTokenInfoDTO().getCurrencyEnum(), ActivityTypeEnum.INVITE_ROULETTE);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivityRecord receiveReward(ActivityRewardParam activityRewardParam) {
        Activity inviteRouletteActivity = checkConfig(activityRewardParam);
        ActivityInviteRouletteConfigParam configParam = inviteRouletteActivity.activityParamToBean(ActivityInviteRouletteConfigParam.class);
        ActivityInviteRouletteProgress oldProgress = checkCanReceived(activityRewardParam.getMemberTokenInfoDTO(), configParam);
        //提现成功,更新进度记录状态
        ActivityInviteRouletteProgress newProgress = new ActivityInviteRouletteProgress();
        newProgress.setProgressEnum(InviteRouletteRewardProgressEnum.RECEIVED);
        newProgress.setUpdateTime(LocalDateTime.now());
        //根据规则设定判断是否重置活动周期
        tryResetLotteryCycle(configParam, oldProgress,newProgress);
        int update = activityInviteRouletteProgressMapper.update(newProgress, new LambdaUpdateWrapper<ActivityInviteRouletteProgress>()
                .eq(ActivityInviteRouletteProgress::getId, oldProgress.getId()).eq(ActivityInviteRouletteProgress::getProgressEnum, InviteRouletteRewardProgressEnum.WAIT_RECEIVED));
        if(update != 1){
            throw new ApiException(CommonCode.ACTIVITY_INVITE_ROULETTE_ALREADY_WITHDRAW);
        }
        //保存抽奖流水记录
        ActivityInviteRouletteRecord activityInviteRouletteRecord = saveLotteryRecord(activityRewardParam, oldProgress, InviteRouletteRewardTypeEnum.WITHDRAW_REWARD, oldProgress.getTotalWinAmount(), inviteRouletteActivity);
        //保存活动领奖记录并发放奖金到用户钱包
        ActivityRecord record = buildActivityRecord(inviteRouletteActivity, activityRewardParam, oldProgress.getTotalWinAmount(),activityInviteRouletteRecord);
        activityRecordService.saveAndOperateWallet(record);
        return record;
    }

    private ActivityRecord buildActivityRecord(Activity inviteRouletteActivity,ActivityRewardParam activityRewardParam,Long totalWinAmount,ActivityInviteRouletteRecord activityInviteRouletteRecord) {
        ActivityRecord activityRecordSave = new ActivityRecord();
        activityRecordSave.setActivityId(inviteRouletteActivity.getId());
        activityRecordSave.setAmount(totalWinAmount);
        activityRecordSave.setActivityTypeEnum(inviteRouletteActivity.getActivityTypeEnum());
        activityRecordSave.setSubActivityTypeEnum(SubActivityTypeEnum.INVITE_ROULETTE);
        LocalDateTime now = LocalDateTime.now();
        activityRecordSave.setCreateTime(now);
        activityRecordSave.setUpdateTime(now);
        activityRecordSave.setMarkDate(now.toLocalDate());
        activityRecordSave.setMemberId(activityRewardParam.getMemberTokenInfoDTO().getId());
        activityRecordSave.setMemberName(activityRewardParam.getMemberTokenInfoDTO().getMemberName());
        activityRecordSave.setMerchantId(inviteRouletteActivity.getMerchantId());
        activityRecordSave.setCurrencyEnum(inviteRouletteActivity.getCurrencyEnum());
        activityRecordSave.setActivityStatusEnum(ActivityStatusEnum.RECEIVED);
        activityRecordSave.setRequestIp(activityRewardParam.getRequestIp());
        activityRecordSave.setUid(this.getUid(activityRecordSave));
        activityRecordSave.setChannelId(activityRewardParam.getMemberTokenInfoDTO().getChannelId());
        activityRecordSave.setIsDirect(activityRewardParam.getMemberTokenInfoDTO().getIsDirect());
        ActivityInviteRouletteRecordParam activityInviteRouletteRecordParam = new ActivityInviteRouletteRecordParam();
        BeanUtils.copyProperties(activityInviteRouletteRecordParam,activityInviteRouletteRecord);
        activityRecordSave.setRecordJson(JSONUtil.toJsonStr(activityInviteRouletteRecordParam));
        return activityRecordSave;
    }


    private void tryResetLotteryCycle(ActivityInviteRouletteConfigParam configParam, ActivityInviteRouletteProgress oldProgress, ActivityInviteRouletteProgress newProgress) {
        if (ObjectUtil.equals(configParam.getResetCycleType(), InviteRouletteResetCycleEnum.RESET_CYCLE.getCode())) {
            //规则设置为立即重置,重置活动结束时间,提前结束活动
            newProgress.setEndTime(LocalDateTime.now());
        } else if (ObjectUtil.equals(configParam.getResetCycleType(),InviteRouletteResetCycleEnum.RESET_CYCLE_NEXT_DAY.getCode()) ) {
            //隔天重置 判断隔天是否在活动周期内
            if (oldProgress.getEndTime().isAfter(LocalDateTime.now().plusDays(1))) {
                //判断活动的自然结束周期是否是隔天后 ,在这之前就不用改
                newProgress.setEndTime(LocalDateTime.now().plusDays(1));
            }
        }
    }

    private ActivityInviteRouletteProgress checkCanReceived(MemberTokenInfoDTO memberTokenInfoDTO,ActivityInviteRouletteConfigParam configParam) {
        ActivityInviteRouletteProgress activityInviteRouletteProgress = activityInviteRouletteProgressMapper.selectOne(new LambdaQueryWrapper<ActivityInviteRouletteProgress>()
                .eq(ActivityInviteRouletteProgress::getMerchantId, memberTokenInfoDTO.getMerchantId())
                .eq(ActivityInviteRouletteProgress::getMemberId, memberTokenInfoDTO.getId())
                .eq(ActivityInviteRouletteProgress::getProgressEnum, InviteRouletteRewardProgressEnum.WAIT_RECEIVED)
                .le(ActivityInviteRouletteProgress::getStartTime, LocalDateTime.now()).ge(ActivityInviteRouletteProgress::getEndTime, new Date()));
        if (Objects.isNull(activityInviteRouletteProgress)) {
            throw new ApiException(CommonCode.ACTIVITY_INVITE_ROULETTE_NOT_PARTICIPATE);
        }
        if (activityInviteRouletteProgress.getTotalWinAmount() < configParam.getWithdrawalConditions()) {
            throw new ApiException(CommonCode.ACTIVITY_INVITE_ROULETTE_NOT_WITHDRAW_CONDITION);
        }
        if (activityInviteRouletteProgress.getProgressEnum() == InviteRouletteRewardProgressEnum.RECEIVED) {
            throw new ApiException(CommonCode.ACTIVITY_INVITE_ROULETTE_ALREADY_WITHDRAW);
        }
        return activityInviteRouletteProgress;
    }

    private Activity checkConfig(ActivityRewardParam activityRewardParam) {

        Activity inviteRouletteActivity = checkInviteRouletteActivity(activityRewardParam);
        if (Objects.isNull(inviteRouletteActivity)) {
            throw new ApiException(CommonCode.ACTIVITY_NOT_START);
        }
        return inviteRouletteActivity;
    }

    @Override
    public ActivityTypeEnum getActivityTypeEnum() {
        return ActivityTypeEnum.INVITE_ROULETTE;
    }

    @Override
    public String getUid(ActivityRecord activityRecord, Object... others) {
        Assert.notNull(activityRecord.getMerchantId());
        Assert.notNull(activityRecord.getActivityTypeEnum());
        Assert.notNull(activityRecord.getSubActivityTypeEnum());
        Assert.notNull(activityRecord.getActivityId());
        Assert.notNull(activityRecord.getMarkDate());
        String uidStr = activityRecord.getMemberId().toString() + activityRecord.getActivityTypeEnum() + activityRecord.getSubActivityTypeEnum() + activityRecord.getActivityId() + activityRecord.getCreateTime();
        return Md5Util.getMd5HexLowerCase(uidStr);
    }

    @Override
    public Boolean getUniqueActivity() {
        return true;
    }

    @Override
    public void extraOperateByUpdate(ActivityParam param, Activity beforeUpdated) {
        Activity oldActivity = activityService.lambdaQuery()
                .eq(Activity::getMerchantId, param.getMerchantId()).eq(Activity::getActivityTypeEnum, param.getActivityTypeEnum())
                .eq(Activity::getCurrencyEnum, param.getCurrencyEnum())
                .eq(Activity::getDelEnum, BooleanEnum.FALSE)
                .one();
        if (Objects.isNull(oldActivity)) {
            return;
        }
        ActivityInviteRouletteConfigParam oldActivityParam = oldActivity.activityParamToBean(ActivityInviteRouletteConfigParam.class);
        ActivityInviteRouletteConfigParam newActivityParam = param.getActivityParam(ActivityInviteRouletteConfigParam.class);
        if (ObjectUtil.notEqual(oldActivityParam.getWithdrawalConditions(),newActivityParam.getWithdrawalConditions())) {
            //变更了提现条件,需要将正在进行中的轮盘活动提前停止
            List<ActivityInviteRouletteProgress> activityInviteRouletteProgresses = activityInviteRouletteProgressMapper.selectList(new LambdaQueryWrapper<ActivityInviteRouletteProgress>()
                    .eq(ActivityInviteRouletteProgress::getMerchantId, param.getMerchantId())
                    .eq(ActivityInviteRouletteProgress::getProgressEnum, InviteRouletteRewardProgressEnum.NOT_MEET_CONDITION)
                    .le(ActivityInviteRouletteProgress::getStartTime, LocalDateTime.now())
                    .ge(ActivityInviteRouletteProgress::getEndTime, LocalDateTime.now()));
            if (CollectionUtils.isEmpty(activityInviteRouletteProgresses)) {
                return;
            }
            Set<Long> processIds = activityInviteRouletteProgresses.stream().filter(Objects::nonNull).map(ActivityInviteRouletteProgress::getId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(processIds)) {
                return;
            }
            ActivityInviteRouletteProgress record = new ActivityInviteRouletteProgress();
            record.setEndTime(LocalDateTime.now());
            record.setUpdateTime(LocalDateTime.now());
            int update = activityInviteRouletteProgressMapper.update(record, new LambdaUpdateWrapper<ActivityInviteRouletteProgress>()
                    .in(ActivityInviteRouletteProgress::getId, processIds));
            if (update <= 0) {
                throw new ApiException(CommonCode.ACTIVITY_INVITE_ROULETTE_BUSY);
            }
        }
    }

    @Override
    @MasterOnly
    public InviteRouletteLotteryProgressDTO getLotteryProgress(MemberTokenInfoDTO memberTokenInfo) {
        Activity inviteRouletteActivity = activityService.getEnableOne(memberTokenInfo.getMerchantId(), memberTokenInfo.getCurrencyEnum(), ActivityTypeEnum.INVITE_ROULETTE);
        if(Objects.isNull(inviteRouletteActivity)) {
            throw new ApiException(CommonCode.ACTIVITY_NOT_START);
        }
        ActivityInviteRouletteConfigParam configParam = inviteRouletteActivity.activityParamToBean(ActivityInviteRouletteConfigParam.class);
        InviteRouletteLotteryProgressDTO dto = new InviteRouletteLotteryProgressDTO();
        dto.setWithdrawalConditions(configParam.getWithdrawalConditions());
        ActivityInviteRouletteProgress progress = activityInviteRouletteProgressMapper.selectOne(new LambdaQueryWrapper<ActivityInviteRouletteProgress>()
                .eq(ActivityInviteRouletteProgress::getMerchantId, memberTokenInfo.getMerchantId())
                .eq(ActivityInviteRouletteProgress::getMemberId, memberTokenInfo.getId())
                .le(ActivityInviteRouletteProgress::getStartTime, LocalDateTime.now()).ge(ActivityInviteRouletteProgress::getEndTime, LocalDateTime.now()));
        buildActivityStatus(memberTokenInfo, progress, dto, configParam);
        return dto;
    }

    private void buildActivityStatus(MemberTokenInfoDTO memberTokenInfo, ActivityInviteRouletteProgress progress, InviteRouletteLotteryProgressDTO dto, ActivityInviteRouletteConfigParam configParam) {
        if (Objects.nonNull(progress)) {
            dto.setTotalWinAmount(progress.getTotalWinAmount());
            dto.setStartTime(progress.getStartTime());
            dto.setEndTime(progress.getEndTime());
            dto.setProgressEnum(progress.getProgressEnum());
            ActivityInviteRouletteRecord record = recordMapper.selectOne(new LambdaQueryWrapper<ActivityInviteRouletteRecord>()
                    .eq(ActivityInviteRouletteRecord::getMerchantId, memberTokenInfo.getMerchantId())
                    .eq(ActivityInviteRouletteRecord::getMemberId, memberTokenInfo.getId())
                    .eq(ActivityInviteRouletteRecord::getTypeEnum, InviteRouletteRewardTypeEnum.LOTTERY_REWARD)
                    .eq(ActivityInviteRouletteRecord::getProgressId, progress.getId())
                    .eq(ActivityInviteRouletteRecord::getReceiveDate, DateTime.now().toDateStr()));
            if (Objects.nonNull(record)) {
                dto.setCanLottery(BooleanEnum.FALSE);
            } else {
                List<ActivityInviteRouletteRecord> records = recordMapper.selectList(new LambdaQueryWrapper<ActivityInviteRouletteRecord>()
                        .eq(ActivityInviteRouletteRecord::getMerchantId, memberTokenInfo.getMerchantId())
                        .eq(ActivityInviteRouletteRecord::getTypeEnum, InviteRouletteRewardTypeEnum.LOTTERY_REWARD)
                        .eq(ActivityInviteRouletteRecord::getMemberId, memberTokenInfo.getId())
                        .eq(ActivityInviteRouletteRecord::getProgressId, progress.getId()));
                //判断pddBonusRollovers.size是否大于限制活动周期
                if (records.size() < configParam.getActivityCycle()) {
                    dto.setCanLottery(BooleanEnum.TRUE);
                } else {
                    dto.setCanLottery(BooleanEnum.FALSE);
                }
            }
        } else {
            dto.setTotalWinAmount(0L);
            dto.setCanLottery(BooleanEnum.TRUE);
        }
    }

    @Override
    public Page<InviteRouletteLotteryRecordDTO> queryLotteryRecord(ActivityInviteRouletteQueryParam param) {
        Page<ActivityInviteRouletteRecord> page = new Page<>(param.getCurrent(), param.getSize());
        IPage<ActivityInviteRouletteRecord> activityInviteRouletteRecordIPage = recordMapper.selectPage(page, new LambdaQueryWrapper<ActivityInviteRouletteRecord>()
                .eq(ActivityInviteRouletteRecord::getMerchantId, param.getMerchantId())
                .between(ActivityInviteRouletteRecord::getCreateTime, param.getStartTime(),param.getEndTime())
                .eq(Objects.nonNull(param.getTypeEnum()),ActivityInviteRouletteRecord::getTypeEnum, param.getTypeEnum())
                .eq(Objects.nonNull(param.getMemberId()) && ObjectUtil.equals(param.getType(),0), ActivityInviteRouletteRecord::getMemberId, param.getMemberId())
                .eq(Objects.nonNull(param.getMemberId()) && ObjectUtil.equals(param.getType(),1), ActivityInviteRouletteRecord::getBeInviteMemberId, param.getMemberId()));
        if (Objects.isNull(activityInviteRouletteRecordIPage) || CollectionUtils.isEmpty(activityInviteRouletteRecordIPage.getRecords())) {
            return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        }
        return new Page<InviteRouletteLotteryRecordDTO>(page.getCurrent(), page.getSize(), page.getTotal())
                .setRecords(activityInviteRouletteRecordIPage.getRecords().stream().map(e -> {
                    InviteRouletteLotteryRecordDTO dto = new InviteRouletteLotteryRecordDTO();
                    BeanUtils.copyProperties(e, dto);
                    return dto;
                }).collect(Collectors.toList()));

    }

    @Override
    public ApiResult exportLotteryRecord(ActivityInviteRouletteQueryParam param) {
        Map<String,Object> paramMap = Maps.newHashMap();
        buildExportParam(paramMap,param);
        AsyncExportParam asyncExportParam = new AsyncExportParam();
        asyncExportParam.setMerchantId(param.getMerchantId());
        asyncExportParam.setLanguage(param.getLanguage());
        asyncExportParam.setCreateBy(param.getCreateBy());
        asyncExportParam.setExportType(ExportTypeEnum.ACTIVITY_INVITE_ROULETTE_RECORD);
        asyncExportParam.setFileName(ExportTypeEnum.ACTIVITY_INVITE_ROULETTE_RECORD.getDesc() + "_" + DateUtil.today() + ".xlsx");

        log.info("exportParam..{},AsyncExportParam...{},map...{}", param, asyncExportParam, JSONUtil.toJsonStr(paramMap));
        ActivityInviteRouletteRecordExportExcelStrategy strategy =
                new ActivityInviteRouletteRecordExportExcelStrategy(ExcelExportParamEnum.ACTIVITY_INVITE_ROULETTE_RECORD.getMapperMethod(),
                        ExcelExportParamEnum.ACTIVITY_INVITE_ROULETTE_RECORD.getNextSearchField(), paramMap, exportExcelConfig.getLimit(), param.getLanguage());
        exportExcelService.asyncExportExcel(asyncExportParam, strategy, param.getIsAll());
        return ApiResult.success();
    }

    @Override
    public Page<InviteRouletteLotteryRecordDTO> getRecordPage(ActivityInviteRouletteQueryParam param) {
        Page<ActivityInviteRouletteRecord> page = new Page<>(param.getCurrent(), param.getSize());
        page.setSearchCount(false);
        //查询用户所处活动周期
        ActivityInviteRouletteProgress process = activityInviteRouletteProgressMapper.selectOne(new LambdaQueryWrapper<ActivityInviteRouletteProgress>()
                .eq(ActivityInviteRouletteProgress::getMerchantId, param.getMerchantId())
                .eq(ActivityInviteRouletteProgress::getMemberId, param.getMemberId())
                .le(ActivityInviteRouletteProgress::getStartTime, new Date()).ge(ActivityInviteRouletteProgress::getEndTime, new Date()));
        if (Objects.isNull(process)) {
            return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        }
        LambdaQueryWrapper<ActivityInviteRouletteRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ActivityInviteRouletteRecord::getBeInviteMemberId, ActivityInviteRouletteRecord::getBeInviteMemberName, ActivityInviteRouletteRecord::getTypeEnum,
                        ActivityInviteRouletteRecord::getMemberId, ActivityInviteRouletteRecord::getMemberName, ActivityInviteRouletteRecord::getAmount, ActivityInviteRouletteRecord::getCreateTime)
                .eq(ActivityInviteRouletteRecord::getMerchantId, param.getMerchantId()).in(ActivityInviteRouletteRecord::getTypeEnum, param.getTypeEnums())
                .eq(ActivityInviteRouletteRecord::getProgressId, process.getId())
                .eq(ObjectUtil.equals(param.getType(), 0), ActivityInviteRouletteRecord::getMemberId, param.getMemberId())
                .eq(ObjectUtil.equals(param.getType(), 1), ActivityInviteRouletteRecord::getBeInviteMemberId, param.getMemberId());

        Page<ActivityInviteRouletteRecord> recordPage = recordMapper.selectPage(page, wrapper);
        if (Objects.isNull(recordPage) || CollectionUtils.isEmpty(recordPage.getRecords())) {
            return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        }
        Set<Long> memberIds = page.getRecords().stream().filter(Objects::nonNull).map(ActivityInviteRouletteRecord::getMemberId).collect(Collectors.toSet());
        Set<Long> beInviteMemberIds = page.getRecords().stream().map(ActivityInviteRouletteRecord::getBeInviteMemberId).collect(Collectors.toSet());
        memberIds.addAll(beInviteMemberIds);
        Map<Long, Member> memberMap = getMemberMap(memberIds,param.getMerchantId());
        return new Page<InviteRouletteLotteryRecordDTO>(page.getCurrent(), page.getSize(), page.getTotal())
                .setRecords(recordPage.getRecords().stream().map(e -> {
                    InviteRouletteLotteryRecordDTO dto = new InviteRouletteLotteryRecordDTO();
                    BeanUtils.copyProperties(e, dto);
                    if (Objects.nonNull(e.getMemberId()) && Objects.nonNull(memberMap.get(e.getMemberId()))) {
                        dto.setNickName(memberMap.get(e.getMemberId()).getNickName());
                    }
                    if (Objects.nonNull(e.getBeInviteMemberId()) && Objects.nonNull(memberMap.get(e.getBeInviteMemberId()))) {
                        dto.setBeInviteNickName(CommonUtil.maskMiddleCharacters(memberMap.get(e.getBeInviteMemberId()).getNickName()));
                    }
                    return dto;
                }).collect(Collectors.toList()));
    }

    private Map<Long, Member> getMemberMap(Set<Long> memberIds, Long merchantId) {
        Map<Long, Member> beInviteMemberMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(memberIds)) {
            List<Member> memberByIds = memberService.getMemberByIds(new ArrayList<>(memberIds), merchantId);
            beInviteMemberMap = memberByIds.stream().collect(Collectors.toMap(Member::getId, Function.identity(), (k1, k2) -> k1));
        }
        return beInviteMemberMap;
    }

    private void buildExportParam(Map<String, Object> paramMap, ActivityInviteRouletteQueryParam param) {
        paramMap.put("merchantId",param.getMerchantId());
        if (ObjectUtil.equals(param.getType(),0)) {
            paramMap.put("memberId",param.getMemberId());
        }
        if (ObjectUtil.equals(param.getType(),1)) {
            paramMap.put("beInviteMemberId",param.getMemberId());
        }
        paramMap.put("startTime",param.getStartTime());
        paramMap.put("endTime",param.getEndTime());
        if (Objects.nonNull(param.getTypeEnum())) {
            paramMap.put("typeEnum",param.getTypeEnum());
        }
        if (param.getIsAll()) {
            //导出全部
            paramMap.put("limit", exportExcelConfig.getLimit());
            paramMap.put("id", 0);
        } else {
            //导出当前页
            paramMap.put("limit", Objects.nonNull(param.getSize()) ? param.getSize() : exportExcelConfig.getLimit());
        }
    }
}
