package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.dto.DateRangeDTO;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.LocalUtil;
import com.wd.lottery.common.util.Md5Util;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.entity.ActivityRecord;
import com.wd.lottery.module.activity.param.ActivityParam;
import com.wd.lottery.module.activity.param.ActivityRewardParam;
import com.wd.lottery.module.activity.param.task.ActivityTaskConfigParam;
import com.wd.lottery.module.activity.param.task.ActivityTaskParam;
import com.wd.lottery.module.activity.param.task.ActivityTaskRecordParam;
import com.wd.lottery.module.activity.service.ActivityRecordService;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.activity.service.ActivityTaskService;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.report.dto.ReportMemberDateDTO;
import com.wd.lottery.module.report.dto.ReportMemberGameDateDTO;
import com.wd.lottery.module.report.entity.ReportMemberDate;
import com.wd.lottery.module.report.entity.ReportMemberGameDate;
import com.wd.lottery.module.report.service.ReportMemberDateService;
import com.wd.lottery.module.report.service.ReportMemberGameDateService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class ActivityTaskServiceImpl implements ActivityTaskService {
    @Resource
    private ActivityService activityService;

    @Resource
    private ReportMemberDateService reportMemberDateService;

    @Resource
    private ReportMemberGameDateService reportMemberGameDateService;

    @Resource
    private ActivityRecordService activityRecordService;

    @Override
    public void checkParam(ActivityParam activityParam) {
        Assert.notNull(activityParam.getStartTime());
        Assert.notNull(activityParam.getEndTime());
        Assert.isFalse(activityParam.getEndTime().isBefore(activityParam.getStartTime()), "Activity endTime is before StartTime");
        ActivityTaskParam activityTaskParam = activityParam.getActivityParam(ActivityTaskParam.class);
        Assert.notNull(activityTaskParam.getTaskPeriodEnum());
        Assert.notNull(activityTaskParam.getTaskTypeEnum());
        if (activityTaskParam.getTaskTypeEnum() == TaskTypeEnum.VALID_BET) {
            Assert.notEmpty(activityTaskParam.getGameCategoryEnumSet());
        }
        Assert.notEmpty(activityTaskParam.getActivityTaskConfigParamList());
        List<ActivityTaskConfigParam> activityTaskConfigParamList = activityTaskParam.getActivityTaskConfigParamList();
        TaskRewardEnum taskRewardEnum = activityTaskParam.getTaskRewardEnum();
        for (ActivityTaskConfigParam activityTaskConfigParam : activityTaskConfigParamList) {
            Assert.notNull(activityTaskConfigParam.getRewardLevel());
            Assert.notNull(activityTaskConfigParam.getCondition());
            Assert.notNull(activityTaskConfigParam.getReward());
            // 最大百分比为 999
            if (taskRewardEnum == TaskRewardEnum.percent) {
                // 赠金比例最大 999%
                Assert.isTrue(activityTaskConfigParam.getReward() <= 999
                        && activityTaskConfigParam.getReward() >= 0);
                // 封顶奖金不能为空
                Assert.notNull(activityTaskConfigParam.getMaxReward());
                Assert.isTrue(activityTaskConfigParam.getMaxReward() >= 0);
            }
        }
    }

    @Override
    public List<ActivityRecord> getRewardList(ActivityRewardParam activityRewardParam) {
        DateTime now = DateUtil.date();
        MemberTokenInfoDTO memberTokenInfoDTO = activityRewardParam.getMemberTokenInfoDTO();
        Activity activity = activityService.getEnableAndStartOneNotNull(memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getCurrencyEnum(), this.getActivityTypeEnum(), activityRewardParam.getActivityId());
        ActivityTaskParam activityParam = activity.activityParamToBean(ActivityTaskParam.class);

        ActivityRecord activityRecord = activityRecordService.initActivityRecord(memberTokenInfoDTO, activity, SubActivityTypeEnum.TASK_REWARD, now.toLocalDateTime(), activityRewardParam.getRequestIp());
        DateRangeDTO dateRangeDTO = getDateRangeDTO(activityParam, now);

        ActivityRecord hasActivityRecord = activityRecordService.getOne(memberTokenInfoDTO.getId(), memberTokenInfoDTO.getMerchantId(), activity.getActivityTypeEnum(), activity.getId(), dateRangeDTO);
        if(hasActivityRecord != null){
            return Collections.singletonList(hasActivityRecord);
        }

        Long completeAmount = getCompleteAmount(activityParam, memberTokenInfoDTO, dateRangeDTO);

        checkComplete(completeAmount, activityParam, activityRecord);
        return Collections.singletonList(activityRecord);
    }

    @Override
    public ActivityRecord receiveReward(ActivityRewardParam activityRewardParam) {
        return activityRecordService.receiveReward(this.getRewardList(activityRewardParam));
    }

    @Override
    public ActivityTypeEnum getActivityTypeEnum() {
        return ActivityTypeEnum.TASK;
    }

    @Override
    public String getUid(ActivityRecord activityRecord, Object ... others) {
        Assert.notNull(activityRecord.getMerchantId());
        Assert.notNull(activityRecord.getActivityTypeEnum());
        Assert.notNull(activityRecord.getSubActivityTypeEnum());
        Assert.notNull(activityRecord.getActivityId());
        Assert.notNull(activityRecord.getMarkDate());
        ActivityTaskParam activityTaskParam = (ActivityTaskParam) others[0];
        String uidStr;
        switch (activityTaskParam.getTaskPeriodEnum()){
            case DAILY:
                uidStr = activityRecord.getMemberId().toString() + StrPool.COLON + activityRecord.getActivityTypeEnum() + StrPool.COLON + activityRecord.getSubActivityTypeEnum() + StrPool.COLON + activityRecord.getActivityId() +  StrPool.COLON + activityRecord.getMarkDate().toString();
                break;
            case WEEKLY:
                uidStr = activityRecord.getMemberId().toString() +  StrPool.COLON  + activityRecord.getActivityTypeEnum() + StrPool.COLON + activityRecord.getSubActivityTypeEnum() + StrPool.COLON + activityRecord.getActivityId() + StrPool.COLON + activityRecord.getMarkDate().getYear() + StrPool.COLON + LocalUtil.getWeek(activityRecord.getMarkDate());
                break;
            default:
                throw new ApiException(CommonCode.PARAM_INVALID);
        }
        return Md5Util.getMd5HexLowerCase(uidStr);
    }

    @Override
    public Boolean getUniqueActivity() {
        return null;
    }

    private void checkComplete(Long completeAmount, ActivityTaskParam activityParam, ActivityRecord activityRecord) {
        activityRecord.setRecordJson(JSONUtil.toJsonStr(new ActivityTaskRecordParam(completeAmount, null,null)));
        List<ActivityTaskConfigParam> activityTaskConfigParamList = activityParam.getActivityTaskConfigParamList();
        Optional<ActivityTaskConfigParam> completeConfig = activityTaskConfigParamList
                .stream()
                .sorted(Comparator.comparingLong(ActivityTaskConfigParam::getCondition).reversed())
                .filter(activityTaskConfigParam -> activityTaskConfigParam.getCondition() <= completeAmount).findFirst();
        if (!completeConfig.isPresent()) {
            return;
        }
        ActivityTaskConfigParam completeActivityTaskConfigParam = completeConfig.get();
        Long amount = completeActivityTaskConfigParam.getReward();
        TaskRewardEnum taskRewardEnum = activityParam.getTaskRewardEnum();
        // 按百分比领奖
        if (taskRewardEnum == TaskRewardEnum.percent) {
            Long rate = completeActivityTaskConfigParam.getReward();
            Long maxReward = completeActivityTaskConfigParam.getMaxReward();
            amount = completeAmount * rate / Constants.HUNDRED_LONG;
            if (amount > maxReward) {
                amount = maxReward;
            }
        }
        activityRecord.setAmount(amount);
        activityRecord.setActivityStatusEnum(ActivityStatusEnum.WAIT_RECEIVE);
        String uid = this.getUid(activityRecord, activityParam);
        activityRecord.setUid(uid);
        activityRecord.setRecordJson(JSONUtil.toJsonStr(new ActivityTaskRecordParam(completeAmount, completeActivityTaskConfigParam.getRewardLevel(),activityParam.getTaskTypeEnum())));
    }

    private Long getCompleteAmount(ActivityTaskParam activityParam, MemberTokenInfoDTO memberTokenInfoDTO, DateRangeDTO dateRangeDTO) {
        Assert.notNull(activityParam);
        Long competeAmount;
        switch (activityParam.getTaskTypeEnum()) {
            case DEPOSIT:
                ReportMemberDate reportMemberDate = getReportMemberDate(memberTokenInfoDTO, dateRangeDTO);
                competeAmount = reportMemberDate.getDepositAmount();
                break;
            case VALID_BET:
                ReportMemberGameDate reportMemberGameDate = getReportMemberGameDate(dateRangeDTO, activityParam, memberTokenInfoDTO);
                competeAmount = reportMemberGameDate.getValidBetAmount();
                break;
            default:
                log.error("taskTypeEnum is invalid, taskTypeEnum:{}", activityParam.getTaskTypeEnum());
                throw new ApiException(CommonCode.PARAM_INVALID);
        }
        return competeAmount;
    }

    @NotNull
    private static DateRangeDTO getDateRangeDTO(ActivityTaskParam activityParam, DateTime now) {
        DateRangeDTO dateRangeDTO = new DateRangeDTO();
        switch (activityParam.getTaskPeriodEnum()) {
            case DAILY:
                dateRangeDTO.setStartDate(now.toLocalDateTime().toLocalDate());
                dateRangeDTO.setEndDate(now.toLocalDateTime().toLocalDate());
                break;
            case WEEKLY:
                dateRangeDTO.setStartDate(DateUtil.beginOfWeek(now).toLocalDateTime().toLocalDate());
                dateRangeDTO.setEndDate(DateUtil.endOfWeek(now).toLocalDateTime().toLocalDate());
                break;
            default:
                log.error("taskPeriodEnum is invalid, activityParam:{}", activityParam);
                throw new ApiException(CommonCode.PARAM_INVALID);
        }
        return dateRangeDTO;
    }

    @NotNull
    private ReportMemberDate getReportMemberDate(MemberTokenInfoDTO memberTokenInfoDTO, DateRangeDTO dateRangeDTO) {
        ReportMemberDateDTO reportMemberDateDTO = new ReportMemberDateDTO();
        reportMemberDateDTO.setMerchantId(memberTokenInfoDTO.getMerchantId());
        reportMemberDateDTO.setMemberId(memberTokenInfoDTO.getId());
        reportMemberDateDTO.setStartDate(dateRangeDTO.getStartDate());
        reportMemberDateDTO.setEndDate(dateRangeDTO.getEndDate());
        return reportMemberDateService.getOneGroupByMemberIdOrElseNew(reportMemberDateDTO);
    }

    @NotNull
    private  ReportMemberGameDate getReportMemberGameDate(DateRangeDTO dateRangeDTO, ActivityTaskParam activityParam, MemberTokenInfoDTO memberTokenInfoDTO) {
        Assert.notNull(activityParam.getGameCategoryEnumSet());
        ReportMemberGameDateDTO reportMemberGameDateDTO = new ReportMemberGameDateDTO();
        reportMemberGameDateDTO.setStartDate(dateRangeDTO.getStartDate());
        reportMemberGameDateDTO.setEndDate(dateRangeDTO.getEndDate());
        reportMemberGameDateDTO.setGameCategoryEnumList(activityParam.getGameCategoryEnumSet());
        reportMemberGameDateDTO.setMemberId(memberTokenInfoDTO.getId());
        reportMemberGameDateDTO.setMerchantId(memberTokenInfoDTO.getMerchantId());
        return reportMemberGameDateService.getOneGroupByMemberIdOrElseNew(reportMemberGameDateDTO);
    }
}
