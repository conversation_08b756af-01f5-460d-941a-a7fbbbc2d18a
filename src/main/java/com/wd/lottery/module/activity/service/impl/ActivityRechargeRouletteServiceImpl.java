package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.Md5Util;
import com.wd.lottery.common.util.RouletteLotteryUtil;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.entity.ActivityRechargeRouletteStatistics;
import com.wd.lottery.module.activity.entity.ActivityRecord;
import com.wd.lottery.module.activity.mapper.ActivityRechargeRouletteStatisticsMapper;
import com.wd.lottery.module.activity.mapper.ActivityRecordMapper;
import com.wd.lottery.module.activity.param.ActivityParam;
import com.wd.lottery.module.activity.param.ActivityRewardParam;
import com.wd.lottery.module.activity.param.rechargeroulette.*;
import com.wd.lottery.module.activity.param.rechargeroulette.*;
import com.wd.lottery.module.activity.service.ActivityRechargeRouletteService;
import com.wd.lottery.module.activity.service.ActivityRecordService;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.activity.vo.ActivityRechargeRouletteStatisticsVo;
import com.wd.lottery.module.cash.constatns.OrderStatusEnum;
import com.wd.lottery.module.cash.service.CashDepositOrderService;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityRechargeRouletteServiceImpl implements ActivityRechargeRouletteService {

    @Resource
    private ActivityService activityService;

    @Resource
    private ActivityRechargeRouletteStatisticsMapper statisticsMapper;

    @Resource
    private ActivityRecordMapper recordMapper;

    @Resource
    private ActivityRecordService activityRecordService;

    @Resource
    private CashDepositOrderService cashDepositOrderService;

    @Resource
    private RedisService redisService;

    @Override
    public void checkParam(ActivityParam activityParam) {
        Assert.notNull(activityParam.getStartTime(), "充值转盘活动开始时间不能为空");
        Assert.notNull(activityParam.getEndTime(), "充值转盘活动结束时间不能为空");
        Assert.notNull(activityParam.getBannerImg(), "banner图片不能为空");
        Assert.notNull(activityParam.getConfigJson(), "configJson not null");
        List<RechargeRouletteConfigParam> params = activityParam.getActivityParamList(RechargeRouletteConfigParam.class);
        Assert.notEmpty(params, "充值转盘活动配置不能为空");
        checkConfigParam(params);
    }

    private void checkConfigParam(List<RechargeRouletteConfigParam> params) {
        for (int i = 0; i < params.size() - 1; i++) {
            Assert.notEmpty(params.get(i).getRulesParams(), "充值转盘条件配置不能为空");
            Assert.isFalse(params.get(i).getRulesParams().size() > ActivityConstants.ACTIVITY_RECHARGE_ROULETTE_RULES, "充值转盘条件配置最多只支持10条");
            Assert.isTrue(RechargeRouletteLotteryRulesParam.isTotalRechargeIncreasing(params.get(i).getRulesParams()), "充值转盘条件配置的累计充值必须递增");
            Assert.notEmpty(params.get(i).getPrizeParams(), "充值转盘奖品配置不能为空");
            Assert.isFalse(params.get(i).getPrizeParams().size() > ActivityConstants.ACTIVITY_RECHARGE_ROULETTE_RULES, "充值转盘奖品配置最多只支持10条");
            checkRuleParam(params.get(i), params.get(i + 1));
        }
    }

    private void checkRuleParam(RechargeRouletteConfigParam currentParam, RechargeRouletteConfigParam nextParam) {
        List<RechargeRouletteLotteryRulesParam> currentRulesParams = currentParam.getRulesParams();
        List<RechargeRouletteLotteryRulesParam> nextRulesParams = nextParam.getRulesParams();
        Assert.notEmpty(currentRulesParams, "rulesParams 列表不能为空");
        Assert.notEmpty(nextRulesParams, "rulesParams 列表不能为空");

        RechargeRouletteLotteryRulesParam lastCurrentRule = currentRulesParams.get(currentRulesParams.size() - 1);
        RechargeRouletteLotteryRulesParam firstNextRule = nextRulesParams.get(0);
        Assert.isTrue(lastCurrentRule.getTotalRecharge() < firstNextRule.getTotalRecharge(), "后一个 rulesParams 列表的第一个元素的 totalRecharge 必须大于前一个 rulesParams 列表的最后一个元素的 totalRecharge");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ActivityRecord> getRewardList(ActivityRewardParam activityRewardParam) {
        Activity activity = checkConfig(activityRewardParam);
        //查询用户当前的累充
        Long totalRecharge = getTotalRecharge(activityRewardParam.getMemberTokenInfoDTO());
        //Long totalRecharge = 125L;
        List<RechargeRouletteConfigParam> paramList = activity.activityParamList(RechargeRouletteConfigParam.class);
        if (CollectionUtils.isEmpty(paramList)) {
            throw new ApiException(CommonCode.ACTIVITY_RECHARGE_ROULETTE_NOT_EXISTS);
        }
        Map<RechargeRouletteEnum, Integer> resultMap = getDefaultResultMap();
        Map<RechargeRouletteEnum, RechargeRouletteConfigParam> map = getRule(totalRecharge, paramList);
        if (Objects.isNull(map)) {
            return Collections.singletonList(getActivityRecord(resultMap, totalRecharge));
        }
        RechargeRouletteEnum nearReouleteEnum = getNearReouleteEnum(map, totalRecharge);
        if (Objects.isNull(nearReouleteEnum)) {
            return Collections.singletonList(getActivityRecord(resultMap, totalRecharge));
        }
        //查询当天是否有统计记录
        List<RechargeRouletteEnum> rouletteEnums = Arrays.stream(RechargeRouletteEnum.values())
                .filter(enumValue -> enumValue.getCode() <= nearReouleteEnum.getCode())
                .collect(Collectors.toList());
        List<ActivityRechargeRouletteStatistics> statistics = getLotteryStatistics(activity, activityRewardParam, rouletteEnums);

        if (CollectionUtils.isEmpty(statistics)) {
            resultMap = saveFirstLotteryStastics(activityRewardParam, map, totalRecharge);
        } else {
            RechargeRouletteStatisticsParam param = new RechargeRouletteStatisticsParam(map, totalRecharge, statistics, nearReouleteEnum);
            resultMap = updateLotteryStastics(param, activityRewardParam);
        }
        ActivityRecord activityRecord = getActivityRecord(resultMap, totalRecharge);
        return Collections.singletonList(activityRecord);
    }

    private static ActivityRecord getActivityRecord(Map<RechargeRouletteEnum, Integer> resultMap, Long totalRecharge) {
        ActivityRechargeRouletteStatisticsVo vo = new ActivityRechargeRouletteStatisticsVo();
        vo.setMap(resultMap);
        vo.setTotalRecharge(totalRecharge);
        ActivityRecord activityRecord = new ActivityRecord();
        activityRecord.setRecordJson(JSONUtil.toJsonStr(vo));
        return activityRecord;
    }

    private Map<RechargeRouletteEnum, Integer> getDefaultResultMap() {
        Map<RechargeRouletteEnum, Integer> resultMap = Maps.newHashMap();
        for (RechargeRouletteEnum value : RechargeRouletteEnum.values()) {
            resultMap.put(value,0);
        }
        return resultMap;
    }

    private Activity checkConfig(ActivityRewardParam activityRewardParam) {
        Activity rechargeRouletteActivity = activityService.getEnableOne(activityRewardParam.getMemberTokenInfoDTO().getMerchantId(),
                activityRewardParam.getMemberTokenInfoDTO().getCurrencyEnum(), ActivityTypeEnum.RECHARGE_ROULETTE);
        if (Objects.isNull(rechargeRouletteActivity)) {

            throw new ApiException(CommonCode.ACTIVITY_RECHARGE_ROULETTE_NOT_EXISTS);
        }
        if (rechargeRouletteActivity.getStartTime().isAfter(LocalDateTime.now()) || rechargeRouletteActivity.getEndTime().isBefore(LocalDateTime.now())) {
            throw new ApiException(CommonCode.ACTIVITY_NOT_START);
        }
        return rechargeRouletteActivity;
    }

    private List<ActivityRechargeRouletteStatistics> getLotteryStatistics(Activity activity, ActivityRewardParam activityRewardParam, List<RechargeRouletteEnum> rouletteEnums) {
        return statisticsMapper.selectList(new LambdaQueryWrapper<ActivityRechargeRouletteStatistics>()
                .eq(ActivityRechargeRouletteStatistics::getMerchantId, activityRewardParam.getMemberTokenInfoDTO().getMerchantId())
                .eq(ActivityRechargeRouletteStatistics::getRouletteId, activity.getId())
                .in(ActivityRechargeRouletteStatistics::getRouletteEnum, rouletteEnums)
                .eq(ActivityRechargeRouletteStatistics::getMemberId, activityRewardParam.getMemberTokenInfoDTO().getId())
                .eq(ActivityRechargeRouletteStatistics::getLotteryDate, LocalDate.now()));
    }

    private RechargeRouletteEnum getNearReouleteEnum(Map<RechargeRouletteEnum, RechargeRouletteConfigParam> map, Long totalRecharge) {
        Optional<RechargeRouletteEnum> rechargeRouletteEnum = map.entrySet().stream()
                .min((entry1, entry2) -> {
                    Long diff1 = entry1.getValue().getRulesParams().stream()
                            .map(RechargeRouletteLotteryRulesParam::getTotalRecharge)
                            .map(recharge -> Math.abs(recharge - totalRecharge))
                            .min(Long::compareTo)
                            .orElse(Long.MAX_VALUE);
                    Long diff2 = entry2.getValue().getRulesParams().stream()
                            .map(RechargeRouletteLotteryRulesParam::getTotalRecharge)
                            .map(recharge -> Math.abs(recharge - totalRecharge))
                            .min(Long::compareTo)
                            .orElse(Long.MAX_VALUE);
                    return diff1.compareTo(diff2);
                })
                .map(Map.Entry::getKey);
        return rechargeRouletteEnum.orElse(null);
    }

    private Map<RechargeRouletteEnum, RechargeRouletteConfigParam> getRule(Long totalRecharge, List<RechargeRouletteConfigParam> paramList) {
        log.info("totalRecharge...{},paramList..{}",totalRecharge,paramList);
        Map<RechargeRouletteEnum, RechargeRouletteConfigParam> collect = paramList.stream()
                .filter(param -> param.getRulesParams().stream()
                        .anyMatch(rule -> rule.getTotalRecharge() <= totalRecharge))
                .collect(Collectors.toMap(
                        RechargeRouletteConfigParam::getRechargeRouletteEnum,
                        param -> param
                ));

        if (collect.isEmpty()) {
            return null;
        }
        return collect;
    }

    private Map<RechargeRouletteEnum, Integer> updateLotteryStastics(RechargeRouletteStatisticsParam param, ActivityRewardParam activityRewardParam) {
        //从满足的规则中找出需要修改的轮盘档位次数统计记录和需要新增的
        List<RechargeRouletteEnum> rouletteEnums = param.getStatistics().stream().map(ActivityRechargeRouletteStatistics::getRouletteEnum).collect(Collectors.toList());
        Map<RechargeRouletteEnum, Integer> resultMap = Maps.newHashMap();
        if (rouletteEnums.contains(param.getNearReouleteEnum())) {
            //更新最新的统计记录
            resultMap = updateExistsStatis(param);
            return resultMap;
        }

        return saveOrUpdateStatis(param, activityRewardParam, rouletteEnums, resultMap);
    }

    private Map<RechargeRouletteEnum, Integer> saveOrUpdateStatis(RechargeRouletteStatisticsParam param, ActivityRewardParam activityRewardParam,
                                                                  List<RechargeRouletteEnum> rouletteEnums, Map<RechargeRouletteEnum, Integer> resultMap) {
        List<RechargeRouletteEnum> insertRouletteEnums = Arrays.stream(RechargeRouletteEnum.values())
                .filter(rouletteEnum -> !rouletteEnums.contains(rouletteEnum) && rouletteEnum.getCode() <= param.getNearReouleteEnum().getCode())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertRouletteEnums)) {
            //新增统计记录
            //找出符合新增的配置记录
            Map<RechargeRouletteEnum, RechargeRouletteConfigParam> newMap = param.getMap().entrySet().stream()
                    .filter(entry -> insertRouletteEnums.contains(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            Map<RechargeRouletteEnum, Integer> insertMap = saveFirstLotteryStastics(activityRewardParam, newMap, param.getTotalRecharge());
            resultMap.putAll(insertMap);
        }
        //判断已有统计记录的最高档位是否需要更新
        RechargeRouletteLotteryRulesParam lotteryRulesParam = param.getMap().get(param.getStatistics().get(param.getStatistics().size() - 1).getRouletteEnum()).getRulesParams().stream()
                .max(Comparator.comparingInt(RechargeRouletteLotteryRulesParam::getLevel))
                .orElse(null);
        Integer totalLotteryNum = getTotalLotteryNum(param.getMap().get(param.getStatistics().get(param.getStatistics().size() - 1).getRouletteEnum()).getRulesParams(), param.getTotalRecharge());
        Integer increaseCanLotteryNum = Math.max((totalLotteryNum - param.getStatistics().get(param.getStatistics().size() - 1).getTotalLotteryNum() + param.getStatistics().get(param.getStatistics().size() - 1).getCanLotteryNum()), 0);
        if (Objects.nonNull(lotteryRulesParam) && !Objects.equals(lotteryRulesParam.getLevel(), param.getStatistics().get(param.getStatistics().size() - 1).getRuleLevel())
                && totalLotteryNum >= param.getStatistics().get(param.getStatistics().size() - 1).getTotalLotteryNum()) {
            int update = statisticsMapper.update(new LambdaUpdateWrapper<ActivityRechargeRouletteStatistics>()
                    .set(ActivityRechargeRouletteStatistics::getUpdateTime, LocalDateTime.now())
                    .set(ActivityRechargeRouletteStatistics::getRuleLevel, lotteryRulesParam.getLevel())
                    .set(ActivityRechargeRouletteStatistics::getTotalLotteryNum, totalLotteryNum)
                    .set(ActivityRechargeRouletteStatistics::getCanLotteryNum, increaseCanLotteryNum)
                    .set(ActivityRechargeRouletteStatistics::getTotalRecharge, lotteryRulesParam.getTotalRecharge())
                    .eq(ActivityRechargeRouletteStatistics::getId, param.getStatistics().get(param.getStatistics().size() - 1).getId())
                    .eq(ActivityRechargeRouletteStatistics::getTotalRecharge, param.getStatistics().get(param.getStatistics().size() - 1).getTotalRecharge()));
            if (update <= 0) {
                throw new ApiException(CommonCode.ACTIVITY_RECHARGE_ROULETTE_SYSTEM_BUSY);
            }
            resultMap.put(param.getStatistics().get(param.getStatistics().size() - 1).getRouletteEnum(), increaseCanLotteryNum);
        }
        return resultMap;
    }

    private Map<RechargeRouletteEnum, Integer> updateExistsStatis(RechargeRouletteStatisticsParam param) {
        Map<RechargeRouletteEnum, ActivityRechargeRouletteStatistics> statisticsMap = param.getStatistics().stream().collect(Collectors.toMap(ActivityRechargeRouletteStatistics::getRouletteEnum,
                Function.identity(), (k1, k2) -> k1));

        Map<RechargeRouletteEnum, Integer> resultMap = param.getStatistics().stream().collect(Collectors.toMap(ActivityRechargeRouletteStatistics::getRouletteEnum, ActivityRechargeRouletteStatistics::getCanLotteryNum));
        ActivityRechargeRouletteStatistics oldStatistics = statisticsMap.get(param.getNearReouleteEnum());
        RechargeRouletteLotteryRulesParam maxRule = getLotteryRules(param.getMap().get(param.getNearReouleteEnum()).getRulesParams(), param.getTotalRecharge());
        Integer totalLotteryNum = getTotalLotteryNum(param.getMap().get(param.getNearReouleteEnum()).getRulesParams(), param.getTotalRecharge());
        if (Objects.equals(maxRule.getLevel(), oldStatistics.getRuleLevel()) || totalLotteryNum <= oldStatistics.getTotalLotteryNum()) {
            log.warn("already..statistics..{},newTotalLotteryNum..{}", maxRule, totalLotteryNum);
            return resultMap;
        }
        Integer increaseCanLotteryNum = Math.max((totalLotteryNum - oldStatistics.getTotalLotteryNum() + oldStatistics.getCanLotteryNum()), 0);
        int update = statisticsMapper.update(new LambdaUpdateWrapper<ActivityRechargeRouletteStatistics>()
                .set(ActivityRechargeRouletteStatistics::getUpdateTime, LocalDateTime.now())
                .set(ActivityRechargeRouletteStatistics::getTotalLotteryNum, totalLotteryNum)
                .set(ActivityRechargeRouletteStatistics::getCanLotteryNum, increaseCanLotteryNum)
                .set(ActivityRechargeRouletteStatistics::getTotalRecharge, maxRule.getTotalRecharge())
                .set(ActivityRechargeRouletteStatistics::getRuleLevel, maxRule.getLevel())
                .eq(ActivityRechargeRouletteStatistics::getId, oldStatistics.getId())
                .eq(ActivityRechargeRouletteStatistics::getTotalRecharge, oldStatistics.getTotalRecharge()));
        if (update <= 0) {
            throw new ApiException(CommonCode.ACTIVITY_RECHARGE_ROULETTE_SYSTEM_BUSY);
        }
        resultMap.put(param.getNearReouleteEnum(), increaseCanLotteryNum);
        return resultMap;
    }

    private Map<RechargeRouletteEnum, Integer> saveFirstLotteryStastics(ActivityRewardParam activityRewardParam,
                                                                        Map<RechargeRouletteEnum, RechargeRouletteConfigParam> map, Long totalRecharge) {
        Map<RechargeRouletteEnum, Integer> resultMap = Maps.newHashMap();
        for (RechargeRouletteEnum rechargeRouletteEnum : map.keySet()) {
            RechargeRouletteConfigParam param = map.get(rechargeRouletteEnum);
            List<RechargeRouletteLotteryRulesParam> rulesParams = param.getRulesParams();
            RechargeRouletteLotteryRulesParam rulesParam = getLotteryRules(rulesParams, totalRecharge);
            Integer totalLotteryNum = getTotalLotteryNum(rulesParams, totalRecharge);
            ActivityRechargeRouletteStatistics statistics = new ActivityRechargeRouletteStatistics();
            statistics.setRouletteEnum(rechargeRouletteEnum);
            statistics.setCurrencyEnum(activityRewardParam.getMemberTokenInfoDTO().getCurrencyEnum());
            statistics.setRouletteId(activityRewardParam.getActivityId());
            statistics.setMerchantId(activityRewardParam.getMemberTokenInfoDTO().getMerchantId());
            statistics.setMemberId(activityRewardParam.getMemberTokenInfoDTO().getId());
            statistics.setRuleLevel(rulesParam.getLevel());
            statistics.setRechargeCondition(rulesParam.getTotalRecharge());
            statistics.setTotalLotteryNum(totalLotteryNum);
            statistics.setCanLotteryNum(totalLotteryNum);
            statistics.setTotalRecharge(rulesParam.getTotalRecharge());
            statistics.setLotteryDate(LocalDate.now());
            statistics.setCreateTime(LocalDateTime.now());
            statisticsMapper.insert(statistics);
            resultMap.put(rechargeRouletteEnum, totalLotteryNum);
        }
        return resultMap;
    }

    private Integer getTotalLotteryNum(List<RechargeRouletteLotteryRulesParam> rulesParams, Long totalRecharge) {
        return rulesParams.stream()
                .sorted(Comparator.comparing(RechargeRouletteLotteryRulesParam::getTotalRecharge).reversed())
                .filter(param -> param.getTotalRecharge() <= totalRecharge)
                .mapToInt(RechargeRouletteLotteryRulesParam::getLotteryNum)
                .sum();
    }

    private RechargeRouletteLotteryRulesParam getLotteryRules(List<RechargeRouletteLotteryRulesParam> rulesParams, Long totalRecharge) {
        RechargeRouletteLotteryRulesParam lotteryRulesParam = rulesParams.stream()
                .sorted(Comparator.comparing(RechargeRouletteLotteryRulesParam::getTotalRecharge).reversed())
                .filter(param -> param.getTotalRecharge() <= totalRecharge)
                .findFirst()
                .orElse(null);
        if (Objects.isNull(lotteryRulesParam)) {
            throw new ApiException(CommonCode.ACTIVITY_RECHARGE_ROULETTE_RULE_NOT_MATCH);
        }
        return lotteryRulesParam;
    }

    private Long getTotalRecharge(MemberTokenInfoDTO memberTokenInfoDTO) {
        return cashDepositOrderService.totalDepositMoney(memberTokenInfoDTO.getMerchantId(), OrderStatusEnum.SUCCESS,
                Collections.singletonList(memberTokenInfoDTO.getId()), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivityRecord receiveReward(ActivityRewardParam activityRewardParam) {
        Assert.notNull(activityRewardParam.getOptional(), "optional not null");
        Activity activity = checkConfig(activityRewardParam);
        List<RechargeRouletteConfigParam> paramList = activity.activityParamList(RechargeRouletteConfigParam.class);
        if (CollectionUtils.isEmpty(paramList)) {
            throw new ApiException(CommonCode.ACTIVITY_RECHARGE_ROULETTE_NOT_EXISTS);
        }

        Map<RechargeRouletteEnum, RechargeRouletteConfigParam> map = paramList.stream().collect(Collectors.toMap(RechargeRouletteConfigParam::getRechargeRouletteEnum, Function.identity(), (k1, k2) -> k1));
        RechargeRouletteEnum rouletteEnum = RechargeRouletteEnum.getByCode(Integer.parseInt(activityRewardParam.getOptional()));
        if (Objects.isNull(rouletteEnum)) {
            throw new ApiException(CommonCode.ACTIVITY_RECHARGE_ROULETTE_NOT_EXISTS);
        }
        ActivityRechargeRouletteStatistics statistics = checkLotteryNum(activityRewardParam, activity, rouletteEnum);
        RechargeRouletteConfigParam configParam = map.get(rouletteEnum);
        RechargeRoulettePrizeParam rechargeRoulettePrizeParam = RouletteLotteryUtil.generateAward(configParam.getPrizeParams());
        if (Objects.isNull(rechargeRoulettePrizeParam)) {
            throw new ApiException(CommonCode.ACTIVITY_RECHARGE_ROULETTE_NOT_WINNING);
        }
        //保存中奖记录
        ActivityRecord activityRecord = buildActivityRecord(rouletteEnum, activity, rechargeRoulettePrizeParam, activityRewardParam);
        //发放奖金(奖品为抽奖时需要增加抽奖次数)
        if (rechargeRoulettePrizeParam.getPrizeEnum() == RechargeRoulettePrizeEnum.CASH) {
            activityRecordService.saveAndOperateWallet(activityRecord);
        }
        //扣减/增加抽奖次数
        updateLotteryNum(statistics,rechargeRoulettePrizeParam);
        //事物提交后释放锁
        releaseLock(activityRewardParam);
        return activityRecord;
    }

    private void releaseLock(ActivityRewardParam activityRewardParam) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    String key = ActivityRedisKeyConstants.ACTIVITY_FREQUENCY_LOCK_KEY + activityRewardParam.getMemberTokenInfoDTO().getId()
                            + activityRewardParam.getActivityId() + activityRewardParam.getActivityTypeEnum() + activityRewardParam.getSubActivityTypeEnum();
                    redisService.del(key);
                }
            });
        }
    }


    private ActivityRecord buildActivityRecord(RechargeRouletteEnum rouletteEnum, Activity activity, RechargeRoulettePrizeParam rechargeRoulettePrizeParam, ActivityRewardParam activityRewardParam) {
        ActivityRecord activityRecordSave = new ActivityRecord();
        activityRecordSave.setActivityId(activity.getId());
        if (rechargeRoulettePrizeParam.getPrizeEnum() == RechargeRoulettePrizeEnum.CASH) {
            activityRecordSave.setAmount(rechargeRoulettePrizeParam.getPrizeNum());
        } else {
            activityRecordSave.setAmount(0L);
        }
        activityRecordSave.setActivityTypeEnum(activity.getActivityTypeEnum());
        activityRecordSave.setSubActivityTypeEnum(SubActivityTypeEnum.RECHARGE_ROULETTE_REWARD);
        LocalDateTime now = LocalDateTime.now();
        activityRecordSave.setCreateTime(now);
        activityRecordSave.setUpdateTime(now);
        activityRecordSave.setMarkDate(now.toLocalDate());
        activityRecordSave.setMemberId(activityRewardParam.getMemberTokenInfoDTO().getId());
        activityRecordSave.setMemberName(activityRewardParam.getMemberTokenInfoDTO().getMemberName());
        activityRecordSave.setMerchantId(activity.getMerchantId());
        activityRecordSave.setCurrencyEnum(activity.getCurrencyEnum());
        activityRecordSave.setActivityStatusEnum(ActivityStatusEnum.RECEIVED);
        activityRecordSave.setRequestIp(activityRewardParam.getRequestIp());
        activityRecordSave.setUid(this.getUid(activityRecordSave));
        activityRecordSave.setChannelId(activityRewardParam.getMemberTokenInfoDTO().getChannelId());
        activityRecordSave.setIsDirect(activityRewardParam.getMemberTokenInfoDTO().getIsDirect());
        RechargeLotteryRecord rechargeLotteryRecord = new RechargeLotteryRecord();
        rechargeLotteryRecord.setRouletteEnum(rouletteEnum);
        rechargeLotteryRecord.setPrizeNum(rechargeRoulettePrizeParam.getPrizeNum());
        rechargeLotteryRecord.setPrizeEnum(rechargeRoulettePrizeParam.getPrizeEnum());
        rechargeLotteryRecord.setId(rechargeRoulettePrizeParam.getId());
        rechargeLotteryRecord.setPrizeUrl(rechargeRoulettePrizeParam.getPrizeUrl());
        activityRecordSave.setRecordJson(JSONUtil.toJsonStr(rechargeLotteryRecord));
        if (rechargeRoulettePrizeParam.getPrizeEnum() == RechargeRoulettePrizeEnum.PHYSICAL || rechargeRoulettePrizeParam.getPrizeEnum() == RechargeRoulettePrizeEnum.LOTTERY_NUM) {
            //非现金类的奖励只保存记录
            recordMapper.insert(activityRecordSave);
        }
        return activityRecordSave;
    }

    private void updateLotteryNum(ActivityRechargeRouletteStatistics statistics,RechargeRoulettePrizeParam rechargeRoulettePrizeParam) {
        Integer totalLotteryNum = statistics.getTotalLotteryNum();
        Integer canLotteryNum = Math.max(statistics.getCanLotteryNum() - 1, 0);
        if (rechargeRoulettePrizeParam.getPrizeEnum() == RechargeRoulettePrizeEnum.LOTTERY_NUM){
            totalLotteryNum = totalLotteryNum + rechargeRoulettePrizeParam.getPrizeNum().intValue();
            canLotteryNum = canLotteryNum + rechargeRoulettePrizeParam.getPrizeNum().intValue();
        }
        int update = statisticsMapper.update(new LambdaUpdateWrapper<ActivityRechargeRouletteStatistics>()
                .set(ActivityRechargeRouletteStatistics::getCanLotteryNum, canLotteryNum)
                .set(rechargeRoulettePrizeParam.getPrizeEnum() == RechargeRoulettePrizeEnum.LOTTERY_NUM, ActivityRechargeRouletteStatistics::getTotalLotteryNum, totalLotteryNum)
                .set(ActivityRechargeRouletteStatistics::getUpdateTime, LocalDateTime.now())
                .eq(ActivityRechargeRouletteStatistics::getId, statistics.getId())
                .eq(ActivityRechargeRouletteStatistics::getCanLotteryNum, statistics.getCanLotteryNum())
                .eq(ActivityRechargeRouletteStatistics::getTotalLotteryNum, statistics.getTotalLotteryNum())
                .gt(ActivityRechargeRouletteStatistics::getCanLotteryNum, 0));
        if (update <= 0) {
            throw new ApiException(CommonCode.ACTIVITY_RECHARGE_ROULETTE_SYSTEM_BUSY);
        }
    }

    private ActivityRechargeRouletteStatistics checkLotteryNum(ActivityRewardParam activityRewardParam, Activity activity, RechargeRouletteEnum rouletteEnum) {
        //检查当前等级抽奖次数是否足够
        List<ActivityRechargeRouletteStatistics> statistics = getLotteryStatistics(activity, activityRewardParam, Collections.singletonList(rouletteEnum));
        if (CollectionUtils.isEmpty(statistics) || Objects.isNull(statistics.get(0)) || statistics.get(0).getCanLotteryNum() <= 0) {
            throw new ApiException(CommonCode.ACTIVITY_RECHARGE_ROULETTE_LOTTERY_NUM_NOT_ENOUGH);
        }
        return statistics.get(0);
    }

    @Override
    public ActivityTypeEnum getActivityTypeEnum() {
        return ActivityTypeEnum.RECHARGE_ROULETTE;
    }

    @Override
    public String getUid(ActivityRecord activityRecord, Object... others) {
        Assert.notNull(activityRecord.getMerchantId());
        Assert.notNull(activityRecord.getActivityTypeEnum());
        Assert.notNull(activityRecord.getSubActivityTypeEnum());
        Assert.notNull(activityRecord.getActivityId());
        Assert.notNull(activityRecord.getMarkDate());
        String uidStr = activityRecord.getMemberId().toString() + activityRecord.getActivityTypeEnum() + activityRecord.getSubActivityTypeEnum() + activityRecord.getActivityId() + activityRecord.getCreateTime();
        return Md5Util.getMd5HexLowerCase(uidStr);
    }

    @Override
    public Boolean getUniqueActivity() {
        return true;
    }

    @Override
    public List<ActivityRechargeRouletteStatisticsVo> getCanLotteryRoulette(MemberTokenInfoDTO memberTokenInfo) {
        List<ActivityRechargeRouletteStatistics> statisticsList = statisticsMapper.selectList(new LambdaQueryWrapper<ActivityRechargeRouletteStatistics>()
                .eq(ActivityRechargeRouletteStatistics::getMemberId, memberTokenInfo.getId())
                .eq(ActivityRechargeRouletteStatistics::getMerchantId, memberTokenInfo.getMerchantId())
                .eq(ActivityRechargeRouletteStatistics::getLotteryDate, LocalDate.now()));
        if (CollectionUtils.isEmpty(statisticsList)) {
            return Collections.emptyList();
        }
        Long totalRecharge = getTotalRecharge(memberTokenInfo);
        return statisticsList.stream().map(statis -> {
            ActivityRechargeRouletteStatisticsVo vo = new ActivityRechargeRouletteStatisticsVo();
            vo.setRouletteEnum(statis.getRouletteEnum());
            vo.setTotalLotteryNum(statis.getTotalLotteryNum());
            vo.setCanLotteryNum(statis.getCanLotteryNum());
            vo.setTotalRecharge(totalRecharge);
            return vo;
        }).collect(Collectors.toList());
    }
}
