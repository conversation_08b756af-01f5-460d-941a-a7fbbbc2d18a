package com.wd.lottery.module.activity.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.dto.DateRangeDTO;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.Md5Util;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.constants.*;
import com.wd.lottery.module.activity.dto.ActivityRankingBonusPoolDto;
import com.wd.lottery.module.activity.dto.ActivityRankingInfoDTO;
import com.wd.lottery.module.activity.entity.Activity;
import com.wd.lottery.module.activity.entity.ActivityRankingRecord;
import com.wd.lottery.module.activity.entity.ActivityRecord;
import com.wd.lottery.module.activity.param.ActivityParam;
import com.wd.lottery.module.activity.param.ActivityRewardParam;
import com.wd.lottery.module.activity.param.rank.ActivityRankingBonusRangeConfig;
import com.wd.lottery.module.activity.param.rank.ActivityRankingParam;
import com.wd.lottery.module.activity.param.rank.ActivityRankingParamConfig;
import com.wd.lottery.module.activity.param.rank.ActivityRankingQueryParam;
import com.wd.lottery.module.activity.service.ActivityRankingRecordService;
import com.wd.lottery.module.activity.service.ActivityRankingService;
import com.wd.lottery.module.activity.service.ActivityRecordService;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.report.entity.ReportMemberDate;
import com.wd.lottery.module.report.service.ReportMemberDateService;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
public class ActivityRankingServiceImpl implements ActivityRankingService {

    @Resource
    private ActivityService activityService;

    @Resource
    private ActivityRecordService activityRecordService;

    @Resource
    private ActivityRankingRecordService activityRankingRecordService;

    @Resource
    private ReportMemberDateService reportMemberDateService;

    @Resource
    private RedisService redisService;

    @Resource
    private MemberService memberService;

    @Resource(name = Constants.DISCARD_THREAD_POOL)
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private static final String RAKING_CACHE = "ranking";
    private static final String POOL_CACHE = "pool";


    @Override
    public void checkParam(ActivityParam activityParam) {
        ActivityRankingParam activityRankingParam = activityParam.getActivityParam(ActivityRankingParam.class);
        List<ActivityRankingParamConfig> dayConfigList = Stream.of(activityRankingParam.getDailyConfig(), activityRankingParam.getWeeklyConfig(), activityRankingParam.getMonthlyConfig())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Assert.isFalse(CollectionUtil.isEmpty(dayConfigList));
        for (ActivityRankingParamConfig activityRankingParamConfig : dayConfigList) {
            checkActivityRankParamConfig(activityRankingParamConfig);
        }
    }

    private void checkActivityRankParamConfig(ActivityRankingParamConfig config) {
        Assert.notNull(config.getActualBonusRate());
        Assert.notNull(config.getUserBonusRate());
        Assert.checkBetween(config.getActualBonusRate(), 0, 1);
        Assert.checkBetween(config.getUserBonusRate(), 0, 1);
        Assert.notNull(config.getValidBetAmountCondition());
        Assert.notNull(config.getDisplayedNumber());
        Assert.isFalse(CollectionUtil.isEmpty(config.getBonusRangeList()));
        BigDecimal totalBonusRate = BigDecimal.ZERO;
        // 遞增檢查
        Integer prev = null;
        for (ActivityRankingBonusRangeConfig rangeConfig : config.getBonusRangeList()) {
            Assert.notNull(rangeConfig.getFrom());
            Assert.notNull(rangeConfig.getTo());
            Assert.isTrue(rangeConfig.getFrom() <= rangeConfig.getTo());
            Assert.isTrue(prev == null || prev < rangeConfig.getFrom());
            Assert.notNull(rangeConfig.getBonusRate());
            Assert.checkBetween(rangeConfig.getBonusRate(), 0, 1);
            totalBonusRate = totalBonusRate.add(rangeConfig.getBonusRate());
            prev = rangeConfig.getFrom();
        }
        Assert.checkBetween(totalBonusRate, 0, 1);
    }


    @Override
    public List<ActivityRecord> getRewardList(ActivityRewardParam activityRewardParam) {
        return this.getRewardListData(activityRewardParam, new HashSet<>());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivityRecord receiveReward(ActivityRewardParam activityRewardParam) {
        Set<Long> rankingRecordIds = new HashSet<>();
        List<ActivityRecord> rewardListData = getRewardListData(activityRewardParam, rankingRecordIds);
        activityRankingRecordService.updateByIdAndStatus(rankingRecordIds);
        return activityRecordService.receiveReward(rewardListData);
    }

    @Override
    public ActivityTypeEnum getActivityTypeEnum() {
        return ActivityTypeEnum.RANKING;
    }

    @Override
    public String getUid(ActivityRecord activityRecord, Object... others) {
        // 獎勵日結，不可能一天領兩次
        String today = LocalDate.now().toString();
        String uidStr = String.format("%s%s%s%s%s", activityRecord.getMerchantId(), activityRecord.getCurrencyEnum(), activityRecord.getMemberId(), activityRecord.getActivityTypeEnum(), today);
        return Md5Util.getMd5HexLowerCase(uidStr);
    }

    @Override
    public Boolean getUniqueActivity() {
        return Boolean.TRUE;
    }

    @Override
    public ActivityRankingInfoDTO getRankingInfo(ActivityRankingQueryParam activityRankingQueryParam) {
        ActivityRankingInfoDTO activityRankingInfo = (ActivityRankingInfoDTO) geDataFromRedis(activityRankingQueryParam, RAKING_CACHE);
        if (activityRankingInfo != null) {
            this.tryRefreshRankingInfoAsync(activityRankingQueryParam);
            return activityRankingInfo;
        }
        return getActivityRankingInfoFromDb(activityRankingQueryParam);
    }

    private void tryRefreshRankingInfoAsync(ActivityRankingQueryParam queryParam) {
        threadPoolTaskExecutor.execute(() -> {
            String lockKey = getLockRedisKey(queryParam, RAKING_CACHE);
            long lockTime = getRefreshRankingInfoLockTime(queryParam);
            if (!redisService.setIfAbsent(lockKey, queryParam.getRequestDate().toString(), lockTime)) {
                return;
            }
            this.getActivityRankingInfoFromDb(queryParam);
        });
    }

    private ActivityRankingInfoDTO getActivityRankingInfoFromDb(ActivityRankingQueryParam queryParam) {
        ActivityRankingInfoDTO result = new ActivityRankingInfoDTO();
        result.setEnableEnum(EnableEnum.FALSE);

        // 不在合法週期直接返回不繼續統計
        Activity rankingActivity = activityService.getEnableOne(queryParam.getMerchantId(), queryParam.getCurrencyEnum(), this.getActivityTypeEnum());
        if (notInValidPeriod(queryParam.getRequestDate(), rankingActivity.getCreateTime().toLocalDate(), queryParam.getRankingTypeEnum())) {
            return result;
        }

        ActivityRankingParam activityRankingParam = rankingActivity.activityParamToBean(ActivityRankingParam.class);
        ActivityRankingParamConfig activityRankingParamConfig = activityRankingParam.getConfigByEnum(queryParam.getRankingTypeEnum());
        // 未配置該類型榜單
        if (Objects.isNull(activityRankingParamConfig)) {
            return result;
        }

        result.setEnableEnum(EnableEnum.TRUE);

        String lockKey = getLockDbKey(queryParam, RAKING_CACHE);
        if (!redisService.setIfAbsent(lockKey, queryParam.getRequestDate().toString(), ActivityRedisKeyConstants.ACTIVITY_RANKING_DB_LOCK_SECONDS)) {
            return result;
        }

        DateRangeDTO dateRange = getTimeRangeByRankingType(queryParam.getRequestDate(), queryParam.getRankingTypeEnum());
        List<ReportMemberDate> sortedValidRankingMember = reportMemberDateService.getValidRankingMember(queryParam.getMerchantId(), queryParam.getCurrencyEnum(), dateRange, activityRankingParamConfig);
        int displayNumber = activityRankingParamConfig.getDisplayedNumber();

        // 取得會員頭像
        Set<Long> memberIds = sortedValidRankingMember.stream().map(ReportMemberDate::getMemberId).collect(Collectors.toSet());
        Map<Long, String> memberAvatarMap = memberService.getMemberAvatarByIds(memberIds).stream()
                .collect(Collectors.toMap(Member::getId, m -> m.getAvatar() == null ? "" : m.getAvatar(), (k1, k2) -> k1)); // avatar可能null

        List<ActivityRankingInfoDTO.ActivityRankingInfo> validMemberList = new ArrayList<>();
        int index = 1;
        for (ReportMemberDate memberReportData : sortedValidRankingMember) {
            if (index <= displayNumber) {
                ActivityRankingInfoDTO.ActivityRankingInfo entity = new ActivityRankingInfoDTO.ActivityRankingInfo();
                entity.setRanking(index++);
                entity.setMemberId(memberReportData.getMemberId());
                entity.setTotalValidBetAmount(memberReportData.getValidBetAmount());
                entity.setAvatar(memberAvatarMap.get(memberReportData.getMemberId()));
                validMemberList.add(entity);
            }
        }
        result.setRankingInfoList(validMemberList);

        String dataCacheKey = getDataCacheKey(queryParam, RAKING_CACHE);
        Long cacheTime = getCacheTimeByRankingType(queryParam.getRankingTypeEnum());
        redisService.set(dataCacheKey, result, cacheTime, TimeUnit.DAYS);
        return result;
    }


    @Override
    public ActivityRankingBonusPoolDto getBonusPoolInfo(ActivityRankingQueryParam queryParam) {
        Activity rankingActivity = activityService.getEnableOne(queryParam.getMerchantId(), queryParam.getCurrencyEnum(), this.getActivityTypeEnum());
        if (rankingActivity == null) {
            throw new ApiException(CommonCode.ACTIVITY_NOT_START);
        }
        // 不在合法週期直接返回不繼續統計
        if (notInValidPeriod(queryParam.getRequestDate(), rankingActivity.getCreateTime().toLocalDate(), queryParam.getRankingTypeEnum())) {
            return new ActivityRankingBonusPoolDto();
        }
        ActivityRankingParam activityRankingParam = rankingActivity.activityParamToBean(ActivityRankingParam.class);
        ActivityRankingParamConfig activityRankingParamConfig = activityRankingParam.getConfigByEnum(queryParam.getRankingTypeEnum());
        if (Objects.isNull(activityRankingParamConfig)) {
            return new ActivityRankingBonusPoolDto();
        }

        ActivityRankingBonusPoolDto result = (ActivityRankingBonusPoolDto) geDataFromRedis(queryParam, POOL_CACHE);
        if (result != null) {
            queryParam.setPrevPoolAmount(result.getCurrPoolAmount());
            queryParam.setPrevTime(result.getCurrTime());
            this.tryRefreshBonusPoolAsync(queryParam, activityRankingParamConfig);
        } else {
            queryParam.setPrevPoolAmount(0L);
            queryParam.setPrevTime(LocalDateTime.now().minusMinutes(10));
            result = this.getActivityRankingBonusPoolDtoFromDb(queryParam, activityRankingParamConfig);
        }

        DateRangeDTO dateRange = getTimeRangeByRankingType(queryParam.getRequestDate(), queryParam.getRankingTypeEnum());
        if (queryParam.getMemberId() != null) {
            Long userTotalValidBetAmount = reportMemberDateService.getMemberValidBetAmountSum(queryParam.getMerchantId(), queryParam.getCurrencyEnum(), queryParam.getMemberId(), dateRange.getStartDate(), dateRange.getEndDate());
            result.setTotalValidBetAmount(userTotalValidBetAmount);
        } else {
            result.setTotalValidBetAmount(0L);
        }
        return result;
    }

    private void tryRefreshBonusPoolAsync(ActivityRankingQueryParam queryParam, ActivityRankingParamConfig activityRankingParamConfig) {
        threadPoolTaskExecutor.execute(() -> {
            String lockKey = getLockRedisKey(queryParam, POOL_CACHE);
            long lockTime = ActivityRedisKeyConstants.ACTIVITY_RANKING_POOL_LOCK_SECONDS;
            if (!redisService.setIfAbsent(lockKey, queryParam.getRequestDate().toString(), lockTime)) {
                return;
            }
            this.getActivityRankingBonusPoolDtoFromDb(queryParam, activityRankingParamConfig);
        });
    }

    private ActivityRankingBonusPoolDto getActivityRankingBonusPoolDtoFromDb(ActivityRankingQueryParam queryParam, ActivityRankingParamConfig activityRankingParamConfig) {
        ActivityRankingBonusPoolDto result = new ActivityRankingBonusPoolDto();

        String lockKey = getLockDbKey(queryParam, POOL_CACHE);
        if (!redisService.setIfAbsent(lockKey, queryParam.getRequestDate().toString(), ActivityRedisKeyConstants.ACTIVITY_RANKING_DB_LOCK_SECONDS)) {
            return result;
        }
        result.setPrevTime(queryParam.getPrevTime());
        result.setPrevPoolAmount(queryParam.getPrevPoolAmount());

        DateRangeDTO dateRange = getTimeRangeByRankingType(queryParam.getRequestDate(), queryParam.getRankingTypeEnum());
        long sumOfValidBetAmount = reportMemberDateService.getSumOfValidBetAmount(queryParam.getMerchantId(), queryParam.getCurrencyEnum(), dateRange);
        // 修改奖金池计算逻辑：底池 + (实际配置 * 投注总额)
        Long basePool = activityRankingParamConfig.getBasePool() != null ? activityRankingParamConfig.getBasePool() : 0L;
        BigDecimal bonusFromBet = BigDecimal.valueOf(sumOfValidBetAmount).multiply(activityRankingParamConfig.getActualBonusRate());
        BigDecimal bonusPool = BigDecimal.valueOf(basePool).add(bonusFromBet);
        result.setCurrTime(LocalDateTime.now());
        result.setCurrPoolAmount(bonusPool.longValue());


        String dataCacheKey = getDataCacheKey(queryParam, POOL_CACHE);
        Long cacheTime = getCacheTimeByRankingType(queryParam.getRankingTypeEnum());
        redisService.set(dataCacheKey, result, cacheTime, TimeUnit.DAYS);

        return result;
    }

    private Long getRefreshRankingInfoLockTime(ActivityRankingQueryParam queryParam) {
        if (RankingTypeEnum.DAY == queryParam.getRankingTypeEnum()) {
            return ActivityRedisKeyConstants.ACTIVITY_RANKING_LOCK_DAY_SECONDS;
        } else {
            return ActivityRedisKeyConstants.ACTIVITY_RANKING_LOCK_WEEK_MONTH_SECONDS;
        }
    }


    @Override
    public List<String> getPeriodList(RankingTypeEnum rankingTypeEnum) {
        LocalDate startDate = LocalDate.now().minusDays(93);
        Set<String> set = new TreeSet<>(Comparator.reverseOrder());
        for (int i = 0; i <= 93; i++) {
            DateRangeDTO dateRange = getTimeRangeByRankingType(startDate.plusDays(i), rankingTypeEnum);
            set.add(dateRange.getStartDate() + StringPool.DASH + dateRange.getEndDate());
        }
        return new ArrayList<>(set);
    }

    @Override
    public DateRangeDTO getTimeRangeByRankingType(LocalDate localDate, RankingTypeEnum rankingTypeEnum) {
        DateRangeDTO dateRangeDTO = new DateRangeDTO();
        switch (rankingTypeEnum) {
            case DAY:
                dateRangeDTO.setStartDate(localDate);
                dateRangeDTO.setEndDate(localDate);
                return dateRangeDTO;
            case WEEK:
                dateRangeDTO.setStartDate(localDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)));
                dateRangeDTO.setEndDate(dateRangeDTO.getStartDate().plusDays(6));
                return dateRangeDTO;
            case MONTH:
                dateRangeDTO.setStartDate(localDate.withDayOfMonth(1));
                dateRangeDTO.setEndDate(localDate.with(TemporalAdjusters.lastDayOfMonth()));
                return dateRangeDTO;
            default:
                throw new ApiException(CommonCode.PARAM_INVALID);
        }
    }


    public List<ActivityRecord> getRewardListData(ActivityRewardParam activityRewardParam, Set<Long> rankingRecordIds) {
        LocalDateTime nowDateTime = LocalDateTime.now();
        MemberTokenInfoDTO memberTokenInfoDTO = activityRewardParam.getMemberTokenInfoDTO();
        Activity rankingActivity = activityService.getEnableOneNotNull(memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getCurrencyEnum(), this.getActivityTypeEnum());
        List<ActivityRankingRecord> rankingRecords = activityRankingRecordService.getMemberWaitReceiveRecord(memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getId());
        if (CollectionUtil.isEmpty(rankingRecords)) {
            return new ArrayList<>();
        }

        ActivityRecord activityRecord = activityRecordService.initActivityRecord(memberTokenInfoDTO, rankingActivity, SubActivityTypeEnum.RANKING_REWARD, nowDateTime, activityRewardParam.getRequestIp());
        long rewardAmount = 0L;
        for (ActivityRankingRecord rankingRecord : rankingRecords) {
            rewardAmount += rankingRecord.getRewardAmount();
        }

        rankingRecordIds.addAll(rankingRecords.stream().map(ActivityRankingRecord::getId).collect(Collectors.toSet()));

        checkComplete(activityRecord, rewardAmount);

        return Collections.singletonList(activityRecord);
    }

    private void checkComplete(ActivityRecord activityRecord, Long amount) {
        if (amount <= 0) return;
        activityRecord.setAmount(amount);
        activityRecord.setActivityStatusEnum(ActivityStatusEnum.WAIT_RECEIVE);
        activityRecord.setUid(this.getUid(activityRecord));
    }

    private Object geDataFromRedis(ActivityRankingQueryParam activityRankingQueryParam, String dataType) {
        return redisService.get(getDataCacheKey(activityRankingQueryParam, dataType));
    }

    private String getLockRedisKey(ActivityRankingQueryParam param, String redisLockType) {
        String periodKey = this.getHashStringByRankingType(param.getRequestDate(), param.getRankingTypeEnum());
        return String.format(ActivityRedisKeyConstants.ACTIVITY_RANKING_REDIS_LOCK_KEY, param.getMerchantId(), param.getCurrencyEnum(), periodKey, redisLockType);
    }

    private String getLockDbKey(ActivityRankingQueryParam param, String dbLockType) {
        String periodKey = this.getHashStringByRankingType(param.getRequestDate(), param.getRankingTypeEnum());
        return String.format(ActivityRedisKeyConstants.ACTIVITY_RANKING_DB_LOCK_KEY, param.getMerchantId(), param.getCurrencyEnum(), periodKey, dbLockType);
    }

    private String getDataCacheKey(ActivityRankingQueryParam param, String dataType) {
        String periodKey = this.getHashStringByRankingType(param.getRequestDate(), param.getRankingTypeEnum());
        return String.format(ActivityRedisKeyConstants.ACTIVITY_RANKING_CACHE_KEY, param.getMerchantId(), param.getCurrencyEnum(), periodKey, dataType);
    }

    private Long getCacheTimeByRankingType(RankingTypeEnum rankingTypeEnum) {
        if (RankingTypeEnum.DAY == rankingTypeEnum) {
            return ActivityRedisKeyConstants.ACTIVITY_RANKING_CACHE_DAY_TIME;
        }
        return ActivityRedisKeyConstants.ACTIVITY_RANKING_CACHE_WEEK_MONTH_TIME;
    }


    private String getHashStringByRankingType(LocalDate localDate, RankingTypeEnum rankingTypeEnum) {
        DateRangeDTO dateRange = getTimeRangeByRankingType(localDate, rankingTypeEnum);
        return dateRange.getStartDate() + StringPool.DASH + dateRange.getEndDate();
    }

    public boolean notInValidPeriod(LocalDate today, LocalDate activityCreateDate, RankingTypeEnum rankingTypeEnum) {
        DateRangeDTO firstPeriod = getTimeRangeByRankingType(activityCreateDate, rankingTypeEnum);
        return !today.isAfter(firstPeriod.getEndDate());
    }

}
