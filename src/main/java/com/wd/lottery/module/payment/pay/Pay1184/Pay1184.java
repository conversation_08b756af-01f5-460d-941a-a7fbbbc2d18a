package com.wd.lottery.module.payment.pay.Pay1184;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1184, VCpay支付代付（cgptf016）
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class Pay1184 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1184][VCpay支付]";
    private final String DF_NAME = "[SFZF-1184][VCpay代付]";
    private final String BALANCE = "[VCpay代付 餘額查詢]";
    private static final String SUCCESS_CODE = "200";
    private static final String CALLBACK_SUCCESS_CODE = "1";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "out_trade_no";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "out_trade_no";

    private final PaymentCommonService paymentCommonService;


    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/wd/save")
            .rechargeApiPath("/pay/save")
            .queryBalanceApiPath("/pay/balance")
            .withdrawNotifyPrint("success")
            .rechargeNotifyPrint("success")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String tradeType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "tradeType",
                PayRequestEnum.DEPOSIT);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("app_id", paymentMerchant.getPaymentMerchantCode());
        params.put("nonce_str", PayUtils.getRandomString(30));
        params.put("trade_type", tradeType);
        params.put("order_amount", doDepositDto.getAmount());
        params.put("out_trade_no", orderNo);
        params.put("notify_url", doDepositDto.getDepositNotifyUrl());
        params.put("back_url", doDepositDto.getDepositNotifyUrl());


        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr ,sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);
        try {
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String payUrl = result.get("pay_url").asText();
                resultDto.setThirdOrderNo(result.get("trade_no").asText());
                resultDto.setRedirectUrl(payUrl);
                log.info("{}[{}] 訂單號:{} 請求成功:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, result);
            } else {
                String message = result.get("msg").asText();
                resultDto.setErrorMsg(message);
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{} 狀態:{} 請求失敗:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, status, result);

            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);

        String status = resMap.get("trade_state");
        String sign = resMap.remove("sign");


        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            resMap.remove("code");//不參與簽名
            resMap.remove("msg");//不參與簽名
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 算出的sign:{} , 收到的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, checkSign ,sign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(Long.valueOf(resMap.get("order_amount")));
            depositPayDto.setFee(Long.valueOf(resMap.get("order_fee")));
            log.info("{}[{}] 回調成功 訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doBalanceDto.getPaymentMerchant();
        String payKey = doBalanceDto.getPaymentMerchant().getPrivateKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("app_id", paymentMerchant.getPaymentMerchantCode());
        params.put("nonce_str", PayUtils.getRandomString(30));

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}]  待加密字串:{} , 加密後:{}", BALANCE,PayRequestEnum.BALANCE.getText(), paramStr, sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(),params);
        try {
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("balance").asText();
                resultDto.setBalance(Long.valueOf(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                String message = result.get("msg").asText();
                resultDto.setMessage(message);
                log.info("{}[{}] 失敗:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", DF_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String payType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payType",
                PayRequestEnum.WITHDRAW);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("app_id", paymentMerchant.getPaymentMerchantCode());
        params.put("nonce_str", PayUtils.getRandomString(30));
        params.put("trade_type", payType);
        params.put("order_amount", doWithdrawDto.getAmount());
        params.put("out_trade_no",orderNo);
        params.put("notify_url", doWithdrawDto.getWithdrawNotifyUrl());
        params.put("bank_code", doWithdrawDto.getBankCodeDto().getBankCode());
        params.put("bank_owner", doWithdrawDto.getUserName());
        params.put("bank_account", doWithdrawDto.getUserBankAccount());
        params.put("identity_type", "PHONE");
        params.put("identity", doWithdrawDto.getMobile() == null ? "**********" : doWithdrawDto.getMobile());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr ,sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);
        String status = result.get("code").asText();
        if (SUCCESS_CODE.equalsIgnoreCase(status)) {
            resultDto.setThirdOrderNo(result.get("trade_no").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("{}  訂單號:{} 請求成功:{}", DF_NAME, orderNo, result);
        } else {
            String message = result.get("msg").asText();
            resultDto.setErrorMsg(message);
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("{}[{}] 訂單號:{}  狀態:{} 請求失敗:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status,
                    result);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", DF_NAME, withdrawNotifyText, orderNo, resMap);

        String status = resMap.get("trade_state");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            resMap.remove("code");//不參與簽名
            resMap.remove("msg");//不參與簽名
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{}  ,算出的sign:{} ,收到的sign:{},",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, checkSign, sign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(Long.valueOf(resMap.get("order_amount")));
            withdrawPayDTO.setFee(Long.valueOf(resMap.get("order_fee")));
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  代付DTO:{}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }
}
