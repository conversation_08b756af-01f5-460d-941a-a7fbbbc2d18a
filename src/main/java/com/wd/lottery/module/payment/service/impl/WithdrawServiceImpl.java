package com.wd.lottery.module.payment.service.impl;


import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.module.payment.constants.PaymentConstants;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import org.springframework.util.Assert;
import com.wd.lottery.module.payment.entity.PaymentChannel;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.WithdrawService;
import com.wd.lottery.module.payment.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class WithdrawServiceImpl implements WithdrawService {

    private final Map<String, PaymentWithdrawHandler> paymentWithdrawHandlerMap;

    @Override
    public WithdrawResultDTO doWithdraw(DoWithdrawDTO doWithdrawDto, PaymentWithdrawHandler payHandler) {
        String withdrawText = PayRequestEnum.WITHDRAW.getText();
        WithdrawResultDTO resultDto = new WithdrawResultDTO();
        String orderNo = doWithdrawDto.getOrderNo();
        resultDto.setOrderNo(orderNo);

        PaymentChannel paymentChannel = doWithdrawDto.getPaymentChannel();
        String channelName = paymentChannel.getName();
        doWithdrawDto.setWithdrawApi(paymentChannel.getWithdrawDomain() + payHandler.getConfig().withdrawApiPath());
        try {
            log.info("[{}][{}][{}] 開始", channelName, withdrawText, orderNo);
            resultDto = payHandler.generateWithdrawRequest(doWithdrawDto, resultDto);
        } catch (Exception e) {
            log.error("[{}][{}][{}][金流發生錯誤]", channelName, withdrawText, orderNo, e);
            // 请求三方超时处理
            if (isTimeOutRequest(e)) {
                return WithdrawResultDTO.timeOutDTO();
            }
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.SERVER_ERROR);
            resultDto.setErrorMsg("系统发生错误，请与客服联系");
            return resultDto;
        } finally {
            log.info("[{}][{}][{}] 完成", channelName, withdrawText, orderNo);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO doWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto, PaymentWithdrawHandler payHandler) {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        String channelName = doWithdrawNotifyDto.getPaymentChannel().getName();
        WithdrawPayDTO withdrawPayDto = new WithdrawPayDTO();
        try {
            log.info("[{}][{}][三方回調內容] [Request Body:{}]", channelName, withdrawNotifyText, doWithdrawNotifyDto.getRequestMap());
            log.info("[{}][{}][三方回調內容] [Request Header:{}]", channelName, withdrawNotifyText, doWithdrawNotifyDto.getHeaderMap());
            log.info("[{}][{}] 開始", channelName, withdrawNotifyText);

            withdrawPayDto = payHandler.handleWithdrawNotify(doWithdrawNotifyDto);
            String orderNo = withdrawPayDto.getOrderNo();
            String withdrawPayDtoJson = JsonUtils.toJson(withdrawPayDto);
            log.info("[{}][{}][{}][回调结果 = {}], 商戶回調: {}", channelName, withdrawNotifyText, orderNo, withdrawPayDtoJson, doWithdrawNotifyDto.getRequestMap());
            return withdrawPayDto;
        } catch (Exception e) {
            log.error("[{}][{}][金流發生錯誤]", channelName, withdrawNotifyText, e);
            withdrawPayDto.setWithdrawStatusEnum(WithdrawStatusEnum.SERVER_ERROR);
            withdrawPayDto.setErrorMsg("系统发生错误，请与客服联系");
            return withdrawPayDto;
        }
    }


    private boolean isTimeOutRequest(Throwable throwable) {
        return "timeout".equals(throwable.getMessage()) || CommonCode.THIRD_REQUEST_TIMEOUT.getMessage().equals(throwable.getMessage());
    }

    /**
     * 根據付款處理器ID獲取出款處理器
     *
     * @param paymentHandlerId 付款處理器ID
     * @return 出款處理器
     */
    @Override
    public PaymentWithdrawHandler getWithdrawHandler(Long paymentHandlerId) {
        return paymentWithdrawHandlerMap.get(PaymentConstants.PAYMENT_HANDLER_IMPL_PREFIX + paymentHandlerId);
    }

    /**
     * 輪詢出款訂單狀態
     *
     * @param doPaymentPollingDTO 輪詢參數
     * @return 輪詢結果
     */
    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrderStatus(DoPaymentPollingDTO doPaymentPollingDTO) {
        try {
            PaymentChannel paymentChannel = doPaymentPollingDTO.getPaymentChannel();
            Assert.notNull(paymentChannel, "支付渠道不能為空");

            PaymentWithdrawHandler payHandler = getWithdrawHandler(paymentChannel.getPaymentHandlerId());
            Assert.notNull(payHandler, "出款處理器不能為空");
            OnlineOrderPollingResultDTO resultDTO = payHandler.pollingWithdrawOrder(doPaymentPollingDTO);
            if (Objects.isNull(resultDTO)) {
                log.info("payer:{} 未实现出款订单轮询", paymentChannel.getPaymentHandlerId());
                return new OnlineOrderPollingResultDTO();
            }
            return resultDTO;
        } catch (Exception e) {
            log.error("輪詢出款訂單發生錯誤 doPaymentPollingDTO:{}", doPaymentPollingDTO, e);
            return new OnlineOrderPollingResultDTO();
        }
    }
}
