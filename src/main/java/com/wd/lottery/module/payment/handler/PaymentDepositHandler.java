package com.wd.lottery.module.payment.handler;


import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;

import java.util.Optional;

public interface PaymentDepositHandler extends PaymentBaseHandler {

    /**
     * 代收回調訂單參數
     *
     * @return order key
     */
    String depositNotifyOrderKey();

    /**
     * 代收下單參數組成, call api, 解析response
     *
     * @return dto
     */
    DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto);

    /**
     * 處理代收回調API回傳結果
     *
     * @return 是否處理成功 dto
     */
    DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception;

    /**
     * 輪詢充值訂單狀態
     * 呼叫三方查詢API，查詢充值訂單的狀態
     * @return 輪詢結果
     */
    OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception;

    default String parseDepositOrderNo(DoPaymentNotifyDTO notifyDTO) {
        return Optional.ofNullable(notifyDTO)
                .map(DoPaymentNotifyDTO::getRequestMap)
                .map(requestMap -> requestMap.get(this.depositNotifyOrderKey()))
                .orElse("");
    }
}
