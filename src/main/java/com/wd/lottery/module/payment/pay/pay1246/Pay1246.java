package com.wd.lottery.module.payment.pay.pay1246;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.RequestUtil;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1246 ,TPYpay支付代付 (PHP) (cgptf016)
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1246 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1246][TPYpay支付]";
    private final String DF_NAME = "[SFZF-1246][TPYpay代付]";
    private final String BALANCE = "[SFZF-1246 餘額查詢]";
    private static final String SUCCESS_CODE = "1";
    private static final String CALLBACK_SUCCESS_CODE = "1";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "orderid";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "orderid";

    private final PaymentCommonService paymentCommonService;
    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/payout/json2")
            .rechargeApiPath("/pay/json2")
            .queryBalanceApiPath("/query/balance")
            .withdrawNotifyPrint("success")
            .rechargeNotifyPrint("success")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String payType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payType", PayRequestEnum.DEPOSIT);


        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mid", doDepositDto.getPaymentMerchant().getPaymentMerchantCode());
        params.put("orderid", orderNo);
        params.put("currency", "PHP");
        params.put("amount", PayUtils.getMoney(doDepositDto.getAmount(), 2));
        params.put("paytype", payType);
        params.put("IP", RequestUtil.getRequestIpFromRequest());
        params.put("notifyurl", doDepositDto.getDepositNotifyUrl());
        params.put("returnurl", doDepositDto.getDepositNotifyUrl());
        params.put("version", "3");
        params.put("note", "note");
        params.put("realname", doDepositDto.getMemberName());
        params.put("payerID", doDepositDto.getMemberName());

        LinkedHashMap<String, Object> signMap = new LinkedHashMap<>();
        signMap.put("mid", params.get("mid"));
        signMap.put("orderid", params.get("orderid"));
        signMap.put("amount", params.get("amount"));
        signMap.put("note", params.get("note"));
        signMap.put("paytype", params.get("paytype"));
        signMap.put("notifyurl", params.get("notifyurl"));
        signMap.put("returnurl", params.get("returnurl"));
        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(signMap);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}]{} 產生簽名字串加密前 [{}] 加密後 [{}]", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, sign);
        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);
            String status = result.get("status").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String payUrl = result.get("api_jump_url").asText();
                resultDto.setThirdOrderNo("ojid");
                resultDto.setRedirectUrl(payUrl);
                log.info("{} 請求成功 訂單號:{} 回傳:{}", ZF_NAME, orderNo, result);
                log.info("{}[{}]訂單號:{} 跳轉地址:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, payUrl);
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{} 請求失敗 訂單號:{} 回傳:{}", ZF_NAME, orderNo, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        String status = resMap.get("status");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            LinkedHashMap<String, String> signMap = new LinkedHashMap<>();
            signMap.put("mid", resMap.get("mid"));
            signMap.put("status", resMap.get("status"));
            signMap.put("id", resMap.get("id"));
            signMap.put("orderid", resMap.get("orderid"));
            signMap.put("orderamount", resMap.get("orderamount"));
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(signMap);
            paramStr += "&" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.get("orderamount")));
            depositPayDto.setFee(0L);
            log.info("{}[{}]訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mid", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());

        try {
            JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params);
            String status = result.get("status").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("balance").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                String message = result.get("msg").asText();
                resultDto.setMessage(message);
                log.info("{}[{}] 失敗:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", DF_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mid", paymentMerchant.getPaymentMerchantCode());
        params.put("orderid", orderNo);
        params.put("amount", PayUtils.getMoney(doWithdrawDto.getAmount(), 2));
        params.put("title", PayUtils.getRandomString(8));
        params.put("bankname", doWithdrawDto.getBankCodeDto().getBankName());
        params.put("bankcardno", doWithdrawDto.getUserBankAccount());
        params.put("bankuser", doWithdrawDto.getUserName());
        params.put("bankbranch", "branch");
        params.put("Notifyurl", doWithdrawDto.getWithdrawNotifyUrl());

        LinkedHashMap<String, Object> signMap = new LinkedHashMap<>();
        signMap.put("amount", params.get("amount"));
        signMap.put("bankcardno", params.get("bankcardno"));
        signMap.put("mid", params.get("mid"));
        signMap.put("orderid", params.get("orderid"));

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(signMap) + "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}] 訂單號:{} 加密後:{} 待加密字串:{} ,",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, sign, paramStr);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);
        String status = result.get("code").asText();
        if (SUCCESS_CODE.equalsIgnoreCase(status)) {
            resultDto.setThirdOrderNo("none");
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("{}  訂單號:{} 請求成功:{}", DF_NAME, orderNo, result);
        } else {
            String message = result.get("msg").asText();
            resultDto.setErrorMsg(message);
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("{}[{}] 訂單號:{}  狀態:{} 請求失敗:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status,
                    result);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();

        String status = resMap.get("status");
        String sign = resMap.remove("sign");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            LinkedHashMap<String, Object> signMap = new LinkedHashMap<>();
            signMap.put("mid", resMap.get("mid"));
            signMap.put("status", resMap.get("status"));
            signMap.put("id", resMap.get("id"));
            signMap.put("orderid", resMap.get("orderid"));
            signMap.put("orderamount", resMap.get("orderamount"));
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(signMap);
            paramStr += "&" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("orderamount")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  = {}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }
}
