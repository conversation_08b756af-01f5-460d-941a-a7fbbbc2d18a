package com.wd.lottery.module.payment.pay.pay1079;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1079, Wepay支付/代付（cgptf008）
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1079 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1079][Wepay支付]";
    private final String DF_NAME = "[SFZF-1079][Wepay代付]";
    private final String BALANCE = "[Wepay 餘額查詢]";
    private static final String SUCCESS_CODE = "200";
    private static final String CALLBACK_SUCCESS_CODE = "1";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "orderNo";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "orderNo";
    private static final String QUERY_DEPOSIT_API_PATH = "/collect/query";
    private static final String QUERY_WITHDRAW_API_PATH = "/pay/query";

    private final PaymentCommonService paymentCommonService;

    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/pay/create")
            .rechargeApiPath("/collect/create")
            .queryBalanceApiPath("/order/balance")
            .withdrawNotifyPrint("success")
            .rechargeNotifyPrint("success")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String channel = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "channel", PayRequestEnum.DEPOSIT);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchId", doDepositDto.getPaymentMerchant().getPaymentMerchantCode());
        params.put("passageId", channel);
        params.put("amount", PayUtils.getMoney(doDepositDto.getAmount()));
        params.put("orderNo", orderNo);
        params.put("notifyUrl", doDepositDto.getDepositNotifyUrl());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        log.info("{}[{}]{} 產生簽名字串加密前 [{}] ", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr);

        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}][{}]{} 產生簽名加密後 [{}]  ", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, sign);
        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                JsonNode data = result.get("data");
                String payUrl = data.get("payUrl").asText();
                String thirdOrderNo = data.get("tradeNo").asText();
                resultDto.setThirdOrderNo(thirdOrderNo);
                resultDto.setRedirectUrl(payUrl);
                log.info("{} 請求成功 訂單號:{} ", ZF_NAME, orderNo);
                log.info("{}[{}]訂單號:{} 跳轉地址:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, payUrl);
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{} 請求失敗 訂單號:{} 请求状态false", ZF_NAME, orderNo);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);
        log.info("{} 訂單號:{} 回調驗籤 密鑰: {}", ZF_NAME, orderNo, payKey );

        String status = resMap.get("payStatus");
        String sign = resMap.remove("sign");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.get("amount")));
            depositPayDto.setFee(0L);
            log.info("{}[{}]訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        String orderNo = String.valueOf(doPaymentPollingDTO.getOrderId());
        String payKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchId", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("orderNo", orderNo);

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        params.put("sign", sign);
        log.info("{}][{}]{} 待加密字符串:{} 加密後:{}  ", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, sign);


        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getDepositDomain() + QUERY_DEPOSIT_API_PATH;

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        try {
            JsonNode result = paymentCommonService.doJsonPost(apiUrl, params);
            String status = result.get("code").asText();
            String payStateCode = result.get("data").get("payStatus").asText();

            log.info("{} 查詢結果 訂單號:{} 狀態碼:{} 回傳:{}", ZF_NAME, orderNo, payStateCode, result);
            if (SUCCESS_CODE.equalsIgnoreCase(status) && "1".equalsIgnoreCase(payStateCode)) {
                String amount = result.get("data").get("amount").asText();
                resultDTO.setIsSuccess(true);
                resultDTO.setAmount(PayUtils.getCent(amount));
                log.info("{} 查詢成功 訂單號:{} 回傳:{}", ZF_NAME, orderNo, result);
            } else {
                log.info("{} 查詢訂單未完成 訂單號:{} 狀態:{} 回傳:{}", ZF_NAME, orderNo, payStateCode, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 查詢請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDTO;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String withdrawCh = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "withdrawCh", PayRequestEnum.WITHDRAW);
        String currency = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "currency", PayRequestEnum.WITHDRAW);

        log.info("{} 訂單號:{} 密鑰:{}", DF_NAME, orderNo, payKey);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchId", paymentMerchant.getPaymentMerchantCode());
        params.put("passageId", withdrawCh);
        params.put("orderNo", orderNo);
        params.put("account", doWithdrawDto.getUserBankAccount());
        params.put("userName", doWithdrawDto.getMemberName());

        if ("INR".equalsIgnoreCase(currency)) {
            params.put("ifsc", doWithdrawDto.getAdditional());
        } else if ("THB".equalsIgnoreCase(currency)){
            params.put("ifsc", doWithdrawDto.getBankCodeDto().getBankCode());
        }
        params.put("amount", PayUtils.getMoney(doWithdrawDto.getAmount()));
        params.put("notifyUrl", doWithdrawDto.getWithdrawNotifyUrl());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);

        log.info("{}[{}] 訂單號:{} 產生簽名字串加密前 [{} ", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr);
        log.info("{}[{}] 訂單號:{} 產生簽名加密後 [{}]  ", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);

        String status = result.get("code").asText();
        if (SUCCESS_CODE.equalsIgnoreCase(status)){
            String thirdOrderNo = result.get("data").get("id").asText();
            resultDto.setThirdOrderNo(thirdOrderNo);
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("{} 請求成功 訂單號:{} ", DF_NAME, orderNo);
        } else {
            resultDto.setErrorMsg(result.get("msg").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("{}[{}] 訂單號:{} 請求失敗 请求状态false message:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo,
                    result.get("msg").asText());
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", DF_NAME, withdrawNotifyText, orderNo, resMap);
        log.info("{} 訂單號:{} 回調驗籤 密鑰: {}", DF_NAME, orderNo, payKey );

        String status = resMap.get("payStatus");
        String sign = resMap.remove("sign");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("amount")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  = {}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        String orderNo = String.valueOf(doPaymentPollingDTO.getOrderId());
        String payKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchId", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("orderNo", orderNo);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_WITHDRAW_API_PATH;

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        params.put("sign", sign);
        log.info("{}][{}]{} 待加密字符串:{} 加密後:{}  ", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, sign);

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        try {
            JsonNode result = paymentCommonService.doJsonPost(apiUrl, params);
            String status = result.get("code").asText();
            String payStateCode = result.get("data").get("payStatus").asText();
            log.info("{} 查詢結果 訂單號:{} 狀態碼:{} 回傳:{}", DF_NAME, orderNo, payStateCode, result);
            if (SUCCESS_CODE.equalsIgnoreCase(status) && "1".equalsIgnoreCase(payStateCode)) {
                resultDTO.setIsSuccess(true);
                resultDTO.setAmount(doPaymentPollingDTO.getAmount());
                log.info("{} 查詢成功 訂單號:{} 回傳:{}", DF_NAME, orderNo, result);
            } else {
                log.info("{} 查詢訂單未完成 訂單號:{} 狀態:{} 回傳:{}", DF_NAME, orderNo, payStateCode, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 查詢請求異常", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDTO;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String payKey = doBalanceDto.getPaymentMerchant().getPrivateKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchId", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}]產生簽名字串加密前 [{}] ", BALANCE,PayRequestEnum.BALANCE.getText(), paramStr);
        log.info("{}[{}]產生簽名加密後 [{}]  ", BALANCE, PayRequestEnum.BALANCE.getText(),sign);
        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(),params);

            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)){
                String balance = result.get("data").get("balanceUsable").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功 = {}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                resultDto.setMessage("error");
                log.info("{}[{}] 失敗 = {}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", DF_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }
}
