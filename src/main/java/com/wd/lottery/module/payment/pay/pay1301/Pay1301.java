package com.wd.lottery.module.payment.pay.pay1301;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * SFZF-1301, Mepay
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1301 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1301][Mepay支付]";
    private final String DF_NAME = "[SFZF-1301][Mepay代付]";
    private final String BALANCE = "[SFZF-1301][Mepay餘額查詢]";
    private static final String SUCCESS_CODE = "0";
    private static final String CALLBACK_SUCCESS_CODE = "2";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "mchOrderNo";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "mchOrderNo";
    private static final String QUERY_ORDER_WITHDRAW_PATH = "/api/transfer/query";
    private static final String QUERY_ORDER_DEPOSIT_PATH = "/api/pay/query";
    private final PaymentCommonService paymentCommonService;
    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/api/transferOrder")
            .rechargeApiPath("/api/pay/unifiedOrder")
            .queryBalanceApiPath("/api/all/queryBalance")
            .withdrawNotifyPrint("success")
            .rechargeNotifyPrint("success")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String priKey = paymentMerchant.getPrivateKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.DEPOSIT);
        String payMethod = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payMethod", PayRequestEnum.DEPOSIT);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", paymentMerchant.getPaymentMerchantCode());
        params.put("appId", appId);
        params.put("mchUserId", doDepositDto.getMemberId());
        params.put("mchOrderNo", orderNo);
        params.put("currency", "PKR");
        params.put("countryId", "10033");
        params.put("subject", "recharge");
        params.put("body", "recharge" + doDepositDto.getAmount());
        params.put("amount", doDepositDto.getAmount());
        params.put("notifyUrl", doDepositDto.getDepositNotifyUrl());
        params.put("reqTime", System.currentTimeMillis());
        params.put("version", "1.0");
        params.put("signType", "MD5");
        params.put("payMethod", payMethod);
        params.put("extParam", doDepositDto.getDepositNotifyUrl());
        Map<String, String> extParamMap = new TreeMap<>();
        extParamMap.put("mobile", "***********");
        extParamMap.put("accountNo", PayUtils.getRandomInt(13));
        params.put("extParam", JSONUtil.toJsonStr(extParamMap));

        try {
            String sign = this.createSign(params, priKey, ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo);
            params.put("sign", sign);
            JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String payUrl = result.get("data").get("payData").asText();
                resultDto.setThirdOrderNo(result.get("data").get("mchOrderNo").asText());
                resultDto.setRedirectUrl(payUrl);
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);

            }
            log.info("{} 請求 訂單號:{} 狀態:{} 回傳:{}", ZF_NAME, orderNo, status, result);
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        String status = resMap.get("state");
        String sign = resMap.remove("sign");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String verifySignature = this.createSign(new TreeMap<>(resMap), payKey, ZF_NAME, depositNotifyText, orderNo);
            if (!verifySignature.equals(sign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 收到的sign:{}}", ZF_NAME, depositNotifyText, orderNo, sign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(Long.valueOf(resMap.get("amount")));
            depositPayDto.setFee(0L);
            log.info("[{}[{}]訂單號:{} 回調成功 回傳：{}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) {
        String priKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doPaymentPollingDTO.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.DEPOSIT);
        long orderNo = doPaymentPollingDTO.getOrderId();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("appId", appId);
        params.put("timestamp", System.currentTimeMillis());
        params.put("version", "1.0");
        params.put("signType", "MD5");
        params.put("mchOrderNo", orderNo);

        String sign = this.createSign(params, priKey, ZF_NAME, PayRequestEnum.DEPOSIT.getText(), String.valueOf(orderNo));
        params.put("sign", sign);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_ORDER_DEPOSIT_PATH;
        JsonNode result = paymentCommonService.doJsonPost(apiUrl, params);

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        String status = result.get("code").asText();
        String state = result.get("data").get("state").asText();
        if (!SUCCESS_CODE.equalsIgnoreCase(status) || !state.equalsIgnoreCase(CALLBACK_SUCCESS_CODE)) {
            return resultDTO;
        }
        Long amount = Long.valueOf(result.get("data").get("amount").asText());
        resultDTO.setAmount(amount);
        resultDTO.setIsSuccess(true);
        return resultDTO;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String priKey = doBalanceDto.getPaymentMerchant().getPrivateKey();
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doBalanceDto.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.BALANCE);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());
        params.put("appId", appId);
        params.put("reqTime", System.currentTimeMillis());
        params.put("version", "1.0");
        params.put("countryId", "10033");
        params.put("signType", "MD5");
        String sign = this.createSign(params, priKey, BALANCE, PayRequestEnum.BALANCE.getText(), null);
        params.put("sign", sign);
        try {
            JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("data").get("balance").get(0).get("availableBalance").asText();
                resultDto.setBalance(Long.valueOf(balance));
                resultDto.setSuccess(true);
            } else {
                resultDto.setMessage(result.get("msg").asText());
            }
            log.info("[{}[{}] result:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", BALANCE, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String priKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.WITHDRAW);
        String accountType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "accountType", PayRequestEnum.WITHDRAW);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", paymentMerchant.getPaymentMerchantCode());
        params.put("appId", appId);
        params.put("mchOrderNo", orderNo);
        params.put("amount", doWithdrawDto.getAmount());
        params.put("currency", "PKR");
        params.put("countryId", "10033");
        params.put("payMethod", "ALL");
        params.put("accountNo", doWithdrawDto.getUserBankAccount());
        params.put("accountType", accountType);
        params.put("accountName", doWithdrawDto.getUserName());
        params.put("bankName", doWithdrawDto.getBankCodeDto().getBankName());
        params.put("transferDesc", doWithdrawDto.getOrderNo());
        params.put("notifyUrl", doWithdrawDto.getWithdrawNotifyUrl());
        params.put("reqTime", System.currentTimeMillis());
        params.put("version", "1.0");
        params.put("signType", "MD5");
        Map<String, String> extParamMap = new TreeMap<>();
        extParamMap.put("mobile", "***********");
        extParamMap.put("accountNo", PayUtils.getRandomInt(13));
        params.put("extParam", JSONUtil.toJsonStr(extParamMap));

        String sign = this.createSign(params, priKey, DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo);
        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                resultDto.setThirdOrderNo(result.get("data").get("mchOrderNo").asText());
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            }
            log.info("{}[{}] 訂單號:{} 請求結果 狀態:{} 回傳:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status, result);
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 提現請求異常", ZF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        String priKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);

        String status = resMap.get("state");
        String sign = resMap.remove("sign");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String verifySignature = this.createSign(new TreeMap<>(resMap), priKey, DF_NAME, withdrawNotifyText, orderNo);
            if (!verifySignature.equals(sign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 收到的sign:[{}]", DF_NAME, withdrawNotifyText, orderNo, sign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(Long.valueOf(resMap.get("amount")));
            withdrawPayDTO.setFee(0L);
            log.info("{}[{}] 訂單號:{} 回調成功 回傳： {}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) {
        String priKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doPaymentPollingDTO.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.DEPOSIT);
        long orderNo = doPaymentPollingDTO.getOrderId();
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("appId", appId);
        params.put("reqTime", System.currentTimeMillis());
        params.put("mchOrderNo", orderNo);
        params.put("version", "1.0");
        params.put("signType", "MD5");
        String sign = this.createSign(params, priKey, DF_NAME, PayRequestEnum.WITHDRAW.getText(), String.valueOf(orderNo));
        params.put("sign", sign);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_ORDER_WITHDRAW_PATH;
        JsonNode result = paymentCommonService.doJsonPost(apiUrl, params);

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        String status = result.get("code").asText();
        String state = result.get("data").get("state").asText();
        if (!SUCCESS_CODE.equalsIgnoreCase(status) || !state.equalsIgnoreCase(CALLBACK_SUCCESS_CODE)) {
            return resultDTO;
        }
        String amount = result.get("data").get("amount").asText();
        resultDTO.setAmount(Long.valueOf(amount));
        resultDTO.setIsSuccess(true);
        return resultDTO;
    }

    private String createSign(Map<String, Object> reqMap, String payKey, String payName, String payEnum, String orderId) {
        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(reqMap) + "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("[{}][{}][{}] 產生簽名字串加密前 [{}] 加密後 [{}]", orderId, payName, payEnum, paramStr, sign);
        return sign;
    }
}
