package com.wd.lottery.module.payment.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.payment.dto.payment_merchant_offline.*;
import com.wd.lottery.module.payment.dto.payment_merchant_offline.*;
import com.wd.lottery.module.payment.entity.PaymentMerchantOffline;

import java.util.List;

public interface PaymentMerchantOfflineService {

    PaymentMerchantOffline findByIdEnableNotNull(Long id, String bankName, String bankAccount);

    List<PaymentMerchantOffline> findListByMerchantIdAndPayName(Long merchantId, String payName, CurrencyEnum currencyEnum);

    List<PaymentMerchantOffline> findByMerchantId(Long merchantId, CurrencyEnum currencyEnum, List<EnableEnum> enableEnumList);

    List<PaymentMerchantOffline> findByIdsAndMerchantIdAndEnable(List<Long> ids, Long merchantId, EnableEnum enableEnum, CurrencyEnum currencyEnum);

    List<PaymentMerchantOffline> findByIdsAndMerchantId(List<Long> ids, Long merchantId, CurrencyEnum currencyEnum);

    Page<PaymentMerchantOfflineDTO> search(SearchPaymentMerchantOfflineDTO searchPaymentMerchantOfflineDto, Long merchantId, CurrencyEnum currencyEnum);

    List<String> offlineOptions(Long merchantId, CurrencyEnum currencyEnum);

    List<PaymentMerchantOfflineOptionsDTO> findAllOptions(Long merchantId, CurrencyEnum currencyEnum);

    Long saveData(SavePaymentMerchantOfflineDTO savePaymentMerchantOfflineDto);

    Long updateData(EditorPaymentMerchantOfflineDTO editorPaymentMerchantOfflineDto);

    Long deleteData(Long id);

    void clearLimit();

    void addDailyDepositLimit(Long id, Long money);

}
