package com.wd.lottery.module.payment.pay.pay1170;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1170, upay支付代付(cgptf016)
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1170 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1170][upay支付]";
    private final String DF_NAME = "[SFZF-1170][upay代付]";
    private final String BALANCE = "[SFZF-1170 upay]";
    private static final String SUCCESS_CODE = "200";
    private static final String CALLBACK_SUCCESS_CODE = "success";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "merchant_orderno";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "merchant_orderno";
    private static final String QUERY_ORDER_API_PATH = "/mcapi/query";

    private final PaymentCommonService paymentCommonService;

    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/mcapi/prepaidpayorder")
            .rechargeApiPath("/mcapi/prepaidorder/v2")
            .queryBalanceApiPath("/mcapi/balance")
            .withdrawNotifyPrint("success")
            .rechargeNotifyPrint("success")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String payType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payType",
                PayRequestEnum.DEPOSIT);


        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchantid", paymentMerchant.getPaymentMerchantCode());
        params.put("merchant_orderno", orderNo);
        params.put("passage_code", payType);
        params.put("currency", "INR");
        params.put("amount", PayUtils.getMoney(doDepositDto.getAmount(), 2));
        params.put("notify_url", doDepositDto.getDepositNotifyUrl());
        params.put("callback_url", doDepositDto.getDepositNotifyUrl());
        params.put("payer_id", doDepositDto.getMobile());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doPost(doDepositDto.getDepositApi(), params);
        try {
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String payUrl = result.get("data").get("payurl").asText();
                resultDto.setThirdOrderNo(result.get("data").get("orderno").asText());
                resultDto.setRedirectUrl(payUrl);
                log.info("{}[{}] 訂單號:{} 請求成功:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, result);
            } else {
                String message = result.get("errmsg").asText();
                resultDto.setErrorMsg(message);
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{} 狀態:{} 請求失敗:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, status, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);

        String status = resMap.get("status");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 算出的sign:{} , 收到的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, checkSign, sign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.get("amount")));
            depositPayDto.setFee(PayUtils.getCent(resMap.get("fee")));
            log.info("{}[{}]訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        String orderNo = String.valueOf(doPaymentPollingDTO.getOrderId());
        String payKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchantid", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("merchant_orderno", orderNo);
        params.put("type", "collect");

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, sign);
        params.put("sign", sign);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getDepositDomain() + QUERY_ORDER_API_PATH;

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        try {
            JsonNode result = paymentCommonService.doPost(apiUrl, params);
            String status = result.get("code").asText();
            String payStateCode = result.get("data").get("status").asText();

            log.info("{} 查詢結果 訂單號:{} 狀態碼:{} 回傳:{}", ZF_NAME, orderNo, payStateCode, result);
            if (SUCCESS_CODE.equalsIgnoreCase(status) && "success".equalsIgnoreCase(payStateCode)) {
                String amount = result.get("data").get("amount").asText();
                resultDTO.setIsSuccess(true);
                resultDTO.setAmount(PayUtils.getCent(amount));
                log.info("{} 查詢成功 訂單號:{} 回傳:{}", ZF_NAME, orderNo, result);
            } else {
                log.info("{} 查詢訂單未完成 訂單號:{} 狀態:{} 回傳:{}", ZF_NAME, orderNo, payStateCode, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 查詢請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDTO;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String payKey = doBalanceDto.getPaymentMerchant().getPrivateKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchantid", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}]  待加密字串:{} , 加密後:{}", BALANCE, PayRequestEnum.BALANCE.getText(), paramStr, sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doPost(doBalanceDto.getBalanceApi(), params);
        try {
            String status = result.get("code").asText();

            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("data").get("balance").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                String message = result.get("errmsg").asText();
                resultDto.setMessage(message);
                log.info("{}[{}] 失敗:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", DF_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();
        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String payOutType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payOutType",
                PayRequestEnum.WITHDRAW);

        log.info("{} 訂單號:{} 密鑰:{}", DF_NAME, orderNo, payKey);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchantid", paymentMerchant.getPaymentMerchantCode());
        params.put("passage_code", payOutType);
        params.put("merchant_orderno", orderNo);
        params.put("currency", "INR");
        params.put("amount", PayUtils.getMoney(doWithdrawDto.getAmount(), 2));
        params.put("notify_url", doWithdrawDto.getWithdrawNotifyUrl());
        params.put("pay_recipients_name", doWithdrawDto.getUserName().trim());

        if ("200001".equalsIgnoreCase(payOutType)) {
            // bank信息代付
            params.put("pay_bankname", doWithdrawDto.getBankCodeDto().getBankName());
            params.put("pay_bank_account", doWithdrawDto.getUserBankAccount());
            params.put("pay_ifsc", doWithdrawDto.getAdditional());
        } else if ("200002".equalsIgnoreCase(payOutType)) {
            //upi信息代付
            params.put("pay_upiid", doWithdrawDto.getAdditional());
        }

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doPost(doWithdrawDto.getWithdrawApi(), params);
        String status = result.get("code").asText();
        if (SUCCESS_CODE.equalsIgnoreCase(status)) {
            resultDto.setThirdOrderNo(result.get("data").get("orderno").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("{}  訂單號:{} 請求成功:{}", DF_NAME, orderNo, result);
        } else {
            String message = result.get("errmsg").asText();
            resultDto.setErrorMsg(message);
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("{}[{}] 訂單號:{}  狀態:{} 請求失敗:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status,
                    result);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", DF_NAME, withdrawNotifyText, orderNo, resMap);

        String status = resMap.get("status");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{}  ,算出的sign:{} ,收到的sign:{},",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, checkSign, sign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("amount")));
            withdrawPayDTO.setFee(PayUtils.getCent(resMap.get("fee")));
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  代付DTO:{}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        String orderNo = String.valueOf(doPaymentPollingDTO.getOrderId());
        String payKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchantid", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("merchant_orderno", orderNo);
        params.put("type", "pay");

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_ORDER_API_PATH;

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        params.put("sign", sign);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr ,sign);

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        try {
            JsonNode result = paymentCommonService.doPost(apiUrl, params);
            String status = result.get("code").asText();
            String payStateCode = result.get("data").get("status").asText();
            log.info("{} 查詢結果 訂單號:{} 狀態碼:{} 回傳:{}", DF_NAME, orderNo, payStateCode, result);
            if (SUCCESS_CODE.equalsIgnoreCase(status) && "success".equalsIgnoreCase(payStateCode)) {
                resultDTO.setIsSuccess(true);
                resultDTO.setAmount(doPaymentPollingDTO.getAmount());
                log.info("{} 查詢成功 訂單號:{} 回傳:{}", DF_NAME, orderNo, result);
            } else {
                log.info("{} 查詢訂單未完成 訂單號:{} 狀態:{} 回傳:{}", DF_NAME, orderNo, payStateCode, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 查詢請求異常", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDTO;
    }
}
