package com.wd.lottery.module.payment.pay.pay1;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;


@Slf4j
@Service
@RequiredArgsConstructor
public class Pay1 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String thirdName = "DPay";
    private static final String SUCCESS_CODE = "1";
    private static final String CALLBACK_SUCCESS_CODE = "1";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "merchant_order";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "merchant_order";

    private final PaymentCommonService paymentCommonService;

    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/Payment")
            .rechargeApiPath("/Index")
            .queryBalanceApiPath("/Look/get_coin")
            .withdrawNotifyPrint("SUCCESS")
            .rechargeNotifyPrint("SUCCESS")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String privateKey = paymentMerchant.getPrivateKey();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String payType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payType", PayRequestEnum.DEPOSIT);
        String uid = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "uid", PayRequestEnum.DEPOSIT);
        Long amount = doDepositDto.getAmount();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date currentDate = new Date();
        String payDate = sdf.format(currentDate);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("uid", uid);
        params.put("merchant_num", paymentMerchant.getPaymentMerchantCode());
        params.put("merchant_order", orderNo);
        params.put("coin", amount);
        params.put("pay_notifyurl", doDepositDto.getDepositNotifyUrl());
        params.put("pay_date", payDate);
        params.put("pay_type", payType);
        params.put("userinfo", doDepositDto.getMemberId());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        log.info("[{}][{}][{}] 產生簽名字串加密前 [{}] ", thirdName, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr + "&key={密钥}");

        paramStr += "&key=" + privateKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("[{}][{}][{}] 產生簽名加密後 [{}]  ", thirdName, PayRequestEnum.DEPOSIT.getText(), orderNo, sign);

        params.put("sign", sign);
        log.info("[{}][{}][{}] request params = {}", thirdName, PayRequestEnum.DEPOSIT.getText(), orderNo, params);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);

            if (SUCCESS_CODE.equals(result.get("code").asText())) {
                resultDto.setThirdOrderNo("");
                resultDto.setRedirectUrl(result.get("payurl").asText());
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
            }
        } catch (Exception e) {
            log.error("[{}][{}][{}] 充值請求異常", thirdName, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        log.info("[{}][{}][{}] 三方回調內容 = {}", thirdName, depositNotifyText, orderNo, resMap);

        String status = resMap.get("code");
        if (!CALLBACK_SUCCESS_CODE.equals(status)) {
            resMap.remove("code");
        }
        String sign = resMap.remove("sign");

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
        paramStr += "&key=" + doDepositNotifyDto.getPaymentMerchant().getPrivateKey();
        String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();

        if (!sign.equals(checkSign)) {
            log.error("[{}][{}][{}][金流發生錯誤] 簽名驗證失敗", thirdName, depositNotifyText, orderNo);
            return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
        }

        log.info("[{}][{}][{}] 訂單狀態 = {}", thirdName, depositNotifyText, orderNo, status);
        if (CALLBACK_SUCCESS_CODE.equals(status)) {
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(Long.parseLong(resMap.get("coin")));
            depositPayDto.setFee(0L);
            log.info("[{}][{}][{}] pay = {}", thirdName, depositNotifyText, orderNo, depositPayDto);
            return depositPayDto;
        }
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String privateKey = paymentMerchant.getPrivateKey();

        String orderNo = doWithdrawDto.getOrderNo();
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String uid = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "uid", PayRequestEnum.WITHDRAW);

        String orderDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("uid", uid);
        params.put("merchant_num", paymentMerchant.getPaymentMerchantCode());
        params.put("order", orderNo);
        params.put("coin", doWithdrawDto.getAmount());
        params.put("target_bank", doWithdrawDto.getUserBankAccount());
        params.put("bank_name", doWithdrawDto.getBankCodeDto().getBankCode());
        params.put("target_bank_user", doWithdrawDto.getMemberName());
        params.put("order_date", orderDate);
        params.put("notifyurl", doWithdrawDto.getWithdrawNotifyUrl());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        log.info("[{}][{}][{}] 產生簽名字串加密前 [{}] ", thirdName, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr + "&key={密钥}");

        paramStr += "&key=" + privateKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("[{}][{}][{}] 產生簽名加密後 [{}]  ", thirdName, PayRequestEnum.WITHDRAW.getText(), orderNo, sign);

        params.put("sign", sign);
        log.info("[{}][{}][{}] request params = {}", thirdName, PayRequestEnum.WITHDRAW.getText(), orderNo, params);

        JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);

        if (SUCCESS_CODE.equals(result.get("code").asText())) {
            resultDto.setThirdOrderNo("");
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
        } else {
            resultDto.setErrorMsg(result.get("msg").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        log.info("[{}][{}][{}] 三方回調內容 = {}", thirdName, withdrawNotifyText, orderNo, resMap);

        String status = resMap.get("code");
        if (!CALLBACK_SUCCESS_CODE.equals(status)) {
            resMap.remove("code");
        }
        String sign = resMap.remove("sign");

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
        paramStr += "&key=" + doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();
        String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();

        if (!sign.equals(checkSign)) {
            log.error("[{}][{}][{}][金流發生錯誤] 簽名驗證失敗", thirdName, withdrawNotifyText, orderNo);
            return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
        }

        log.info("[{}][{}][{}] 訂單狀態 = {}", thirdName, withdrawNotifyText, orderNo, status);
        if (CALLBACK_SUCCESS_CODE.equals(status)) {
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(Long.parseLong(resMap.get("coin")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("[{}][{}][{}] pay = {}", thirdName, withdrawNotifyText, orderNo, withdrawPayDTO);
            return withdrawPayDTO;
        }
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doBalanceDto.getPaymentMerchant();
        String privateKey = paymentMerchant.getPrivateKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doBalanceDto.getPaymentDynamicColumnValues();
        String uid = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "uid", PayRequestEnum.BALANCE);

        String findDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        Map<String, Object> params = new TreeMap<>();
        params.put("merchant_num", paymentMerchant.getPaymentMerchantCode());
        params.put("uid", uid);
        params.put("find_date", findDate);

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        log.info("[{}][{}] 產生簽名字串加密前 [{}] ", thirdName, PayRequestEnum.BALANCE.getText(), paramStr + "&key={密钥}");

        paramStr += "&key=" + privateKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("[{}][{}] 產生簽名加密後 [{}]  ", thirdName, PayRequestEnum.BALANCE.getText(), sign);

        params.put("sign", sign);
        log.info("[{}][{}] request params = {}", thirdName, PayRequestEnum.BALANCE.getText(), params);

        try {
           JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params);

            String status = result.get("code").asText();
            if (SUCCESS_CODE.equals(status)) {
                JsonNode data = result.get("data");
                resultDto.setBalance(PayUtils.getCent(data.get("coin").asText()));
                resultDto.setSuccess(true);
            } else {
                resultDto.setMessage(result.get("msg").asText());
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", thirdName, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

}
