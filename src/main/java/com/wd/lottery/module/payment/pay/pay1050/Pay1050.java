package com.wd.lottery.module.payment.pay.pay1050;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.PaymentConstants;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1050 ,TIGERPAY-Maya支付 - PHP (cgptf008)
 * SFZF-1051 ,TIGERPAY-Maya代付 - PHP (cgptf008)
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class Pay1050 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {

    private final String ZF_NAME = "[TIGERPAY-GCash/Maya支付]";
    private final String DF_NAME = "[TIGERPAY-GCash/Maya代付]";
    private static final String CALLBACK_SUCCESS_CODE = "completed";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "out_trade_no";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "out_trade_no";

    private final PaymentCommonService paymentCommonService;

    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/api/payment")
            .rechargeApiPath("/api/transaction")
            .queryBalanceApiPath("/api/balance/inquiry")
            .withdrawNotifyPrint("ok")
            .rechargeNotifyPrint("ok")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String apiToken = paymentMerchant.getPrivateKey();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        log.info("{} 訂單號:{} 動態密鑰: {}  ", ZF_NAME, orderNo, paymentDynamicColumnValues.toString());


        TreeMap<String, Object> params = new TreeMap<>();
        params.put("amount", PayUtils.getMoney(doDepositDto.getAmount()));
        params.put("callback_url", doDepositDto.getDepositNotifyUrl());
        params.put("out_trade_no", orderNo);

        String token = "Bearer " + apiToken;
        Map<String, String> headers = new HashMap<>();
        headers.put(PaymentConstants.AUTHORIZATION, token);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params, headers);

            if (result.get("success").asBoolean()) {
                JsonNode data = result.get("data");
                String payUrl = data.get("uri").asText();
                String thirdOrderNo = data.get("trade_no").asText();
                resultDto.setThirdOrderNo(thirdOrderNo);
                resultDto.setRedirectUrl(payUrl);
                log.info("{} 請求成功 訂單號:{} ", ZF_NAME, orderNo);
                log.info("{}[{}]訂單號:{} 跳轉地址:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, payUrl);
            } else {
                resultDto.setErrorMsg(result.get("message").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{}[{}]{} 請求失敗 error:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, result.get("errors"));
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);

        String apiToken = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();
        String notifyToken = doDepositNotifyDto.getPaymentMerchant().getPublicKey();
        log.info("{} 訂單號:{} 回調驗籤 apiToken: {},notifyToken: {}  ", ZF_NAME, orderNo, apiToken, notifyToken);


        String status = resMap.get("state");
        String sign = resMap.remove("sign");

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
        paramStr += apiToken + notifyToken;
        String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);

        if (!sign.equalsIgnoreCase(checkSign)) {
            log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                    ZF_NAME, depositNotifyText, orderNo, paramStr, sign, checkSign);
            return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
        }

        log.info("{}[{}]訂單號:{} 訂單狀態 = {}", ZF_NAME, depositNotifyText, orderNo, status);
        if (CALLBACK_SUCCESS_CODE.equals(status)) {
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.get("amount")));
            depositPayDto.setFee(0L);
            log.info("{}[{}]訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);

    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String apiToken = doBalanceDto.getPaymentMerchant().getPrivateKey();
        String token = "Bearer " + apiToken;
        Map<String, String> headers = new HashMap<>();
        headers.put(PaymentConstants.AUTHORIZATION, token);
        try {
            JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), new HashMap<>(), headers);
            if (result.get("success").asBoolean()) {
                String balance = result.path("data").get("balance").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
            } else {
                resultDto.setMessage("error");
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", DF_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String apiToken = paymentMerchant.getPrivateKey();
        String notifyToken = paymentMerchant.getPublicKey();
        String orderNo = doWithdrawDto.getOrderNo();
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String channel = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "channel", PayRequestEnum.WITHDRAW);

        log.info("{} 訂單號:{} apiToken: {},notifyToken: {}  ", DF_NAME, orderNo, apiToken, notifyToken);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("out_trade_no", doWithdrawDto.getOrderNo());
        params.put("bank_id", channel);
        params.put("bank_owner", doWithdrawDto.getMemberName());
        String mobile = String.valueOf(doWithdrawDto.getMobile());
        if (String.valueOf(doWithdrawDto.getMobile()).startsWith("63")) {
            mobile = "0" + String.valueOf(doWithdrawDto.getMobile()).substring(2);
        }
        params.put("account_number", mobile);
        params.put("amount", PayUtils.getMoney(doWithdrawDto.getAmount()));
        params.put("callback_url", doWithdrawDto.getWithdrawNotifyUrl());


        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);

        paramStr += apiToken + notifyToken;
        log.info("{}[{}]{} 產生簽名字串加密前 [{}] ", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr );
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}]{} 產生簽名加密後 [{}]  ", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, sign);

        params.put("sign", sign);
        log.info("{}[{}]{} request params = {}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, params);

        String token = "Bearer " + apiToken;
        Map<String, String> headers = new HashMap<>();
        headers.put(PaymentConstants.AUTHORIZATION, token);
        try {
            JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params, headers);
            if (result.get("success").asBoolean()) {
                resultDto.setThirdOrderNo("");
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
                log.info("{} 請求成功 訂單號:{} ", DF_NAME, orderNo);
            } else {
                resultDto.setErrorMsg(result.get("message").asText());
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
                log.info("{}[{}]{} 請求失敗 error:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, result.get("errors"));
            }
        } catch (Exception e) {
            log.error("{}[{}]{} response 解析異常 ", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        log.info("{}[{}]{} 三方回調內容 = {}", DF_NAME, withdrawNotifyText, orderNo, resMap);
        String apiToken = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();
        String notifyToken = doWithdrawNotifyDto.getPaymentMerchant().getPublicKey();
        log.info("{} 訂單號:{} 回調驗籤 apiToken: {},notifyToken: {}  ", DF_NAME, orderNo, apiToken, notifyToken);

        String status = resMap.get("state");
        String sign = resMap.remove("sign");
        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
        paramStr += apiToken + notifyToken;
        String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);

        if (!sign.equalsIgnoreCase(checkSign)) {
            log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                    DF_NAME, withdrawNotifyText, orderNo, paramStr, sign, checkSign);
            return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
        }

        log.info("{}[{}]訂單號:{} 訂單狀態 = {}", DF_NAME, withdrawNotifyText, orderNo, status);
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            try {
                withdrawPayDTO.setOrderNo(orderNo);
                withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("amount")));
                withdrawPayDTO.setFee(0L);
                withdrawPayDTO.setBalance(null);
                log.info("{} 回調成功 訂單號:{} ", DF_NAME, orderNo);
                log.info("{}[{}]訂單號:{} pay = {}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
                return withdrawPayDTO;
            }catch (Exception e){
                log.error("{} 回調失敗發生例外 訂單號:{} ", DF_NAME, orderNo, e);
            }
        }
        log.info("{}[{}]訂單號:{} 回調失敗", DF_NAME, withdrawNotifyText, orderNo);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }
}
