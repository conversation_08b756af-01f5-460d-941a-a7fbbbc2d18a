package com.wd.lottery.module.payment.mq;

import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.module.cash.constatns.OrderStatusEnum;
import com.wd.lottery.module.cash.constatns.SubTradeTypeEnum;
import com.wd.lottery.module.cash.entity.CashDepositOrder;
import com.wd.lottery.module.cash.service.CashDepositOrderService;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.dto.payment.DepositPayDTO;
import com.wd.lottery.module.payment.service.PaymentMerchantService;
import com.wd.lottery.module.payment.service.PaymentResultService;
import com.wd.lottery.module.payment.util.PayUtils;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
@RequiredArgsConstructor
public class DepositListener {

    @Value("${order.amount.range:100}")
    private Long amountRange;

    private final CashDepositOrderService cashDepositOrderService;

    private final PaymentResultService paymentResultService;

    private final PaymentMerchantService paymentMerchantService;

    @Transactional
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = RabbitMQConstants.PAYMENT_DEPOSIT_NOTIFY_QUEUE),
            exchange = @Exchange(value = RabbitMQConstants.PAYMENT_DEPOSIT_NOTIFY_EXCHANGE, type = ExchangeTypes.FANOUT)
    ))
    public void depositNotify(String msg, Channel channel, Message message) {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        MessageProperties properties = message.getMessageProperties();

        try {
            DepositPayDTO depositPayDto = JSONUtil.toBean(msg, DepositPayDTO.class);
            String channelName = depositPayDto.getPaymentChannelName();
            String orderNo = depositPayDto.getOrderNo();
            Long paymentMerchantId = depositPayDto.getPaymentMerchantId();

            if (StringUtils.isBlank(orderNo)) {
                log.error("[{}][{}] 訂單號為空", depositNotifyText, paymentMerchantId);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }

            // 取訂單表
            CashDepositOrder cashDepositOrder = cashDepositOrderService.findById(Long.valueOf(orderNo), depositPayDto.getMerchantId());
            if (cashDepositOrder == null) {
                log.error("[{}][{}] order表無此訂單號資料, paymentMerchantId:{}", depositNotifyText, orderNo, paymentMerchantId);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            if (!Objects.equals(cashDepositOrder.getPaymentMerchantId(), paymentMerchantId)) {
                log.error("[{}][{}] 回調訂單號非此商戶之訂單, 訂單商戶:{}, 回調商戶:{}", depositNotifyText,
                        orderNo, cashDepositOrder.getPaymentMerchantId(), paymentMerchantId);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            if (cashDepositOrder.getStatusEnum() != OrderStatusEnum.PROGRESSING) {
                log.error("[{}][{}] 訂單原始狀態非充值中", depositNotifyText, orderNo);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 訂單失敗處理(系統錯誤, 三方錯誤, 驗簽錯誤)
            if (depositPayDto.getDepositStatusEnum() == DepositStatusEnum.SERVER_ERROR ||
                    depositPayDto.isSignError() || depositPayDto.isStatusError()) {
                String errorTitle = depositPayDto.isSignError() ? "驗簽失敗" : depositPayDto.isStatusError() ? "其他原因" : "金流發生錯誤";
                log.error("[{}][{}][{}][{}] msg:{}", channelName, depositNotifyText, orderNo, errorTitle, depositPayDto.getErrorMsg());

                cashDepositOrderService.updateOrderStatusOfAuto(cashDepositOrder, depositPayDto.getErrorMsg(),
                        null, null, OrderStatusEnum.FAILED);

                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 依照貨幣單位還原金額
            this.adjustDepositCallbackAmountByCurrency(depositPayDto, cashDepositOrder.getCurrencyEnum());

            // 回調金額落差處理(落差過大會轉為失敗), 回調金額為0
            if (!PayUtils.checkMoney(depositPayDto.getAmount(), cashDepositOrder.getRealMoney(), amountRange) || depositPayDto.getAmount() == 0L) {
                log.error("[{}][{}] 訂單金額異常 超過範圍值, 訂單實際金額為:{}, 三方回調實際金額為:{}",
                        depositNotifyText, orderNo, cashDepositOrder.getRealMoney(), depositPayDto.getAmount());

                cashDepositOrderService.updateOrderStatusOfAuto(cashDepositOrder, "回调订单金额异常",
                        null, null, OrderStatusEnum.FAILED);

                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            LocalDateTime payTime = LocalDateTime.now();

            // 更新訂單狀態(成功)
            boolean isUpdateOrder = cashDepositOrderService.updateOrderStatusOfAuto(cashDepositOrder, depositPayDto.getErrorMsg(),
                    payTime, depositPayDto.getFee(), OrderStatusEnum.SUCCESS);
            if (!isUpdateOrder) {
                log.error("[{}][{}] 更新訂單狀態(成功)異常, 更新失敗", depositNotifyText, orderNo);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 首充紀錄
            BooleanEnum isFirstCharge = paymentResultService.depositHis(cashDepositOrder, payTime);

            // 新增會員錢包
            paymentResultService.memberWalletOperate(cashDepositOrder.getMemberId(), cashDepositOrder.getMemberName(),
                    cashDepositOrder.getId(), cashDepositOrder.getExchangeRealMoney(), depositPayDto.getMerchantId(), payTime, SubTradeTypeEnum.MEMBER_DEPOSIT , null,isFirstCharge);

            // 充值額外獎勵處理
            paymentResultService.depositReward(cashDepositOrder, payTime);

            // 增加每日累積金額, 次數
            paymentMerchantService.addDailyDepositLimit(paymentMerchantId, cashDepositOrder.getOrderMoney());

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("[{}] 異常, queue:{}, msg:{}", depositNotifyText, properties.getConsumerQueue(), msg, e);
            try {
                TimeUnit.SECONDS.sleep(Constants.DEFAULT_RABBIT_CONSUMER_FAIL_SLEEP_SECONDS);
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
            } catch (Exception e1) {
                log.error("[{}] 異常 msg:{} ", depositNotifyText, msg, e1);
            }
        }
    }

    void adjustDepositCallbackAmountByCurrency(DepositPayDTO depositDTO, CurrencyEnum currencyEnum) {
        depositDTO.setAmount(depositDTO.getAmount() / currencyEnum.getBaseUnit());
    }

}
