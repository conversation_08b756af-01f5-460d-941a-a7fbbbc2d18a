package com.wd.lottery.module.payment.pay.pay1285;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

/**
 * SFZF-1285, Ablepay  巴基斯坦
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1285 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1285][Ablepay支付]";
    private final String DF_NAME = "[SFZF-1285][Ablepay代付]";
    private final String BALANCE = "[SFZF-1285][Ablepay餘額查詢]";
    private static final String SUCCESS_CODE = "200";
    private static final String CALLBACK_SUCCESS_CODE = "SUCCESS";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "tradeSn";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "tradeSn";


    private static final String QUERY_ORDER_WITHDRAW_PATH = "/cashout/query";

    private static final String QUERY_ORDER_DEPOSIT_PATH = "/cashin/query";

    private final PaymentCommonService paymentCommonService;
    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/cashout/create")
            .rechargeApiPath("/cashin/create")
            .queryBalanceApiPath("/cashout/balance")
            .withdrawNotifyPrint("OK")
            .rechargeNotifyPrint("OK")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String priKey = paymentMerchant.getPrivateKey();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String payType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payType", PayRequestEnum.DEPOSIT);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mid", Integer.parseInt(doDepositDto.getPaymentMerchant().getPaymentMerchantCode()));
        params.put("timestamp", System.currentTimeMillis() / 1000);
        params.put("tradeSn", orderNo);
        params.put("currency", "PKR");
        params.put("amount", Integer.valueOf(PayUtils.getMoney(doDepositDto.getAmount())));
        params.put("notifyUrl", doDepositDto.getDepositNotifyUrl());
        params.put("remarks", "remarks");
        params.put("phone", "***********");
        params.put("tradeCode", payType);


        try {
            String paramStr = JSONUtil.toJsonStr(params);
            String sign = this.privateCreateSign(paramStr, priKey);
            log.info("{}[{}]{} 產生簽名字串加密前 [{}] 加密後 [{}]", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, sign);
            TreeMap<String, String> header = new TreeMap<>();
            header.put("X-Sign", sign);
            JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params, header);
            String status = result.get("status").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String payUrl = result.get("data").get("link").asText();
                resultDto.setThirdOrderNo(result.get("data").get("platOrderSn").asText());
                resultDto.setRedirectUrl(payUrl);
                log.info("{} 請求成功 訂單號:{} 回傳:{}", ZF_NAME, orderNo, result);
                log.info("{}[{}]訂單號:{} 跳轉地址:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, payUrl);
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{} 請求失敗 訂單號:{} 狀態:{} 回傳:{}", ZF_NAME, orderNo, status, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new LinkedHashMap<>(doDepositNotifyDto.getRequestMap());
        Map<String, String> header = new LinkedHashMap<>(doDepositNotifyDto.getHeaderMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String pubKey = doDepositNotifyDto.getPaymentMerchant().getPublicKey();


        String status = resMap.get("orderStatus");
        String sign = header.get("X-Sign");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            Map<String, Object> jsonMap = new LinkedHashMap<>();
            jsonMap.put("mid", new BigDecimal(resMap.get("mid")));
            jsonMap.put("tradeSn", resMap.get("tradeSn"));
            jsonMap.put("platOrderSn", resMap.get("platOrderSn"));
            jsonMap.put("orderStatus", resMap.get("orderStatus"));
            jsonMap.put("amount", new BigDecimal(resMap.get("amount")));
            jsonMap.put("actualAmount", new BigDecimal(resMap.get("actualAmount")));
            jsonMap.put("fee", new BigDecimal(resMap.get("fee")));
            jsonMap.put("errCode", resMap.get("errCode"));
            jsonMap.put("errMsg", resMap.get("errMsg"));
            jsonMap.put("finishTime", new BigDecimal(resMap.get("finishTime")));
            jsonMap.put("bankRefNo", resMap.get("bankRefNo"));
            jsonMap.put("timestamp", new BigDecimal(resMap.get("timestamp")));

            String paramStr = JSONUtil.toJsonStr(jsonMap);
            boolean checkSign = this.publicVerifySign(paramStr, sign, pubKey);
            if (!checkSign) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}}", ZF_NAME, depositNotifyText, orderNo, paramStr, sign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.getOrDefault("actualAmount", resMap.get("amount"))));
            depositPayDto.setFee(PayUtils.getCent(resMap.get("fee")));
            log.info("{}[{}]訂單號:{} {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        String priKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();
        long orderNo = doPaymentPollingDTO.getOrderId();
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mid", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("tradeSn", orderNo);
        params.put("timestamp", System.currentTimeMillis() / 1000);
        String paramStr = JSONUtil.toJsonStr(params);
        String sign = this.privateCreateSign(paramStr, priKey);
        log.info("{}[{}]{} 產生簽名字串加密前 [{}] 加密後 [{}]", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, sign);
        TreeMap<String, String> header = new TreeMap<>();
        header.put("X-Sign", sign);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_ORDER_DEPOSIT_PATH;
        JsonNode result = paymentCommonService.doJsonPost(apiUrl, params, header);
        log.info("{}}[{}] 查单结果{}", ZF_NAME, orderNo, result);

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        String status = result.get("status").asText();
        String oderStatus = result.get("data").get("orderStatus").asText();
        if (!SUCCESS_CODE.equalsIgnoreCase(status) || !CALLBACK_SUCCESS_CODE.equalsIgnoreCase(oderStatus)) {
            return resultDTO;
        }
        Long amount = PayUtils.getCent(result.get("data").get("actualAmount").asText());
        resultDTO.setAmount(amount);
        resultDTO.setIsSuccess(true);
        return resultDTO;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String priKey = doBalanceDto.getPaymentMerchant().getPrivateKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mid", Integer.valueOf(doBalanceDto.getPaymentMerchant().getPaymentMerchantCode()));
        params.put("timestamp", System.currentTimeMillis() / 1000);

        try {
            String paramStr = JSONUtil.toJsonStr(params);
            String sign = this.privateCreateSign(paramStr, priKey);
            log.info("{}[{}]產生簽名字串加密前 [{}] 加密後 [{}] ", BALANCE, PayRequestEnum.BALANCE.getText(), paramStr, sign);

            TreeMap<String, String> header = new TreeMap<>();
            header.put("X-Sign", sign);
            JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params, header);

            String status = result.get("status").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("data").get("bal").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                resultDto.setMessage(result.get("msg").asText());
                log.info("{}[{}] 失敗:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", BALANCE, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String priKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mid", Integer.valueOf(paymentMerchant.getPaymentMerchantCode()));
        params.put("timestamp", System.currentTimeMillis() / 1000);
        params.put("amount", Integer.valueOf(PayUtils.getMoney(doWithdrawDto.getAmount())));
        params.put("tradeSn", orderNo);
        params.put("account", doWithdrawDto.getUserBankAccount());
        params.put("tradeCode", doWithdrawDto.getBankCodeDto().getBankCode());
        params.put("currency", "PKR");
        params.put("notifyUrl", doWithdrawDto.getWithdrawNotifyUrl());
        params.put("cnic", "*************");
        params.put("remarks", "remarks");
        params.put("phone", "***********");

        try {
            String paramStr = JSONUtil.toJsonStr(params);
            String sign = this.privateCreateSign(paramStr, priKey);
            log.info("{}[{}] 訂單號:{} 產生簽名字串加密前 [{}] 加密後 [{}]", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, sign);
            TreeMap<String, String> header = new TreeMap<>();
            header.put("X-Sign", sign);

            JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params, header);
            String status = result.get("status").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                resultDto.setThirdOrderNo(result.get("data").get("platOrderSn").asText());
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
                log.info("{} 請求成功 訂單號:{} 回傳:{}", DF_NAME, orderNo, result);
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{} 請求失敗 狀態:{} 回傳:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 提現請求異常", ZF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new LinkedHashMap<>(doWithdrawNotifyDto.getRequestMap());
        Map<String, String> header = new LinkedHashMap<>(doWithdrawNotifyDto.getHeaderMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String pubKey = doWithdrawNotifyDto.getPaymentMerchant().getPublicKey();

        String status = resMap.get("orderStatus");
        String sign = header.get("X-Sign");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            Map<String, Object> jsonMap = new LinkedHashMap<>();
            jsonMap.put("mid", new BigDecimal(resMap.get("mid")));
            jsonMap.put("tradeSn", resMap.get("tradeSn"));
            jsonMap.put("platOrderSn", resMap.get("platOrderSn"));
            jsonMap.put("orderStatus", resMap.get("orderStatus"));
            jsonMap.put("amount", new BigDecimal(resMap.get("amount")));
            jsonMap.put("actualAmount", new BigDecimal(resMap.get("actualAmount")));
            jsonMap.put("fee", new BigDecimal(resMap.get("fee")));
            jsonMap.put("errCode", resMap.get("errCode"));
            jsonMap.put("errMsg", resMap.get("errMsg"));
            jsonMap.put("finishTime", new BigDecimal(resMap.get("finishTime")));
            jsonMap.put("bankRefNo", resMap.get("bankRefNo"));
            jsonMap.put("timestamp", new BigDecimal(resMap.get("timestamp")));

            String paramStr = JSONUtil.toJsonStr(jsonMap);
            boolean checkSign = this.publicVerifySign(paramStr, sign, pubKey);
            if (!checkSign) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}", DF_NAME, withdrawNotifyText, orderNo, paramStr, sign );
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(resMap.getOrDefault("actualAmount", resMap.get("amount"))));
            withdrawPayDTO.setFee(PayUtils.getCent(resMap.get("fee")));
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  = {}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        String priKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();
        long orderNo = doPaymentPollingDTO.getOrderId();
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mid", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("tradeSn", orderNo);
        params.put("timestamp", System.currentTimeMillis() / 1000);
        String paramStr = JSONUtil.toJsonStr(params);
        String sign = this.privateCreateSign(paramStr, priKey);
        log.info("{}[{}]{} 產生簽名字串加密前 [{}] 加密後 [{}]", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, sign);
        TreeMap<String, String> header = new TreeMap<>();
        header.put("X-Sign", sign);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_ORDER_WITHDRAW_PATH;
        JsonNode result = paymentCommonService.doJsonPost(apiUrl, params, header);
        log.info("{}}[{}] 查单结果{}", DF_NAME, orderNo, result);

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        String status = result.get("status").asText();
        String orderStatus = result.get("data").get("orderStatus").asText();
        if (!SUCCESS_CODE.equalsIgnoreCase(status) || !CALLBACK_SUCCESS_CODE.equalsIgnoreCase(orderStatus)) {
            return resultDTO;
        }
        Long amount = PayUtils.getCent(result.get("data").get("actualAmount").asText());
        resultDTO.setAmount(amount);
        resultDTO.setIsSuccess(true);
        return resultDTO;
    }

    private String privateCreateSign(String content, String privateStr) throws Exception {
        //解析私钥
        byte[] keyBytes = Base64.getDecoder().decode(privateStr);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

        //使用私钥进行签名（SHA1withRSA）
        Signature signature = Signature.getInstance("SHA1withRSA");
        signature.initSign(privateKey);
        signature.update(content.getBytes("UTF-8"));

        //生成 Base64 编码的签名
        byte[] signatureBytes = signature.sign();
        return Base64.getEncoder().encodeToString(signatureBytes);
    }

    private boolean publicVerifySign(String content, String sign, String publicStr) throws Exception {
        //解析公钥
        byte[] keyBytes = Base64.getDecoder().decode(publicStr);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(keySpec);

        //初始化验签
        Signature signature = Signature.getInstance("SHA1withRSA");
        signature.initVerify(publicKey);
        signature.update(content.getBytes("UTF-8"));

        //验证签名
        byte[] signatureBytes = Base64.getDecoder().decode(sign);
        return signature.verify(signatureBytes);
    }
}
