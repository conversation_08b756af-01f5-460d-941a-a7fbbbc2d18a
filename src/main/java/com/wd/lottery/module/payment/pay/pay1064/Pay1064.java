package com.wd.lottery.module.payment.pay.pay1064;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1065, feifanpay代付 (CGPTF008)
 * SFZF-1064, feifanpay支付 (CGPTF008)
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1064 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {

    private final String ZF_NAME = "[SFZF-1064][feifanpay支付]";
    private final String DF_NAME = "[SFZF-1065][feifanpay代付]";
    private final String BALANCE = "[feifanpay 餘額查詢]";
    private static final String SUCCESS_CODE = "200";
    private static final String CALLBACK_SUCCESS_CODE = "1";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "orderid";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "orderid";

    private final PaymentCommonService paymentCommonService;

    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/pay/transfer")
            .rechargeApiPath("/payment/create")
            .queryBalanceApiPath("/payment/query/balance")
            .withdrawNotifyPrint("success")
            .rechargeNotifyPrint("success")
            .jumpMode(JumpModeEnum.REDIRECT);


    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String payAccountId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payAccountId", PayRequestEnum.DEPOSIT);
        String channel = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "channel", PayRequestEnum.DEPOSIT);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("parter", payAccountId);
        params.put("value", PayUtils.getMoney(doDepositDto.getAmount()));
        params.put("type", channel);
        params.put("orderid", orderNo);
        params.put("notifyurl", doDepositDto.getDepositNotifyUrl());
        params.put("callbackurl", doDepositDto.getDepositNotifyUrl());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        log.info("{}[{}] 訂單號:{} 產生簽名字串加密前 [{}] ", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr);

        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}][{}] 訂單號:{} 產生簽名加密後 [{}]  ", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, sign);
        params.put("ip", "127.0.0.1");
        params.put("sign", sign);


        try {
            JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String payUrl = result.path("param").get("payment_url").asText();
                resultDto.setThirdOrderNo("none");
                resultDto.setRedirectUrl(payUrl);
                log.info("{} 請求成功 訂單號:{} ", ZF_NAME, orderNo);
                log.info("{}[{}]訂單號:{} 跳轉地址:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, payUrl);
            } else {
                resultDto.setErrorMsg(result.get("info").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{} 請求失敗 訂單號:{} 狀態:{}", ZF_NAME, orderNo, status);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);
        log.info("{} 訂單號:{} 回調驗籤 密鑰: {}", ZF_NAME, orderNo, payKey );

        String status = resMap.get("opstate");
        String sign = resMap.remove("sign");

        //交易状态，0:未支付,1:已支付
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.get("ovalue")));
            depositPayDto.setFee(0L);
            log.info("{}[{}]訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doBalanceDto.getPaymentDynamicColumnValues();
        String payAccountId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payAccountId", PayRequestEnum.BALANCE);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("parter", payAccountId);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params);
            String status = result.get("code").asText();

            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.path("param").get("balance").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功 = {}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                String info = result.get("info").asText();
                resultDto.setMessage(info);
                log.info("{}[{}] 失敗 = {}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", DF_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String payAccountId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payAccountId", PayRequestEnum.WITHDRAW);


        log.info("{} 訂單號:{} 密鑰:{}", DF_NAME, orderNo, payKey);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("parter", payAccountId);
        params.put("order_no", orderNo );
        params.put("type", doWithdrawDto.getBankCodeDto().getBankCode());
        params.put("name", doWithdrawDto.getMemberName());
        params.put("account_number", doWithdrawDto.getUserBankAccount());
        params.put("money", PayUtils.getMoney(doWithdrawDto.getAmount()));
        params.put("notify_url", doWithdrawDto.getWithdrawNotifyUrl());


        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params) + "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);

        log.info("{}[{}] 訂單號:{} 產生簽名字串加密前 [{} ", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr);
        log.info("{}[{}] 訂單號:{} 產生簽名加密後 [{}]  ", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, sign);

        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);

        String status = result.get("code").asText();
        if (SUCCESS_CODE.equalsIgnoreCase(status)) {
            resultDto.setThirdOrderNo("none");
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("{} 請求成功 訂單號:{} ", DF_NAME, orderNo);
        } else {
            resultDto.setErrorMsg(result.get("message").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("{}[{}] 訂單號:{} 請求失敗 狀態:{} message:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo,status,
                    result.get("message").asText());
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", DF_NAME, withdrawNotifyText, orderNo, resMap);
        log.info("{} 訂單號:{} 回調驗籤 密鑰: {}", DF_NAME, orderNo, payKey );

        String status = resMap.get("opstate");
        String sign = resMap.remove("sign");
        //交易状态，0:支付失败,1:已支付
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("ovalue")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  = {}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }
}
