package com.wd.lottery.module.payment.pay.pay1242;


import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.util.RequestUtil;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;

/**
 * SFZF-1242, owowenpay支付代付 PHP(cgptf016)
 * 規格特殊 不能當參考
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1242 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1242][owowenpay支付]";
    private final String DF_NAME = "[SFZF-1242][owowenpay代付]";
    private final String BALANCE = "[SFZF-1242 餘額查詢]";
    private static final String SUCCESS_CODE = "0000";
    private static final String DEPOSIT_CALLBACK_SUCCESS_CODE = "0000";
    private static final String WITHDRAW_CALLBACK_SUCCESS_CODE = "00";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "outTradeNo";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "outTradeNo";

    private final PaymentCommonService paymentCommonService;

    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/cashOutVp/apply.shtml")
            .rechargeApiPath("/pay/apply.shtml")
            .queryBalanceApiPath("/balanceQuery/apply.shtml")
            .withdrawNotifyPrint("SUCCESS")
            .rechargeNotifyPrint("SUCCESS")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String payType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payType", PayRequestEnum.DEPOSIT);

        TreeMap<String, String> params = new TreeMap<>();
        params.put("appID", paymentMerchant.getPaymentMerchantCode());
        params.put("currencyCode", "PHP");
        params.put("tradeCode", payType);
        params.put("randomNo", PayUtils.getRandomString(10));
        params.put("outTradeNo", orderNo);
        params.put("totalAmount", String.valueOf(doDepositDto.getAmount()));
        params.put("productTitle", "product");
        params.put("notifyUrl", doDepositDto.getDepositNotifyUrl());
        params.put("tradeIP", RequestUtil.getRequestIpFromRequest());
        params.put("payEmail", StrUtil.isBlank(doDepositDto.getEmail()) ? "<EMAIL>" : doDepositDto.getEmail());
        params.put("payPhone", doDepositDto.getMobile() == null ? "***********" : String.valueOf(doDepositDto.getMobile()));
        params.put("payBankCode", "gcash");
        params.put("payBankCard", "***********");
        params.put("payName", doDepositDto.getRealName());

        try {
            String paramStr = JSONUtil.toJsonStr(params);
            paramStr += payKey;
            String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
            log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, sign);
            params.put("sign", sign);
            TreeMap<String, Object> applyParams = new TreeMap<>();
            applyParams.put("ApplyParams", JSONUtil.toJsonStr(params));
            JsonNode result = paymentCommonService.doPost(doDepositDto.getDepositApi(), applyParams);

            String status = result.get("resultCode").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                resultDto.setThirdOrderNo("none");
                resultDto.setRedirectUrl(result.get("payURL").asText());
                log.info("{}[{}] 訂單號:{} 請求成功:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, result);
            } else {
                resultDto.setErrorMsg(result.get("stateInfo").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{} 狀態:{} 請求失敗:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, status, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }


    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> noticeParams = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        Map<String, String> resMap =
                JSONUtil.parseObj(noticeParams.get("NoticeParams")).toBean(new TypeReference<TreeMap<String, String>>() {
                });
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(this.depositNotifyOrderKey());
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        String status = resMap.get("payCode");
        String sign = resMap.remove("sign");
        if (DEPOSIT_CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = JSONUtil.toJsonStr(resMap);
            paramStr += payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(Long.valueOf(resMap.get("totalAmount")));
            depositPayDto.setFee(0L);
            log.info("{}[{}]訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String payKey = doBalanceDto.getPaymentMerchant().getPrivateKey();

        TreeMap<String, String> params = new TreeMap<>();
        params.put("appID", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());
        params.put("currencyCode", "PHP");
        String paramStr = JSONUtil.toJsonStr(params);
        paramStr += payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}]產生簽名字串加密前 [{}] 加密後 [{}] ", BALANCE, PayRequestEnum.BALANCE.getText(), paramStr, sign);
        params.put("sign", sign);
        TreeMap<String, Object> queryParams = new TreeMap<>();
        queryParams.put("QueryParams", JSONUtil.toJsonStr(params));

        try {
            JsonNode result = paymentCommonService.doPost(doBalanceDto.getBalanceApi(), queryParams);
            String status = result.get("resultCode").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("balance").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功 = {}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                resultDto.setMessage(result.get("resultMsg").asText());
                log.info("{}[{}] 失敗 = {}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", BALANCE, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();
        String desKey = paymentMerchant.getPublicKey();
        String orderNo = doWithdrawDto.getOrderNo();
        BankCodeDTO bankCodeDto = doWithdrawDto.getBankCodeDto();

        String bankAcctName = EncryptionDecryptionUtils.Encrypt3DES(doWithdrawDto.getUserName(), desKey);
        String bankAcctNo = EncryptionDecryptionUtils.Encrypt3DES(doWithdrawDto.getUserBankAccount(), desKey);
        String totalAmount = EncryptionDecryptionUtils.Encrypt3DES(String.valueOf(doWithdrawDto.getAmount()), desKey);
        //錢包的號碼就是手機
        String accPhone = bankAcctNo;

        log.info("{}[{}] 訂單號:{} 3DES參數加密前 bankAcctName:{} bankAcctNo:{} totalAmount:{} accPhone:{}",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo,
                doWithdrawDto.getUserName(), doWithdrawDto.getUserBankAccount(), doWithdrawDto.getAmount(),
                doWithdrawDto.getUserBankAccount());

        TreeMap<String, String> params = new TreeMap<>();
        params.put("appID", paymentMerchant.getPaymentMerchantCode());
        params.put("currencyCode", "PHP");
        params.put("randomNo", PayUtils.getRandomString(10));
        params.put("outTradeNo", orderNo);
        params.put("bankCode", bankCodeDto.getBankCode().toUpperCase());
        params.put("bankAcctName", bankAcctName);
        params.put("bankAcctNo", bankAcctNo);
        params.put("totalAmount", totalAmount);
        params.put("accPhone", accPhone);
        params.put("notifyUrl", doWithdrawDto.getWithdrawNotifyUrl());
        params.put("identityNo", bankAcctNo);
        params.put("identityType", bankCodeDto.getBankCode().toUpperCase());

        String paramStr = JSONUtil.toJsonStr(params);
        paramStr += payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,私鑰:{} ,加密後:{}",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, payKey, sign);
        params.put("sign", sign);

        TreeMap<String, Object> cashOutParams = new TreeMap<>();
        String encode = URLEncoder.encode(JSONUtil.toJsonStr(params), Constants.UFT8);
        log.info("{}[{}] 訂單號:{} CashOutParams UrlEncode加密 待加密:{} ,加密後:{} 代付參數密鑰:{}",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, JSONUtil.toJsonStr(params), encode, desKey);
        cashOutParams.put("CashOutParams", encode);

        JsonNode result = paymentCommonService.doPost(doWithdrawDto.getWithdrawApi(), cashOutParams);

        String status = result.get("resultCode").asText();
        if (SUCCESS_CODE.equalsIgnoreCase(status)) {
            resultDto.setThirdOrderNo("none");
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("{}  訂單號:{} 請求成功:{}", DF_NAME, orderNo, result);
        } else {
            resultDto.setErrorMsg(result.get("resultMsg").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("{}[{}] 訂單號:{}  狀態:{} 請求失敗:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status,
                    result);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> noticeParams = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        Map<String, String> resMap =
                JSONUtil.parseObj(noticeParams.get("NoticeParams")).toBean(new TypeReference<TreeMap<String, String>>() {
                });
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();
        String status = resMap.get("remitResult");
        String sign = resMap.remove("sign");
        if (WITHDRAW_CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = JSONUtil.toJsonStr(resMap);
            paramStr += payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(Long.valueOf(resMap.get("totalAmount")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  = {}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public String parseDepositOrderNo(DoPaymentNotifyDTO notifyDTO) {
        return Optional.ofNullable(notifyDTO)
                .map(DoPaymentNotifyDTO::getRequestMap)
                .map(requestMap -> requestMap.get("NoticeParams"))
                .map(JSONUtil::parseObj)
                .map(jsonObject -> Optional.ofNullable(
                                jsonObject.get(this.depositNotifyOrderKey()))
                        .map(Object::toString)
                        .orElse(""))
                .orElse("");
    }

    @Override
    public String parseWithdrawOrderNo(DoPaymentNotifyDTO notifyDTO) {
        return Optional.ofNullable(notifyDTO)
                .map(DoPaymentNotifyDTO::getRequestMap)
                .map(requestMap -> requestMap.get("NoticeParams"))
                .map(JSONUtil::parseObj)
                .map(jsonObject -> Optional.ofNullable(
                                jsonObject.get(this.withdrawNotifyOrderKey()))
                        .map(Object::toString)
                        .orElse(""))
                .orElse("");
    }
}
