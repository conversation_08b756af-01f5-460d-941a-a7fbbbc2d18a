package com.wd.lottery.module.payment.pay.pay1069;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.StreamSupport;

@AllArgsConstructor
@Service
@Slf4j
public class Pay1069 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {

    private final PaymentCommonService paymentCommonService;
    private static final String thirdName = "JYPAY";

    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "merOrderNo";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "merOrderNo";

    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawNotifyPrint(Constants.SUCCESS)
            .rechargeNotifyPrint(Constants.SUCCESS)
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String privateKey = paymentMerchant.getPrivateKey();
        Long amount = doDepositDto.getAmount();

        List<PaymentDynamicColumnValue> keyValues = doDepositDto.getPaymentDynamicColumnValues();
        String channel = PayUtils.getDynamicColumnValue(keyValues, Constants.CHANNEL, PayRequestEnum.DEPOSIT);
        String currency = PayUtils.getDynamicColumnValue(keyValues, Constants.CURRENCY, PayRequestEnum.DEPOSIT);

        Map<String, Object> params = new TreeMap<>();
        params.put("merNo", paymentMerchant.getPaymentMerchantCode());
        params.put("merOrderNo", orderNo);
        params.put("name", "zhang san");
        params.put("email", doDepositDto.getEmail());
        params.put("phone", doDepositDto.getMobile());
        params.put("orderAmount", PayUtils.getMoney(amount, 2));
        params.put("currency", currency);
        params.put("busiCode", channel);
        params.put("pageUrl", doDepositDto.getDepositNotifyUrl());
        params.put("notifyUrl", doDepositDto.getDepositNotifyUrl());
        params.put("timestamp", System.currentTimeMillis());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        log.info("[{}][{}][{}] 產生簽名字串加密前 [{}] ", thirdName, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr);
        String sign = EncryptionDecryptionUtils.hashWithSHA256(paramStr, privateKey);
        log.info("[{}][{}][{}] 產生簽名加密後 [{}]  ", thirdName, PayRequestEnum.DEPOSIT.getText(), orderNo, sign);

        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);

            String status = result.get("code").asText();
            if ("200".equalsIgnoreCase(status)) {
                JsonNode data = result.get("data");
                String payUrl = data.get("orderData").asText();
                resultDto.setThirdOrderNo(data.get("orderNo").asText());
                resultDto.setRedirectUrl(payUrl);
                log.info("[{}] 請求成功 訂單號:{} ", thirdName, orderNo);
                log.info("[{}][{}]訂單號:{} 跳轉地址:{}", thirdName, PayRequestEnum.DEPOSIT.getText(), orderNo, payUrl);
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("[{}] 請求失敗 訂單號:{} 狀態:{}", thirdName, orderNo, status);
            }
        } catch (Exception e) {
            log.error("[{}][{}]訂單號:{} 充值請求異常", thirdName, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String privateKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();
        log.info("[{}][{}]訂單號:{} 回調參數 = {}", thirdName, depositNotifyText, orderNo, resMap);

        String status = resMap.get("status");
        String sign = resMap.remove("sign");

        if (!"5".equalsIgnoreCase(status)) {
            log.info("[{}][{}]訂單號:{} 回調失敗 訂單狀態:{}", thirdName, depositNotifyText, orderNo, status);
            return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
        }
        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
        String checkSign = EncryptionDecryptionUtils.hashWithSHA256(paramStr, privateKey);
        if (!sign.equalsIgnoreCase(checkSign)) {
            log.error("[{}][{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                    thirdName, depositNotifyText, orderNo, paramStr, sign, checkSign);
            return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
        }
        depositPayDto.setOrderNo(orderNo);
        depositPayDto.setAmount(PayUtils.getCent(resMap.get("payAmount")));
        depositPayDto.setFee(0L);
        log.info("[{}][{}]回調成功 訂單號:{} = {}", thirdName, depositNotifyText, orderNo, depositPayDto);
        return depositPayDto;
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String privateKey = doBalanceDto.getPaymentMerchant().getPrivateKey();
        List<PaymentDynamicColumnValue> keyValues = doBalanceDto.getPaymentDynamicColumnValues();
        String currency = PayUtils.getDynamicColumnValue(keyValues, Constants.CURRENCY, PayRequestEnum.WITHDRAW);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merNo", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());
        params.put("requestNo", PayUtils.getRandomString(10));
        params.put("timestamp", System.currentTimeMillis());
        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);

        log.info("[{}][{}] 產生簽名字串加密前 [{}]", thirdName, PayRequestEnum.BALANCE.getText(), paramStr);
        String sign = EncryptionDecryptionUtils.hashWithSHA256(paramStr, privateKey);
        log.info("[{}][{}] 產生簽名加密後 [{}]", thirdName, PayRequestEnum.BALANCE.getText(),sign);
        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params);

            String status = result.get("code").asText();
            if ("200".equals(status)) {
                JsonNode list = result.path("data").path("list");
                Long balance = StreamSupport.stream(list.spliterator(), false)
                        .filter(account -> currency.equals(account.get("currency").asText()))
                        .findFirst()
                        .map(account -> PayUtils.getCent(account.get("balance").asText()))
                        .orElse(0L);;
                resultDto.setSuccess(true);
                resultDto.setBalance(balance);
                log.info("{}[{}] 成功 = {}", thirdName, PayRequestEnum.BALANCE.getText(), resultDto);
            } else {
                resultDto.setMessage(result.get("msg").asText());
                log.info("{}[{}] 失敗 = {}", thirdName, PayRequestEnum.BALANCE.getText(), resultDto);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", thirdName, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }

        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String privateKey = paymentMerchant.getPrivateKey();
        String rsaPrivateKey = paymentMerchant.getPublicKey();
        String orderNo = doWithdrawDto.getOrderNo();

        List<PaymentDynamicColumnValue> keyValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String withdrawChannel = PayUtils.getDynamicColumnValue(keyValues, "withdrawChannel", PayRequestEnum.WITHDRAW);
        String currency = PayUtils.getDynamicColumnValue(keyValues, Constants.CURRENCY, PayRequestEnum.WITHDRAW);

        Map<String, Object> params = new TreeMap<>();
        params.put("accNo", doWithdrawDto.getUserBankAccount());
        params.put("accName", doWithdrawDto.getMemberName());
        params.put("bankCode", doWithdrawDto.getBankCodeDto().getBankCode());
        params.put("busiCode", withdrawChannel);
        params.put("currency", currency);
        params.put("email", doWithdrawDto.getEmail());
        params.put("merNo", paymentMerchant.getPaymentMerchantCode());
        params.put("merOrderNo", doWithdrawDto.getOrderNo());
        params.put("notifyUrl", doWithdrawDto.getWithdrawNotifyUrl());
        params.put("orderAmount", PayUtils.getMoney(doWithdrawDto.getAmount(), 2));
        params.put("phone", doWithdrawDto.getMobile());
        params.put("timestamp", System.currentTimeMillis());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        log.info("[{}][{}][{}] 產生簽名字串加密前 [{}] ", thirdName, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr );
        String sha256Hash = EncryptionDecryptionUtils.hashWithSHA256(paramStr, privateKey);
        String base64RsaSign = EncryptionDecryptionUtils.encryptWithRSA(sha256Hash, rsaPrivateKey);
        log.info("[{}][{}][{}] 產生簽名加密後 [{}]  ", thirdName, PayRequestEnum.WITHDRAW.getText(), orderNo, base64RsaSign);

        params.put("sign", base64RsaSign);
        log.info("[{}][{}][{}] request params = {}", thirdName, PayRequestEnum.WITHDRAW.getText(), orderNo, params);

        JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);
        String status = result.get("code").asText();
        if ("200".equals(status)) {
            JsonNode data = result.get("data");
            resultDto.setThirdOrderNo(data.get("orderNo").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("[{}] 請求成功 訂單號:{} ", thirdName, orderNo);
        } else {
            resultDto.setErrorMsg(result.get("msg").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("[{}][{}]{} 請求失敗 error:{}", thirdName, PayRequestEnum.WITHDRAW.getText(), orderNo, resultDto.getErrorMsg());
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String privateKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("[{}][{}]訂單號:{} 回調參數 = {}", thirdName, withdrawNotifyText, orderNo, resMap);

        String status = resMap.get("status");
        String sign = resMap.remove("sign");

        if (!"7".equalsIgnoreCase(status)) {
            log.info("[{}][{}]訂單號:{} 回調失敗 訂單狀態:{}", thirdName, withdrawNotifyText, orderNo, status);
            return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
        }

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
        String checkSign = EncryptionDecryptionUtils.hashWithSHA256(paramStr, privateKey);
        if (!sign.equalsIgnoreCase(checkSign)) {
            log.error("[{}][{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                    thirdName, withdrawNotifyText, orderNo, paramStr, sign, checkSign);
            return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
        }
        withdrawPayDTO.setOrderNo(orderNo);
        withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("orderAmount")));
        withdrawPayDTO.setFee(0L);
        log.info("[{}][{}]回調成功 訂單號:{}  = {}", thirdName, withdrawNotifyText, orderNo, withdrawPayDTO);
        return withdrawPayDTO;
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }
}
