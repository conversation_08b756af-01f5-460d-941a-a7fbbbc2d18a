package com.wd.lottery.module.payment.pay.pay1305;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;


/**
 * SFZF-1305, Tarspay支付/代付 (PKR) (cgptf031)
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1305 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1305][TarsPay支付]";
    private final String DF_NAME = "[SFZF-1305][TarsPay代付]";
    private final String BALANCE_NAME = "[SFZF-1305][TarsPay餘額查詢]";
    private static final String SUCCESS_CODE = "0";
    private static final String CALLBACK_SUCCESS_CODE = "2";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "mchOrderNo";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "mchOrderNo";
    private static final String QUERY_ORDER_WITHDRAW_PATH = "/api/payOutInfo";
    private static final String QUERY_ORDER_DEPOSIT_PATH = "/api/payInInfo";
    private static final String REQUEST_METHOD = "POST";

    @Autowired
    private final PaymentCommonService paymentCommonService;

    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/api/payOut/unifiedOrder")
            .rechargeApiPath("/api/pay/unifiedOrder")
            .queryBalanceApiPath("/api/balanceInfo")
            .withdrawNotifyPrint("OK")
            .rechargeNotifyPrint("OK")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String privateKey = paymentMerchant.getPrivateKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String wayCode = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "wayCode", PayRequestEnum.DEPOSIT);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", paymentMerchant.getPaymentMerchantCode());
        params.put("mchOrderNo", orderNo);
        params.put("currency", "PKR");
        params.put("amount", PayUtils.getMoney(doDepositDto.getAmount()));
        params.put("notifyUrl", doDepositDto.getDepositNotifyUrl());
        params.put("wayCode", wayCode);
        params.put("step", "0");

        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String sign = this.createSign(params, privateKey, ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, REQUEST_METHOD, newPaymentConfig.rechargeApiPath(), timestamp);
            Map<String, String> headerMap = getHeaderMap(doDepositDto.getPaymentMerchant().getPublicKey(), sign, timestamp);

            JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params, headerMap);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                resultDto.setThirdOrderNo(result.get("data").get("mchOrderNo").asText());
                resultDto.setRedirectUrl(result.get("data").get("payUrl").asText());
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
            }
            log.info("{} 請求 訂單號:{} 狀態:{} 回傳:{}", ZF_NAME, orderNo, status, result);
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        Map<String, String> headerMap = new TreeMap<>(doDepositNotifyDto.getHeaderMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositNotifyDto.getPaymentDynamicColumnValues();
        String systemKey = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "systemKey", PayRequestEnum.DEPOSIT_NOTIFY);

        String status = resMap.get("state");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            Pay1305NotifyDTO notifyDTO = JSONUtil.toBean(JSONUtil.toJsonStr(resMap), Pay1305NotifyDTO.class);
            Map<String, Object> jsonMap = new TreeMap<>(JSONUtil.toBean(JSONUtil.toJsonStr(notifyDTO), Map.class));
            String jsonStr = JSONUtil.toJsonStr(jsonMap);
            String sign = headerMap.get("X-RESP-SIGNATURE");
            if (!Pay1305Utils.verifyEcdsaSignature(jsonStr, sign, systemKey)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] jsonStr:{} 收到的sign:{}", ZF_NAME, depositNotifyText, orderNo, jsonStr, sign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(notifyDTO.getPayAmount()));
            depositPayDto.setFee(0L);
            log.info("[{}[{}]訂單號:{} 回調成功 回傳：{}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) {
        String privateKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();
        String orderNo = String.valueOf(doPaymentPollingDTO.getOrderId());

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("mchOrderNo", orderNo);

        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = this.createSign(params, privateKey, ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, REQUEST_METHOD, QUERY_ORDER_DEPOSIT_PATH, timestamp);
        Map<String, String> headerMap = getHeaderMap(doPaymentPollingDTO.getPaymentMerchant().getPublicKey(), sign, timestamp);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_ORDER_DEPOSIT_PATH;
        JsonNode result = paymentCommonService.doJsonPost(apiUrl, params, headerMap);

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        String status = result.get("code").asText();
        String state = result.get("data").get("state").asText();
        if (!SUCCESS_CODE.equalsIgnoreCase(status) || !state.equalsIgnoreCase(CALLBACK_SUCCESS_CODE)) {
            return resultDTO;
        }
        String amount = result.get("data").get("payAmount").asText();
        resultDTO.setAmount(PayUtils.getCent(amount));
        resultDTO.setIsSuccess(true);
        return resultDTO;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String privateKey = doBalanceDto.getPaymentMerchant().getPrivateKey();

        Map<String, Object> params = new HashMap<>();
        params.put("mchNo", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());

        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String sign = this.createSign(params, privateKey, BALANCE_NAME, PayRequestEnum.BALANCE.getText(), null, REQUEST_METHOD, newPaymentConfig.queryBalanceApiPath(), timestamp);
            Map<String, String> headerMap = getHeaderMap(doBalanceDto.getPaymentMerchant().getPublicKey(), sign, timestamp);

            JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params, headerMap);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("data").get("availableAmount").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
            } else {
                resultDto.setMessage(result.get("msg").asText());
            }
            log.info("[{}[{}] result:{}", BALANCE_NAME, PayRequestEnum.BALANCE.getText(), result);
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常", BALANCE_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String privateKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String wayCode = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "wayCode", PayRequestEnum.WITHDRAW);

        Map<String, Object> params = new TreeMap<>();
        params.put("mchNo", paymentMerchant.getPaymentMerchantCode());
        params.put("mchOrderNo", orderNo);
        params.put("amount", PayUtils.getMoney(doWithdrawDto.getAmount()));
        params.put("wayCode", wayCode);
        params.put("currency", "PKR");
        params.put("notifyUrl", doWithdrawDto.getWithdrawNotifyUrl());
        params.put("customerContact", doWithdrawDto.getUserBankAccount());

        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String sign = this.createSign(params, privateKey, DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, REQUEST_METHOD, newPaymentConfig.withdrawApiPath(), timestamp);
            Map<String, String> headerMap = getHeaderMap(doWithdrawDto.getPaymentMerchant().getPublicKey(), sign, timestamp);

            JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params, headerMap);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                resultDto.setThirdOrderNo(result.get("data").get("mchOrderNo").asText());
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            }
            log.info("{}[{}] 訂單號:{} 請求結果 狀態:{} 回傳:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status, result);
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 提現請求異常", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        Map<String, String> headerMap = new TreeMap<>(doWithdrawNotifyDto.getHeaderMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawNotifyDto.getPaymentDynamicColumnValues();
        String systemKey = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "systemKey", PayRequestEnum.WITHDRAW_NOTIFY);

        String status = resMap.get("state");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            Pay1305NotifyDTO notifyDTO = JSONUtil.toBean(JSONUtil.toJsonStr(resMap), Pay1305NotifyDTO.class);
            Map<String, Object> jsonMap = new TreeMap<>(JSONUtil.toBean(JSONUtil.toJsonStr(notifyDTO), Map.class));
            String jsonStr = JSONUtil.toJsonStr(jsonMap);
            String sign = headerMap.get("X-RESP-SIGNATURE");
            if (!Pay1305Utils.verifyEcdsaSignature(jsonStr, sign, systemKey)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] jsonStr:{}  收到的sign:[{}]", DF_NAME, withdrawNotifyText, orderNo, jsonStr, sign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(notifyDTO.getPayAmount()));
            withdrawPayDTO.setFee(0L);
            log.info("{}[{}] 訂單號:{} 回調成功 回傳： {}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) {
        String orderNo = String.valueOf(doPaymentPollingDTO.getOrderId());
        String privateKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();

        Map<String, Object> params = new TreeMap<>();
        params.put("mchNo", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("mchOrderNo", orderNo);

        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = this.createSign(params, privateKey, DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, REQUEST_METHOD, QUERY_ORDER_WITHDRAW_PATH, timestamp);
        Map<String, String> headerMap = getHeaderMap(doPaymentPollingDTO.getPaymentMerchant().getPublicKey(), sign, timestamp);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_ORDER_WITHDRAW_PATH;
        JsonNode result = paymentCommonService.doJsonPost(apiUrl, params, headerMap);

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        String status = result.get("code").asText();
        String state = result.get("data").get("state").asText();
        if (!SUCCESS_CODE.equalsIgnoreCase(status) || !state.equalsIgnoreCase(CALLBACK_SUCCESS_CODE)) {
            return resultDTO;
        }
        String amount = result.get("data").get("payAmount").asText();
        resultDTO.setAmount(PayUtils.getCent(amount));
        resultDTO.setIsSuccess(true);
        return resultDTO;
    }

    private String createSign(Map<String, Object> reqMap, String privateKey, String payName, String payEnum, String orderId, String requestMethod, String url, String nonce) {
        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(reqMap);
        String data = buildDataString(requestMethod, url, nonce, paramStr);
        Pay1305Utils kit = new Pay1305Utils(privateKey);
        String sign = kit.sign(data.getBytes(StandardCharsets.UTF_8));
        log.info("[{}][{}][{}] 產生簽名字串加密前 [{}] 加密後 [{}]", orderId, payName, payEnum, data, sign);
        return sign;
    }

    private Map<String, String> getHeaderMap(String publicKey, String sign, String nonce) {
        Map<String, String> headerMap = new TreeMap<>();
        headerMap.put("X-API-KEY", publicKey);
        headerMap.put("X-API-NONCE", nonce);
        headerMap.put("X-API-SIGNATURE", sign);
        return headerMap;
    }

    private String buildDataString(String requestMethod, String url, String nonce, String paramStr) {
        return (requestMethod != null ? requestMethod : "") +
                "|" +
                (url != null ? url : "") +
                "|" +
                (nonce != null ? nonce : "") +
                "|" +
                (paramStr != null ? paramStr : "");
    }
}
