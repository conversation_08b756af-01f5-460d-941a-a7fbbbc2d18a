package com.wd.lottery.module.payment.pay.pay1066;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.springframework.stereotype.Service;

import java.security.PublicKey;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1066, QRpay支付（cgptf008）
 * SFZF-1067, QRpay代付（cgptf008）
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class Pay1066 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1066][QRpay支付]";
    private final String DF_NAME = "[SFZF-1067][QRpay代付]";
    private final String BALANCE = "[QRpay 餘額查詢]";
    private static final String SUCCESS_CODE = "1000";
    private static final String CALLBACK_SUCCESS_CODE = "2000";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "order_id";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "order_id";

    private final PaymentCommonService paymentCommonService;

    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/merchant/v2")
            .rechargeApiPath("/merchant/v2")
            .queryBalanceApiPath("/merchant/v2")
            .withdrawNotifyPrint("SUCCESS")
            .rechargeNotifyPrint("SUCCESS")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();

        //RSA私鑰
        String priKey = paymentMerchant.getPrivateKey();


        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        //商戶安全碼
        String safeCode = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "safeCode", PayRequestEnum.DEPOSIT);
        //GUID
        String guid = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "guid", PayRequestEnum.DEPOSIT);
        //交易密鑰
        String transactionSecret = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "transactionSecret", PayRequestEnum.DEPOSIT);
        //交易通道
        String channel = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "channel", PayRequestEnum.DEPOSIT);

        TreeMap<String, String> params = new TreeMap<>();
        params.put("user_id", doDepositDto.getPaymentMerchant().getPaymentMerchantCode());
        params.put("order_id", doDepositDto.getOrderNo());
        params.put("amount", PayUtils.getMoney(doDepositDto.getAmount()));
        params.put("currency", "PHP");
        params.put("channel", channel);
        params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("callback_url", doDepositDto.getDepositNotifyUrl());

        String depositUrl = doDepositDto.getDepositApi() + "/" + guid + "/payment";


        //RSA簽名
        String sign = Pay1066Util.generateSignature(params, priKey, "payment", safeCode);
        log.info("{}[{}] 訂單號:{} 產生簽名字串加密前 [{}] ", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, params);
        log.info("{}][{}] 訂單號:{} 產生簽名加密後 [{}]  ", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, sign);
        params.put("sign", sign);

        //加密
        String aesBody = Pay1066Util.aes256Encode(JSONUtil.toJsonStr(params), safeCode).trim();
        //取得X-Transaction-Signature
        String signature = Pay1066Util.transactionSignature(aesBody, transactionSecret);
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Transaction-Signature", signature);
        headers.put("Content-Type", "text/plain");

        try {
            RequestBody body = RequestBody.create(aesBody, MediaType.parse("text/plain"));
            JsonNode result = paymentCommonService.doPost(depositUrl, body, headers);

            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                JsonNode data = result.get("data");
                String payUrl = data.get("pay_url").asText();
                resultDto.setThirdOrderNo(data.get("transaction_id").asText());
                resultDto.setRedirectUrl(payUrl);
                log.info("{} 請求成功 訂單號:{} ", ZF_NAME, orderNo);
                log.info("{}[{}]訂單號:{} 跳轉地址:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, payUrl);
            } else {
                resultDto.setErrorMsg(result.get("message").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{} 請求失敗 訂單號:{} 狀態:{}", ZF_NAME, orderNo, status);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }



    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        PaymentMerchant paymentMerchant = doDepositNotifyDto.getPaymentMerchant();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);

        //RSA公鑰
        String pubKey = paymentMerchant.getPublicKey();
        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositNotifyDto.getPaymentDynamicColumnValues();
        //商戶安全碼
        String safeCode = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "safeCode", PayRequestEnum.DEPOSIT_NOTIFY);

        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);

        String status = resMap.get("status");
        String sign = resMap.remove("sign");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {

            PublicKey publicKey = Pay1066Util.getPublicKey(pubKey);

            boolean result = Pay1066Util.verifySignature(resMap, publicKey, sign, "payment_order_response", safeCode);

            if (!result) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 參數:{} RSA公鑰:{}, 收到的sign:{}, 類型:{}, 商戶安全碼:{}",
                        ZF_NAME, depositNotifyText, orderNo, resMap, pubKey, sign, "payment_order_response", safeCode);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.get("accept_amount")));
            depositPayDto.setFee(0L);
            log.info("{}[{}] 訂單號:{}  DepositPayDto:{}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doBalanceDto.getPaymentMerchant();
        //RSA私鑰
        String priKey = paymentMerchant.getPrivateKey();
        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doBalanceDto.getPaymentDynamicColumnValues();
        //商戶安全碼
        String safeCode = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "safeCode", PayRequestEnum.BALANCE);
        //GUID
        String guid = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "guid", PayRequestEnum.BALANCE);
        //交易密鑰
        String transactionSecret = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "transactionSecret", PayRequestEnum.BALANCE);

        TreeMap<String, String> params = new TreeMap<>();
        params.put("user_id", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());
        //返回总余额及可提取的余额
        params.put("is_balance_mode", "2");
        params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));

        String balanceUrl = doBalanceDto.getBalanceApi() + "/" + guid + "/balance";


        //RSA簽名
        String sign = Pay1066Util.generateSignature(params, priKey, "balance", safeCode);
        log.info("{}[{}]  產生簽名字串加密前 [{}] ", BALANCE, PayRequestEnum.BALANCE.getText(), params);
        log.info("{}][{}] 產生簽名加密後 [{}]  ", BALANCE, PayRequestEnum.BALANCE.getText(), sign);
        params.put("sign", sign);

        log.info("{}[{}] 未加密請求參數:{},地址:{}", BALANCE, PayRequestEnum.BALANCE.getText(), params,
                balanceUrl);

        //加密
        String aesBody = Pay1066Util.aes256Encode(JSONUtil.toJsonStr(params), safeCode).trim();
        //取得X-Transaction-Signature
        String signature = Pay1066Util.transactionSignature(aesBody, transactionSecret);

        log.info("{}[{}]  body:[{}]secret:[{}]transactionSignature:[{}]",
                BALANCE, PayRequestEnum.BALANCE.getText(), aesBody, transactionSecret, signature);

        Map<String, String> headers = new HashMap<>();
        headers.put("X-Transaction-Signature", signature);
        headers.put("Content-Type", "text/plain");

        try {
            log.info("{}[{}] 加密後請求參數:{},地址:{}", BALANCE, PayRequestEnum.BALANCE.getText(), aesBody,
                    balanceUrl);
            log.info("{}[{}] X-Transaction-Signature:{}", BALANCE, PayRequestEnum.BALANCE.getText(), signature);
            RequestBody body = RequestBody.create(aesBody, MediaType.parse("text/plain"));
            JsonNode result = paymentCommonService.doPost(balanceUrl, body, headers);
            String status = result.get("code").asText();

            if (SUCCESS_CODE.equalsIgnoreCase(status)){
                JsonNode data = result.get("data");
                String balance = data.get("php").get("d0").get("available").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功 = {}", BALANCE, PayRequestEnum.BALANCE.getText(), resultDto);
            } else {
                resultDto.setMessage(result.get("message").asText());
                log.info("{}[{}] 失敗 = {}", BALANCE, PayRequestEnum.BALANCE.getText(), resultDto);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", BALANCE, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) {
        String orderNo = doWithdrawDto.getOrderNo();
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();

        //RSA私鑰
        String priKey = paymentMerchant.getPrivateKey();
        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        //商戶安全碼
        String safeCode = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "safeCode", PayRequestEnum.WITHDRAW);
        //GUID
        String guid = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "guid", PayRequestEnum.WITHDRAW);
        //交易密鑰
        String transactionSecret = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "transactionSecret", PayRequestEnum.WITHDRAW);
        //交易通道
        String channel = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "channel", PayRequestEnum.WITHDRAW);
        //代付通道
        String withdrawType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "withdrawType", PayRequestEnum.WITHDRAW);

        //Details 物件
        TreeMap<String, String> details = new TreeMap<>();
        details.put("channel_subject", withdrawType);
        details.put("account_number", doWithdrawDto.getUserBankAccount());
        details.put("email", doWithdrawDto.getEmail());
        details.put("phone", this.getPhone(String.valueOf(doWithdrawDto.getMobile())));
        details.put("name", doWithdrawDto.getMemberName());
        details.put("address", "address");

        TreeMap<String, String> params = new TreeMap<>();
        params.put("user_id", doWithdrawDto.getPaymentMerchant().getPaymentMerchantCode());
        params.put("order_id", doWithdrawDto.getOrderNo());
        params.put("amount", PayUtils.getMoney(doWithdrawDto.getAmount()));
        params.put("currency", "PHP");
        params.put("channel", channel);
        params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("callback_url", doWithdrawDto.getWithdrawNotifyUrl());

        String withdrawUrl = doWithdrawDto.getWithdrawApi() + "/" + guid + "/withdraw";

        //RSA簽名
        String sign = Pay1066Util.generateSignature(params, priKey, "withdraw", safeCode);
        log.info("{}[{}] 訂單號:{} 產生簽名字串加密前 [{}] ", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, params);
        log.info("{}][{}] 訂單號:{} 產生簽名加密後 [{}]  ", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, sign);

        TreeMap<String, Object> request = new TreeMap<>(params);
        request.put("details", details);
        request.put("sign", sign);

        log.info("{}[{}]訂單號:{} 未加密請求參數:{},地址:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, request,
                withdrawUrl);

        //加密
        String aesBody = Pay1066Util.aes256Encode(JSONUtil.toJsonStr(request), safeCode).trim();
        //取得X-Transaction-Signature
        String signature = Pay1066Util.transactionSignature(aesBody, transactionSecret);
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Transaction-Signature", signature);
        headers.put("Content-Type", "text/plain");

        log.info("{}[{}] 訂單號:{} body:[{}]secret:[{}]transactionSignature:[{}]",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, aesBody, transactionSecret, signature);
        RequestBody body = RequestBody.create(aesBody, MediaType.parse("text/plain"));
        JsonNode result = paymentCommonService.doPost(withdrawUrl, body, headers);

        String status = result.get("code").asText();
        if (SUCCESS_CODE.equalsIgnoreCase(status)) {
            JsonNode data = result.get("data");
            resultDto.setThirdOrderNo(data.get("transaction_id").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("{} 請求成功 訂單號:{} data:{}", DF_NAME, orderNo, data);
        } else {
            resultDto.setErrorMsg(result.get("message").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("{}[{}] 訂單號:{} 請求失敗 狀態:{} message:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo,status,
                    resultDto.getMessage());
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        PaymentMerchant paymentMerchant = doWithdrawNotifyDto.getPaymentMerchant();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);

        //RSA公鑰
        String pubKey = paymentMerchant.getPublicKey();
        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawNotifyDto.getPaymentDynamicColumnValues();
        //商戶安全碼
        String safeCode = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "safeCode", PayRequestEnum.WITHDRAW_NOTIFY);

        log.info("{}[{}]訂單號:{} 回調參數 = {}", DF_NAME, withdrawNotifyText, orderNo, resMap);

        String status = resMap.get("status");
        String sign = resMap.remove("sign");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {

            PublicKey publicKey = Pay1066Util.getPublicKey(pubKey);

            boolean result = Pay1066Util.verifySignature(resMap, publicKey, sign, "withdraw_order_response", safeCode);

            if (!result) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 參數:{} RSA公鑰:{}, 收到的sign:{}, 類型:{}, 商戶安全碼:{}",
                        DF_NAME, withdrawNotifyText, orderNo, resMap, pubKey, sign, "withdraw_order_response", safeCode);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("accept_amount")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  = {}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代付回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    private String getPhone(String input){
        if (input == null || input.length() < 2) {
            return "09123456789";
        }
        int index = input.indexOf("09");

        if (index != -1) {
            // 从找到的09开始，一直到字符串的结尾
            return input.substring(index);
        } else {
            return "09123456789";
        }
    }
}
