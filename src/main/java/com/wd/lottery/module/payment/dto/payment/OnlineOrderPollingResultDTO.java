package com.wd.lottery.module.payment.dto.payment;

import com.wd.lottery.module.payment.entity.PaymentChannel;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 订单轮询任务查询结果 DTO
 * <p>
 * 用于表示订单轮询任务从第三方支付平台查询到的订单状态信息
 * </p>
 */
@Data
public class OnlineOrderPollingResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "本地订单号")
    private String orderNo;

    @Schema(description = "第三方订单号")
    private String thirdOrderNo;

    @Schema(description = "订单金额（分）")
    private Long amount;

    @Schema(description = "手续费（分）")
    private Long fee;

    @Schema(description = "是否成功")
    private Boolean isSuccess;

    @Schema(description = "三方商戶資訊", hidden = true)
    private PaymentMerchant paymentMerchant;

    @Schema(description = "三方渠道資訊", hidden = true)
    private PaymentChannel paymentChannel;

    public OnlineOrderPollingResultDTO() {
        this.amount = 0L;
        this.fee = 0L;
        this.isSuccess = false;
    }

}
