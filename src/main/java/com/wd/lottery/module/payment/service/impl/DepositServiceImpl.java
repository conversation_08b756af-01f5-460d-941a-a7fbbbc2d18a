package com.wd.lottery.module.payment.service.impl;


import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.config.SystemConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.PaymentConstants;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentChannel;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.service.DepositService;
import com.wd.lottery.module.payment.service.PaymentDynamicColumnValueService;
import com.wd.lottery.module.payment.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class DepositServiceImpl implements DepositService {

    private final PaymentDynamicColumnValueService paymentDynamicColumnValueService;

    private final SystemConfig systemConfig;

    private final Map<String, PaymentDepositHandler> paymentDepositHandlerMap;

    @Override
    public DepositResultDTO doDeposit(DoDepositDTO doDepositDto) {
        String depositText = PayRequestEnum.DEPOSIT.getText();
        DepositResultDTO resultDto = new DepositResultDTO();
        String orderNo = doDepositDto.getOrderNo();
        resultDto.setOrderNo(orderNo);

        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValueList = paymentDynamicColumnValueService.getListByPaymentMerchantId(paymentMerchant.getId());
        doDepositDto.setPaymentDynamicColumnValues(paymentDynamicColumnValueList);

        // 取server domain
        doDepositDto.setDepositNotifyUrl(systemConfig.getPaymentCallbackDomain() + systemConfig.getContextPath() + StringPool.SLASH + systemConfig.getCallBackPath() +
                StringPool.SLASH + systemConfig.getPaymentModulePath() + PaymentConstants.NOTIFY_DEPOSIT_PATH + paymentMerchant.getPaymentChannelId() + StringPool.SLASH + paymentMerchant.getId());

        PaymentChannel paymentChannel = doDepositDto.getPaymentChannel();

        PaymentDepositHandler payHandler = getDepositHandler(paymentChannel.getPaymentHandlerId());

        if (payHandler == null) {
            log.error("[{}] 未實作PayService, channelId:{}, paymentHandlerId:{}", depositText, paymentMerchant.getPaymentChannelId(), paymentChannel.getPaymentHandlerId());
            resultDto.setDepositStatusEnum(DepositStatusEnum.SERVER_ERROR);
            return resultDto;
        }

        resultDto.setJumpMode(payHandler.getConfig().jumpMode());
        String channelName = paymentChannel.getName();
        doDepositDto.setDepositApi(paymentChannel.getDepositDomain() + payHandler.getConfig().rechargeApiPath());
        try {
            log.info("[{}][{}][{}] 開始", channelName, depositText, orderNo);
            resultDto = payHandler.generateDepositRequest(doDepositDto, resultDto);
        } catch (Exception e) {
            log.error("[{}][{}][{}][金流發生錯誤]", channelName, depositText, orderNo, e);

            resultDto.setDepositStatusEnum(DepositStatusEnum.SERVER_ERROR);
            resultDto.setErrorMsg("系统发生错误");
            return resultDto;
        } finally {
            log.info("[{}][{}][{}] 完成", channelName, depositText, orderNo);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO doDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto, PaymentDepositHandler payHandler) {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        String channelName = doDepositNotifyDto.getPaymentChannel().getName();
        DepositPayDTO depositPayDto = new DepositPayDTO();
        try {
            log.info("[{}][{}][三方回調內容] [Request Body:{}]", channelName, depositNotifyText, doDepositNotifyDto.getRequestMap());
            log.info("[{}][{}][三方回調內容] [Request Header:{}]", channelName, depositNotifyText, doDepositNotifyDto.getHeaderMap());
            log.info("[{}][{}] 開始", channelName, depositNotifyText);

            depositPayDto = payHandler.handleDepositNotify(doDepositNotifyDto);
            String orderNo = depositPayDto.getOrderNo();
            String depositPayDtoJson = JsonUtils.toJson(depositPayDto);
            log.info("[{}][{}][{}][回调结果 = {}], 商戶回調: {}", channelName, depositNotifyText, orderNo, depositPayDtoJson, doDepositNotifyDto.getRequestMap());

            // 驗簽失敗
            if (depositPayDto.isSignError()) {
                log.info("[{}][{}][{}][驗簽失敗] 结果：{}", channelName, depositNotifyText, orderNo, depositPayDtoJson);
                return depositPayDto;
            }

            // 三方回傳狀態fail
            if (depositPayDto.isStatusError()) {
                log.info("[{}][{}][{}][其他原因] 结果：{}", channelName, depositNotifyText, orderNo, depositPayDtoJson);
                return depositPayDto;
            }

            log.info("[{}][{}] 结束, 结果：{}", channelName, depositNotifyText, depositPayDtoJson);
            return depositPayDto;
        } catch (Exception e) {
            log.error("[{}][{}][金流發生錯誤]", channelName, depositNotifyText, e);
            depositPayDto.setDepositStatusEnum(DepositStatusEnum.SERVER_ERROR);
            depositPayDto.setErrorMsg("系统发生错误");
            return depositPayDto;
        }
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrderStatus(DoPaymentPollingDTO doPaymentPollingDTO) {
        try {
            PaymentChannel paymentChannel = doPaymentPollingDTO.getPaymentChannel();
            Assert.notNull(paymentChannel);

            PaymentDepositHandler payHandler = getDepositHandler(paymentChannel.getPaymentHandlerId());
            Assert.notNull(payHandler);

            OnlineOrderPollingResultDTO resultDTO = payHandler.pollingDepositOrder(doPaymentPollingDTO);
            if (Objects.isNull(resultDTO)) {
                log.info("payer:{} 未实现充值订单轮询", paymentChannel.getPaymentHandlerId());
                return new OnlineOrderPollingResultDTO();
            }
            return resultDTO;
        } catch (Exception e) {
            log.error("轮询充值订单发生错误 doPaymentPollingDTO:{}", doPaymentPollingDTO, e);
            return new OnlineOrderPollingResultDTO();
        }
    }

    /**
     * 根據付款處理器ID獲取充值處理器
     *
     * @param paymentHandlerId 付款處理器ID
     * @return 充值處理器
     */
    @Override
    public PaymentDepositHandler getDepositHandler(Long paymentHandlerId) {
        return paymentDepositHandlerMap.get(PaymentConstants.PAYMENT_HANDLER_IMPL_PREFIX + paymentHandlerId);
    }

}
