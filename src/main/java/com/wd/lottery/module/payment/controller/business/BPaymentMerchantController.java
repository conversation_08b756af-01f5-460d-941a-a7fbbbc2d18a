package com.wd.lottery.module.payment.controller.business;


import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.module.payment.dto.paymentmerchant.*;
import com.wd.lottery.module.payment.dto.paymentmerchant.*;
import com.wd.lottery.module.payment.service.PaymentMerchantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;

@Tag(name = "三方商戶管理")
@RestController
@RequestMapping(value = "${business-path}/${module-path.payment}/merchants", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class BPaymentMerchantController {

    private final PaymentMerchantService paymentMerchantService;

    @Operation(summary = "多條件查詢")
    @GetMapping("/search")
    public ApiResult<?> findByCondition(@ParameterObject SearchPaymentMerchantDTO searchPaymentMerchantDto) {
        searchPaymentMerchantDto.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        return ApiResult.success(paymentMerchantService.search(searchPaymentMerchantDto, AdminTokenInfoUtil.getRequestCurrencyEnumNotNull()));
    }

    @Operation(summary = "查線上充值類型")
    @GetMapping("/onlineOptions")
    public ApiResult<?> onlineOptions() {
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        CurrencyEnum currencyEnum = AdminTokenInfoUtil.getRequestCurrencyEnumNotNull();
        return ApiResult.success(paymentMerchantService.onlineOptions(merchantId, currencyEnum));
    }

    @Operation(summary = "查可提現三方")
    @GetMapping("/withdrawChannelNameOptions")
    public ApiResult<?> withdrawChannelNameOptions() {
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        CurrencyEnum currencyEnum = AdminTokenInfoUtil.getRequestCurrencyEnumNotNull();
        return ApiResult.success(paymentMerchantService.withdrawChannelNameOptions(merchantId, currencyEnum));
    }

    @Operation(summary = "商戶配置,查可用三方商戶")
    @GetMapping("/merchantConfigOptions")
    public ApiResult<?> merchantConfigOptions() {
        CurrencyEnum currencyEnum = AdminTokenInfoUtil.getRequestCurrencyEnumNotNull();
        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        return ApiResult.success(paymentMerchantService.findAllOptions(merchantId, currencyEnum));
    }

    @Operation(summary = "新增商戶", description = "參數dynamicColumnValues不傳id")
    @PostMapping("/insert")
    public ApiResult<?> insert(@RequestBody @Valid SavePaymentMerchantDTO savePaymentMerchantDto) {
        savePaymentMerchantDto.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        savePaymentMerchantDto.setAdminName(AdminTokenInfoUtil.getAdminName());
        savePaymentMerchantDto.setUpdateTime(LocalDateTime.now());
        return ApiResult.success(paymentMerchantService.saveData(savePaymentMerchantDto));
    }

    @Operation(summary = "編輯商戶", description = "參數paymentMerchantKey公鑰或私鑰若無修改則傳空")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody @Valid EditorPaymentMerchantDTO editorPaymentChannelDto) {
        editorPaymentChannelDto.setAdminName(AdminTokenInfoUtil.getAdminName());
        editorPaymentChannelDto.setUpdateTime(LocalDateTime.now());
        return ApiResult.success(paymentMerchantService.updateData(editorPaymentChannelDto));
    }

    @Operation(summary = "刪除商戶")
    @PostMapping("/delete")
    public ApiResult<?> delete(@RequestBody @Valid DeletePaymentMerchantDTO deletePaymentMerchantDto) {
        return ApiResult.success(paymentMerchantService.deleteData(deletePaymentMerchantDto.getId()));
    }

    @Operation(summary = "修改商户金流配置启用状态", description = "修改商户金流配置启用状态")
    @PostMapping("/updateEnableEnum")
    public ApiResult<?> updateEnableEnum(@RequestBody @Valid PaymentMerchantUpdateEnableEnumParam enableEnumParam) {
        enableEnumParam.setUpdateBy(AdminTokenInfoUtil.getAdminName());
        return ApiResult.success(paymentMerchantService.updateEnableEnum(enableEnumParam));
    }

}
