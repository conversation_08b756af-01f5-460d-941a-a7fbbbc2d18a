package com.wd.lottery.module.payment.pay.pay8888;

import cn.hutool.core.thread.ThreadUtil;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/***
 * 模拟回调和请求的假三方（仅供测试）
 */

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class Pay8888 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private static final String thirdName = "模擬Pay";
    private static final String defaultUrl = "https://www.baidu.com";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "orderNo";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "orderNo";

    private final PaymentCommonService paymentCommonService;

    @Value("${payment.notify.fake:true}")
    private boolean fake;

    private static final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("")
            .rechargeApiPath("")
            .queryBalanceApiPath("")
            .withdrawNotifyPrint("ok")
            .rechargeNotifyPrint("ok")
            .jumpMode(JumpModeEnum.REDIRECT);


    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        Long amount = doDepositDto.getAmount();
        String orderNo = doDepositDto.getOrderNo();
        String merchantCode = paymentMerchant.getPaymentMerchantCode();
        TreeMap<String, Object> params = new TreeMap<>();
        params.put(DEPOSIT_NOTIFY_ORDER_KEY, orderNo);
        params.put("amount", amount);
        params.put("merchNo", merchantCode);
        params.put("notifyurl", doDepositDto.getDepositNotifyUrl());
        log.info("[{}][{}][{}] request params = {}", thirdName, PayRequestEnum.DEPOSIT.getText(), orderNo, params);

        this.doFakeRequest(doDepositDto.getDepositApi(), new HashMap<>());
        this.doFakeNotify((String) params.get(DEPOSIT_NOTIFY_ORDER_KEY)
                , (String) params.get("merchNo")
                , Long.toString((Long) params.get("amount"))
                , (String) params.get("notifyurl"));

        resultDto.setThirdOrderNo("");
        resultDto.setRedirectUrl(defaultUrl);
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        Map<String, String> resMap = doDepositNotifyDto.getRequestMap();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        if (Boolean.parseBoolean(resMap.get("status"))) {
            DepositPayDTO depositPayDto = new DepositPayDTO();
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(Long.parseLong(resMap.get("amount")));
            depositPayDto.setFee(0L);
            log.info("[{}][{}][{}] pay = {}", thirdName, PayRequestEnum.DEPOSIT_NOTIFY.getText(), resMap.get(DEPOSIT_NOTIFY_ORDER_KEY), depositPayDto);
            return depositPayDto;
        } else {
            return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, resMap.get("status"));
        }
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        OnlineOrderPollingResultDTO pollingResultDTO = new OnlineOrderPollingResultDTO();
        pollingResultDTO.setAmount(doPaymentPollingDTO.getAmount());
        pollingResultDTO.setIsSuccess(true);
        return pollingResultDTO;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String merchantCode = paymentMerchant.getPaymentMerchantCode();
        String orderNo = doWithdrawDto.getOrderNo();
        Long amount = doWithdrawDto.getAmount();
        TreeMap<String, Object> params = new TreeMap<>();
        params.put(WITHDRAW_NOTIFY_ORDER_KEY, orderNo);
        params.put("amount", amount);
        params.put("merchNo", merchantCode);
        params.put("notifyurl", doWithdrawDto.getWithdrawNotifyUrl());
        log.info("[{}][{}][{}] request params = {}", thirdName, PayRequestEnum.WITHDRAW.getText(), orderNo, params);

        this.doFakeRequest(doWithdrawDto.getWithdrawApi(), new HashMap<>());
        this.doFakeNotify((String) params.get(WITHDRAW_NOTIFY_ORDER_KEY)
                , (String) params.get("merchNo")
                , Long.toString((Long) params.get("amount"))
                , (String) params.get("notifyurl"));

        resultDto.setThirdOrderNo("");
        resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        Map<String, String> resMap = doWithdrawNotifyDto.getRequestMap();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        if (Boolean.parseBoolean(resMap.get("status"))) {
            WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(Long.parseLong(resMap.get("amount")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("[{}][{}][{}] pay = {}", thirdName, PayRequestEnum.WITHDRAW_NOTIFY.getText(), resMap.get(WITHDRAW_NOTIFY_ORDER_KEY), withdrawPayDTO);
            return withdrawPayDTO;
        } else {
            return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, resMap.get("status"));
        }
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        OnlineOrderPollingResultDTO pollingResultDTO = new OnlineOrderPollingResultDTO();
        pollingResultDTO.setAmount(doPaymentPollingDTO.getAmount());
        pollingResultDTO.setIsSuccess(true);
        return pollingResultDTO;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        resultDto.setBalance(PayUtils.getCent("3345678"));
        resultDto.setSuccess(true);
        return resultDto;
    }

    private void doFakeRequest(String url, Map<String, Object> params) {
        // todo
    }

    private void doFakeNotify(String orderNo, String merchNo, String amount, String url) {
        if (!fake) return;
        ExecutorService executor = Executors.newFixedThreadPool(1);
        executor.submit(() -> {
            ThreadUtil.sleep(30 * 1000);
            Map<String, Object> param = new TreeMap<>();
            param.put("orderNo", orderNo);
            param.put("merchNo", merchNo);
            param.put("amount", amount);
            param.put("status", fake);
            log.info("[{}] 訂單號 = {}, url = {}, params = {}", "模擬回調", orderNo, url, param);
            paymentCommonService.doPost(url, param);
        });
        executor.shutdown();
    }
}
