package com.wd.lottery.module.payment.pay.pay1254;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1254, QEPay支付代付 -PKR
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class Pay1254 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1254][QEPay支付-PKR]";
    private final String DF_NAME = "[SFZF-1254][QEPay代付-PKR]";
    private final String BALANCE = "[SFZF-1254 餘額查詢]";
    private static final String SUCCESS_CODE = "SUCCESS";
    private static final String CALLBACK_SUCCESS_CODE = "1";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "mchOrderNo";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "merTransferId";
    private static final String QUERY_ORDER_API_PATH = "/query/transfer";


    private final PaymentCommonService paymentCommonService;
    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/pay/transfer")
            .rechargeApiPath("/pay/web")
            .queryBalanceApiPath("/query/balance")
            .withdrawNotifyPrint("success")
            .rechargeNotifyPrint("success")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String payType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payType",
                PayRequestEnum.DEPOSIT);


        TreeMap<String, Object> params = new TreeMap<>();
        params.put("version", "1.0");
        params.put("mch_id", paymentMerchant.getPaymentMerchantCode());
        params.put("notify_url", doDepositDto.getDepositNotifyUrl());
        params.put("mch_order_no", orderNo);
        params.put("pay_type", payType);
        params.put("trade_amount", PayUtils.getMoney(doDepositDto.getAmount(), 2));
        params.put("order_date", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        params.put("goods_name", "goods_name");


        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, sign);
        params.put("sign_type", "MD5");
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doPost(doDepositDto.getDepositApi(), params);
        try {
            String status = result.get("respCode").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String payUrl = result.get("payInfo").asText();
                resultDto.setThirdOrderNo(result.get("orderNo").asText());
                resultDto.setRedirectUrl(payUrl);
                log.info("{}[{}] 訂單號:{} 請求成功:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, result);
            } else {
                String message = result.get("tradeMsg").asText();
                resultDto.setErrorMsg(message);
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{} 狀態:{} 請求失敗:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, status, result);

            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);

        String status = resMap.get("tradeResult");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            resMap.remove("signType");
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 算出的sign:{} , 收到的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, checkSign, sign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.get("amount")));
            depositPayDto.setFee(0L);
            log.info("{}[{}]訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String payKey = doBalanceDto.getPaymentMerchant().getPublicKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mch_id", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}]  待加密字串:{} , 加密後:{}", BALANCE, PayRequestEnum.BALANCE.getText(), paramStr, sign);
        params.put("sign_type", "MD5");
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doPost(doBalanceDto.getBalanceApi(), params);
        try {
            String status = result.get("respCode").asText();

            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("availableAmount").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                String message = result.get("errorMsg").asText();
                resultDto.setMessage(message);
                log.info("{}[{}] 失敗:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", DF_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPublicKey();
        String orderNo = doWithdrawDto.getOrderNo();

        log.info("{} 訂單號:{} 密鑰:{}", DF_NAME, orderNo, payKey);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mch_id", paymentMerchant.getPaymentMerchantCode());
        params.put("mch_transferId", orderNo);
        params.put("transfer_amount", PayUtils.getMoney(doWithdrawDto.getAmount(), 2));
        params.put("apply_date", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        params.put("bank_code", doWithdrawDto.getBankCodeDto().getBankCode());
        params.put("receive_name", doWithdrawDto.getUserName());
        params.put("receive_account", doWithdrawDto.getUserBankAccount());
        params.put("back_url", doWithdrawDto.getWithdrawNotifyUrl());
        params.put("remark", doWithdrawDto.getUserBankAccount());
        params.put("receiver_telephone", doWithdrawDto.getMobile() == null ? "***********" : "0" + doWithdrawDto.getMobile());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, sign);
        params.put("sign_type", "MD5");
        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doPost(doWithdrawDto.getWithdrawApi(), params);
            String status = result.get("respCode").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                resultDto.setThirdOrderNo(result.get("tradeNo").asText());
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
                log.info("{}  訂單號:{} 請求成功:{}", DF_NAME, orderNo, result);
            } else {
                String message = result.get("errorMsg").asText();
                resultDto.setErrorMsg(message);
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{}  狀態:{} 請求失敗:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status,
                        result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 提現請求異常", ZF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPublicKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", DF_NAME, withdrawNotifyText, orderNo, resMap);

        String status = resMap.get("tradeResult");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            resMap.remove("signType");
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{}  ,算出的sign:{} ,收到的sign:{},",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, checkSign, sign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("transferAmount")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  代付DTO:{}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        String orderNo = String.valueOf(doPaymentPollingDTO.getOrderId());
        String payKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();

        log.info("{} 訂單號:{} 密鑰:{}", DF_NAME, orderNo, payKey);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mch_id", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("mch_transferId", orderNo);

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, sign);
        params.put("sign_type", "MD5");
        params.put("sign", sign);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_ORDER_API_PATH;
        JsonNode result = paymentCommonService.doPost(apiUrl, params);

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        try {
            String status = result.get("tradeResult").asText();
            if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
                log.info("{}[{}] 訂單號:{} 查詢失敗 狀態:{} 回傳:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status, result);
                return resultDTO;
            }
            Long amount = PayUtils.getCent(result.get("transferAmount").asText());
            resultDTO.setAmount(amount);
            resultDTO.setIsSuccess(true);
            return resultDTO;
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 查詢請求異常", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            return resultDTO;
        }
    }
}
