package com.wd.lottery.module.payment.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.entity.CreateEntity;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.cash.constatns.PlatformTypeEnum;
import com.wd.lottery.module.common.constants.CommonConstants;
import com.wd.lottery.module.common.constants.LogSubTypeConstants;
import com.wd.lottery.module.common.constants.LogTypeConstants;
import com.wd.lottery.module.merchant.dto.MerchantConfigDepositPlatformDTO;
import com.wd.lottery.module.merchant.dto.MerchantConfigDepositPlatformPaymentMerchantOfflineDTO;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.payment.dto.payment_merchant_offline.*;
import com.wd.lottery.module.payment.dto.payment_merchant_offline.*;
import com.wd.lottery.module.payment.entity.PaymentBank;
import com.wd.lottery.module.payment.entity.PaymentMerchantOffline;
import com.wd.lottery.module.payment.mapper.PaymentMerchantOfflineMapper;
import com.wd.lottery.module.payment.service.PaymentBankService;
import com.wd.lottery.module.payment.service.PaymentMerchantOfflineService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 線下充值渠道
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentMerchantOfflineServiceImpl extends ServiceImpl<PaymentMerchantOfflineMapper, PaymentMerchantOffline> implements PaymentMerchantOfflineService {

    private final PaymentBankService paymentBankService;

    private final MerchantConfigService merchantConfigService;

    private final PaymentMerchantOfflineMapper paymentMerchantOfflineMapper;

    /**
     * 查單筆
     *
     * @param id          uid
     * @param bankName    銀行名稱/虛擬幣名稱/錢包名稱
     * @param bankAccount 銀行帳號/錢包地址
     * @return entity
     */
    @Override
    public PaymentMerchantOffline findByIdEnableNotNull(Long id, String bankName, String bankAccount) {
        LambdaQueryWrapper<PaymentMerchantOffline> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentMerchantOffline::getId, id);
        queryWrapper.eq(PaymentMerchantOffline::getBankName, bankName);
        queryWrapper.eq(PaymentMerchantOffline::getBankAccount, bankAccount);
        queryWrapper.eq(PaymentMerchantOffline::getEnableEnum, EnableEnum.TRUE);
        PaymentMerchantOffline paymentMerchantOffline = super.getOne(queryWrapper);

        if (paymentMerchantOffline == null) {
            log.error("線下充值渠道, 查詢參數異常, 無此資料, uid:{}, bankName:{}, bankAccount:{}", id, bankName, bankAccount);
            throw new ApiException(CommonCode.CASH_OFFLINE_PAYMENT_IS_NULL);
        }

        return paymentMerchantOffline;
    }


    /**
     * 返回當前商戶的線下充值渠道
     *
     * @param merchantId merchant uid
     * @param payName    充值類型
     * @return list entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_LONG_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<PaymentMerchantOffline> findListByMerchantIdAndPayName(Long merchantId, String payName, CurrencyEnum currencyEnum) {
        LambdaQueryWrapper<PaymentMerchantOffline> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentMerchantOffline::getMerchantId, merchantId);

        if (StringUtils.isNotBlank(payName)) {
            MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonConstants.COMMON_DICT_KEY_DEPOSIT_PLATFORM, currencyEnum);
            if (merchantConfig != null) {
                List<MerchantConfigDepositPlatformDTO> merchantConfigDepositPlatformDtoList = merchantConfig.merchantConfigToListNotEmpty(MerchantConfigDepositPlatformDTO.class);

                List<MerchantConfigDepositPlatformDTO> onlinePlatformList = merchantConfigDepositPlatformDtoList.stream()
                        .filter(dto -> PlatformTypeEnum.OFFLINE.equals(dto.getPlatformTypeEnum()))
                        .filter(dto -> dto.getPayName().equals(payName))
                        .collect(Collectors.toList());

                List<Long> paymentMerchantOfflineIdList = onlinePlatformList.stream()
                        .flatMap(dto -> dto.getPaymentMerchantOffline().stream())
                        .map(MerchantConfigDepositPlatformPaymentMerchantOfflineDTO::getPaymentMerchantOfflineId)
                        .collect(Collectors.toList());

                if (CollectionUtil.isEmpty(paymentMerchantOfflineIdList)) {
                    return new ArrayList<>();
                }
                queryWrapper.in(PaymentMerchantOffline::getId, paymentMerchantOfflineIdList);
            }
        }

        return super.list(queryWrapper);
    }

    /**
     * 依條件查詢
     *
     * @param merchantId     merchant uid
     * @param enableEnumList enable enum
     * @param currencyEnum   currency enum
     * @return list entity
     */
    @Override
    public List<PaymentMerchantOffline> findByMerchantId(Long merchantId, CurrencyEnum currencyEnum, List<EnableEnum> enableEnumList) {
        LambdaQueryWrapper<PaymentMerchantOffline> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentMerchantOffline::getMerchantId, merchantId);
        queryWrapper.eq(PaymentMerchantOffline::getCurrencyEnum, currencyEnum);
        queryWrapper.in(PaymentMerchantOffline::getEnableEnum, enableEnumList);
        return super.list(queryWrapper);
    }

    /**
     * 依uid, 商戶返回資料
     *
     * @param ids              list uid
     * @param merchantId       merchant uid
     * @param enableEnum       enable enum
     * @param currencyEnumList list currency enum
     * @return list entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<PaymentMerchantOffline> findByIdsAndMerchantIdAndEnable(List<Long> ids, Long merchantId, EnableEnum enableEnum, CurrencyEnum currencyEnum) {
        Assert.notNull(ids);
        Assert.isTrue(ids.size() > 0);
        Assert.notNull(merchantId);
        Assert.notNull(enableEnum);
        Assert.notNull(currencyEnum);

        LambdaQueryWrapper<PaymentMerchantOffline> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PaymentMerchantOffline::getId, ids);
        queryWrapper.eq(PaymentMerchantOffline::getMerchantId, merchantId);
        queryWrapper.eq(PaymentMerchantOffline::getEnableEnum, enableEnum);
        queryWrapper.eq(PaymentMerchantOffline::getCurrencyEnum, currencyEnum);
        return super.list(queryWrapper);
    }

    /**
     * 查多筆
     *
     * @param ids          list uid
     * @param merchantId   merchant uid
     * @param currencyEnum currency enum
     * @return list entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<PaymentMerchantOffline> findByIdsAndMerchantId(List<Long> ids, Long merchantId, CurrencyEnum currencyEnum) {
        Assert.notNull(ids);
        Assert.isTrue(ids.size() > 0);
        Assert.notNull(merchantId);
        Assert.notNull(currencyEnum);

        LambdaQueryWrapper<PaymentMerchantOffline> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PaymentMerchantOffline::getId, ids);
        queryWrapper.eq(PaymentMerchantOffline::getMerchantId, merchantId);
        queryWrapper.eq(PaymentMerchantOffline::getCurrencyEnum, currencyEnum);
        return super.list(queryWrapper);
    }

    /**
     * 查詢
     *
     * @param searchPaymentMerchantOfflineDto dto
     * @return page
     */
    @Override
    public Page<PaymentMerchantOfflineDTO> search(SearchPaymentMerchantOfflineDTO searchPaymentMerchantOfflineDto, Long merchantId, CurrencyEnum currencyEnum) {
        List<PaymentMerchantOfflineDTO> responseDtoList = new ArrayList<>();
        Page<PaymentMerchantOffline> basePage = new Page<>(searchPaymentMerchantOfflineDto.getCurrent(), searchPaymentMerchantOfflineDto.getSize());
        LambdaQueryWrapper<PaymentMerchantOffline> queryWrapper = new LambdaQueryWrapper<>();

        String payName = searchPaymentMerchantOfflineDto.getPayName();
        if (StringUtils.isNotBlank(payName)) {
            MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonConstants.COMMON_DICT_KEY_DEPOSIT_PLATFORM, currencyEnum);
            if (merchantConfig != null) {
                List<MerchantConfigDepositPlatformDTO> merchantConfigDepositPlatformDtoList = merchantConfig.merchantConfigToListNotEmpty(MerchantConfigDepositPlatformDTO.class);

                List<MerchantConfigDepositPlatformDTO> offlinePlatformList = merchantConfigDepositPlatformDtoList.stream()
                        .filter(dto -> PlatformTypeEnum.OFFLINE.equals(dto.getPlatformTypeEnum()))
                        .filter(dto -> dto.getPayName().equals(payName))
                        .collect(Collectors.toList());

                List<Long> paymentMerchantOfflineIdList = offlinePlatformList.stream()
                        .flatMap(dto -> dto.getPaymentMerchantOffline().stream())
                        .map(MerchantConfigDepositPlatformPaymentMerchantOfflineDTO::getPaymentMerchantOfflineId)
                        .collect(Collectors.toList());

                if (CollectionUtil.isEmpty(paymentMerchantOfflineIdList)) {
                    return new Page<>();
                }
                queryWrapper.in(PaymentMerchantOffline::getId, paymentMerchantOfflineIdList);
            }
        }

        if (StringUtils.isNotBlank(searchPaymentMerchantOfflineDto.getChannelName())) {
            queryWrapper.eq(PaymentMerchantOffline::getChannelName, searchPaymentMerchantOfflineDto.getChannelName());
        }

        if (StringUtils.isNotBlank(searchPaymentMerchantOfflineDto.getBankName())) {
            queryWrapper.eq(PaymentMerchantOffline::getBankName, searchPaymentMerchantOfflineDto.getBankName());
        }

        if (StringUtils.isNotBlank(searchPaymentMerchantOfflineDto.getBankAccount())) {
            queryWrapper.eq(PaymentMerchantOffline::getBankAccount, searchPaymentMerchantOfflineDto.getBankAccount());
        }

        if (StringUtils.isNotBlank(searchPaymentMerchantOfflineDto.getBankAccountName())) {
            queryWrapper.eq(PaymentMerchantOffline::getBankAccountName, searchPaymentMerchantOfflineDto.getBankAccountName());
        }

        if (searchPaymentMerchantOfflineDto.getPlatformEnum() != null) {
            queryWrapper.eq(PaymentMerchantOffline::getPlatformEnum, searchPaymentMerchantOfflineDto.getPlatformEnum());
        }

        if (searchPaymentMerchantOfflineDto.getEnableEnum() != null) {
            queryWrapper.eq(PaymentMerchantOffline::getEnableEnum, searchPaymentMerchantOfflineDto.getEnableEnum());
        }

        queryWrapper.eq(PaymentMerchantOffline::getMerchantId, merchantId);
        queryWrapper.in(PaymentMerchantOffline::getCurrencyEnum, currencyEnum);
        queryWrapper.orderByDesc(CreateEntity::getCreateTime);

        Page<PaymentMerchantOffline> paymentMerchantOfflinePage = super.page(basePage, queryWrapper);
        List<PaymentMerchantOffline> records = paymentMerchantOfflinePage.getRecords();

        records.forEach(paymentMerchantOffline -> {
            PaymentMerchantOfflineDTO paymentMerchantOfflineDTO = BeanUtil.copyProperties(paymentMerchantOffline, PaymentMerchantOfflineDTO.class);
            responseDtoList.add(paymentMerchantOfflineDTO);
        });

        Page<PaymentMerchantOfflineDTO> page = new Page<>(paymentMerchantOfflinePage.getPages(), paymentMerchantOfflinePage.getSize());
        page.setRecords(responseDtoList);
        page.setCurrent(paymentMerchantOfflinePage.getCurrent());
        page.setPages(paymentMerchantOfflinePage.getPages());
        page.setTotal(paymentMerchantOfflinePage.getTotal());
        return page;
    }

    /**
     * 返回商戶配置的線下充值類型
     *
     * @param merchantId   merchant uid
     * @param currencyEnum currency enum
     * @return list string
     */
    @Override
    public List<String> offlineOptions(Long merchantId, CurrencyEnum currencyEnum) {
        List<String> offlineOptions = new ArrayList<>();
        MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonConstants.COMMON_DICT_KEY_DEPOSIT_PLATFORM, currencyEnum);
        if (merchantConfig == null) {
            return offlineOptions;
        }

        List<MerchantConfigDepositPlatformDTO> merchantConfigDepositPlatformDtoList = merchantConfig.merchantConfigToListNotEmpty(MerchantConfigDepositPlatformDTO.class);
        if (CollectionUtil.isEmpty(merchantConfigDepositPlatformDtoList)) {
            return offlineOptions;
        }

        List<MerchantConfigDepositPlatformDTO> onlinePlatformList = merchantConfigDepositPlatformDtoList.stream()
                .filter(dto -> PlatformTypeEnum.OFFLINE.equals(dto.getPlatformTypeEnum()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(onlinePlatformList)) {
            return offlineOptions;
        }

        offlineOptions = onlinePlatformList.stream().map(MerchantConfigDepositPlatformDTO::getPayName).collect(Collectors.toList());
        return offlineOptions;
    }

    /**
     * 商戶配置,查可用三方商戶
     *
     * @param merchantId   merchant uid
     * @param currencyEnum currency enum
     * @return list dto
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<PaymentMerchantOfflineOptionsDTO> findAllOptions(Long merchantId, CurrencyEnum currencyEnum) {
        List<PaymentMerchantOfflineOptionsDTO> paymentMerchantOfflineDtoList = new ArrayList<>();
        List<PaymentMerchantOffline> paymentMerchantOfflineList = this.findByMerchantId(merchantId, currencyEnum, Arrays.asList(EnableEnum.TRUE, EnableEnum.FALSE));
        if (CollectionUtil.isNotEmpty(paymentMerchantOfflineList)) {
            paymentMerchantOfflineDtoList = BeanUtil.copyToList(paymentMerchantOfflineList, PaymentMerchantOfflineOptionsDTO.class);
            paymentMerchantOfflineDtoList.forEach(paymentMerchantOfflineOptionsDto -> paymentMerchantOfflineOptionsDto.setPlatformTypeEnum(PlatformTypeEnum.OFFLINE));
        }
        return paymentMerchantOfflineDtoList;
    }

    /**
     * 新增
     *
     * @param savePaymentMerchantOfflineDto dto
     * @return uid
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.PAYMENT_MERCHANT_OFFLINE, subType = LogSubTypeConstants.CREATE,
            success = "线下充值渠道, 币种:{{#savePaymentMerchantOfflineDto.currencyEnum}}, 平台类型:{{#savePaymentMerchantOfflineDto.platformEnum}}, " +
                    "渠道名称:{{#savePaymentMerchantOfflineDto.channelName}}, 渠道uid:{{#savePaymentMerchantOfflineDto.paymentBankId}}, " +
                    "名称:{{#savePaymentMerchantOfflineDto.bankName}}, 账号:{{#savePaymentMerchantOfflineDto.bankAccount}}, " +
                    "额外赠送比例:{{#rewardRate}}%, 启用:{{#savePaymentMerchantOfflineDto.enableEnum}}")
    public Long saveData(SavePaymentMerchantOfflineDTO savePaymentMerchantOfflineDto) {
        Long paymentBankId = savePaymentMerchantOfflineDto.getPaymentBankId();

        PaymentBank paymentBank = paymentBankService.findById(paymentBankId);
        if (paymentBank == null) {
            log.error("[線下充值渠道][新增] payment_bank uid錯誤 無此資料, uid:{}", paymentBankId);
            throw new ApiException(CommonCode.NO_DATA);
        }

        if (paymentBank.getCurrencyEnum() != savePaymentMerchantOfflineDto.getCurrencyEnum()) {
            log.error("[線下充值渠道][新增] 幣種錯誤, 當前操作幣種為:{}, 新增銀行卡幣種為:{}", savePaymentMerchantOfflineDto.getCurrencyEnum(), paymentBank.getCurrencyEnum());
            throw new ApiException(CommonCode.CURRENCY_ERROR);
        }

        if (paymentBank.getPlatformEnum() != savePaymentMerchantOfflineDto.getPlatformEnum()) {
            log.error("[線下充值渠道][新增] 平台錯誤, 當前操作平台為:{}, 新增銀行卡平台為:{}", savePaymentMerchantOfflineDto.getPlatformEnum(), paymentBank.getPlatformEnum());
            throw new ApiException(CommonCode.PLATFORM_ERROR);
        }

        PaymentMerchantOffline paymentMerchantOffline = BeanUtil.copyProperties(savePaymentMerchantOfflineDto, PaymentMerchantOffline.class);
        paymentMerchantOffline.setId(IdWorker.getId());
        paymentMerchantOffline.setCurrencyEnum(paymentBank.getCurrencyEnum());
        paymentMerchantOffline.setQuickAmount(savePaymentMerchantOfflineDto.getQuickAmounts().toString());
        paymentMerchantOffline.setCreateBy(savePaymentMerchantOfflineDto.getAdminName());
        paymentMerchantOffline.setCreateTime(savePaymentMerchantOfflineDto.getUpdateTime());
        paymentMerchantOffline.setUpdateBy(savePaymentMerchantOfflineDto.getAdminName());
        paymentMerchantOffline.setUpdateTime(savePaymentMerchantOfflineDto.getUpdateTime());
        if (CollUtil.isNotEmpty(savePaymentMerchantOfflineDto.getLevel())) {
            paymentMerchantOffline.setLevel(savePaymentMerchantOfflineDto.getLevel().toString());
        }
        boolean isSave = super.save(paymentMerchantOffline);
        if (!isSave) {
            log.error("[線下充值渠道][新增] 新增失敗, dto:{}", savePaymentMerchantOfflineDto);
            throw new ApiException(CommonCode.FAILED);
        }

        LogRecordContext.putVariable("rewardRate", savePaymentMerchantOfflineDto.getRewardRate() / Constants.HUNDRED_LONG);
        return paymentMerchantOffline.getId();
    }

    /**
     * 編輯
     *
     * @param editorPaymentMerchantOfflineDto dto
     * @return uid
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.PAYMENT_MERCHANT_OFFLINE, subType = LogSubTypeConstants.UPDATE,
            success = "线下充值渠道, " +
                    "渠道名称:{{#editorPaymentMerchantOfflineDto.channelName}}, 渠道uid:{{#editorPaymentMerchantOfflineDto.paymentBankId}}, " +
                    "名称:{{#editorPaymentMerchantOfflineDto.bankName}}, 账号:{{#editorPaymentMerchantOfflineDto.bankAccount}}, " +
                    "额外赠送比例:{{#rewardRate}}%, 启用:{{#editorPaymentMerchantOfflineDto.enableEnum}}")
    public Long updateData(EditorPaymentMerchantOfflineDTO editorPaymentMerchantOfflineDto) {
        Long id = editorPaymentMerchantOfflineDto.getId();

        PaymentMerchantOffline paymentMerchantOffline = super.getById(id);
        if (paymentMerchantOffline == null) {
            log.error("[線下充值渠道][更新] 無此資料");
            throw new ApiException(CommonCode.NO_DATA);
        }

        if (Objects.equals(paymentMerchantOffline.getMerchantId(), editorPaymentMerchantOfflineDto.getMerchantId()) &&
                paymentMerchantOffline.getChannelName().equals(editorPaymentMerchantOfflineDto.getChannelName()) &&
                Objects.equals(paymentMerchantOffline.getPaymentBankId(), editorPaymentMerchantOfflineDto.getPaymentBankId())) {
            throw new ApiException(CommonCode.DATA_REPEAT);
        }

        Long paymentBankId = editorPaymentMerchantOfflineDto.getPaymentBankId();
        if (!Objects.equals(paymentMerchantOffline.getPaymentBankId(), paymentBankId)) {
            PaymentBank paymentBank = paymentBankService.findById(paymentBankId);
            if (paymentBank == null) {
                log.error("[線下充值渠道][更新] payment_bank uid錯誤 無此資料, uid:{}", paymentBankId);
                throw new ApiException(CommonCode.NO_DATA);
            }

            if (paymentBank.getCurrencyEnum() != editorPaymentMerchantOfflineDto.getCurrencyEnum()) {
                log.error("[線下充值渠道][更新] 幣種錯誤, 當前操作幣種為:{}, 新增銀行卡幣種為:{}", editorPaymentMerchantOfflineDto.getCurrencyEnum(), paymentBank.getCurrencyEnum());
                throw new ApiException(CommonCode.CURRENCY_ERROR);
            }

            if (paymentBank.getPlatformEnum() != editorPaymentMerchantOfflineDto.getPlatformEnum()) {
                log.error("[線下充值渠道][更新] 平台錯誤, 當前操作平台為:{}, 新增銀行卡平台為:{}", editorPaymentMerchantOfflineDto.getPlatformEnum(), paymentBank.getPlatformEnum());
                throw new ApiException(CommonCode.PLATFORM_ERROR);
            }
        }

        BeanUtils.copyProperties(editorPaymentMerchantOfflineDto, paymentMerchantOffline);
        paymentMerchantOffline.setQuickAmount(editorPaymentMerchantOfflineDto.getQuickAmounts().toString());
        paymentMerchantOffline.setUpdateTime(editorPaymentMerchantOfflineDto.getUpdateTime());
        paymentMerchantOffline.setUpdateBy(editorPaymentMerchantOfflineDto.getAdminName());
        if (CollUtil.isNotEmpty(editorPaymentMerchantOfflineDto.getLevel())) {
            paymentMerchantOffline.setLevel(editorPaymentMerchantOfflineDto.getLevel().toString());
        } else {
            paymentMerchantOffline.setLevel("");
        }

        boolean isUpdate = super.updateById(paymentMerchantOffline);
        if (!isUpdate) {
            log.error("[線下充值渠道] 更新資料異常, id:{}", id);
            throw new ApiException(CommonCode.FAILED);
        }

        LogRecordContext.putVariable("rewardRate", editorPaymentMerchantOfflineDto.getRewardRate() / Constants.HUNDRED_LONG);
        return id;
    }

    /**
     * 刪除
     *
     * @param id uid
     * @return uid
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.PAYMENT_MERCHANT_OFFLINE, subType = LogSubTypeConstants.DELETE,
            success = "线下充值渠道, 渠道名称:{{#channelName}}, 渠道uid:{{#paymentBankId}} 名称:{{#bankName}}, 账号:{{#bankAccount}}")
    public Long deleteData(Long id) {
        PaymentMerchantOffline paymentMerchantOffline = super.getById(id);
        if (paymentMerchantOffline == null) {
            log.error("[線下充值渠道][刪除] 無此資料");
            throw new ApiException(CommonCode.NO_DATA);
        }

        boolean isRemove = super.removeById(id);
        if (!isRemove) {
            log.error("[線下充值渠道] 刪除資料異常, id:{}", id);
            throw new ApiException(CommonCode.FAILED);
        }

        LogRecordContext.putVariable("channelName", paymentMerchantOffline.getChannelName());
        LogRecordContext.putVariable("paymentBankId", paymentMerchantOffline.getPaymentBankId());
        LogRecordContext.putVariable("bankName", paymentMerchantOffline.getBankName());
        LogRecordContext.putVariable("bankAccount", paymentMerchantOffline.getBankAccount());

        return id;
    }

    @Override
    public void clearLimit() {
        super.lambdaUpdate()
                .set(PaymentMerchantOffline::getDailyDepositMoney, 0L)
                .set(PaymentMerchantOffline::getDailyDepositCount, 0L)
                .gt(PaymentMerchantOffline::getDailyDepositMoney, 0L)
                .or()
                .gt(PaymentMerchantOffline::getDailyDepositCount, 0L)
                .update();
    }

    /**
     * 增加每日充值金額, 次數
     *
     * @param id    uid
     * @param money 充值金額
     */
    @Override
    public void addDailyDepositLimit(Long id, Long money) {
        paymentMerchantOfflineMapper.addDailyDepositLimit(id, money);
    }

}
