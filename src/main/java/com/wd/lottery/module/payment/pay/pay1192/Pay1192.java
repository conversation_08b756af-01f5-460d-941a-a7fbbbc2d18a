package com.wd.lottery.module.payment.pay.pay1192;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1192 , Greenpay支付代付 INR (CGPTF016)
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class Pay1192 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1192][Greenpay支付]";
    private final String DF_NAME = "[SFZF-1192][Greenpay代付]";
    private final String BALANCE = "[SFZF-1192 餘額查詢]";
    private static final String SUCCESS_CODE = "0";
    private static final String CALLBACK_SUCCESS_CODE = "SUCCESS";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "merchantOrderNo";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "merchantOrderNo";
    private static final String QUERY_DEPOSIT_API_PATH = "/payment/orderQuery";
    private static final String QUERY_WITHDRAW_API_PATH = "/payout/orderQuery";

    private final PaymentCommonService paymentCommonService;

    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/gateway/payout/init")
            .rechargeApiPath("/gateway/payment/init")
            .queryBalanceApiPath("/gateway/payout/balance")
            .withdrawNotifyPrint("{\"code\":200}")
            .rechargeNotifyPrint("{\"code\":200}")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String aesKey = paymentMerchant.getPrivateKey();
        String aesIv = paymentMerchant.getPublicKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String payCode = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payCode",
                PayRequestEnum.DEPOSIT);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("amount", PayUtils.getMoney(doDepositDto.getAmount(), 2));
        params.put("merchantOrderNo", orderNo);
        params.put("customerName", doDepositDto.getMemberName());
        params.put("customerEmail", StrUtil.isBlank(doDepositDto.getEmail()) ? "<EMAIL>" : doDepositDto.getEmail());
        params.put("customerPhone", doDepositDto.getMobile() == null ? 9876543210L : doDepositDto.getMobile());
        params.put("transferMode", payCode);
        params.put("notifyUrl", doDepositDto.getDepositNotifyUrl());

        TreeMap<String, Object> request = new TreeMap<>();
        String encodeStr = "";
        try {
            encodeStr = Base64.encodeBase64String(EncryptionDecryptionUtils.encryptAes(JSONUtil.toJsonStr(params), aesKey, aesIv));
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 加密異常, aesKey:{}, aesIv:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, aesKey, aesIv, e);
            throw new ApiException(CommonCode.FAILED);
        }
        request.put("data", encodeStr);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} , 加密後:{}",
                ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, JSONUtil.toJsonStr(params), encodeStr, aesKey, aesIv);

        Map<String, String> header = new TreeMap<>();
        header.put("merchant_key", paymentMerchant.getPaymentMerchantCode());

        JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), request, header);
        try {
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                resultDto.setThirdOrderNo(result.get("data").get("orderNo").asText());
                resultDto.setRedirectUrl(result.get("data").get("paymentLinkUrl").asText());
                log.info("{}[{}] 訂單號:{} 請求成功:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, result);
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{} 狀態:{} 請求失敗:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, status, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        JSONObject dataJson = JSONUtil.parseObj(resMap.get("data"));
        Map<String, String> dataMap = dataJson.toBean(Map.class);
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = dataMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String aesKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);


        String status = dataMap.get("status");
        String sign = resMap.get("signature_n");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = "";
            paramStr += doDepositNotifyDto.getPaymentMerchant().getPaymentMerchantCode();
            paramStr += dataMap.get("message");
            paramStr += dataMap.get("amount");
            paramStr += dataMap.get("status");
            paramStr += dataMap.get("merchantOrderNo");
            paramStr += dataMap.get("orderNo");
            paramStr += aesKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 算出的sign:{} , 收到的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, checkSign, sign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(dataMap.get("amount")));
            depositPayDto.setFee(0L);
            log.info("{}[{}] 回調成功 訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        String orderNo = String.valueOf(doPaymentPollingDTO.getOrderId());
        PaymentMerchant paymentMerchant = doPaymentPollingDTO.getPaymentMerchant();
        String aesKey = paymentMerchant.getPrivateKey();
        String aesIv = paymentMerchant.getPublicKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchantOrderNo", orderNo);

        TreeMap<String, Object> request = new TreeMap<>();
        String encodeStr = "";
        try {
            encodeStr = Base64.encodeBase64String(EncryptionDecryptionUtils.encryptAes(JSONUtil.toJsonStr(params), aesKey, aesIv));
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 加密異常, aesKey:{}, aesIv:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, aesKey, aesIv, e);
            throw new ApiException(CommonCode.FAILED);
        }
        request.put("data", encodeStr);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} , 加密後:{}",
                ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, JSONUtil.toJsonStr(params), encodeStr);

        Map<String, String> header = new TreeMap<>();
        header.put("merchant_key", paymentMerchant.getPaymentMerchantCode());

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getDepositDomain() + QUERY_DEPOSIT_API_PATH;

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        try {
            JsonNode result = paymentCommonService.doJsonPost(apiUrl, params);
            String status = result.get("code").asText();
            String payStateCode = result.get("data").get("status").asText();

            log.info("{} 查詢結果 訂單號:{} 狀態碼:{} 回傳:{}", ZF_NAME, orderNo, payStateCode, result);
            if (SUCCESS_CODE.equalsIgnoreCase(status) && "SUCCESS".equalsIgnoreCase(payStateCode)) {
                resultDTO.setIsSuccess(true);
                resultDTO.setAmount(doPaymentPollingDTO.getAmount());
                log.info("{} 查詢成功 訂單號:{} 回傳:{}", ZF_NAME, orderNo, result);
            } else {
                log.info("{} 查詢訂單未完成 訂單號:{} 狀態:{} 回傳:{}", ZF_NAME, orderNo, payStateCode, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 查詢請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDTO;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doBalanceDto.getPaymentMerchant();
        String aesIv = doBalanceDto.getPaymentMerchant().getPublicKey();
        String aesKey = doBalanceDto.getPaymentMerchant().getPrivateKey();

        TreeMap<String, Object> params = new TreeMap<>();
        TreeMap<String, Object> request = new TreeMap<>();
        String encodeStr = "";
        try {
            encodeStr = Base64.encodeBase64String(EncryptionDecryptionUtils.encryptAes(JSONUtil.toJsonStr(params), aesKey, aesIv));
        } catch (Exception e) {
            log.error("{}[{}]} 加密異常 aesKey:{}, aesIv:{}", BALANCE, PayRequestEnum.BALANCE.getText(), aesKey, aesIv, e);
            throw new ApiException(CommonCode.FAILED);
        }
        request.put("data", encodeStr);
        log.info("{}[{}] 待加密字串:{} , 加密後:{}", BALANCE, PayRequestEnum.BALANCE.getText(), JSONUtil.toJsonStr(params), encodeStr);

        Map<String, String> header = new TreeMap<>();
        header.put("merchant_key", paymentMerchant.getPaymentMerchantCode());

        JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), request, header);
        try {
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("data").get("balance").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                String message = result.get("msg").asText();
                resultDto.setMessage(message);
                log.info("{}[{}] 失敗:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", DF_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String aesKey = paymentMerchant.getPrivateKey();
        String aesIv = paymentMerchant.getPublicKey();
        String orderNo = doWithdrawDto.getOrderNo();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String transType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "transType", PayRequestEnum.WITHDRAW);
        String transMode = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "transMode", PayRequestEnum.WITHDRAW);


        TreeMap<String, Object> params = new TreeMap<>();
        params.put("amount", PayUtils.getMoney(doWithdrawDto.getAmount(), 2));
        params.put("merchantOrderNo", orderNo);
        params.put("transferType", transType);
        if ("BANK_TRANSFER".equals(transType)) {
            params.put("transferMode", transMode);
            params.put("beneficiaryAccount", doWithdrawDto.getUserBankAccount());
            params.put("beneficiaryIFSC", doWithdrawDto.getAdditional());
        } else if ("UPI".equals(transType)) {
            params.put("beneficiaryVPA", doWithdrawDto.getUserBankAccount());
        }
        params.put("beneficiaryName", doWithdrawDto.getUserName());
        params.put("beneficiaryEmail", StrUtil.isBlank(doWithdrawDto.getEmail()) ? "<EMAIL>" : doWithdrawDto.getEmail());
        params.put("beneficiaryPhoneNo", doWithdrawDto.getMobile() == null ? 9876543210L : doWithdrawDto.getMobile());
        params.put("notifyUrl", doWithdrawDto.getWithdrawNotifyUrl());

        TreeMap<String, Object> request = new TreeMap<>();
        String encodeStr = "";
        try {
            encodeStr = Base64.encodeBase64String(EncryptionDecryptionUtils.encryptAes(JSONUtil.toJsonStr(params), aesKey, aesIv));
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 加密異常 aesKey:{}, aesIv:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, aesKey, aesIv, e);
            throw new ApiException(CommonCode.FAILED);
        }
        request.put("data", encodeStr);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} , 加密後:{}, ",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, JSONUtil.toJsonStr(params), encodeStr);

        Map<String, String> header = new TreeMap<>();
        header.put("merchant_key", paymentMerchant.getPaymentMerchantCode());

        JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), request, header);

        String status = result.get("code").asText();
        if (SUCCESS_CODE.equalsIgnoreCase(status)) {
            resultDto.setThirdOrderNo(result.get("data").get("orderNo").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("{}  訂單號:{} 請求成功:{}", DF_NAME, orderNo, result);
        } else {
            resultDto.setErrorMsg(result.get("msg").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("{}[{}] 訂單號:{}  狀態:{} 請求失敗:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status,
                    result);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        JSONObject dataJson = JSONUtil.parseObj(resMap.get("data"));
        Map<String, String> dataMap = dataJson.toBean(Map.class);
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = dataMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String aesKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", DF_NAME, withdrawNotifyText, orderNo, resMap);

        String status = dataMap.get("status");
        String sign = resMap.get("signature_n");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = "";
            paramStr += doWithdrawNotifyDto.getPaymentMerchant().getPaymentMerchantCode();
            paramStr += dataMap.get("message");
            paramStr += dataMap.get("amount");
            paramStr += dataMap.get("status");
            paramStr += dataMap.get("merchantOrderNo");
            paramStr += dataMap.get("orderNo");
            paramStr += aesKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{}  ,算出的sign:{} ,收到的sign:{},",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, checkSign, sign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(dataMap.get("amount")));
            withdrawPayDTO.setFee(PayUtils.getCent(dataMap.get("processAmount")));
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  代付DTO:{}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        PaymentMerchant paymentMerchant = doPaymentPollingDTO.getPaymentMerchant();
        String aesKey = paymentMerchant.getPrivateKey();
        String aesIv = paymentMerchant.getPublicKey();
        String orderNo = String.valueOf(doPaymentPollingDTO.getOrderId());

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchantOrderNo", orderNo);

        TreeMap<String, Object> request = new TreeMap<>();
        String encodeStr = "";
        try {
            encodeStr = Base64.encodeBase64String(EncryptionDecryptionUtils.encryptAes(JSONUtil.toJsonStr(params), aesKey, aesIv));
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 加密異常 aesKey:{}, aesIv:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, aesKey, aesIv, e);
            throw new ApiException(CommonCode.FAILED);
        }
        request.put("data", encodeStr);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} , 加密後:{}, ",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, JSONUtil.toJsonStr(params), encodeStr);

        Map<String, String> header = new TreeMap<>();
        header.put("merchant_key", paymentMerchant.getPaymentMerchantCode());

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_WITHDRAW_API_PATH;

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        try {
            JsonNode result = paymentCommonService.doJsonPost(apiUrl, params);
            String status = result.get("code").asText();
            String payStateCode = result.get("data").get("status").asText();
            log.info("{} 查詢結果 訂單號:{} 狀態碼:{} 回傳:{}", DF_NAME, orderNo, payStateCode, result);
            if (SUCCESS_CODE.equalsIgnoreCase(status) && "SUCCESS".equalsIgnoreCase(payStateCode)) {
                resultDTO.setIsSuccess(true);
                resultDTO.setAmount(doPaymentPollingDTO.getAmount());
                log.info("{} 查詢成功 訂單號:{} 回傳:{}", DF_NAME, orderNo, result);
            } else {
                log.info("{} 查詢訂單未完成 訂單號:{} 狀態:{} 回傳:{}", DF_NAME, orderNo, payStateCode, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 查詢請求異常", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDTO;
    }

    @Override
    public String parseDepositOrderNo(DoPaymentNotifyDTO notifyDTO) {
        JSONObject dataJson = JSONUtil.parseObj(notifyDTO.getRequestMap().get("data"));
        Map<String, String> dataMap = dataJson.toBean(Map.class);
        return dataMap.get(this.depositNotifyOrderKey());
    }

    @Override
    public String parseWithdrawOrderNo(DoPaymentNotifyDTO notifyDTO) {
        JSONObject dataJson = JSONUtil.parseObj(notifyDTO.getRequestMap().get("data"));
        Map<String, String> dataMap = dataJson.toBean(Map.class);
        return dataMap.get(this.withdrawNotifyOrderKey());
    }

}
