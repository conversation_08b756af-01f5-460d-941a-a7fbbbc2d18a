package com.wd.lottery.module.payment.pay.pay1241;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1241, MGM支付代付  孟加拉BDT
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class Pay1241 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1241][MGM支付-BDT]";
    private final String DF_NAME = "[SFZF-1241][MGM代付-BDT]";
    private final String BALANCE = "[SFZF-1241 餘額查詢]";
    private static final String SUCCESS_CODE = "ok";
    private static final String CALLBACK_SUCCESS_CODE = "SUCCESS";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "order_id";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "order_id";

    private final PaymentCommonService paymentCommonService;
    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/api/settle/settlement")
            .rechargeApiPath("/api/pay/payment")
            .queryBalanceApiPath("/api/merchant/balance")
            .withdrawNotifyPrint("SUCCESS")
            .rechargeNotifyPrint("SUCCESS")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }
    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.DEPOSIT);

        String timeStamp = String.valueOf(System.currentTimeMillis());

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchant_no", paymentMerchant.getPaymentMerchantCode());

        TreeMap<String, Object> dataMap = new TreeMap<>();
        dataMap.put("country", "BGD");
        dataMap.put("currency", "BDT");
        dataMap.put("payment_method_id", "WALLET");
        dataMap.put("payment_method_flow", "REDIRECT");
        dataMap.put("order_id", orderNo);
        dataMap.put("amount", PayUtils.getMoney(doDepositDto.getAmount(), 2));
        dataMap.put("notification_url", doDepositDto.getDepositNotifyUrl());
        dataMap.put("timestamp", timeStamp);

        String paramStr =  PayUtils.convertMapToQueryStringIgnoreEmpty(dataMap);
        paramStr += "&key=" + payKey;
        String signature = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        dataMap.put("signature", signature);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, signature);

        params.put("data", dataMap);
        log.info("{}[{}] 訂單號:{} 請求參數:{} ,地址:{}",
                ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, params, doDepositDto.getDepositApi());

        Map<String, String> headers = new TreeMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("ApiVersion", "1.0");
        headers.put("AppId", appId);
        headers.put("Noncestr", PayUtils.getRandomString(8));
        headers.put("Timestamp", timeStamp);

        JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params, headers);
        try {
            String status = result.get("state").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String payUrl = result.get("data").get("redirect_url").asText();
                resultDto.setThirdOrderNo(result.get("data").get("payment_id").asText());
                resultDto.setRedirectUrl(payUrl);
                log.info("{}[{}] 訂單號:{} 請求成功:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, result);
            } else {
                String message = result.get("errorMsg").asText();
                resultDto.setErrorMsg(message);
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{} 狀態:{} 請求失敗:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, status, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);

        String status = resMap.get("status");
        String sign = resMap.remove("signature");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 算出的sign:{} , 收到的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, checkSign, sign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.get("amount")));
            depositPayDto.setFee(0L);
            log.info("{}[{}]訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String payKey = doBalanceDto.getPaymentMerchant().getPublicKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doBalanceDto.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.BALANCE);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchant_no", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String signature = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}]  待加密字串:{} , 加密後:{}", BALANCE, PayRequestEnum.BALANCE.getText(), paramStr, signature);
        params.put("signature", signature);

        Map<String, String> headers = new TreeMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("ApiVersion", "1.0");
        headers.put("AppId", appId);
        headers.put("Noncestr", PayUtils.getRandomString(8));
        headers.put("Timestamp", String.valueOf(System.currentTimeMillis()));

        JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params, headers);
        try {
            String status = result.get("state").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("data").get("available_balance").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                String message = result.get("errorMsg").asText();
                resultDto.setMessage(message);
                log.info("{}[{}] 失敗:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", DF_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPublicKey();
        String orderNo = doWithdrawDto.getOrderNo();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.WITHDRAW);

        String timeStamp = String.valueOf(System.currentTimeMillis());

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchant_no", paymentMerchant.getPaymentMerchantCode());

        TreeMap<String, Object> dataMap = new TreeMap<>();
        dataMap.put("country", "BGD");
        dataMap.put("currency", "BDT");
        dataMap.put("order_id", orderNo);
        dataMap.put("amount", PayUtils.getMoney(doWithdrawDto.getAmount(), 2));
        dataMap.put("notification_url", doWithdrawDto.getWithdrawNotifyUrl());
        dataMap.put("timestamp", timeStamp);

        TreeMap<String, Object> payee = new TreeMap<>();
        payee.put("name", doWithdrawDto.getUserName());
        payee.put("account", doWithdrawDto.getUserBankAccount());
        payee.put("account_type", "BANK");
        payee.put("bank_code", doWithdrawDto.getBankCodeDto().getBankCode());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(dataMap) + "&key=" + payKey;;
        String signature = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, signature);
        dataMap.put("signature", signature);
        dataMap.put("payee", payee);

        params.put("data", dataMap);
        log.info("{}[{}] 訂單號:{} 請求參數:{} ,地址:{}",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, params, doWithdrawDto.getWithdrawApi());

        Map<String, String> headers = new TreeMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("ApiVersion", "1.0");
        headers.put("AppId", appId);
        headers.put("Noncestr", PayUtils.getRandomString(8));
        headers.put("Timestamp", timeStamp);

        JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params, headers);
        String status = result.get("state").asText();
        if (SUCCESS_CODE.equalsIgnoreCase(status)) {
            resultDto.setThirdOrderNo(result.get("data").get("settlement_id").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("{}  訂單號:{} 請求成功:{}", DF_NAME, orderNo, result);
        } else {
            String message = result.get("errorMsg").asText();
            resultDto.setErrorMsg(message);
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("{}[{}] 訂單號:{}  狀態:{} 請求失敗:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status,
                    result);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPublicKey();

        String status = resMap.get("status");
        resMap.remove("payee");
        String sign = resMap.remove("signature");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{}  ,算出的sign:{} ,收到的sign:{},",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, checkSign, sign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("amount")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  代付DTO:{}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }
}
