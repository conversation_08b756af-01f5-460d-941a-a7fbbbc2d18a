package com.wd.lottery.module.payment.pay.pay1209;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.ManualCallbackService;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * SFZF-1209 Airtel money 支付/代付
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1209 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1209][AirtelMoney支付]";
    private final String DF_NAME = "[SFZF-1209][AirtelMoney代付]";
    private static final String API_OAUTH_PATH = "/auth/oauth2/token";
    private static final String API_RECHARGE_PATH = "/merchant/v1/payments/";
    private static final String API_WITHDRAW_PATH = "/standard/v1/disbursements/";
    private static final String PAYMENT_COUNTRY = "ZM";
    private static final String PAYMENT_CURRENCY = "ZMW";

    private final PaymentCommonService paymentCommonService;
    private final ManualCallbackService manualCallbackService;

    private final PaymentConfig paymentConfig = new PaymentConfig()
            .withdrawNotifyPrint("SUCCESS")
            .rechargeNotifyPrint("SUCCESS")
            .jumpMode(JumpModeEnum.NONE);

    @Override
    public String depositNotifyOrderKey() {
        return "depositOrderId";
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String aesKey = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "aesKey", PayRequestEnum.DEPOSIT);
        String iv = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "iv", PayRequestEnum.DEPOSIT);
        String rsaKey = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "rsaKey", PayRequestEnum.DEPOSIT);

        String orderNo = doDepositDto.getOrderNo();

        Pay1209DepositReqDto depositReqDto = new Pay1209DepositReqDto();
        depositReqDto.setReference("Grape Deposit Request");

        Pay1209DepositReqDto.Subscriber subscriber = new Pay1209DepositReqDto.Subscriber();
        subscriber.setMsisdn(doDepositDto.getMemberWithdrawAccount().getAccount()); // 手機號
        subscriber.setCountry(PAYMENT_COUNTRY);
        subscriber.setCurrency(PAYMENT_CURRENCY);
        depositReqDto.setSubscriber(subscriber);

        Pay1209DepositReqDto.Transaction transaction = new Pay1209DepositReqDto.Transaction();
        transaction.setId(orderNo);
        transaction.setCountry(PAYMENT_COUNTRY);
        transaction.setCurrency(PAYMENT_CURRENCY);
        Long amount = Long.parseLong(PayUtils.getMoney(doDepositDto.getAmount()));
        transaction.setAmount(amount);
        depositReqDto.setTransaction(transaction);

        try {
            String payload = JSONUtil.toJsonStr(depositReqDto);
            String encryptKey = RSAUtil.encrypt(String.format("%s%s", aesKey, iv), rsaKey);
            String sign = AESUtil.encrypt(payload, aesKey, iv);

            Map<String, String> headers = new HashMap<>();
            headers.put("x-key", encryptKey);
            headers.put("x-signature", sign);
            headers.put("X-Country", PAYMENT_COUNTRY);
            headers.put("X-Currency", PAYMENT_CURRENCY);
            headers.put("Authorization", getBasicAuthByApi(orderNo, doDepositDto.getDepositApi(), doDepositDto.getPaymentMerchant()));

            String depositApi = String.format("%s%s", doDepositDto.getDepositApi(), API_RECHARGE_PATH);
            JsonNode apiResult = paymentCommonService.doJsonPost(depositApi, payload, headers);
            String transactionStatus = apiResult.path("data").path("transaction").path("status").asText();
            if (!"Success.".equals(transactionStatus)) {
                String status = apiResult.path("status").asText();
                resultDto.setErrorMsg(status);
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{} 請求失敗 訂單號:{} payload:{} apiResult:{}", ZF_NAME, orderNo, payload, apiResult);
                return resultDto;
            }
            resultDto.setThirdOrderNo(orderNo);

            // 異步回調
            ManualCallbackDto manualCallbackDto = new ManualCallbackDto();
            manualCallbackDto.setDepositOrderId(Long.parseLong(orderNo));
            manualCallbackDto.setMerchantId(doDepositDto.getPaymentMerchant().getMerchantId());
            manualCallbackDto.setAmount(amount);
            manualCallbackDto.setCallbackUrl(doDepositDto.getDepositNotifyUrl());
            manualCallbackService.enqueueCallbackRequest(manualCallbackDto);
            log.info("{} 請求成功 訂單號:{} ", ZF_NAME, orderNo);
            return resultDto;
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        PaymentMerchant paymentMerchant = doDepositNotifyDto.getPaymentMerchant();
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();

        DepositPayDTO depositPayDto = new DepositPayDTO();
        Map<String, String> resultMap = new HashMap<>(doDepositNotifyDto.getRequestMap());
        String orderNo = resultMap.get(this.depositNotifyOrderKey());
        String amount = resultMap.getOrDefault("amount", "0");


        try {
            String baseUrl = doDepositNotifyDto.getPaymentChannel().getDepositDomain();
            String enquiryUrl = String.format("%s/standard/v1/payments/%s", baseUrl, orderNo);
            Map<String, String> headers = new HashMap<>();
            headers.put("X-Country", PAYMENT_COUNTRY);
            headers.put("X-Currency", PAYMENT_CURRENCY);
            headers.put("Authorization", getBasicAuthByApi(orderNo, baseUrl, paymentMerchant));
            JsonNode apiResult = paymentCommonService.doGet(enquiryUrl, new HashMap<>(), headers);
            String transactionStatus = apiResult.path("data").path("transaction").path("status").asText();
            if (!"TS".equals(transactionStatus)) {
                log.info("{} 代收回調失敗 訂單狀態:{} apiResult:{}", ZF_NAME, orderNo, apiResult);
                return PayUtils.payStatusPending(orderNo, DepositStatusEnum.NOTIFY_FAIL, transactionStatus);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(amount));
            depositPayDto.setFee(0L);
            log.info("{}[{}] 回調成功 訂單號:{}", ZF_NAME, depositNotifyText, orderNo);
            return depositPayDto;
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 代收回調請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            return PayUtils.payStatusPending(orderNo, DepositStatusEnum.NOTIFY_FAIL, CommonCode.FAILED.getMessage());
        }
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        return null;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return "withdrawOrderId";
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String rsaKey = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "rsaKey", PayRequestEnum.WITHDRAW);

        String orderNo = doWithdrawDto.getOrderNo();

        Pay1209WithdrawReqDto withdrawReqDto = new Pay1209WithdrawReqDto();
        withdrawReqDto.setReference("Grape Withdraw Request");

        Pay1209WithdrawReqDto.Payee payee = new Pay1209WithdrawReqDto.Payee();
        payee.setMsisdn(doWithdrawDto.getUserBankAccount()); // 手機號
        withdrawReqDto.setPayee(payee);

        Pay1209WithdrawReqDto.Transaction transaction = new Pay1209WithdrawReqDto.Transaction();
        transaction.setId(orderNo);
        Long amount = Long.parseLong(PayUtils.getMoney(doWithdrawDto.getAmount()));
        transaction.setAmount(amount);
        withdrawReqDto.setTransaction(transaction);

        try {
            String pin = doWithdrawDto.getPaymentMerchant().getPublicKey();
            Assert.notNull(pin, "publicKey is null");
            withdrawReqDto.setPin(RSAUtil.encrypt(pin, rsaKey));

            String payload = JSONUtil.toJsonStr(withdrawReqDto);

            Map<String, String> headers = new HashMap<>();
            headers.put("X-Country", PAYMENT_COUNTRY);
            headers.put("X-Currency", PAYMENT_CURRENCY);
            headers.put("Authorization", getBasicAuthByApi(orderNo, doWithdrawDto.getWithdrawApi(), doWithdrawDto.getPaymentMerchant()));

            String withdrawApi = String.format("%s%s", doWithdrawDto.getWithdrawApi(), API_WITHDRAW_PATH);
            JsonNode apiResult = paymentCommonService.doJsonPost(withdrawApi, payload, headers);
            String transactionStatus = apiResult.path("data").path("transaction").path("status").asText();
            if (!"TS".equals(transactionStatus)) {
                String status = apiResult.path("status").asText();
                resultDto.setErrorMsg(status);
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
                log.info("{} 代付請求失敗 訂單號:{} payload:{} apiResult:{}", DF_NAME, orderNo, payload, apiResult);
                return resultDto;
            }
            resultDto.setThirdOrderNo(orderNo);
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);

            // 異步回調
            ManualCallbackDto manualCallbackDto = new ManualCallbackDto();
            manualCallbackDto.setWithdrawOrderId(Long.parseLong(orderNo));
            manualCallbackDto.setMerchantId(doWithdrawDto.getPaymentMerchant().getMerchantId());
            manualCallbackDto.setAmount(amount);
            manualCallbackDto.setCallbackUrl(doWithdrawDto.getWithdrawNotifyUrl());
            manualCallbackService.enqueueCallbackRequest(manualCallbackDto);
            log.info("{} 代付請求成功 訂單號:{} ", DF_NAME, orderNo);
            return resultDto;
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 代付請求異常", DF_NAME, PayRequestEnum.WITHDRAW_NOTIFY.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawNotifyDto.getPaymentMerchant();
        String depositNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();

        Map<String, String> resultMap = new HashMap<>(doWithdrawNotifyDto.getRequestMap());
        String orderNo = resultMap.get(this.withdrawNotifyOrderKey());

        try {
            String baseUrl = doWithdrawNotifyDto.getPaymentChannel().getWithdrawDomain();
            String enquiryUrl = String.format("%s%s%s", baseUrl, API_WITHDRAW_PATH, orderNo);
            Map<String, String> headers = new HashMap<>();
            headers.put("X-Country", PAYMENT_COUNTRY);
            headers.put("X-Currency", PAYMENT_CURRENCY);
            headers.put("Authorization", getBasicAuthByApi(orderNo, baseUrl, paymentMerchant));
            JsonNode apiResult = paymentCommonService.doGet(enquiryUrl, new HashMap<>(), headers);
            String transactionStatus = apiResult.path("data").path("transaction").path("status").asText();
            if (!"TS".equals(transactionStatus)) {
                log.info("{} 代付回調失敗 訂單狀態:{} apiResult:{}", DF_NAME, orderNo, apiResult);
                return PayUtils.withdrawStatusPending(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, transactionStatus);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            String amount = resultMap.getOrDefault("amount", "0");
            withdrawPayDTO.setAmount(PayUtils.getCent(amount));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            withdrawPayDTO.setOrderIsPending(false);
            log.info("{}[{}] 代付回調成功 訂單號:{}", DF_NAME, depositNotifyText, orderNo);
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 代付回調請求異常", DF_NAME, PayRequestEnum.WITHDRAW_NOTIFY.getText(), orderNo, e);
            return PayUtils.withdrawStatusPending(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, e.getMessage());
        }
        return withdrawPayDTO;
    }

    @Override
    public PaymentConfig getConfig() {
        return paymentConfig;
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return new OnlineOrderPollingResultDTO();
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return new OnlineOrderPollingResultDTO();
    }

    private String getBasicAuthByApi(String orderId, String baseUrl, PaymentMerchant paymentMerchant) {
        try {
            Assert.notNull(paymentMerchant.getPrivateKey());
            String url = String.format("%s%s", baseUrl, API_OAUTH_PATH);
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("client_id", paymentMerchant.getPaymentMerchantCode());
            paramMap.put("client_secret", paymentMerchant.getPrivateKey());
            paramMap.put("grant_type", "client_credentials");
            JsonNode apiResult = paymentCommonService.doJsonPost(url, paramMap);

            String accessToken = Optional.ofNullable(apiResult.get("access_token"))
                    .map(JsonNode::asText)
                    .orElseThrow(() -> new ApiException(CommonCode.THIRD_OR_SERVER_FAILED));
            return String.format("Bearer %s", accessToken);
        } catch (Exception e) {
            log.error("AirtelMoney get authorization請求異常 paymentMerchantId:{} orderId:{}", paymentMerchant.getId(), orderId, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
    }

}
