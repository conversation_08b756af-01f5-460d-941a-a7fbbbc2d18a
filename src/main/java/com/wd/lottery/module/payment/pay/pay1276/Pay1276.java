package com.wd.lottery.module.payment.pay.pay1276;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1276 , u88TGPAY(cgptf016)(PHP)
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1276 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1276][u88TGPAY支付]";
    private final String DF_NAME = "[SFZF-1276][u88TGPAY代付]";
    private final String BALANCE = "[u88TGPAY][餘額查詢]";
    private static final String SUCCESS_CODE = "200";
    private static final String CALLBACK_SUCCESS_CODE = "2";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "merchant_order_no";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "merchant_order_no";

    private final PaymentCommonService paymentCommonService;
    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/payment/v1/send/create")
            .rechargeApiPath("/payment/v1/receive/create")
            .queryBalanceApiPath("/payment/v1/query/balance")
            .withdrawNotifyPrint("OK")
            .rechargeNotifyPrint("OK")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String payType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payType", PayRequestEnum.DEPOSIT);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchant_id", doDepositDto.getPaymentMerchant().getPaymentMerchantCode());
        params.put("merchant_order_no", orderNo);
        params.put("currency", "PHP");
        params.put("amount", PayUtils.getMoney(doDepositDto.getAmount(), 2));
        params.put("notify_url", doDepositDto.getDepositNotifyUrl());
        params.put("pay_type", payType);


        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}]{} 產生簽名字串加密前 [{}] 加密後 [{}]", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, sign);
        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String payUrl = result.get("data").get("pay_url").asText();
                resultDto.setThirdOrderNo(result.get("data").get("order_no").asText());
                resultDto.setRedirectUrl(payUrl);
                log.info("{} 請求成功 訂單號:{} 回傳:{}", ZF_NAME, orderNo, result);
                log.info("{}[{}]訂單號:{} 跳轉地址:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, payUrl);
            } else {
                resultDto.setErrorMsg(result.get("message").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{} 請求失敗 訂單號:{} 狀態:{} 回傳:{}", ZF_NAME, orderNo, status, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);
        log.info("{} 訂單號:{} 回調驗籤 密鑰: {}", ZF_NAME, orderNo, payKey);

        String status = resMap.get("status");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.getOrDefault("paid_amount", resMap.get("amount"))));
            depositPayDto.setFee(0L);
            log.info("{}[{}]訂單號:{} {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String payKey = doBalanceDto.getPaymentMerchant().getPrivateKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchant_id", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());
        params.put("country_id", "5"); //5 菲律賓
        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}]產生簽名字串加密前 [{}] 加密後 [{}] ", BALANCE, PayRequestEnum.BALANCE.getText(), paramStr, sign);
        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equals(status)) {
                String balance = result.get("data").get("available_balance").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                resultDto.setMessage(result.get("message").asText());
                log.info("{}[{}] 失敗:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", BALANCE, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String payOutType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payOutType", PayRequestEnum.WITHDRAW);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchant_id", paymentMerchant.getPaymentMerchantCode());
        params.put("merchant_order_no", orderNo);
        params.put("pay_type", payOutType);
        params.put("currency", "PHP");
        params.put("amount", PayUtils.getMoney(doWithdrawDto.getAmount(), 2));
        params.put("notify_url", doWithdrawDto.getWithdrawNotifyUrl());
        params.put("bank_code", doWithdrawDto.getBankCodeDto().getBankCode());
        params.put("account_name", doWithdrawDto.getUserName());
        params.put("account_number", doWithdrawDto.getUserBankAccount());


        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}] 訂單號:{} 產生簽名字串加密前 [{}] 加密後 [{}]", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, sign);
        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                resultDto.setThirdOrderNo(result.get("data").get("order_no").asText());
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
                log.info("{} 請求成功 訂單號:{} 回傳:{}", DF_NAME, orderNo, result);
            } else {
                resultDto.setErrorMsg(result.get("message").asText());
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{} 請求失敗 狀態:{} 回傳:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 提現請求異常", ZF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();

        String status = resMap.get("status");
        String sign = resMap.remove("sign");
        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("amount")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  = {}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }
}
