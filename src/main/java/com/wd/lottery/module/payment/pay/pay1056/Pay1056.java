package com.wd.lottery.module.payment.pay.pay1056;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@AllArgsConstructor
@Service
@Slf4j
public class Pay1056 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {

    private final PaymentCommonService paymentCommonService;
    private static final String thirdName = "GCpay";

    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "order_id";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "order_id";

    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawNotifyPrint("OK")
            .rechargeNotifyPrint("OK")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String privateKey = paymentMerchant.getPrivateKey();
        Long amount = doDepositDto.getAmount();

        List<PaymentDynamicColumnValue> keyValues = doDepositDto.getPaymentDynamicColumnValues();
        String channel = PayUtils.getDynamicColumnValue(keyValues, Constants.CHANNEL, PayRequestEnum.DEPOSIT);

        Map<String, Object> params = new TreeMap<>();
        params.put("pay_customer_id", paymentMerchant.getPaymentMerchantCode());
        params.put("pay_apply_date", System.currentTimeMillis() / 1000);
        params.put("pay_order_id", orderNo);
        params.put("pay_notify_url", doDepositDto.getDepositNotifyUrl());
        params.put("pay_amount", PayUtils.getMoney(amount, 2));
        params.put("pay_channel_id", channel);

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params) + "&key=" + privateKey;
        String sign = paymentCommonService.getMd5Sign(paramStr).toUpperCase();

        params.put("pay_md5_sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);

        String status = result.get("code").asText();
        if ("0".equalsIgnoreCase(status)) {
            String payUrl = result.path("data").get("view_url").asText();
            resultDto.setThirdOrderNo(result.path("data").get("transaction_id").asText());
            resultDto.setRedirectUrl(payUrl);
            log.info("[{}] 請求成功 訂單號:{} ", thirdName, orderNo);
            log.info("[{}][{}]訂單號:{} 跳轉地址:{}", thirdName, PayRequestEnum.DEPOSIT.getText(), orderNo, payUrl);
        } else {
            resultDto.setErrorMsg(result.get("message").asText());
            resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
            log.info("[{}] 請求失敗 訂單號:{} 狀態:{}", thirdName, orderNo, status);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();
        log.info("[{}][{}]訂單號:{} 回調參數 = {}", thirdName, depositNotifyText, orderNo, resMap);

        String status = resMap.get("status");
        String sign = resMap.remove("sign");

        if (!"30000".equalsIgnoreCase(status)) {
            log.info("[{}][{}]訂單號:{} 回調失敗 訂單狀態:{}", thirdName, depositNotifyText, orderNo, status);
            return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
        }
        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap) + "&key=" + payKey;
        String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
        if (!sign.equalsIgnoreCase(checkSign)) {
            log.error("[{}][{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                    thirdName, depositNotifyText, orderNo, paramStr, sign, checkSign);
            return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
        }
        depositPayDto.setOrderNo(orderNo);
        depositPayDto.setAmount(PayUtils.getCent(resMap.get("real_amount")));
        depositPayDto.setFee(0L);
        log.info("[{}][{}]回調成功 訂單號:{} = {}", thirdName, depositNotifyText, orderNo, depositPayDto);
        return depositPayDto;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String privateKey = doBalanceDto.getPaymentMerchant().getPrivateKey();
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("pay_customer_id", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());
        params.put("pay_apply_date", System.currentTimeMillis() / 1000);
        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params) + "&key=" + privateKey;

        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        params.put("pay_md5_sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params);

            String status = result.get("code").asText();
            if ("0".equals(status)) {
                String balance = result.path("data").get("balance").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功 = {}", thirdName, PayRequestEnum.BALANCE.getText(), resultDto);
            } else {
                resultDto.setMessage(result.get("message").asText());
                log.info("{}[{}] 失敗 = {}", thirdName, PayRequestEnum.BALANCE.getText(), resultDto);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", thirdName, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }

        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String privateKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();

        Map<String, Object> params = new TreeMap<>();
        params.put("pay_customer_id", paymentMerchant.getPaymentMerchantCode());
        params.put("pay_apply_date", System.currentTimeMillis() / 1000);
        params.put("pay_order_id", doWithdrawDto.getOrderNo());
        params.put("pay_notify_url", doWithdrawDto.getWithdrawNotifyUrl());
        params.put("pay_amount", PayUtils.getMoney(doWithdrawDto.getAmount(), 2));
        params.put("pay_account_name", doWithdrawDto.getMemberName());
        params.put("pay_card_no", doWithdrawDto.getUserBankAccount());
        params.put("pay_bank_name", doWithdrawDto.getBankCodeDto().getBankCode());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params) + "&key=" + privateKey;

        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();

        params.put("pay_md5_sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);
        String status = result.get("code").asText();
        if ("0".equals(status)) {
            resultDto.setThirdOrderNo(result.path("data").get("transaction_id").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("[{}] 請求成功 訂單號:{} ", thirdName, orderNo);
        } else {
            resultDto.setErrorMsg(result.get("message").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("[{}][{}]{} 請求失敗 error:{}", thirdName, PayRequestEnum.WITHDRAW.getText(), orderNo, resultDto.getMessage());
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("[{}][{}]訂單號:{} 回調參數 = {}", thirdName, withdrawNotifyText, orderNo, resMap);

        String status = resMap.get("transaction_code");
        String sign = resMap.remove("sign");

        if (!"30000".equalsIgnoreCase(status)) {
            log.info("[{}][{}]訂單號:{} 回調失敗 訂單狀態:{}", thirdName, withdrawNotifyText, orderNo, status);
            return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
        }

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap) + "&key=" + payKey;
        String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        if (!sign.equalsIgnoreCase(checkSign)) {
            log.error("[{}][{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                    thirdName, withdrawNotifyText, orderNo, paramStr, sign, checkSign);
            return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
        }
        withdrawPayDTO.setOrderNo(orderNo);
        withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("amount")));
        withdrawPayDTO.setFee(0L);
        withdrawPayDTO.setBalance(null);
        log.info("[{}][{}]回調成功 訂單號:{}  = {}", thirdName, withdrawNotifyText, orderNo, withdrawPayDTO);
        return withdrawPayDTO;
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return new OnlineOrderPollingResultDTO();
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return new OnlineOrderPollingResultDTO();
    }
}
