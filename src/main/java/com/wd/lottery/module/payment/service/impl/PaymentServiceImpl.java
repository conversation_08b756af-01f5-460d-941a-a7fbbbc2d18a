package com.wd.lottery.module.payment.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.common.constans.*;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.common.util.MemberTokenInfoUtil;
import com.wd.lottery.common.util.TcpUtil;
import com.wd.lottery.config.SystemConfig;
import com.wd.lottery.module.cash.constatns.OrderStatusEnum;
import com.wd.lottery.module.cash.constatns.PlatformEnum;
import com.wd.lottery.module.cash.constatns.PlatformTypeEnum;
import com.wd.lottery.module.cash.entity.CashDepositOrder;
import com.wd.lottery.module.cash.entity.CashWithdrawOrder;
import com.wd.lottery.module.cash.service.CashDepositOrderService;
import com.wd.lottery.module.cash.service.CashWithdrawOrderService;
import com.wd.lottery.module.common.constants.CommonConstants;
import com.wd.lottery.module.common.constants.LogSubTypeConstants;
import com.wd.lottery.module.common.constants.LogTypeConstants;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.entity.MemberWithdrawAccount;
import com.wd.lottery.module.member.service.MemberService;
import com.wd.lottery.module.member.service.MemberWithdrawAccountService;
import com.wd.lottery.module.merchant.dto.MerchantConfigDepositPlatformDTO;
import com.wd.lottery.module.merchant.dto.MerchantConfigDepositPlatformPaymentMerchantDTO;
import com.wd.lottery.module.merchant.dto.MerchantConfigDepositPlatformPaymentMerchantOfflineDTO;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.payment.config.PaymentFrequencyConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.PaymentConstants;
import com.wd.lottery.module.payment.constants.PaymentRedisKeyConstants;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.*;
import com.wd.lottery.module.payment.handler.PaymentBaseHandler;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.param.PaymentOfflineDepositParam;
import com.wd.lottery.module.payment.param.PaymentOnlineDepositParam;
import com.wd.lottery.module.payment.service.*;
import com.wd.lottery.module.payment.service.*;
import com.wd.lottery.module.payment.util.PayUtils;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class PaymentServiceImpl implements PaymentService {

    private final static String SUCCESS = "success";
    private final Map<String, PaymentBaseHandler> paymentBaseHandlerMap;
    private final DepositService depositService;

    private final WithdrawService withdrawService;

    private final BalanceService balanceService;

    private final RedisService redisService;

    private final CashDepositOrderService cashDepositOrderService;

    private final CashWithdrawOrderService cashWithdrawOrderService;

    private final MerchantConfigService merchantConfigService;

    private final PaymentMerchantService paymentMerchantService;

    private final PaymentChannelService paymentChannelService;

    private final PaymentDynamicColumnValueService paymentDynamicColumnValueService;

    private final PaymentChannelBankService paymentChannelBankService;

    private final PaymentMerchantOfflineService paymentMerchantOfflineService;

    private final AmqpTemplate amqpTemplate;

    private final SystemConfig systemConfig;

    private final MemberService memberService;

    private final MemberWithdrawAccountService memberWithdrawAccountService;

    private final PaymentFrequencyConfig paymentFrequencyConfig;


    /**
     * 充值入口(線上)
     *
     * @return dto
     */
    @Override
    public DepositResultDTO onlineDeposit(PaymentOnlineDepositParam paymentOnlineDepositParam, MemberTokenInfoDTO memberTokenInfoDTO) {
        redisService.frequencyControl(PaymentRedisKeyConstants.DEPOSIT_INTERVALS +
                        String.format("%s_%s",MemberTokenInfoUtil.getMerchantId(),
                                MemberTokenInfoUtil.getMemberId()),
                paymentFrequencyConfig.getDepositIntervalsSeconds());

        try {

            String depositText = PayRequestEnum.DEPOSIT.getText();
            PaymentMerchant paymentMerchant = paymentMerchantService.getEnableNotNull(paymentOnlineDepositParam.getPaymentMerchantId(), memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getCurrencyEnum());
            // 三方配置檢查(是否為充值)
            this.checkThirdConfig(paymentMerchant.getPaymentChannel(), PayRequestEnum.DEPOSIT);

            // tcp連線檢查
            this.checkThirdTcp(paymentMerchant, PayRequestEnum.DEPOSIT);

            // 驗證停用金額/次數
            this.checkDepositOrWithdrawLimit(paymentMerchant, paymentOnlineDepositParam.getOrderMoney(), PayRequestEnum.DEPOSIT, paymentMerchant.getChannelName());

            MerchantConfigDepositPlatformPaymentMerchantDTO merchantConfigDepositPlatformPaymentMerchantDto = this.getMerchantConfigOnlineDepositPlatformChannelDTO(paymentOnlineDepositParam, memberTokenInfoDTO, paymentMerchant);

            long exchangeOrderMoney = getExchangeOrderMoney(paymentOnlineDepositParam.getOrderMoney(), merchantConfigDepositPlatformPaymentMerchantDto.getExchangeRate());

            // 建立訂單
            CashDepositOrder cashDepositOrder;
            cashDepositOrder = cashDepositOrderService.createOnlineOrder(paymentMerchant, memberTokenInfoDTO, paymentOnlineDepositParam.getOrderMoney(), exchangeOrderMoney, paymentOnlineDepositParam.getDeviceEnum(), paymentOnlineDepositParam.getPayName(), paymentOnlineDepositParam.getMemberName(), paymentOnlineDepositParam.getUpdateTime());

            DoDepositDTO doDepositDto = new DoDepositDTO();
            doDepositDto.setMemberId(memberTokenInfoDTO.getId());
            doDepositDto.setMemberName(memberTokenInfoDTO.getMemberName());
            doDepositDto.setMobile(memberTokenInfoDTO.getMobile());
            doDepositDto.setEmail(memberTokenInfoDTO.getEmail());
            doDepositDto.setAmount(cashDepositOrder.getRealMoney());
            doDepositDto.setOrderNo(String.valueOf(cashDepositOrder.getId()));
            doDepositDto.setPaymentChannel(paymentMerchant.getPaymentChannel());
            doDepositDto.setPaymentMerchant(paymentMerchant);
            doDepositDto.setRealName(memberTokenInfoDTO.getRealName());

            this.setMemberWithdrawAccountIfExist(doDepositDto, paymentOnlineDepositParam, paymentMerchant, memberTokenInfoDTO);

            // 依照平台貨幣單位轉換訂單金額
            this.adjustDepositRequestAmountByCurrency(doDepositDto, cashDepositOrder.getCurrencyEnum());

            DepositResultDTO depositResultDto = depositService.doDeposit(doDepositDto);

            // 更新錯誤訊息及失敗訂單
            if (depositResultDto.getDepositStatusEnum() == DepositStatusEnum.THIRD_ERROR ||
                    depositResultDto.getDepositStatusEnum() == DepositStatusEnum.SERVER_ERROR) {
                String errorMsg = depositResultDto.getErrorMsg();
                log.error("[{}][{}][{}][金流發生錯誤] {}", paymentMerchant.getPaymentChannelId(), depositText, doDepositDto.getOrderNo(), errorMsg);

                cashDepositOrderService.updateStatusAndMessage(cashDepositOrder, OrderStatusEnum.FAILED, errorMsg);
                throw new ApiException(CommonCode.CASH_DEPOSIT_UPDATE_ORDER_FAIL);
            }

            cashDepositOrderService.tryUpdateThirdNo(cashDepositOrder, depositResultDto.getThirdOrderNo());

            return depositResultDto;
        } catch (Exception e) {
            log.error("[線上充值] 發生異常: {}", e.getMessage(), e);
            throw new ApiException(CommonCode.CASH_ONLINE_DEPOSIT_FAILURE);
        }
    }

    /**
     * 充值入口(線下)
     *
     * @return order no
     */
    @Override
    public Long offlineDeposit(PaymentOfflineDepositParam paymentOfflineDepositParam, MemberTokenInfoDTO memberTokenInfoDTO) {
        redisService.frequencyControl(PaymentRedisKeyConstants.DEPOSIT_INTERVALS +
                String.format("%s_%s",MemberTokenInfoUtil.getMerchantId(),
                        MemberTokenInfoUtil.getMemberId()),
                paymentFrequencyConfig.getDepositIntervalsSeconds());

        PaymentMerchantOffline paymentMerchantOffline = paymentMerchantOfflineService.findByIdEnableNotNull(paymentOfflineDepositParam.getPaymentMerchantOfflineId(),
                paymentOfflineDepositParam.getBankName(), paymentOfflineDepositParam.getBankAccount());

        // 驗證停用金額/次數
        this.checkOfflineDepositLimit(paymentMerchantOffline, paymentOfflineDepositParam.getOrderMoney(), paymentMerchantOffline.getChannelName());

        MerchantConfigDepositPlatformPaymentMerchantOfflineDTO merchantConfigOfflineDepositPlatformChannelDto = this.getMerchantConfigOfflineDepositPlatformChannelDTO(paymentOfflineDepositParam, memberTokenInfoDTO, paymentMerchantOffline);

        if (isVirtualDeposit(paymentOfflineDepositParam)) {
            validateVirtualDepositParam(memberTokenInfoDTO.getMerchantId(), paymentOfflineDepositParam.getTxId());
        } else {
            validateOtherDepositParam(paymentOfflineDepositParam);
        }

        long exchangeOrderMoney = getExchangeOrderMoney(paymentOfflineDepositParam.getOrderMoney(), merchantConfigOfflineDepositPlatformChannelDto.getExchangeRate());
        paymentOfflineDepositParam.setExchangeOrderMoney(exchangeOrderMoney);

        CashDepositOrder cashDepositOrder = cashDepositOrderService.createOfflineOrder(paymentMerchantOffline, paymentOfflineDepositParam, memberTokenInfoDTO);

        return cashDepositOrder.getId();
    }

    /**
     * 充值回調入口
     *
     * @param paymentMerchantId  payment_merchant uid
     * @param channelId          payment_channel uid
     * @param doPaymentNotifyDto dto
     * @return dto
     */
    @Override
    public String depositNotify(Long channelId, Long paymentMerchantId, DoPaymentNotifyDTO doPaymentNotifyDto) {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        String rechargeNotifyPrint = SUCCESS;

        PaymentChannel paymentChannel = getPaymentChannelNotNull(channelId);

        PaymentDepositHandler payer = depositService.getDepositHandler(paymentChannel.getPaymentHandlerId());
        if (payer == null) {
            log.error("[{}] 未實作PayService, paymentHandlerId:{}", depositNotifyText, paymentChannel.getPaymentHandlerId());
            return rechargeNotifyPrint;
        }

        String orderNo = payer.parseDepositOrderNo(doPaymentNotifyDto);
        if (StringUtils.isBlank(orderNo)) {
            log.error("[{}] 訂單號異常", depositNotifyText);
            return rechargeNotifyPrint;
        }

        doPaymentNotifyDto.setPaymentChannel(paymentChannel);

        String lockKey = PaymentRedisKeyConstants.PAYMENT_NOTIFY_LOCK + orderNo;
        boolean isLock = redisService.setIfAbsent(lockKey, "", PaymentRedisKeyConstants.PAYMENT_NOTIFY_TIME);
        if (Boolean.FALSE.equals(isLock)) {
            log.info("[{}][{}], redis lock", depositNotifyText, orderNo);
            return rechargeNotifyPrint;
        }


        PaymentMerchant paymentMerchant = paymentMerchantService.findByIdByCache(paymentMerchantId);
        doPaymentNotifyDto.setPaymentMerchant(paymentMerchant);

        List<PaymentDynamicColumnValue> paymentDynamicColumnValueList = paymentDynamicColumnValueService.getListByPaymentMerchantId(paymentMerchantId);
        doPaymentNotifyDto.setPaymentDynamicColumnValues(paymentDynamicColumnValueList);

        String channelName = paymentChannel.getName();

        DepositPayDTO depositPayDto = depositService.doDepositNotify(doPaymentNotifyDto, payer);
        depositPayDto.setPaymentChannelName(channelName);
        depositPayDto.setPaymentMerchantId(paymentMerchantId);
        depositPayDto.setMerchantId(paymentMerchant.getMerchantId());

        rechargeNotifyPrint = payer.getConfig().rechargeNotifyPrint();

        // 如果還在等待會員付款的訂單不處理
        if (Objects.equals(depositPayDto.getOrderIsPending(), true)) {
            return rechargeNotifyPrint;
        }

        // 訂單邏輯處理
        amqpTemplate.convertAndSend(RabbitMQConstants.PAYMENT_DEPOSIT_NOTIFY_EXCHANGE, "", JSONUtil.toJsonStr(depositPayDto));

        log.info("[{}][{}][{}] success", channelName, depositNotifyText, orderNo);
        log.info("[{}][{}][{}] rechargeNotifyPrint = {}", channelName, depositNotifyText, orderNo, rechargeNotifyPrint);
        return rechargeNotifyPrint;
    }


    /**
     * 提現入口(後台審核提現)
     *
     * @return dto
     */
    @Override
    @LogRecord(bizNo = "", type = LogTypeConstants.CASH_WITHDRAW_ORDER, subType = LogSubTypeConstants.UPDATE,
            success = "提现审核 三方出款, 订单号:{{#orderId}}, 三方金流:{{#channelName}}")
    public WithdrawResultDTO withdraw(Long channelId, Long paymentMerchantId, Long orderId, String adminName) {
        String withdrawText = PayRequestEnum.WITHDRAW.getText();
        DoWithdrawDTO doWithdrawDto = new DoWithdrawDTO();

        PaymentChannel paymentChannel = paymentChannelService.findById(channelId);
        this.checkWithdrawPaymentChannel(paymentChannel);
        doWithdrawDto.setPaymentChannel(paymentChannel);

        PaymentMerchant paymentMerchant = paymentMerchantService.findById(paymentMerchantId);
        this.checkPaymentMerchant(paymentMerchant, paymentChannel.getName(), paymentMerchant.getMerchantId());
        doWithdrawDto.setPaymentMerchant(paymentMerchant);

        Long merchantId = AdminTokenInfoUtil.getRequestMerchantIdNotNull();
        if (!Objects.equals(merchantId, paymentMerchant.getMerchantId())) {
            log.error("[{}][{}] 商戶錯誤, 操作者merchantId:{}, 訂單merchantId:{}", withdrawText, doWithdrawDto.getOrderNo(), merchantId, paymentMerchant.getMerchantId());
            throw new ApiException(CommonCode.PARAM_INVALID);
        }

        List<PaymentDynamicColumnValue> paymentDynamicColumnValueList = paymentDynamicColumnValueService.getListByPaymentMerchantId(paymentMerchantId);
        doWithdrawDto.setPaymentDynamicColumnValues(paymentDynamicColumnValueList);

        // 取server domain
        doWithdrawDto.setWithdrawNotifyUrl(systemConfig.getPaymentCallbackDomain() + systemConfig.getContextPath() + StringPool.SLASH + systemConfig.getCallBackPath() +
                StringPool.SLASH + systemConfig.getPaymentModulePath() + PaymentConstants.NOTIFY_WITHDRAW_PATH + paymentMerchant.getPaymentChannelId() + StringPool.SLASH + paymentMerchant.getId());

        // getBean, check rd有無串接三方
        PaymentWithdrawHandler payer = (PaymentWithdrawHandler) this.getPayer(paymentChannel.getPaymentHandlerId());
        if (payer == null) {
            log.error("[{}][{}][{}] 未實作PayService, channelId:{}, paymentHandlerId:{}", paymentChannel.getName(), withdrawText, doWithdrawDto.getOrderNo(), channelId, paymentChannel.getPaymentHandlerId());
            throw new ApiException(CommonCode.PARAM_INVALID);
        }

        CashWithdrawOrder cashWithdrawOrder = cashWithdrawOrderService.findById(orderId, paymentMerchant.getMerchantId());
        if (cashWithdrawOrder == null) {
            log.error("[{}][{}][{}] 訂單uid錯誤 無此資料", paymentChannel.getName(), withdrawText, orderId);
            throw new ApiException(CommonCode.BET_ORDER_NOT_EXISTS);
        }

        if (cashWithdrawOrder.getLockEnum() == BooleanEnum.FALSE) {
            log.error("[{}][{}][{}] 訂單鎖定狀態未鎖定", paymentChannel.getName(), withdrawText, orderId);
            throw new ApiException(CommonCode.LOCK_STATUS_ERROR);
        }

        if (!cashWithdrawOrder.getLocker().equals(adminName)) {
            log.error("[{}][{}][{}] 訂單操作者異常,非當前訂單鎖定者, 訂單操作者:{}, 訂單鎖定者:{}", paymentChannel.getName(), withdrawText, orderId, adminName, cashWithdrawOrder.getLocker());
            throw new ApiException(CommonCode.LOCKER_ERROR);
        }

        // 只有狀態是用戶提現還未經過三方處理的訂單或經過三方處理但是失敗的訂單後解鎖的才可以提單
        if (cashWithdrawOrder.getStatusEnum() != OrderStatusEnum.PENDING && cashWithdrawOrder.getStatusEnum() != OrderStatusEnum.FAILED && cashWithdrawOrder.getStatusEnum() != OrderStatusEnum.PROGRESSING) {
            log.error("[{}][{}][{}] 訂單狀態異常:{}", paymentChannel.getName(), withdrawText, cashWithdrawOrder.getId(), cashWithdrawOrder.getStatusEnum());
            throw new ApiException(CommonCode.STATUS_ERROR);
        }

        // 驗證停用金額/次數
        this.checkDepositOrWithdrawLimit(paymentMerchant, cashWithdrawOrder.getRealMoney(), PayRequestEnum.WITHDRAW, paymentChannel.getName());

        // set用戶資訊
        Member member = memberService.getMemberById(cashWithdrawOrder.getMemberId(), merchantId);
        doWithdrawDto.setMemberId(member.getId());
        doWithdrawDto.setMemberName(member.getMemberName());
        doWithdrawDto.setMobile(member.getMobile());
        doWithdrawDto.setEmail(member.getEmail());

        // set 銀行編碼
        List<PaymentChannelBank> paymentChannelBanks = paymentChannelBankService.findAllByPaymentChannelId(paymentChannel.getId());
        doWithdrawDto.setBankCodeDto(PayUtils.changeBankCode(paymentChannelBanks, cashWithdrawOrder.getBankId(), PayRequestEnum.WITHDRAW));
        doWithdrawDto.setUserBankAccount(cashWithdrawOrder.getBankAccount());

        doWithdrawDto.setAdditional(cashWithdrawOrder.getAdditional());
        doWithdrawDto.setMemberName(cashWithdrawOrder.getMemberName());
        doWithdrawDto.setAmount(cashWithdrawOrder.getRealMoney());
        doWithdrawDto.setOrderNo(String.valueOf(cashWithdrawOrder.getId()));
        doWithdrawDto.setUserName(cashWithdrawOrder.getRealName().trim());

        // 依照平台貨幣單位轉換訂單金額
        this.adjustWithdrawRequestAmountByCurrency(doWithdrawDto, cashWithdrawOrder.getCurrencyEnum());

        // 開始三方代付
        cashWithdrawOrderService.updateOrderToProgressing(cashWithdrawOrder);
        WithdrawResultDTO withdrawResultDto = withdrawService.doWithdraw(doWithdrawDto, payer);

        // 更新訂單的錯誤訊息
        if (withdrawResultDto.getWithdrawStatusEnum() == WithdrawStatusEnum.THIRD_ERROR ||
                withdrawResultDto.getWithdrawStatusEnum() == WithdrawStatusEnum.SERVER_ERROR) {
            String errorMsg = withdrawResultDto.getErrorMsg();
            log.error("[{}][{}][{}][金流發生錯誤] {}", paymentChannel.getName(), withdrawText, doWithdrawDto.getOrderNo(), errorMsg);

            cashWithdrawOrderService.updateThirdMessage(cashWithdrawOrder, errorMsg, paymentMerchant.getChannelName(),
                    channelId, paymentChannel.getName(), paymentMerchantId, paymentMerchant.getRemark());

            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }

        // 三方成功後更新當前使用的三方資訊
        cashWithdrawOrderService.updateThirdInformation(cashWithdrawOrder, withdrawResultDto, paymentChannel, paymentMerchant, adminName);

        LogRecordContext.putVariable("channelName", paymentMerchant.getChannelName());

        return withdrawResultDto;
    }

    /**
     * 提現回調入口
     *
     * @param paymentMerchantId  payment_merchant uid
     * @param channelId          payment_channel uid
     * @param doPaymentNotifyDto dto
     * @return dto
     */
    @Override
    public String withdrawNotify(Long channelId, Long paymentMerchantId, DoPaymentNotifyDTO doPaymentNotifyDto) {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        String rechargeNotifyPrint = SUCCESS;

        PaymentChannel paymentChannel = getPaymentChannelNotNull(channelId);
        doPaymentNotifyDto.setPaymentChannel(paymentChannel);

        PaymentWithdrawHandler payer = (PaymentWithdrawHandler) this.getPayer(paymentChannel.getPaymentHandlerId());
        if (payer == null) {
            log.error("[{}] 未實作PayService, channelId:{}, paymentHandlerId:{}", withdrawNotifyText, channelId, paymentChannel.getPaymentHandlerId());
            return rechargeNotifyPrint;
        }


        String orderNo = payer.parseWithdrawOrderNo(doPaymentNotifyDto);
        if (StringUtils.isBlank(orderNo)) {
            log.error("[{}] 訂單號異常", withdrawNotifyText);
            return rechargeNotifyPrint;
        }

        String lockKey = PaymentRedisKeyConstants.PAYMENT_NOTIFY_LOCK + orderNo;
        boolean isLock = redisService.setIfAbsent(lockKey, "", PaymentRedisKeyConstants.PAYMENT_NOTIFY_TIME);
        if (Boolean.FALSE.equals(isLock)) {
            log.info("[{}][{}], redis lock", withdrawNotifyText, orderNo);
            return rechargeNotifyPrint;
        }

        this.checkChannelWithdrawAvailability(paymentChannel);

        PaymentMerchant paymentMerchant = paymentMerchantService.findByIdByCache(paymentMerchantId);
        doPaymentNotifyDto.setPaymentMerchant(paymentMerchant);

        List<PaymentDynamicColumnValue> paymentDynamicColumnValueList = paymentDynamicColumnValueService.getListByPaymentMerchantId(paymentMerchantId);
        doPaymentNotifyDto.setPaymentDynamicColumnValues(paymentDynamicColumnValueList);

        String channelName = paymentChannel.getName();

        WithdrawPayDTO withdrawPayDto = withdrawService.doWithdrawNotify(doPaymentNotifyDto, payer);
        withdrawPayDto.setOrderNo(orderNo);
        withdrawPayDto.setPaymentChannelId(paymentChannel.getId());
        withdrawPayDto.setPaymentChannelName(channelName);
        withdrawPayDto.setPaymentMerchantId(paymentMerchantId);
        withdrawPayDto.setMerchantId(paymentMerchant.getMerchantId());

        rechargeNotifyPrint = payer.getConfig().rechargeNotifyPrint();

        // 等待會員付款的訂單不處理
        if (Objects.equals(withdrawPayDto.getOrderIsPending(), true)) {
            return rechargeNotifyPrint;
        }

        // 訂單邏輯處理
        amqpTemplate.convertAndSend(RabbitMQConstants.PAYMENT_WITHDRAW_NOTIFY_EXCHANGE, "", JSONUtil.toJsonStr(withdrawPayDto));


        log.info("[{}][{}][{}] success", channelName, withdrawNotifyText, orderNo);
        log.info("[{}][{}][{}] rechargeNotifyPrint = {}", channelName, withdrawNotifyText, orderNo, rechargeNotifyPrint);
        return rechargeNotifyPrint;
    }

    @Override
    public BalanceResultDTO balance(QueryBalanceDTO queryBalanceDto) {
        String balanceText = PayRequestEnum.BALANCE.getText();
        Long paymentChannelId = queryBalanceDto.getPaymentChannelId();
        Long paymentMerchantId = queryBalanceDto.getPaymentMerchantId();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValueList = paymentDynamicColumnValueService.getListByPaymentMerchantId(queryBalanceDto.getPaymentMerchantId());
        Map<Long, List<PaymentDynamicColumnValue>> paymentDynamicColumnValueMap = paymentDynamicColumnValueList.stream()
                .collect(Collectors.groupingBy(PaymentDynamicColumnValue::getPaymentMerchantId));

        DoBalanceDTO doBalanceDto = new DoBalanceDTO();
        PaymentMerchant paymentMerchant = paymentMerchantService.findByIdByCache(paymentMerchantId);
        doBalanceDto.setPaymentMerchant(paymentMerchant);

        PaymentChannel paymentChannel = paymentChannelService.findById(paymentChannelId);
        doBalanceDto.setPaymentChannel(paymentChannel);

        PaymentQueryBalanceHandler payer = (PaymentQueryBalanceHandler) this.getPayer(paymentChannel.getPaymentHandlerId());
        if (payer == null) {
            log.error("[{}] 未實作PayService, channelId:{}, paymentHandlerId:{}", balanceText, paymentChannelId, paymentChannel.getPaymentHandlerId());
            BalanceResultDTO balanceResultDto = new BalanceResultDTO();
            balanceResultDto.setPaymentChannelName(paymentChannel.getName());
            balanceResultDto.setMerchantCode(paymentMerchant.getPaymentMerchantCode());
            balanceResultDto.setRemark(paymentMerchant.getRemark());
            balanceResultDto.setCreateTime(paymentMerchant.getCreateTime());
            balanceResultDto.setSuccess(false);
            balanceResultDto.setMessage("未实作PayService");
            return balanceResultDto;
        }

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = paymentDynamicColumnValueMap.get(paymentMerchantId);
        if (paymentDynamicColumnValues != null) {
            doBalanceDto.setPaymentDynamicColumnValues(paymentDynamicColumnValues);
        }

        return balanceService.doQueryBalance(doBalanceDto, payer);
    }

    @Override
    public OnlineOrderPollingResultDTO queryOnlineDepositOrderStatus(DoPaymentPollingDTO doPaymentPollingDTO) {
        try {
            Assert.notNull(doPaymentPollingDTO.getMerchantId(), "商戶ID不能為空");
            Assert.notNull(doPaymentPollingDTO.getPaymentChannelId(), "支付渠道ID不能為空");
            Assert.notNull(doPaymentPollingDTO.getPaymentMerchantId(), "支付商戶ID不能為空");

            PaymentMerchant paymentMerchant = paymentMerchantService.findByIdByCache(doPaymentPollingDTO.getPaymentMerchantId());
            doPaymentPollingDTO.setPaymentMerchant(paymentMerchant);

            PaymentChannel paymentChannel = paymentChannelService.findById(doPaymentPollingDTO.getPaymentChannelId());
            doPaymentPollingDTO.setPaymentChannel(paymentChannel);

            List<PaymentDynamicColumnValue> paymentDynamicColumnValueList = paymentDynamicColumnValueService.getListByPaymentMerchantId(doPaymentPollingDTO.getPaymentMerchantId());
            Map<Long, List<PaymentDynamicColumnValue>> paymentDynamicColumnValueMap = paymentDynamicColumnValueList.stream()
                    .collect(Collectors.groupingBy(PaymentDynamicColumnValue::getPaymentMerchantId));
            if (CollectionUtil.isNotEmpty(paymentDynamicColumnValueMap)) {
                doPaymentPollingDTO.setPaymentDynamicColumnValues(paymentDynamicColumnValueMap.get(doPaymentPollingDTO.getPaymentMerchantId()));
            }
            OnlineOrderPollingResultDTO resultDTO = depositService.pollingDepositOrderStatus(doPaymentPollingDTO);
            resultDTO.setPaymentChannel(paymentChannel);
            resultDTO.setPaymentMerchant(paymentMerchant);
            resultDTO.setOrderNo(String.valueOf(doPaymentPollingDTO.getOrderId()));
            return resultDTO;
        } catch (Exception e) {
            log.error("轮询充值订单发生错误 doPaymentPollingDTO:{}", doPaymentPollingDTO, e);
            return new OnlineOrderPollingResultDTO();
        }
    }

    @Override
    public OnlineOrderPollingResultDTO queryWithdrawOrderStatus(DoPaymentPollingDTO doPaymentPollingDTO) {
        try {
            Assert.notNull(doPaymentPollingDTO.getMerchantId(), "商戶ID不能為空");
            Assert.notNull(doPaymentPollingDTO.getPaymentChannelId(), "支付渠道ID不能為空");
            Assert.notNull(doPaymentPollingDTO.getPaymentMerchantId(), "支付商戶ID不能為空");

            PaymentMerchant paymentMerchant = paymentMerchantService.findByIdByCache(doPaymentPollingDTO.getPaymentMerchantId());
            doPaymentPollingDTO.setPaymentMerchant(paymentMerchant);

            PaymentChannel paymentChannel = paymentChannelService.findById(doPaymentPollingDTO.getPaymentChannelId());
            doPaymentPollingDTO.setPaymentChannel(paymentChannel);

            List<PaymentDynamicColumnValue> paymentDynamicColumnValueList = paymentDynamicColumnValueService.getListByPaymentMerchantId(doPaymentPollingDTO.getPaymentMerchantId());
            Map<Long, List<PaymentDynamicColumnValue>> paymentDynamicColumnValueMap = paymentDynamicColumnValueList.stream()
                    .collect(Collectors.groupingBy(PaymentDynamicColumnValue::getPaymentMerchantId));
            if (CollectionUtil.isNotEmpty(paymentDynamicColumnValueMap)) {
                doPaymentPollingDTO.setPaymentDynamicColumnValues(paymentDynamicColumnValueMap.get(doPaymentPollingDTO.getPaymentMerchantId()));
            }
            OnlineOrderPollingResultDTO resultDTO = withdrawService.pollingWithdrawOrderStatus(doPaymentPollingDTO);
            resultDTO.setPaymentChannel(paymentChannel);
            resultDTO.setPaymentMerchant(paymentMerchant);
            resultDTO.setOrderNo(String.valueOf(doPaymentPollingDTO.getOrderId()));
            return resultDTO;
        } catch (Exception e) {
            log.error("輪詢出款訂單發生錯誤 doPaymentPollingDTO:{}", doPaymentPollingDTO, e);
            return new OnlineOrderPollingResultDTO();
        }
    }

    public PaymentBaseHandler getPayer(Long paymentHandlerId) {
        return paymentBaseHandlerMap.get("pay" + paymentHandlerId);
    }

    private void checkWithdrawPaymentChannel(PaymentChannel paymentChannel) {
        if (paymentChannel.getEnableEnum() == EnableEnum.FALSE) {
            log.error("[{}][{}][{}] 當前三方已被停用", paymentChannel.getName(), PayRequestEnum.WITHDRAW.getText(), null);
            throw new ApiException(CommonCode.PAYMENT_MERCHANT_CHANNEL_DISABLE);
        }
        checkChannelWithdrawAvailability(paymentChannel);
    }

    private void checkChannelWithdrawAvailability(PaymentChannel paymentChannel) {
        if (paymentChannel.getWithdrawEnableEnum() == EnableEnum.FALSE) {
            log.error("[{}][{}][{}] 三方配置為非代付", paymentChannel.getName(), PayRequestEnum.WITHDRAW.getText(), null);
            throw new ApiException(CommonCode.THIRD_CONFIGURATION_IS_NOT_WITHDRAW);
        }
    }

    private long getExchangeOrderMoney(Long orderMoney, BigDecimal exchangeRate) {
        if (Objects.isNull(exchangeRate)) {
            return orderMoney;
        }
        if (exchangeRate.equals(BigDecimal.ZERO)) {
            return orderMoney;
        }
        return exchangeRate.multiply(new BigDecimal(orderMoney)).longValue();
    }

    private MerchantConfigDepositPlatformPaymentMerchantDTO getMerchantConfigOnlineDepositPlatformChannelDTO(PaymentOnlineDepositParam paymentOnlineDepositParam, MemberTokenInfoDTO memberTokenInfoDTO, PaymentMerchant paymentMerchant) {
        MerchantConfig depositPlatformMerchantConfig = merchantConfigService.getByMerchantIdAndDictKey(memberTokenInfoDTO.getMerchantId(), CommonConstants.COMMON_DICT_KEY_DEPOSIT_PLATFORM, memberTokenInfoDTO.getCurrencyEnum());
        if (depositPlatformMerchantConfig == null) {
            log.error("depositPlatformMerchantConfig is null");
            throw new ApiException(CommonCode.CASH_DEPOSIT_PLATFORM_IS_NULL);
        }

        List<MerchantConfigDepositPlatformDTO> merchantConfigDepositPlatformDTOList = depositPlatformMerchantConfig.merchantConfigToListNotEmpty(MerchantConfigDepositPlatformDTO.class);
        MerchantConfigDepositPlatformDTO merchantConfigDepositPlatformDTO = merchantConfigDepositPlatformDTOList.stream()
                .filter(dto -> dto.getPlatformTypeEnum() == PlatformTypeEnum.ONLINE)
                .filter(dto -> paymentOnlineDepositParam.getPayName().equals(dto.getPayName()))
                .findFirst()
                .orElseThrow(() -> {
                    log.warn("merchantConfigDepositPlatformDTO not match, merchantConfigDepositPlatformDTOList:{}", merchantConfigDepositPlatformDTOList);
                    return new ApiException(CommonCode.CASH_DEPOSIT_PLATFORM_IS_NULL);
                });
        List<MerchantConfigDepositPlatformPaymentMerchantDTO> merchantConfigDepositPlatformPaymentMerchantDTOList = merchantConfigDepositPlatformDTO.getPaymentMerchant();
        if (CollUtil.isEmpty(merchantConfigDepositPlatformPaymentMerchantDTOList)) {
            log.warn("merchantConfigDepositPlatformChannelDTOList is empty, merchantConfigDepositPlatformDTOList:{}", merchantConfigDepositPlatformDTOList);
            throw new ApiException(CommonCode.CASH_DEPOSIT_PLATFORM_IS_NULL);
        }

        return merchantConfigDepositPlatformPaymentMerchantDTOList.stream()
                .filter(dto -> paymentMerchant.getId().equals(dto.getPaymentMerchantId()))
                .findFirst()
                .orElseThrow(() -> {
                    log.warn("merchantConfigDepositPlatformChannelDTOList not match, merchantConfigDepositPlatformChannelDTOList:{}", merchantConfigDepositPlatformPaymentMerchantDTOList);
                    return new ApiException(CommonCode.CASH_DEPOSIT_PLATFORM_IS_NULL);
                });
    }

    private MerchantConfigDepositPlatformPaymentMerchantOfflineDTO getMerchantConfigOfflineDepositPlatformChannelDTO(PaymentOfflineDepositParam paymentOfflineDepositParam, MemberTokenInfoDTO memberTokenInfoDTO, PaymentMerchantOffline paymentMerchantOffline) {
        MerchantConfig depositPlatformMerchantConfig = merchantConfigService.getByMerchantIdAndDictKey(memberTokenInfoDTO.getMerchantId(), CommonConstants.COMMON_DICT_KEY_DEPOSIT_PLATFORM, memberTokenInfoDTO.getCurrencyEnum());
        if (depositPlatformMerchantConfig == null) {
            log.error("depositPlatformMerchantConfig is null");
            throw new ApiException(CommonCode.CASH_DEPOSIT_PLATFORM_IS_NULL);
        }

        List<MerchantConfigDepositPlatformDTO> merchantConfigDepositPlatformDTOList = depositPlatformMerchantConfig.merchantConfigToListNotEmpty(MerchantConfigDepositPlatformDTO.class);
        MerchantConfigDepositPlatformDTO merchantConfigDepositPlatformDTO = merchantConfigDepositPlatformDTOList.stream()
                .filter(dto -> dto.getPlatformTypeEnum() == PlatformTypeEnum.OFFLINE)
                .filter(dto -> paymentOfflineDepositParam.getPayName().equals(dto.getPayName()))
                .findFirst()
                .orElseThrow(() -> {
                    log.warn("merchantConfigDepositPlatformDTO not match, merchantConfigDepositPlatformDTOList:{}", merchantConfigDepositPlatformDTOList);
                    return new ApiException(CommonCode.CASH_DEPOSIT_PLATFORM_IS_NULL);
                });
        List<MerchantConfigDepositPlatformPaymentMerchantOfflineDTO> MerchantConfigDepositPlatformPaymentMerchantOfflineDtoList = merchantConfigDepositPlatformDTO.getPaymentMerchantOffline();
        if (CollUtil.isEmpty(MerchantConfigDepositPlatformPaymentMerchantOfflineDtoList)) {
            log.warn("merchantConfigDepositPlatformChannelDTOList is empty, merchantConfigDepositPlatformDTOList:{}", merchantConfigDepositPlatformDTOList);
            throw new ApiException(CommonCode.CASH_DEPOSIT_PLATFORM_IS_NULL);
        }

        return MerchantConfigDepositPlatformPaymentMerchantOfflineDtoList.stream()
                .filter(dto -> paymentMerchantOffline.getId().equals(dto.getPaymentMerchantOfflineId()))
                .findFirst()
                .orElseThrow(() -> {
                    log.warn("merchantConfigDepositPlatformChannelDTOList not match, MerchantConfigDepositPlatformPaymentMerchantOfflineDtoList:{}", MerchantConfigDepositPlatformPaymentMerchantOfflineDtoList);
                    return new ApiException(CommonCode.CASH_DEPOSIT_PLATFORM_IS_NULL);
                });
    }

    private void checkPaymentMerchant(PaymentMerchant paymentMerchant, String channelName, Long merchantId) {
        if (paymentMerchant == null) {
            log.error("[{}][{}][{}] 當前三方無配置, 商戶:{}", channelName, PayRequestEnum.WITHDRAW.getText(), null, merchantId);
            throw new ApiException(CommonCode.THIRD_NO_CONFIGURATION);
        }
        if (paymentMerchant.getEnableEnum() == EnableEnum.FALSE) {
            log.error("[{}][{}][{}] 當前三方已被客戶停用, 商戶:{} ", channelName, PayRequestEnum.WITHDRAW.getText(), null, merchantId);
            throw new ApiException(CommonCode.PAYMENT_MERCHANT_CHANNEL_DISABLE);
        }
        boolean isChannelDisabled = (PayRequestEnum.WITHDRAW.getRequestType().intValue() == Constants.PaymentType.DEPOSIT)
                ? !paymentMerchant.isDepositEnabled()
                : !paymentMerchant.isWithdrawalEnabled();

        if (isChannelDisabled) {
            log.error("[{}][{}][{}] 當前三方已被客戶停用, 商戶:{} ", channelName, PayRequestEnum.WITHDRAW.getText(), null, merchantId);
            throw new ApiException(CommonCode.PAYMENT_MERCHANT_CHANNEL_DISABLE);
        }
    }

    private void checkDepositOrWithdrawLimit(PaymentMerchant paymentMerchant, Long money, PayRequestEnum payRequestEnum, String channelName) {
        Long paymentMerchantId = paymentMerchant.getId();

        if (payRequestEnum == PayRequestEnum.DEPOSIT) {
            long dailyDepositMoney = paymentMerchant.getDailyDepositMoney() + money;
            if (dailyDepositMoney > paymentMerchant.getDailyDepositDeactivateMoney()) {
                log.error("[{}][{}] 當前三方以達到充值金額限制, 商戶:{} ", channelName, payRequestEnum.getText(), paymentMerchantId);
                throw new ApiException(CommonCode.DEPOSIT_DAILY_DEACTIVATE_MONEY);
            }

            long dailyDepositCount = paymentMerchant.getDailyDepositCount() + 1L;
            if (dailyDepositCount > paymentMerchant.getDailyDepositDeactivateCount()) {
                log.error("[{}][{}] 當前三方以達到充值次數限制, 商戶:{} ", channelName, payRequestEnum.getText(), paymentMerchantId);
                throw new ApiException(CommonCode.DEPOSIT_DAILY_DEACTIVATE_COUNT);
            }
        }

        if (payRequestEnum == PayRequestEnum.WITHDRAW) {
            long dailyWithdrawMoney = paymentMerchant.getDailyWithdrawMoney() + money;
            if (dailyWithdrawMoney > paymentMerchant.getDailyWithdrawDeactivateMoney()) {
                log.error("[{}][{}] 當前三方以達到出款金額限制, 商戶:{} ", channelName, payRequestEnum.getText(), paymentMerchantId);
                throw new ApiException(CommonCode.WITHDRAW_DAILY_DEACTIVATE_MONEY);
            }

            long dailyWithdrawCount = paymentMerchant.getDailyWithdrawCount() + 1L;
            if (dailyWithdrawCount > paymentMerchant.getDailyWithdrawDeactivateCount()) {
                log.error("[{}][{}] 當前三方以達到出款次數限制, 商戶:{} ", channelName, payRequestEnum.getText(), paymentMerchantId);
                throw new ApiException(CommonCode.WITHDRAW_DAILY_DEACTIVATE_COUNT);
            }
        }
    }

    private void checkOfflineDepositLimit(PaymentMerchantOffline paymentMerchantOffline, Long money, String channelName) {
        Long paymentMerchantOfflineId = paymentMerchantOffline.getId();

        long dailyDepositMoney = paymentMerchantOffline.getDailyDepositMoney() + money;
        if (dailyDepositMoney > paymentMerchantOffline.getDailyDepositDeactivateMoney()) {
            log.error("[{}] 當前渠道以達到充值金額限制, 商戶:{} ", channelName, paymentMerchantOfflineId);
            throw new ApiException(CommonCode.DEPOSIT_DAILY_DEACTIVATE_MONEY);
        }

        long dailyDepositCount = paymentMerchantOffline.getDailyDepositCount() + 1L;
        if (dailyDepositCount > paymentMerchantOffline.getDailyDepositDeactivateCount()) {
            log.error("[{}] 當前渠道以達到充值次數限制, 商戶:{} ", channelName, paymentMerchantOfflineId);
            throw new ApiException(CommonCode.DEPOSIT_DAILY_DEACTIVATE_COUNT);
        }
    }

    private void checkThirdConfig(PaymentChannel paymentChannel, PayRequestEnum payRequestEnum) {
        if (paymentChannel.getDepositEnableEnum() == EnableEnum.FALSE) {
            log.error("[{}][{}] 三方代收配置為非代收", payRequestEnum.getText(), paymentChannel.getName());
            throw new ApiException(CommonCode.CASH_PAYMENT_CHANNEL_ENABLE_IS_FALSE);
        }
    }

    private void checkThirdTcp(PaymentMerchant paymentMerchant, PayRequestEnum payRequestEnum) {
        PaymentChannel paymentChannel = paymentMerchant.getPaymentChannel();
        String url = paymentChannel.getDepositDomain();

        if (StringUtils.isBlank(url)) {
            log.error("[{}][{}] 三方金流tcp檢查, 無配置", payRequestEnum.getText(), paymentChannel.getName());
            throw new ApiException(CommonCode.CASH_PAYMENT_CHANNEL_NO_CONFIG);
        }
        HttpUrl httpUrl = HttpUrl.parse(url);
        if (httpUrl == null || !TcpUtil.connect(httpUrl.host(), httpUrl.port())) {
            log.error("[{}][{}] 三方金流tcp連線檢查失敗, domain:{}",
                    payRequestEnum.getText(),
                    paymentChannel.getName(),
                    paymentChannel.getDepositDomain());
            throw new ApiException(CommonCode.CASH_PAYMENT_CHANNEL_TCP_CHECK_FAIL);
        }
    }

    private boolean isVirtualDeposit(PaymentOfflineDepositParam paymentOfflineDepositParam) {
        return Objects.equals(PlatformEnum.VIRTUAL, paymentOfflineDepositParam.getPlatformEnum());
    }

    private void validateVirtualDepositParam(Long merchantId, String txId) {
        if (StringUtils.isBlank(txId) || txId.chars().anyMatch(ch -> !Character.isLetterOrDigit(ch))) {
            throw new ApiException(CommonCode.CASH_OFFLINE_DEPOSIT_TXID_IS_NULL);
        }
        String key = String.join(StringPool.COLON, PaymentRedisKeyConstants.TXID_CHECK_INTERVALS, String.valueOf(merchantId), txId);
        redisService.frequencyControl(key, paymentFrequencyConfig.getTxidCheckIntervalsSeconds());
        if (cashDepositOrderService.hasNonCancelledOrderWithSameTxId(merchantId, txId)) {
            throw new ApiException(CommonCode.CASH_OFFLINE_DEPOSIT_TXID_DUPLICATE);
        }
    }


    private PaymentChannel getPaymentChannelNotNull(Long channelId) {
        PaymentChannel paymentChannel = paymentChannelService.findById(channelId);
        if (paymentChannel == null) {
            log.error("[{}]當前三方無配置", channelId);
            throw new ApiException(CommonCode.THIRD_NO_CONFIGURATION);
        }
        return paymentChannel;
    }

    private void validateOtherDepositParam(PaymentOfflineDepositParam param) {
        if (StringUtils.isBlank(param.getDepositCertificate()) && StringUtils.isBlank(param.getTxId())) {
            throw new ApiException(CommonCode.CASH_OFFLINE_DEPOSIT_CERTIFICATE_OR_TXID_REQUIRED);
        }
    }

    void adjustDepositRequestAmountByCurrency(DoDepositDTO depositDTO, CurrencyEnum currencyEnum) {
        depositDTO.setAmount(depositDTO.getAmount() * currencyEnum.getBaseUnit());
    }

    private void adjustWithdrawRequestAmountByCurrency(DoWithdrawDTO doWithdrawDto, CurrencyEnum currencyEnum) {
        doWithdrawDto.setAmount(doWithdrawDto.getAmount() * currencyEnum.getBaseUnit());
    }

    private void setMemberWithdrawAccountIfExist(DoDepositDTO depositDTO,
                                                 PaymentOnlineDepositParam depositParam,
                                                 PaymentMerchant paymentMerchant,
                                                 MemberTokenInfoDTO memberTokenInfoDTO) {
        if (!Objects.equals(paymentMerchant.getBankcardRequired(), Boolean.TRUE)) {
            return;
        }
        if (depositParam.getMemberWithdrawAccountId() == null) {
            throw new ApiException(CommonCode.MERCHANT_ACCOUNT_NOT_EXISTS);
        }

        MemberWithdrawAccount memberWithdrawAccount = memberWithdrawAccountService.findByIdAndMemberIdAndMerchantIdNotNull(
                depositParam.getMemberWithdrawAccountId(), memberTokenInfoDTO.getId(), memberTokenInfoDTO.getMerchantId());
        if (Objects.isNull(memberWithdrawAccount)) {
            throw new ApiException(CommonCode.MERCHANT_ACCOUNT_NOT_EXISTS);
        }
        depositDTO.setMemberWithdrawAccount(memberWithdrawAccount);
    }

}
