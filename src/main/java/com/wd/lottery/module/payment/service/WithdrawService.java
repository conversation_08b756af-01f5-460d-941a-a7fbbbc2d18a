package com.wd.lottery.module.payment.service;

import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;

public interface WithdrawService {

    /**
     * 執行提款
     */
    WithdrawResultDTO doWithdraw(DoWithdrawDTO doWithdrawDto, PaymentWithdrawHandler payer);

    /**
     * 執行提款回調
     */
    WithdrawPayDTO doWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto, PaymentWithdrawHandler payer);

    /**
     * 根據付款處理器ID獲取出款處理器
     *
     * @param paymentHandlerId 付款處理器ID
     * @return 出款處理器
     */
    PaymentWithdrawHandler getWithdrawHandler(Long paymentHandlerId);

    /**
     * 輪詢出款訂單狀態
     *
     * @param doPaymentPollingDTO 輪詢參數
     * @return 輪詢結果
     */
    OnlineOrderPollingResultDTO pollingWithdrawOrderStatus(DoPaymentPollingDTO doPaymentPollingDTO);
}
