package com.wd.lottery.module.payment.pay.pay1197;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1197 , OTGPAY支付代付 IDR (cgptt017)
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1197 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1197][OTGPAY支付]";
    private final String DF_NAME = "[SFZF-1197][OTGPAY代付]";
    private final String BALANCE = "[SFZF-1197 餘額查詢]";
    private static final String SUCCESS_CODE = "1";
    private static final String CALLBACK_SUCCESS_CODE = "2";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "orderId";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "orderId";
    private static final String QUERY_DEPOSIT_API_PATH = "/idr/collect/query";
    private static final String QUERY_WITHDRAW_API_PATH = "/idr/pay/query";

    private final PaymentCommonService paymentCommonService;
    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/idr/pay/apply")
            .rechargeApiPath("/idr/collect/apply")
            .queryBalanceApiPath("/idr/account/ledger")
            .withdrawNotifyPrint("SUCCESS")
            .rechargeNotifyPrint("SUCCESS")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String appId = paymentMerchant.getPaymentMerchantCode();
        String priKey = paymentMerchant.getPrivateKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String cryptStr = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "cryptStr",
                PayRequestEnum.DEPOSIT);
        String payType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "payType",
                PayRequestEnum.DEPOSIT);
        String inBankCode = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "inBankCode",
                PayRequestEnum.DEPOSIT);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("appId", appId);
        params.put("orderId", orderNo);
        params.put("name", doDepositDto.getMemberName().split("@")[0]);
        params.put("email", StrUtil.isBlank(doDepositDto.getEmail()) ? "<EMAIL>" : doDepositDto.getEmail());
        params.put("amount", PayUtils.getMoney(doDepositDto.getAmount()));
        params.put("payType", payType);
        params.put("inBankCode", inBankCode);
        params.put("callBackUrl", doDepositDto.getDepositNotifyUrl());
        params.put("notifyUrl", doDepositDto.getDepositNotifyUrl());


        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + cryptStr;
        String sign = Pay1197Util.buildRSASignByPrivateKey(paramStr, priKey);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,私鑰:{} ,加密後:{}",
                ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, priKey, sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);
        try {
            String status = result.get("status").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                resultDto.setThirdOrderNo(result.get("data").get("orderNo").asText());
                resultDto.setRedirectUrl(result.get("data").get("payUrl").asText());
                log.info("{}[{}] 訂單號:{} 請求成功:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, result);
            } else {
                resultDto.setErrorMsg(result.get("msg").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{} 狀態:{} 請求失敗:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, status, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY).split("_")[0];
        String pubKey = doDepositNotifyDto.getPaymentMerchant().getPublicKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositNotifyDto.getPaymentDynamicColumnValues();
        String cryptStr = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "cryptStr",
                PayRequestEnum.DEPOSIT_NOTIFY);

        String status = resMap.get("status");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + cryptStr;
            boolean check = Pay1197Util.buildRSAverifyByPublicKey(paramStr, pubKey, sign);
            if (!check) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} , 公鑰:{} ,收到的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, pubKey, sign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.get("amount")));
            depositPayDto.setFee(PayUtils.getCent(resMap.get("fee")));
            log.info("{}[{}] 回調成功 訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        String orderNo = String.valueOf(doPaymentPollingDTO.getOrderId());
        String priKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doPaymentPollingDTO.getPaymentDynamicColumnValues();
        String cryptStr = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "cryptStr",
                PayRequestEnum.DEPOSIT);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("appId", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("orderId", orderNo);
        params.put("orderNo", doPaymentPollingDTO.getThirdNo());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + cryptStr;
        String sign = Pay1197Util.buildRSASignByPrivateKey(paramStr, priKey);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,私鑰:{} ,加密後:{}",
                ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, priKey, sign);
        params.put("sign", sign);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getDepositDomain() + QUERY_DEPOSIT_API_PATH;

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        try {
            JsonNode result = paymentCommonService.doJsonPost(apiUrl, params);
            String status = result.get("status").asText();
            String payStateCode = result.get("data").get("status").asText();

            log.info("{} 查詢結果 訂單號:{} 狀態碼:{} 回傳:{}", ZF_NAME, orderNo, payStateCode, result);
            if (SUCCESS_CODE.equalsIgnoreCase(status) && "2".equalsIgnoreCase(payStateCode)) {
                String amount = result.get("data").get("actualAmount").asText();
                resultDTO.setIsSuccess(true);
                resultDTO.setAmount(PayUtils.getCent(amount));
                log.info("{} 查詢成功 訂單號:{} 回傳:{}", ZF_NAME, orderNo, result);
            } else {
                log.info("{} 查詢訂單未完成 訂單號:{} 狀態:{} 回傳:{}", ZF_NAME, orderNo, payStateCode, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 查詢請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDTO;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doBalanceDto.getPaymentMerchant();
        String priKey = doBalanceDto.getPaymentMerchant().getPrivateKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doBalanceDto.getPaymentDynamicColumnValues();
        String cryptStr = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "cryptStr",
                PayRequestEnum.BALANCE);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("appId", paymentMerchant.getPaymentMerchantCode());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + cryptStr;
        String sign = Pay1197Util.buildRSASignByPrivateKey(paramStr, priKey);
        log.info("{}[{}]  待加密字串:{} ,私鑰:{} ,加密後:{}",
                BALANCE, PayRequestEnum.BALANCE.getText(), paramStr, priKey, sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params);
        try {
            String status = result.get("status").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("data").get("availableBal").asText();
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                String message = result.get("msg").asText();
                resultDto.setMessage(message);
                log.info("{}[{}] 失敗:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", DF_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String priKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String cryptStr = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "cryptStr",
                PayRequestEnum.WITHDRAW);
        String accountType = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "accountType",
                PayRequestEnum.WITHDRAW);


        TreeMap<String, Object> params = new TreeMap<>();
        params.put("appId", paymentMerchant.getPaymentMerchantCode());
        params.put("orderId", orderNo);
        params.put("amount", PayUtils.getMoney(doWithdrawDto.getAmount()));
        params.put("accountType", accountType);
        params.put("bankCode", doWithdrawDto.getBankCodeDto().getBankCode());
        params.put("accountNo", doWithdrawDto.getUserBankAccount());
        params.put("name", doWithdrawDto.getUserName());
        params.put("phone", doWithdrawDto.getMobile() == null ? 9876543210L : doWithdrawDto.getMobile());
        params.put("email", StrUtil.isBlank(doWithdrawDto.getEmail()) ? "<EMAIL>" : doWithdrawDto.getEmail());
        params.put("notifyUrl", doWithdrawDto.getWithdrawNotifyUrl());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + cryptStr;
        String sign = Pay1197Util.buildRSASignByPrivateKey(paramStr, priKey);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,私鑰:{} ,加密後:{}",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, priKey, sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);
        String status = result.get("status").asText();
        if (SUCCESS_CODE.equalsIgnoreCase(status)) {
            resultDto.setThirdOrderNo(result.get("data").get("orderNo").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("{}  訂單號:{} 請求成功:{}", DF_NAME, orderNo, result);
        } else {
            resultDto.setErrorMsg(result.get("msg").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("{}[{}] 訂單號:{}  狀態:{} 請求失敗:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status,
                    result);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY).split("_")[0];
        String pubKey = doWithdrawNotifyDto.getPaymentMerchant().getPublicKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", DF_NAME, withdrawNotifyText, orderNo, resMap);

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawNotifyDto.getPaymentDynamicColumnValues();
        String cryptStr = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "cryptStr",
                PayRequestEnum.WITHDRAW_NOTIFY);

        String status = resMap.get("status");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + cryptStr;
            boolean check = Pay1197Util.buildRSAverifyByPublicKey(paramStr, pubKey, sign);
            if (!check) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} ,公鑰:{} ,收到的sign:{},",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, pubKey, sign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("amount")));
            withdrawPayDTO.setFee(PayUtils.getCent(resMap.get("fee")));
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  代付DTO:{}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        String orderNo = String.valueOf(doPaymentPollingDTO.getOrderId());
        String priKey = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();

        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doPaymentPollingDTO.getPaymentDynamicColumnValues();
        String cryptStr = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "cryptStr",
                PayRequestEnum.WITHDRAW);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("appId", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("orderId", orderNo);
        params.put("orderNo", doPaymentPollingDTO.getThirdNo());

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_WITHDRAW_API_PATH;

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + cryptStr;
        String sign = Pay1197Util.buildRSASignByPrivateKey(paramStr, priKey);
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,私鑰:{} ,加密後:{}",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, priKey, sign);
        params.put("sign", sign);

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        try {
            JsonNode result = paymentCommonService.doJsonPost(apiUrl, params);
            String status = result.get("status").asText();
            String payStateCode = result.get("data").get("status").asText();
            log.info("{} 查詢結果 訂單號:{} 狀態碼:{} 回傳:{}", DF_NAME, orderNo, payStateCode, result);
            if (SUCCESS_CODE.equalsIgnoreCase(status) && "2".equalsIgnoreCase(payStateCode)) {
                resultDTO.setIsSuccess(true);
                resultDTO.setAmount(doPaymentPollingDTO.getAmount());
                log.info("{} 查詢成功 訂單號:{} 回傳:{}", DF_NAME, orderNo, result);
            } else {
                log.info("{} 查詢訂單未完成 訂單號:{} 狀態:{} 回傳:{}", DF_NAME, orderNo, payStateCode, result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 查詢請求異常", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDTO;
    }

    @Override
    public String parseDepositOrderNo(DoPaymentNotifyDTO notifyDTO) {
        return notifyDTO.getRequestMap().get(DEPOSIT_NOTIFY_ORDER_KEY).split("_")[0];
    }

    @Override
    public String parseWithdrawOrderNo(DoPaymentNotifyDTO notifyDTO) {
        return notifyDTO.getRequestMap().get(WITHDRAW_NOTIFY_ORDER_KEY).split("_")[0];
    }
}
