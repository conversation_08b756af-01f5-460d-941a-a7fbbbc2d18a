package com.wd.lottery.module.payment.pay.pay1181;

import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1181 , NBpay菲支付代付 (cgptf016)
 */
@AllArgsConstructor
@Service
@Slf4j
public class Pay1181 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1181][NBpay菲支付]";
    private final String DF_NAME = "[SFZF-1181][NBpay菲代付]";
    private final String BALANCE = "[NBpay菲支付 餘額查詢]";
    private static final String SUCCESS_CODE = "1";
    private static final String CALLBACK_SUCCESS_CODE = "5";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "order_id";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "order_id";

    private final PaymentCommonService paymentCommonService;
    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/api/daifu")
            .rechargeApiPath("/api/transfer")
            .queryBalanceApiPath("/api/me")
            .withdrawNotifyPrint("SUCCESS")
            .rechargeNotifyPrint("SUCCESS")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String channel = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "channel", PayRequestEnum.DEPOSIT);
        String bankCode = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "bankCode", PayRequestEnum.DEPOSIT);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchant", doDepositDto.getPaymentMerchant().getPaymentMerchantCode());
        params.put("payment_type", channel);
        params.put("amount", PayUtils.getMoney(doDepositDto.getAmount(),2));
        params.put("order_id", orderNo);
        params.put("bank_code", bankCode);
        params.put("callback_url", doDepositDto.getDepositNotifyUrl());
        params.put("return_url", doDepositDto.getDepositNotifyUrl());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}]{} 產生簽名字串加密前 [{}] 加密後 [{}]", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr ,sign);
        params.put("sign", sign);


        try {
            JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);
            String status = result.get("status").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String payUrl = result.get("redirect_url").asText();
                resultDto.setThirdOrderNo("none");
                resultDto.setRedirectUrl(payUrl);
                log.info("{} 請求成功 訂單號:{} ", ZF_NAME, orderNo);
                log.info("{}[{}]訂單號:{} 跳轉地址:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, payUrl);
            } else {
                resultDto.setErrorMsg(result.get("message").asText());
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{} 請求失敗 訂單號:{} 狀態:{}", ZF_NAME, orderNo, status);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", ZF_NAME, depositNotifyText, orderNo, resMap);
        log.info("{} 訂單號:{} 回調驗籤 密鑰: {}", ZF_NAME, orderNo, payKey );

        String status = resMap.get("status");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(PayUtils.getCent(resMap.get("amount")));
            depositPayDto.setFee(0L);
            log.info("{}[{}]訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            log.info("{} 回調成功 訂單號:{}  ", ZF_NAME, orderNo);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        String payKey = doBalanceDto.getPaymentMerchant().getPrivateKey();

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchant", doBalanceDto.getPaymentMerchant().getPaymentMerchantCode());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}]產生簽名字串加密前 [{}] 加密後 [{}] ", BALANCE,PayRequestEnum.BALANCE.getText(), paramStr,sign);
        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(),params);
            String balance = result.get("balance").asText();

            if (balance != null) {
                resultDto.setBalance(PayUtils.getCent(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功 = {}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                resultDto.setMessage("error");
                log.info("{}[{}] 失敗 = {}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", BALANCE, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();


        log.info("{} 訂單號:{} 密鑰:{}", DF_NAME, orderNo, payKey);

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("merchant", paymentMerchant.getPaymentMerchantCode());
        params.put("total_amount", PayUtils.getMoney(doWithdrawDto.getAmount()));
        params.put("callback_url", doWithdrawDto.getWithdrawNotifyUrl());
        params.put("order_id", doWithdrawDto.getOrderNo());
        params.put("bank", doWithdrawDto.getBankCodeDto().getBankCode());
        params.put("bank_card_name", doWithdrawDto.getMemberName());
        params.put("bank_card_account", doWithdrawDto.getUserBankAccount());
        params.put("bank_card_remark", "no");


        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr);
        log.info("{}[{}] 訂單號:{} 產生簽名字串加密前 [{}] 加密後 [{}]",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr ,sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);

        String status = result.get("status").asText();
        if (SUCCESS_CODE.equalsIgnoreCase(status)) {
            resultDto.setThirdOrderNo("none");
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
            log.info("{} 請求成功 訂單號:{} ", DF_NAME, orderNo);
        } else {
            resultDto.setErrorMsg(result.get("message").asText());
            resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
            log.info("{}[{}] 訂單號:{} 請求失敗 狀態:{} message:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo,status,
                    result.get("message").asText());
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", DF_NAME, withdrawNotifyText, orderNo, resMap);
        log.info("{} 訂單號:{} 回調驗籤 密鑰: {}", DF_NAME, orderNo, payKey );

        String status = resMap.get("status");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr);
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 收到的sign:{}, 算出的sign:{}",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, sign, checkSign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(PayUtils.getCent(resMap.get("amount")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  = {}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        return null;
    }
}
