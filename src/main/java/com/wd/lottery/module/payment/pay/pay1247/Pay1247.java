package com.wd.lottery.module.payment.pay.pay1247;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.PayRequestEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.payment.config.PaymentConfig;
import com.wd.lottery.module.payment.constants.DepositStatusEnum;
import com.wd.lottery.module.payment.constants.JumpModeEnum;
import com.wd.lottery.module.payment.constants.WithdrawStatusEnum;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;
import com.wd.lottery.module.payment.handler.PaymentQueryBalanceHandler;
import com.wd.lottery.module.payment.handler.PaymentWithdrawHandler;
import com.wd.lottery.module.payment.service.PaymentCommonService;
import com.wd.lottery.module.payment.util.EncryptionDecryptionUtils;
import com.wd.lottery.module.payment.util.PayUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * SFZF-1247, PPay菲律宾支付代付（cgptf016）
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class Pay1247 implements PaymentDepositHandler, PaymentWithdrawHandler, PaymentQueryBalanceHandler {
    private final String ZF_NAME = "[SFZF-1247][PPay菲律宾支付]";
    private final String DF_NAME = "[SFZF-1247][PPay菲律宾代付]";
    private final String BALANCE = "[SFZF-1247 餘額查詢]";
    private static final String SUCCESS_CODE = "0";
    private static final String CALLBACK_SUCCESS_CODE = "2";
    private static final String DEPOSIT_NOTIFY_ORDER_KEY = "mchOrderNo";
    private static final String WITHDRAW_NOTIFY_ORDER_KEY = "mchOrderNo";
    private static final String QUERY_RECHARGE_ORDER_API_PATH = "/api/pay/query";
    private static final String QUERY_WITHDRAW_ORDER_API_PATH = "/api/payout/query";

    private final PaymentCommonService paymentCommonService;
    private final PaymentConfig newPaymentConfig = new PaymentConfig()
            .withdrawApiPath("/api/payout/pay")
            .rechargeApiPath("/api/pay/pay")
            .queryBalanceApiPath("/api/payout/balance")
            .withdrawNotifyPrint("success")
            .rechargeNotifyPrint("success")
            .jumpMode(JumpModeEnum.REDIRECT);

    @Override
    public PaymentConfig getConfig() {
        return newPaymentConfig;
    }

    @Override
    public String depositNotifyOrderKey() {
        return DEPOSIT_NOTIFY_ORDER_KEY;
    }

    @Override
    public DepositResultDTO generateDepositRequest(DoDepositDTO doDepositDto, DepositResultDTO resultDto) {
        String orderNo = doDepositDto.getOrderNo();
        PaymentMerchant paymentMerchant = doDepositDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doDepositDto.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.DEPOSIT);


        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", paymentMerchant.getPaymentMerchantCode());
        params.put("appId", appId);
        params.put("mchOrderNo", orderNo);
        params.put("amount", doDepositDto.getAmount());
        params.put("customerName", doDepositDto.getMemberName());
        params.put("customerEmail", StrUtil.isBlank(doDepositDto.getEmail()) ? "<EMAIL>" : doDepositDto.getEmail());
        params.put("customerPhone", doDepositDto.getMobile() == null ? 9127536410L : doDepositDto.getMobile());
        params.put("notifyUrl", doDepositDto.getDepositNotifyUrl());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, paramStr, sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doDepositDto.getDepositApi(), params);
        try {
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String payUrl = result.get("data").get("payData").asText();
                resultDto.setThirdOrderNo(result.get("data").get("payOrderId").asText());
                resultDto.setRedirectUrl(payUrl);
                log.info("{}[{}] 訂單號:{} 請求成功:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, result);
            } else {
                String message = result.get("msg").asText();
                resultDto.setErrorMsg(message);
                resultDto.setDepositStatusEnum(DepositStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{} 狀態:{} 請求失敗:{}", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, status, result);

            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 充值請求異常", ZF_NAME, PayRequestEnum.DEPOSIT.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public DepositPayDTO handleDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto) throws Exception {
        String depositNotifyText = PayRequestEnum.DEPOSIT_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doDepositNotifyDto.getRequestMap());
        DepositPayDTO depositPayDto = new DepositPayDTO();
        String orderNo = resMap.get(DEPOSIT_NOTIFY_ORDER_KEY);
        String payKey = doDepositNotifyDto.getPaymentMerchant().getPrivateKey();

        String status = resMap.get("state");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{} 算出的sign:{} , 收到的sign:{}",
                        ZF_NAME, depositNotifyText, orderNo, paramStr, checkSign, sign);
                return PayUtils.paySignError(orderNo, DepositStatusEnum.SIGN_CHECK_ERROR, status);
            }
            depositPayDto.setOrderNo(orderNo);
            depositPayDto.setAmount(Long.valueOf(resMap.get("amount")));
            depositPayDto.setFee(0L);
            log.info("{}[{}] 回調成功 訂單號:{}  = {}", ZF_NAME, depositNotifyText, orderNo, depositPayDto);
            return depositPayDto;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", ZF_NAME, depositNotifyText, orderNo, status);
        return PayUtils.payStatusError(orderNo, DepositStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingDepositOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doPaymentPollingDTO.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.DEPOSIT);

        String key = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("appId", appId);
        params.put("mchOrderNo", doPaymentPollingDTO.getOrderId());
        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params) + "&key=" + key;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        params.put("sign", sign);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getDepositDomain() + QUERY_RECHARGE_ORDER_API_PATH;
        JsonNode result = paymentCommonService.doJsonPost(apiUrl, params);
        log.info("[PPay菲律宾支付][{}] 查单结果", doPaymentPollingDTO.getOrderId());

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        String status = result.get("code").asText();
        String orderStatus = result.path("data").path("state").asText();
        if (!SUCCESS_CODE.equalsIgnoreCase(status) || !CALLBACK_SUCCESS_CODE.equalsIgnoreCase(orderStatus)) {
            return resultDTO;
        }
        Long amount = Long.valueOf(result.path("data").path("amount").asText());
        resultDTO.setAmount(amount);
        resultDTO.setIsSuccess(true);
        return resultDTO;
    }

    @Override
    public BalanceResultDTO generateQueryBalancesRequest(DoBalanceDTO doBalanceDto, BalanceResultDTO resultDto) {
        PaymentMerchant paymentMerchant = doBalanceDto.getPaymentMerchant();
        String payKey = doBalanceDto.getPaymentMerchant().getPrivateKey();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doBalanceDto.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.BALANCE);


        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", paymentMerchant.getPaymentMerchantCode());
        params.put("appId", appId);

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}]  待加密字串:{} , 加密後:{}", BALANCE, PayRequestEnum.BALANCE.getText(), paramStr, sign);
        params.put("sign", sign);

        JsonNode result = paymentCommonService.doJsonPost(doBalanceDto.getBalanceApi(), params);
        try {
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                String balance = result.get("data").get("payoutBalance").asText();
                resultDto.setBalance(Long.valueOf(balance));
                resultDto.setSuccess(true);
                log.info("{}[{}] 成功:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            } else {
                String message = result.get("msg").asText();
                resultDto.setMessage(message);
                log.info("{}[{}] 失敗:{}", BALANCE, PayRequestEnum.BALANCE.getText(), result);
            }
        } catch (Exception e) {
            log.error("[{}][{}] response 解析異常 ", DF_NAME, PayRequestEnum.BALANCE.getText(), e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public String withdrawNotifyOrderKey() {
        return WITHDRAW_NOTIFY_ORDER_KEY;
    }

    @Override
    public WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception {
        PaymentMerchant paymentMerchant = doWithdrawDto.getPaymentMerchant();
        String payKey = paymentMerchant.getPrivateKey();
        String orderNo = doWithdrawDto.getOrderNo();

        // 動態密鑰
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doWithdrawDto.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.WITHDRAW);


        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", paymentMerchant.getPaymentMerchantCode());
        params.put("appId", appId);
        params.put("mchOrderNo", orderNo);
        params.put("amount", doWithdrawDto.getAmount());
        params.put("entryType", "GCASH");
        params.put("accountNo", doWithdrawDto.getUserBankAccount());
        params.put("accountCode", doWithdrawDto.getBankCodeDto().getBankCode());
        params.put("accountName", doWithdrawDto.getUserName());
        params.put("accountEmail", StrUtil.isBlank(doWithdrawDto.getEmail()) ? "<EMAIL>" : doWithdrawDto.getEmail());
        params.put("accountPhone", doWithdrawDto.getMobile() == null ? 9876543210L : doWithdrawDto.getMobile());
        params.put("notifyUrl", doWithdrawDto.getWithdrawNotifyUrl());

        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params);
        paramStr += "&key=" + payKey;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        log.info("{}[{}] 訂單號:{} 待加密字串:{} ,加密後:{}",
                DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, paramStr, sign);
        params.put("sign", sign);

        try {
            JsonNode result = paymentCommonService.doJsonPost(doWithdrawDto.getWithdrawApi(), params);
            String status = result.get("code").asText();
            if (SUCCESS_CODE.equalsIgnoreCase(status)) {
                resultDto.setThirdOrderNo(result.get("data").get("transferId").asText());
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.PAID);
                log.info("{}  訂單號:{} 請求成功:{}", DF_NAME, orderNo, result);
            } else {
                String message = result.get("msg").asText();
                resultDto.setErrorMsg(message);
                resultDto.setWithdrawStatusEnum(WithdrawStatusEnum.THIRD_ERROR);
                log.info("{}[{}] 訂單號:{}  狀態:{} 請求失敗:{}", DF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, status,
                        result);
            }
        } catch (Exception e) {
            log.error("{}[{}]訂單號:{} 代付請求異常", ZF_NAME, PayRequestEnum.WITHDRAW.getText(), orderNo, e);
            throw new ApiException(CommonCode.THIRD_OR_SERVER_FAILED);
        }
        return resultDto;
    }

    @Override
    public WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception {
        String withdrawNotifyText = PayRequestEnum.WITHDRAW_NOTIFY.getText();
        Map<String, String> resMap = new TreeMap<>(doWithdrawNotifyDto.getRequestMap());
        WithdrawPayDTO withdrawPayDTO = new WithdrawPayDTO();
        String orderNo = resMap.get(WITHDRAW_NOTIFY_ORDER_KEY);
        String payKey = doWithdrawNotifyDto.getPaymentMerchant().getPrivateKey();

        log.info("{}[{}]訂單號:{} 回調參數 = {}", DF_NAME, withdrawNotifyText, orderNo, resMap);

        String status = resMap.get("state");
        String sign = resMap.remove("sign");

        if (CALLBACK_SUCCESS_CODE.equalsIgnoreCase(status)) {
            String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(resMap);
            paramStr += "&key=" + payKey;
            String checkSign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
            if (!sign.equalsIgnoreCase(checkSign)) {
                log.error("{}[{}] 訂單號:{}[簽名驗證失敗] 待加密字串:{}  ,算出的sign:{} ,收到的sign:{},",
                        DF_NAME, withdrawNotifyText, orderNo, paramStr, checkSign, sign);
                return PayUtils.withdrawSignError(orderNo, WithdrawStatusEnum.SIGN_CHECK_ERROR, status);
            }
            withdrawPayDTO.setOrderNo(orderNo);
            withdrawPayDTO.setAmount(Long.valueOf(resMap.get("amount")));
            withdrawPayDTO.setFee(0L);
            withdrawPayDTO.setBalance(null);
            log.info("{}[{}]訂單號:{}  代付DTO:{}", DF_NAME, withdrawNotifyText, orderNo, withdrawPayDTO);
            log.info("{} 回調成功 訂單號:{}  ", DF_NAME, orderNo);
            return withdrawPayDTO;
        }
        log.info("{}[{}]訂單號:{} 代收回調失敗 訂單狀態:{}", DF_NAME, withdrawNotifyText, orderNo, status);
        return PayUtils.withdrawStatusNotify(orderNo, WithdrawStatusEnum.NOTIFY_FAIL, status);
    }

    @Override
    public OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception {
        List<PaymentDynamicColumnValue> paymentDynamicColumnValues = doPaymentPollingDTO.getPaymentDynamicColumnValues();
        String appId = PayUtils.getDynamicColumnValue(paymentDynamicColumnValues, "appId", PayRequestEnum.WITHDRAW);

        String key = doPaymentPollingDTO.getPaymentMerchant().getPrivateKey();
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mchNo", doPaymentPollingDTO.getPaymentMerchant().getPaymentMerchantCode());
        params.put("appId", appId);
        params.put("mchOrderNo", doPaymentPollingDTO.getOrderId());
        String paramStr = PayUtils.convertMapToQueryStringIgnoreEmpty(params) + "&key=" + key;
        String sign = EncryptionDecryptionUtils.md5Encode(paramStr).toUpperCase();
        params.put("sign", sign);

        String apiUrl = doPaymentPollingDTO.getPaymentChannel().getWithdrawDomain() + QUERY_WITHDRAW_ORDER_API_PATH;
        JsonNode result = paymentCommonService.doJsonPost(apiUrl, params);
        log.info("[PPay菲律宾支付][{}] 查单结果", doPaymentPollingDTO.getOrderId());

        OnlineOrderPollingResultDTO resultDTO = new OnlineOrderPollingResultDTO();
        String status = result.get("code").asText();
        String orderStatus = result.path("data").path("state").asText();
        if (!SUCCESS_CODE.equalsIgnoreCase(status) || !CALLBACK_SUCCESS_CODE.equalsIgnoreCase(orderStatus)) {
            return resultDTO;
        }
        resultDTO.setAmount(doPaymentPollingDTO.getAmount());
        resultDTO.setIsSuccess(true);
        return resultDTO;
    }

}
