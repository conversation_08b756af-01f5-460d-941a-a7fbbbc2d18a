package com.wd.lottery.module.payment.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.payment.dto.paymentmerchant.*;
import com.wd.lottery.module.payment.dto.paymentmerchant.*;
import com.wd.lottery.module.payment.entity.PaymentMerchant;

import jakarta.validation.constraints.NotNull;
import java.util.List;

public interface PaymentMerchantService {

    List<PaymentMerchant> findListInChannelIdAndMerchantIdInEnableEnum(List<Long> paymentChannelIds, Long merchantId, List<EnableEnum> enableEnumList);

    List<PaymentMerchant> findByPaymentChannelIds(List<Long> paymentChannelIds, Long merchantId, CurrencyEnum currencyEnum);

    List<PaymentMerchant> findListByMerchantIdAndPayName(Long merchantId, String payName, CurrencyEnum currencyEnum);

    List<PaymentMerchant> findListByIdsAndMerchantId(List<Long> ids, Long merchantId, List<EnableEnum> enableEnumList);

    PaymentMerchant findByIdByCache(Long id);

    PaymentMerchant findById(Long id);

    PaymentMerchant findByIdEnableEnum(Long id, EnableEnum enableEnum);

    List<PaymentMerchantOptionsDTO> findAllOptions(Long merchantId, CurrencyEnum currencyEnum);

    List<String> onlineOptions(Long merchantId, CurrencyEnum currencyEnum);

    List<String> withdrawChannelNameOptions(Long merchantId, CurrencyEnum currencyEnum);

    Page<ResponsePaymentMerchantSearchDto> search(SearchPaymentMerchantDTO searchPaymentMerchantDto, CurrencyEnum currencyEnum);

    Long saveData(SavePaymentMerchantDTO savePaymentMerchantDto);

    Long updateData(EditorPaymentMerchantDTO editorPaymentMerchantDto);

    void updateBalance(Long paymentMerchantId, Long balance);

    Long deleteData(Long id);

    @NotNull PaymentMerchant getEnableNotNull(Long id, Long merchantId, CurrencyEnum currencyEnum);

    void clearLimit();

    void addDailyDepositLimit(Long id, Long money);

    void addDailyWithdrawLimit(Long id, Long money);

    boolean updateEnableEnum(PaymentMerchantUpdateEnableEnumParam enableEnumParam);
}
