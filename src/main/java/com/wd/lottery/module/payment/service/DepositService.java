package com.wd.lottery.module.payment.service;


import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.handler.PaymentDepositHandler;

public interface DepositService {

    /**
     * 執行充值
     */
    DepositResultDTO doDeposit(DoDepositDTO doDepositDto);

    /**
     * 執行充值回調
     */
    DepositPayDTO doDepositNotify(DoPaymentNotifyDTO doDepositNotifyDto, PaymentDepositHandler payer);

    /**
     * 根據ID獲取充值處理類
     *
     * @param paymentHandlerId 付款處理器ID
     * @return 充值處理類
     */
    PaymentDepositHandler getDepositHandler(Long paymentHandlerId);

    /**
     * 轮询充值订单状态
     *
     * @param doPaymentPollingDTO
     * @return
     */
    OnlineOrderPollingResultDTO pollingDepositOrderStatus(DoPaymentPollingDTO doPaymentPollingDTO);

}
