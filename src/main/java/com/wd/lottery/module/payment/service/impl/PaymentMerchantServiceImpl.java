package com.wd.lottery.module.payment.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.entity.CreateEntity;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.cash.constatns.OrderChannelNameEnum;
import com.wd.lottery.module.cash.constatns.PlatformTypeEnum;
import com.wd.lottery.module.common.constants.CommonConstants;
import com.wd.lottery.module.common.constants.LogSubTypeConstants;
import com.wd.lottery.module.common.constants.LogTypeConstants;
import com.wd.lottery.module.merchant.dto.MerchantConfigDepositPlatformDTO;
import com.wd.lottery.module.merchant.dto.MerchantConfigDepositPlatformPaymentMerchantDTO;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.payment.dto.paymentmerchant.*;
import com.wd.lottery.module.payment.dto.paymentmerchant.*;
import com.wd.lottery.module.payment.entity.PaymentChannel;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumnValue;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.mapper.PaymentMerchantMapper;
import com.wd.lottery.module.payment.service.PaymentChannelService;
import com.wd.lottery.module.payment.service.PaymentDynamicColumnValueService;
import com.wd.lottery.module.payment.service.PaymentMerchantService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 三方管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentMerchantServiceImpl extends ServiceImpl<PaymentMerchantMapper, PaymentMerchant> implements PaymentMerchantService {

    private final PaymentChannelService paymentChannelService;

    private final PaymentDynamicColumnValueService paymentDynamicColumnValueService;

    private final MerchantConfigService merchantConfigService;

    private final PaymentMerchantMapper paymentMerchantMapper;

    /**
     * 返回當前商戶的三方金流
     *
     * @param merchantId     merchant uid
     * @param enableEnumList 停用/啟用
     * @return list entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<PaymentMerchant> findListInChannelIdAndMerchantIdInEnableEnum(List<Long> paymentChannelIds, Long merchantId, List<EnableEnum> enableEnumList) {
        LambdaQueryWrapper<PaymentMerchant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentMerchant::getMerchantId, merchantId);
        queryWrapper.in(PaymentMerchant::getEnableEnum, enableEnumList);
        queryWrapper.in(PaymentMerchant::getPaymentChannelId, paymentChannelIds);
        return super.list(queryWrapper);
    }

    /**
     * 返回當前商戶的三方金流
     *
     * @param paymentChannelIds paymentChannel uid list
     * @param merchantId        merchant uid
     * @return list entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<PaymentMerchant> findByPaymentChannelIds(List<Long> paymentChannelIds, Long merchantId, CurrencyEnum currencyEnum) {
        if (CollectionUtil.isEmpty(paymentChannelIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(PaymentMerchant::getMerchantId, merchantId)
                .eq(PaymentMerchant::getCurrencyEnum, currencyEnum)
                .in(PaymentMerchant::getPaymentChannelId, paymentChannelIds)
                .list();
    }

    /**
     * 返回當前商戶的三方金流
     *
     * @param merchantId merchant uid
     * @param payName    充值類型
     * @return list entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_LONG_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<PaymentMerchant> findListByMerchantIdAndPayName(Long merchantId, String payName, CurrencyEnum currencyEnum) {
        LambdaQueryWrapper<PaymentMerchant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentMerchant::getMerchantId, merchantId);

        if (StringUtils.isNotBlank(payName)) {
            MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonConstants.COMMON_DICT_KEY_DEPOSIT_PLATFORM, currencyEnum);
            if (merchantConfig != null) {
                List<MerchantConfigDepositPlatformDTO> merchantConfigDepositPlatformDtoList = merchantConfig.merchantConfigToListNotEmpty(MerchantConfigDepositPlatformDTO.class);

                List<MerchantConfigDepositPlatformDTO> onlinePlatformList = merchantConfigDepositPlatformDtoList.stream()
                        .filter(dto -> PlatformTypeEnum.ONLINE.equals(dto.getPlatformTypeEnum()))
                        .filter(dto -> dto.getPayName().equals(payName))
                        .collect(Collectors.toList());

                List<Long> paymentMerchantIdList = onlinePlatformList.stream()
                        .flatMap(dto -> dto.getPaymentMerchant().stream())
                        .map(MerchantConfigDepositPlatformPaymentMerchantDTO::getPaymentMerchantId)
                        .collect(Collectors.toList());

                if (CollectionUtil.isEmpty(paymentMerchantIdList)) {
                    return new ArrayList<>();
                }
                queryWrapper.in(PaymentMerchant::getId, paymentMerchantIdList);
            }
        }

        return super.list(queryWrapper);
    }

    /**
     * 依uid及當前商戶返回所有配置
     *
     * @param ids        list uid
     * @param merchantId merchant uid
     * @return list entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_PAYMENT_CHANNEL_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<PaymentMerchant> findListByIdsAndMerchantId(List<Long> ids, Long merchantId, List<EnableEnum> enableEnumList) {
        LambdaQueryWrapper<PaymentMerchant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PaymentMerchant::getId, ids);
        queryWrapper.eq(PaymentMerchant::getMerchantId, merchantId);
        queryWrapper.in(PaymentMerchant::getEnableEnum, enableEnumList);

        return super.list(queryWrapper);
    }

    /**
     * 查單筆資料
     *
     * @param id uid
     * @return entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_LONG_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public PaymentMerchant findByIdByCache(Long id) {
        return super.getById(id);
    }

    @Override
    public PaymentMerchant findById(Long id) {
        return super.getById(id);
    }

    /**
     * 查單筆
     *
     * @param id         payment_merchant id
     * @param enableEnum enableEnum
     * @return entity
     */
    @Override
    public PaymentMerchant findByIdEnableEnum(Long id, EnableEnum enableEnum) {
        LambdaQueryWrapper<PaymentMerchant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentMerchant::getId, id);
        queryWrapper.eq(PaymentMerchant::getEnableEnum, enableEnum);
        return super.getOne(queryWrapper);
    }

    /**
     * 商戶配置,查可用三方商戶
     *
     * @param merchantId   merchant uid
     * @param currencyEnum currency enum
     * @return list dto
     */
    @Override
    public List<PaymentMerchantOptionsDTO> findAllOptions(Long merchantId, CurrencyEnum currencyEnum) {
        List<PaymentMerchantOptionsDTO> paymentMerchantDtoList = new ArrayList<>();
        List<PaymentChannel> paymentChannelList = paymentChannelService.findByEnableEnumAndCurrencyEnum(Arrays.asList(EnableEnum.TRUE, EnableEnum.FALSE), currencyEnum);
        List<Long> paymentChannelIds = paymentChannelList.stream().map(PaymentChannel::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(paymentChannelIds)) {
            return paymentMerchantDtoList;
        }

        Map<Long, PaymentChannel> paymentChannelMap = paymentChannelList.stream().collect(Collectors.toMap(PaymentChannel::getId, Function.identity()));
        List<PaymentMerchant> paymentMerchantList = this.findListInChannelIdAndMerchantIdInEnableEnum(paymentChannelIds, merchantId, Arrays.asList(EnableEnum.TRUE, EnableEnum.FALSE));

        if (CollectionUtil.isNotEmpty(paymentMerchantList)) {
            paymentMerchantDtoList = BeanUtil.copyToList(paymentMerchantList, PaymentMerchantOptionsDTO.class);
            paymentMerchantDtoList.forEach(paymentMerchantOptionsDto -> {
                paymentMerchantOptionsDto.setPlatformTypeEnum(PlatformTypeEnum.ONLINE);
                PaymentChannel paymentChannel = paymentChannelMap.get(paymentMerchantOptionsDto.getPaymentChannelId());
                if (paymentChannel != null && paymentChannel.getEnableEnum() == EnableEnum.FALSE) {
                    paymentMerchantOptionsDto.setEnableEnum(EnableEnum.FALSE);
                }

                if (paymentChannel != null) {
                    paymentMerchantOptionsDto.setDepositEnableEnum(paymentChannel.getDepositEnableEnum());
                }
            });
        }

        return paymentMerchantDtoList;
    }

    /**
     * 返回商戶配置的線上充值類型
     *
     * @param merchantId   merchant uid
     * @param currencyEnum currency enum
     * @return list string
     */
    @Override
    public List<String> onlineOptions(Long merchantId, CurrencyEnum currencyEnum) {
        List<String> onlineOptions = new ArrayList<>();
        MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonConstants.COMMON_DICT_KEY_DEPOSIT_PLATFORM, currencyEnum);
        if (merchantConfig == null) {
            return onlineOptions;
        }

        List<MerchantConfigDepositPlatformDTO> merchantConfigDepositPlatformDtoList = merchantConfig.merchantConfigToListNotEmpty(MerchantConfigDepositPlatformDTO.class);
        if (CollectionUtil.isEmpty(merchantConfigDepositPlatformDtoList)) {
            return onlineOptions;
        }

        List<MerchantConfigDepositPlatformDTO> onlinePlatformList = merchantConfigDepositPlatformDtoList.stream()
                .filter(dto -> PlatformTypeEnum.ONLINE.equals(dto.getPlatformTypeEnum()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(onlinePlatformList)) {
            return onlineOptions;
        }

        onlineOptions = onlinePlatformList.stream().map(MerchantConfigDepositPlatformDTO::getPayName).collect(Collectors.toList());
        return onlineOptions;
    }

    @Override
    public List<String> withdrawChannelNameOptions(Long merchantId, CurrencyEnum currencyEnum) {
        List<String> channelNameList = new ArrayList<>();
        channelNameList.add(OrderChannelNameEnum.MANUAL_WITHDRAW.getText());
        List<PaymentChannel> paymentChannelList = paymentChannelService.findByEnableEnumAndCurrencyEnum(Collections.singletonList(EnableEnum.TRUE), currencyEnum);

        if (CollectionUtil.isNotEmpty(paymentChannelList)) {
            List<Long> paymentChannelIdList = paymentChannelList.stream()
                    .filter(s -> s.getWithdrawEnableEnum() == EnableEnum.TRUE)
                    .map(PaymentChannel::getId)
                    .collect(Collectors.toList());
            List<PaymentMerchant> paymentMerchantList = this.findListInChannelIdAndMerchantIdInEnableEnum(paymentChannelIdList, merchantId, Collections.singletonList(EnableEnum.TRUE));
            channelNameList.addAll(paymentMerchantList.stream().map(PaymentMerchant::getChannelName).collect(Collectors.toList()));
        }
        return channelNameList;
    }

    /**
     * 多條件查詢
     *
     * @param searchPaymentMerchantDto dto
     * @return page responseThirdPartyMerchantManageDto
     */
    @Override
    public Page<ResponsePaymentMerchantSearchDto> search(SearchPaymentMerchantDTO searchPaymentMerchantDto, CurrencyEnum currencyEnum) {
        List<ResponsePaymentMerchantSearchDto> responseDtoBaseList = new ArrayList<>();
        Page<PaymentMerchant> basePage = new Page<>(searchPaymentMerchantDto.getCurrent(), searchPaymentMerchantDto.getSize());

        LambdaQueryWrapper<PaymentMerchant> queryWrapper = new LambdaQueryWrapper<>();

        List<Long> paymentChannelIds = searchPaymentMerchantDto.getPaymentChannelIds();
        String merchantCode = searchPaymentMerchantDto.getPaymentMerchantCode();
        EnableEnum deposit = searchPaymentMerchantDto.getDepositEnum();
        EnableEnum withdraw = searchPaymentMerchantDto.getWithdrawEnum();

        // 先查payment_channel表,再查payment_merchant,因代收/代付條件在payment_channel表內
        List<PaymentChannel> paymentChannelList = paymentChannelService.findByInIdAndDepositAndWithdraw(paymentChannelIds, deposit, withdraw);
        paymentChannelIds = paymentChannelList.stream().map(PaymentChannel::getId).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(paymentChannelIds)) {
            queryWrapper.in(PaymentMerchant::getPaymentChannelId, paymentChannelIds);
        }

        if (StringUtils.isNotBlank(merchantCode)) {
            queryWrapper.like(PaymentMerchant::getPaymentMerchantCode, merchantCode);
        }

        String payName = searchPaymentMerchantDto.getPayName();
        if (StringUtils.isNotBlank(payName)) {
            Long merchantId = searchPaymentMerchantDto.getMerchantId();
            MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonConstants.COMMON_DICT_KEY_DEPOSIT_PLATFORM, currencyEnum);
            if (merchantConfig != null) {
                List<MerchantConfigDepositPlatformDTO> merchantConfigDepositPlatformDtoList = merchantConfig.merchantConfigToListNotEmpty(MerchantConfigDepositPlatformDTO.class);

                List<MerchantConfigDepositPlatformDTO> onlinePlatformList = merchantConfigDepositPlatformDtoList.stream()
                        .filter(dto -> PlatformTypeEnum.ONLINE.equals(dto.getPlatformTypeEnum()))
                        .filter(dto -> dto.getPayName().equals(payName))
                        .collect(Collectors.toList());

                List<Long> paymentMerchantIdList = onlinePlatformList.stream()
                        .flatMap(dto -> dto.getPaymentMerchant().stream())
                        .map(MerchantConfigDepositPlatformPaymentMerchantDTO::getPaymentMerchantId)
                        .collect(Collectors.toList());

                if (CollectionUtil.isEmpty(paymentMerchantIdList)) {
                    return new Page<>();
                }
                queryWrapper.in(PaymentMerchant::getId, paymentMerchantIdList);
            }
        }

        if (searchPaymentMerchantDto.getPlatformEnum() != null) {
            queryWrapper.eq(PaymentMerchant::getPlatformEnum, searchPaymentMerchantDto.getPlatformEnum());
        }

        if (StringUtils.isNotBlank(searchPaymentMerchantDto.getChannelName())) {
            queryWrapper.eq(PaymentMerchant::getChannelName, searchPaymentMerchantDto.getChannelName());
        }

        if (searchPaymentMerchantDto.getEnableEnum() != null) {
            queryWrapper.eq(PaymentMerchant::getEnableEnum, searchPaymentMerchantDto.getEnableEnum());
        }

        queryWrapper.eq(PaymentMerchant::getCurrencyEnum, currencyEnum);
        queryWrapper.eq(PaymentMerchant::getMerchantId, searchPaymentMerchantDto.getMerchantId());
        queryWrapper.orderByDesc(CreateEntity::getCreateTime);

        Page<PaymentMerchant> paymentMerchantPage = super.page(basePage, queryWrapper);
        List<PaymentMerchant> paymentMerchantList = paymentMerchantPage.getRecords();
        if (CollectionUtil.isNotEmpty(paymentMerchantList)) {
            Map<Long, PaymentChannel> paymentChannelMap = paymentChannelList.stream()
                    .collect(Collectors.toMap(PaymentChannel::getId, data -> data));

            // 取paymentDynamicColumnValue對應資料
            List<Long> paymentMerchantIds = paymentMerchantList.stream().map(PaymentMerchant::getId).collect(Collectors.toList());
            List<PaymentDynamicColumnValue> paymentDynamicColumnValues = paymentDynamicColumnValueService.searchInPaymentMerchantId(paymentMerchantIds);
            Map<Long, List<PaymentDynamicColumnValue>> paymentDynamicColumnValueMap = paymentDynamicColumnValues.stream()
                    .collect(Collectors.groupingBy(PaymentDynamicColumnValue::getPaymentMerchantId));

            // 重新封裝新的dto供前端使用
            List<ResponsePaymentMerchantSearchDto> responseDtoList = BeanUtil.copyToList(paymentMerchantList, ResponsePaymentMerchantSearchDto.class);
            for (ResponsePaymentMerchantSearchDto responsePaymentMerchantSearchDto : responseDtoList) {
                PaymentChannel paymentChannel = paymentChannelMap.get(responsePaymentMerchantSearchDto.getPaymentChannelId());
                if (paymentChannel != null) {
                    responsePaymentMerchantSearchDto.setDepositEnum(paymentChannel.getDepositEnableEnum());
                    responsePaymentMerchantSearchDto.setWithdrawEnum(paymentChannel.getWithdrawEnableEnum());
                    responsePaymentMerchantSearchDto.setDynamicColumnValues(BeanUtil.copyToList(paymentDynamicColumnValueMap.get(responsePaymentMerchantSearchDto.getId()), DynamicColumnValueDTO.class));
                    responseDtoBaseList.add(responsePaymentMerchantSearchDto);
                }
            }
        }

        // 重新建立新的page(沿用basePage資料數量等資訊,因為資料量一樣)
        Page<ResponsePaymentMerchantSearchDto> page = new Page<>(paymentMerchantPage.getPages(), paymentMerchantPage.getSize());
        page.setRecords(responseDtoBaseList);
        page.setCurrent(paymentMerchantPage.getCurrent());
        page.setPages(paymentMerchantPage.getPages());
        page.setTotal(paymentMerchantPage.getTotal());
        return page;
    }

    /**
     * 新增商戶
     *
     * @param savePaymentMerchantDto dto
     * @return responsePaymentMerchantDto
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.PAYMENT_MERCHANT, subType = LogSubTypeConstants.CREATE,
            success = "三方商户管理, 三方金流编号:{{#payName}}, 币种:{{#currencyEnum}}" +
                    "三方名称:{{#savePaymentMerchantDto.channelName}}, 商户编号:{{#savePaymentMerchantDto.paymentMerchantCode}}, " +
                    "额外赠送比例:{{#rewardRate}}%, 启用:{{#savePaymentMerchantDto.enableEnum}}")
    public Long saveData(SavePaymentMerchantDTO savePaymentMerchantDto) {
        Long paymentMerchantId;
        Long paymentChannelId = savePaymentMerchantDto.getPaymentChannelId();

        // 因baseDto與編輯共用,所以這邊需判斷必填不用@NutNull
        if (StringUtils.isBlank(savePaymentMerchantDto.getPrivateKey())) {
            throw new ApiException(CommonCode.PARAM_INVALID);
        }

        try {
            PaymentMerchant paymentMerchant = new PaymentMerchant();
            BeanUtils.copyProperties(savePaymentMerchantDto, paymentMerchant);

            PaymentChannel paymentChannel = paymentChannelService.findById(paymentChannelId);
            if (paymentChannel == null) {
                throw new ApiException(CommonCode.NO_DATA);
            }
            paymentMerchant.setId(IdWorker.getId());
            paymentMerchant.setPlatformEnum(paymentChannel.getPlatformEnum());
            paymentMerchant.setPaymentChannelId(paymentChannel.getId());
            paymentMerchant.setCurrencyEnum(paymentChannel.getCurrencyEnum());
            paymentMerchant.setQuickAmount(savePaymentMerchantDto.getQuickAmounts().toString());
            paymentMerchant.setCreateBy(savePaymentMerchantDto.getAdminName());
            paymentMerchant.setCreateTime(savePaymentMerchantDto.getUpdateTime());
            paymentMerchant.setUpdateBy(savePaymentMerchantDto.getAdminName());
            paymentMerchant.setUpdateTime(savePaymentMerchantDto.getUpdateTime());
            paymentMerchant.setBankcardRequired(savePaymentMerchantDto.getBankcardRequired());
            if (CollUtil.isNotEmpty(savePaymentMerchantDto.getLevel())) {
                paymentMerchant.setLevel(savePaymentMerchantDto.getLevel().toString());
            }

            boolean isSave = super.save(paymentMerchant);
            if (!isSave) {
                log.error("[三方商戶管理] 新增三方商戶配置異常, payment channel id:{}", paymentChannelId);
                throw new ApiException(CommonCode.FAILED);
            }

            List<DynamicColumnValueDTO> dynamicColumnValues = savePaymentMerchantDto.getDynamicColumnValues();
            List<PaymentDynamicColumnValue> paymentDynamicColumnValues = BeanUtil.copyToList(dynamicColumnValues, PaymentDynamicColumnValue.class);
            paymentDynamicColumnValues.forEach(s -> {
                s.setId(IdWorker.getId());
                s.setPaymentMerchantId(paymentMerchant.getId());
                s.setUpdateBy(savePaymentMerchantDto.getAdminName());
                s.setUpdateTime(savePaymentMerchantDto.getUpdateTime());
                s.setCreateBy(savePaymentMerchantDto.getAdminName());
                s.setCreateTime(savePaymentMerchantDto.getUpdateTime());
            });
            paymentDynamicColumnValueService.batchSaveOrUpdate(paymentDynamicColumnValues);
            paymentMerchantId = paymentMerchant.getId();

            LogRecordContext.putVariable("currencyEnum", paymentChannel.getCurrencyEnum());
            LogRecordContext.putVariable("payName", paymentChannel.getName() + "(" + paymentChannel.getId() + ")");
            LogRecordContext.putVariable("rewardRate", savePaymentMerchantDto.getRewardRate() / Constants.HUNDRED_LONG);
        } catch (Exception e) {
            log.error("[三方商戶管理] 新增三方商戶配置異常, payment channel id:{}", paymentChannelId, e);
            throw new ApiException(CommonCode.FAILED);
        }

        return paymentMerchantId;
    }

    /**
     * 編輯商戶
     *
     * @param editorPaymentMerchantDto dto
     * @return paymentChannel entity
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.PAYMENT_MERCHANT, subType = LogSubTypeConstants.UPDATE,
            success = "三方商户管理, 三方商户uid:{{#editorPaymentMerchantDto.id}}" +
                    "三方名称:{{#editorPaymentMerchantDto.channelName}}, 商户编号:{{#editorPaymentMerchantDto.paymentMerchantCode}}, " +
                    "额外赠送比例:{{#rewardRate}}%, 启用:{{#editorPaymentMerchantDto.enableEnum}}{{#additionalLog}}")
    public Long updateData(EditorPaymentMerchantDTO editorPaymentMerchantDto) {
        Long id = editorPaymentMerchantDto.getId();
        PaymentMerchant paymentMerchant = super.getById(id);
        if (paymentMerchant == null) {
            throw new ApiException(CommonCode.NO_DATA);
        }

        try {
            BeanUtils.copyProperties(editorPaymentMerchantDto, paymentMerchant);
            paymentMerchant.setQuickAmount(editorPaymentMerchantDto.getQuickAmounts().toString());
            if (CollUtil.isNotEmpty(editorPaymentMerchantDto.getLevel())) {
                paymentMerchant.setLevel(editorPaymentMerchantDto.getLevel().toString());
            } else {
                paymentMerchant.setLevel("");
            }
            paymentMerchant.setUpdateTime(editorPaymentMerchantDto.getUpdateTime());
            paymentMerchant.setUpdateBy(editorPaymentMerchantDto.getAdminName());

            boolean isUpdate = super.updateById(paymentMerchant);
            if (!isUpdate) {
                log.error("[三方商戶管理] 更新三方商戶配置異常, payment merchant id:{}", id);
                throw new ApiException(CommonCode.FAILED);
            }

            List<Long> paymentMerchantIds = new ArrayList<>();
            paymentMerchantIds.add(id);
            List<PaymentDynamicColumnValue> paymentDynamicColumnValues = paymentDynamicColumnValueService.searchInPaymentMerchantId(paymentMerchantIds);
            if (CollectionUtil.isNotEmpty(paymentDynamicColumnValues)) {
                List<DynamicColumnValueDTO> dynamicColumnValues = editorPaymentMerchantDto.getDynamicColumnValues();
                paymentDynamicColumnValues = BeanUtil.copyToList(dynamicColumnValues, PaymentDynamicColumnValue.class);
                paymentDynamicColumnValues.forEach(s -> {
                    if (s.getId() == null) {
                        s.setId(IdWorker.getId());
                        s.setCreateTime(editorPaymentMerchantDto.getUpdateTime());
                        s.setCreateBy(editorPaymentMerchantDto.getAdminName());
                    }
                    s.setUpdateTime(editorPaymentMerchantDto.getUpdateTime());
                    s.setUpdateBy(editorPaymentMerchantDto.getAdminName());
                });
                paymentDynamicColumnValueService.batchSaveOrUpdate(paymentDynamicColumnValues);
            }

            String additionalLog = "";
            if (StringUtils.isNotBlank(editorPaymentMerchantDto.getPrivateKey())) {
                additionalLog += ", 私钥(密钥)异动";
            }
            if (StringUtils.isNotBlank(editorPaymentMerchantDto.getPublicKey())) {
                additionalLog += ", 公钥异动";
            }
            LogRecordContext.putVariable("additionalLog", additionalLog);
            LogRecordContext.putVariable("rewardRate", editorPaymentMerchantDto.getRewardRate() / Constants.HUNDRED_LONG);
        } catch (Exception e) {
            log.error("[三方商戶管理] 更新三方商戶配置異常, payment merchant id:{}", id, e);
            throw new ApiException(CommonCode.FAILED);
        }

        return id;
    }

    /**
     * 更新三方餘額
     *
     * @param paymentMerchantId uid
     * @param balance           balance
     * @return uid
     */
    @Override
    public void updateBalance(Long paymentMerchantId, Long balance) {
        Assert.notNull(balance);

        PaymentMerchant paymentMerchant = new PaymentMerchant();
        paymentMerchant.setId(paymentMerchantId);
        paymentMerchant.setBalance(balance);

        super.updateById(paymentMerchant);
    }

    /**
     * 刪除商戶
     *
     * @param id paymentMerchant id
     * @return 刪除成功的id
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.PAYMENT_MERCHANT, subType = LogSubTypeConstants.DELETE,
            success = "三方商户管理, 三方商户uid:{{#id}}, 三方名称:{{#channelName}}")
    public Long deleteData(Long id) {
        PaymentMerchant paymentMerchant = super.getById(id);
        if (paymentMerchant == null) {
            throw new ApiException(CommonCode.NO_DATA);
        }
        boolean isDelete = super.removeById(id);
        if (!isDelete) {
            log.error("[三方商戶管理] 刪除三方異常, id:{}", id);
            throw new ApiException(CommonCode.FAILED);
        }

        paymentDynamicColumnValueService.deleteByPaymentMerchantId(id);

        LogRecordContext.putVariable("channelName", paymentMerchant.getChannelName());
        return id;
    }

    @NotNull
    @Override
    public PaymentMerchant getEnableNotNull(Long id, Long merchantId, CurrencyEnum currencyEnum) {
        Assert.notNull(id, "id not null");
        Assert.notNull(merchantId, "merchantId not null");
        Assert.notNull(currencyEnum, "currencyEnum not null");

        PaymentMerchant paymentMerchant = super.lambdaQuery()
                .eq(PaymentMerchant::getId, id)
                .eq(PaymentMerchant::getMerchantId, merchantId)
                .eq(PaymentMerchant::getEnableEnum, EnableEnum.TRUE)
                .in(PaymentMerchant::getCurrencyEnum, CurrencyEnum.getCurrencyEnumAndAllList(currencyEnum))
                .one();
        if (Objects.isNull(paymentMerchant)) {
            throw new ApiException(CommonCode.PAYMENT_MERCHANT_CHANNEL_DISABLE);
        }
        Long paymentChannelId = paymentMerchant.getPaymentChannelId();
        PaymentChannel paymentChannel = paymentChannelService.findByIdEnableEnum(paymentChannelId, EnableEnum.TRUE);
        if (Objects.isNull(paymentChannel)) {
            throw new ApiException(CommonCode.PAYMENT_MERCHANT_CHANNEL_DISABLE);
        }
        paymentMerchant.setPaymentChannel(paymentChannel);
        return paymentMerchant;
    }

    @Override
    public void clearLimit() {
        super.lambdaUpdate()
                .set(PaymentMerchant::getDailyDepositMoney, 0L)
                .set(PaymentMerchant::getDailyDepositCount, 0L)
                .set(PaymentMerchant::getDailyWithdrawMoney, 0L)
                .set(PaymentMerchant::getDailyWithdrawCount, 0L)
                .gt(PaymentMerchant::getDailyDepositMoney, 0L)
                .or()
                .gt(PaymentMerchant::getDailyDepositCount, 0L)
                .or()
                .gt(PaymentMerchant::getDailyWithdrawMoney, 0L)
                .or()
                .gt(PaymentMerchant::getDailyWithdrawCount, 0L)
                .update();
    }

    /**
     * 增加每日充值金額, 次數
     *
     * @param id    uid
     * @param money 充值金額
     */
    @Override
    public void addDailyDepositLimit(Long id, Long money) {
        paymentMerchantMapper.addDailyDepositLimit(id, money);
    }

    /**
     * 增加每日提現金額, 次數
     *
     * @param id    id
     * @param money 提現金額
     */
    @Override
    public void addDailyWithdrawLimit(Long id, Long money) {
        paymentMerchantMapper.addDailyWithdrawLimit(id, money);
    }


    @Override
    @Transactional
    public boolean updateEnableEnum(PaymentMerchantUpdateEnableEnumParam enableEnumParam) {
        PaymentMerchant paymentMerchant = new PaymentMerchant();
        paymentMerchant.setId(enableEnumParam.getId());
        paymentMerchant.setEnableEnum(enableEnumParam.getEnableEnum());
        paymentMerchant.setUpdateBy(enableEnumParam.getUpdateBy());
        paymentMerchant.setUpdateTime(LocalDateTime.now());
        return super.updateById(paymentMerchant);
    }
}
