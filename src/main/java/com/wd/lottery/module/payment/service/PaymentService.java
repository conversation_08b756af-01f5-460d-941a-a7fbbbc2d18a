package com.wd.lottery.module.payment.service;


import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.param.PaymentOfflineDepositParam;
import com.wd.lottery.module.payment.param.PaymentOnlineDepositParam;

public interface PaymentService {
    DepositResultDTO onlineDeposit(PaymentOnlineDepositParam paymentOnlineDepositParam, MemberTokenInfoDTO memberTokenInfoDTO);

    Long offlineDeposit(PaymentOfflineDepositParam paymentOfflineDepositParam, MemberTokenInfoDTO memberTokenInfoDTO);

    String depositNotify(Long channelId, Long paymentMerchantId, DoPaymentNotifyDTO doPaymentNotifyDto);

    WithdrawResultDTO withdraw(Long channelId, Long paymentMerchantId, Long orderId, String adminName);

    String withdrawNotify(Long channelId, Long paymentMerchantId, DoPaymentNotifyDTO doPaymentNotifyDto);

    BalanceResultDTO balance(QueryBalanceDTO queryBalanceDto);

    OnlineOrderPollingResultDTO queryOnlineDepositOrderStatus(DoPaymentPollingDTO doPaymentPollingDTO);

    OnlineOrderPollingResultDTO queryWithdrawOrderStatus(DoPaymentPollingDTO doPaymentPollingDTO);

}
