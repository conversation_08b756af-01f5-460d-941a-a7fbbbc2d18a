package com.wd.lottery.module.payment.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.module.payment.dto.payment_channel.*;
import com.wd.lottery.module.payment.dto.payment_channel.*;
import com.wd.lottery.module.payment.entity.PaymentChannel;

import java.util.List;

public interface PaymentChannelService {
    PaymentChannel findById(Long id);

    PaymentChannel findByIdEnableEnum(Long id, EnableEnum enableEnum);

    List<PaymentChannel> findByEnableEnum(EnableEnum enableEnum);

    List<PaymentChannel> findByEnableEnumAndCurrencyEnum(List<EnableEnum> enableEnumList, CurrencyEnum currencyEnum);

    List<PaymentChannel> findByInIdAndDepositAndWithdraw(List<Long> ids, EnableEnum deposit, EnableEnum withdraw);

    List<ResponsePaymentChannelOptionsDto> findAllOptions(CurrencyEnum currencyEnum);

    Page<ResponsePaymentChannelSearchDTO> search(SearchPaymentChannelDTO searchPaymentChannelDto);

    Long saveData(SavePaymentChannelDTO savePaymentChannelDto);

    Long updateData(EditorPaymentChannelDTO editorPaymentChannelDto);

    Long deleteData(Long id);
}
