package com.wd.lottery.module.payment.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.entity.CreateAndUpdateEntity;
import com.wd.lottery.common.entity.CreateEntity;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.common.constants.LogSubTypeConstants;
import com.wd.lottery.module.common.constants.LogTypeConstants;
import com.wd.lottery.module.payment.dto.payment_channel.*;
import com.wd.lottery.module.payment.dto.payment_channel.*;
import com.wd.lottery.module.payment.dto.payment_white_list.PaymentWhiteListDTO;
import com.wd.lottery.module.payment.entity.PaymentChannel;
import com.wd.lottery.module.payment.entity.PaymentChannelBank;
import com.wd.lottery.module.payment.entity.PaymentDynamicColumn;
import com.wd.lottery.module.payment.mapper.PaymentChannelMapper;
import com.wd.lottery.module.payment.service.PaymentChannelBankService;
import com.wd.lottery.module.payment.service.PaymentChannelService;
import com.wd.lottery.module.payment.service.PaymentDynamicColumnService;
import com.wd.lottery.module.payment.service.PaymentWhiteListService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 三方管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentChannelServiceImpl extends ServiceImpl<PaymentChannelMapper, PaymentChannel> implements PaymentChannelService {

    private final PaymentDynamicColumnService paymentDynamicColumnService;

    private final PaymentChannelBankService paymentChannelBankService;

    private final PaymentWhiteListService paymentWhiteListService;


    /**
     * 查單筆資料
     *
     * @param id paymentChannel id
     * @return paymentChannel entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_LONG_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public PaymentChannel findById(Long id) {
        PaymentChannel paymentChannel = super.getById(id);
        if (paymentChannel == null) {
            throw new ApiException(CommonCode.NO_DATA);
        }

        return paymentChannel;
    }

    /**
     * 查單筆
     *
     * @param id         paymentChannel id
     * @param enableEnum enableEnum
     * @return entity
     */
    @Override
    public PaymentChannel findByIdEnableEnum(Long id, EnableEnum enableEnum) {
        LambdaQueryWrapper<PaymentChannel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentChannel::getId, id);
        queryWrapper.eq(PaymentChannel::getEnableEnum, enableEnum);
        return super.getOne(queryWrapper);
    }

    /**
     * 查list
     *
     * @param enableEnum 啟用/停用
     * @return list entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_PAYMENT_CHANNEL_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<PaymentChannel> findByEnableEnum(EnableEnum enableEnum) {
        LambdaQueryWrapper<PaymentChannel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentChannel::getEnableEnum, enableEnum);
        return super.list(queryWrapper);
    }

    /**
     * 查list
     *
     * @param enableEnumList 啟用/停用
     * @return list entity
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<PaymentChannel> findByEnableEnumAndCurrencyEnum(List<EnableEnum> enableEnumList, CurrencyEnum currencyEnum) {
        LambdaQueryWrapper<PaymentChannel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PaymentChannel::getEnableEnum, enableEnumList);
        queryWrapper.eq(PaymentChannel::getCurrencyEnum, currencyEnum);
        return super.list(queryWrapper);
    }


    /**
     * 依條件查資料(供外部使用)
     *
     * @param ids      list uid
     * @param deposit  是否代收
     * @param withdraw 是否代付
     * @return list payment entity
     */
    @Override
    public List<PaymentChannel> findByInIdAndDepositAndWithdraw(List<Long> ids, EnableEnum deposit, EnableEnum withdraw) {
        LambdaQueryWrapper<PaymentChannel> queryWrapper = new LambdaQueryWrapper<>();
        if (ids != null && ids.size() > 0) {
            queryWrapper.in(PaymentChannel::getId, ids);
        }

        if (deposit != null) {
            queryWrapper.eq(PaymentChannel::getDepositEnableEnum, deposit);
        }

        if (withdraw != null) {
            queryWrapper.eq(PaymentChannel::getWithdrawEnableEnum, withdraw);
        }

        return super.list(queryWrapper);
    }

    /**
     * 查全部三方資料(三方商戶管理用-下拉選單 >> 新增用)
     *
     * @return List dto
     */
    @Override
    @Cacheable(value = Constants.LOCAL_CACHE_SHORT_NAME, keyGenerator = Constants.KEY_GENERATOR)
    public List<ResponsePaymentChannelOptionsDto> findAllOptions(CurrencyEnum currencyEnum) {
        List<ResponsePaymentChannelOptionsDto> responsePaymentChannelOptionsDtoList = new ArrayList<>();

        LambdaQueryWrapper<PaymentChannel> queryWrapper = new LambdaQueryWrapper<>();
        // 只取啟用的
        queryWrapper.eq(PaymentChannel::getCurrencyEnum, currencyEnum);
        queryWrapper.eq(PaymentChannel::getEnableEnum, EnableEnum.TRUE);
        queryWrapper.orderByDesc(CreateAndUpdateEntity::getUpdateTime);
        List<PaymentChannel> paymentChannels = super.list(queryWrapper);
        if (CollectionUtils.isEmpty(paymentChannels)) {
            return responsePaymentChannelOptionsDtoList;
        }

        // 查對應的動態欄位key,中文名,描述
        List<Long> paymentChannelIds = paymentChannels.stream().map(PaymentChannel::getId).collect(Collectors.toList());
        List<PaymentDynamicColumn> paymentDynamicColumns = paymentDynamicColumnService.findAllByPaymentChannelId(paymentChannelIds);
        // 將動態欄位轉型map<paymentChannelId, List<PaymentDynamicColumn>>
        Map<Long, List<PaymentDynamicColumn>> paymentDynamicColumnMap = paymentDynamicColumns.stream()
                .collect(Collectors.groupingBy(PaymentDynamicColumn::getPaymentChannelId));

        // 重新封裝畫面所需的資料
        paymentChannels.forEach(paymentChannel -> {
            ResponsePaymentChannelOptionsDto responsePaymentChannelOptionsDto = new ResponsePaymentChannelOptionsDto();
            BeanUtils.copyProperties(paymentChannel, responsePaymentChannelOptionsDto);
            responsePaymentChannelOptionsDto.setPaymentChannelId(paymentChannel.getId());

            List<PaymentDynamicColumn> paymentDynamicColumnList = paymentDynamicColumnMap.get(paymentChannel.getId());
            List<ResponseDynamicColumnDTO> responseDynamicColumnDTOList = BeanUtil.copyToList(paymentDynamicColumnList, ResponseDynamicColumnDTO.class);
            responsePaymentChannelOptionsDto.setDynamicColumns(responseDynamicColumnDTOList);
            responsePaymentChannelOptionsDtoList.add(responsePaymentChannelOptionsDto);
        });

        return responsePaymentChannelOptionsDtoList;
    }

    /**
     * 多條件查詢
     *
     * @param searchPaymentChannelDto dto
     * @return page
     */
    @Override
    public Page<ResponsePaymentChannelSearchDTO> search(SearchPaymentChannelDTO searchPaymentChannelDto) {
        List<ResponsePaymentChannelSearchDTO> responseDtoList = new ArrayList<>();
        Page<PaymentChannel> basePage = new Page<>(searchPaymentChannelDto.getCurrent(), searchPaymentChannelDto.getSize());
        LambdaQueryWrapper<PaymentChannel> queryWrapper = buildSearchWrapper(searchPaymentChannelDto);

        Page<PaymentChannel> paymentChannelPage = super.page(basePage, queryWrapper);
        List<PaymentChannel> paymentChannels = paymentChannelPage.getRecords();

        if (CollectionUtil.isNotEmpty(paymentChannels)) {
            List<Long> paymentChannelIds = paymentChannels.stream().map(PaymentChannel::getId).collect(Collectors.toList());
            // 將paymentChannelId取出對應的銀行編碼表
            List<PaymentChannelBank> paymentChannelBankList = paymentChannelBankService.findAllByPaymentChannelId(paymentChannelIds);
            Map<Long, List<PaymentChannelBank>> paymentChannelBankMap = paymentChannelBankList.stream()
                    .collect(Collectors.groupingBy(PaymentChannelBank::getPaymentChannelId));

            // 將paymentChannelId取出對應的動態欄位key表
            List<PaymentDynamicColumn> paymentDynamicColumnList = paymentDynamicColumnService.findAllByPaymentChannelId(paymentChannelIds);
            Map<Long, List<PaymentDynamicColumn>> paymentDynamicColumnMap = paymentDynamicColumnList.stream()
                    .collect(Collectors.groupingBy(PaymentDynamicColumn::getPaymentChannelId));

            // 白名單
            List<PaymentWhiteListDTO> paymentWhiteListDtoList = paymentWhiteListService.searchByPaymentChannelId(paymentChannelIds);
            Map<Long, List<PaymentWhiteListDTO>> paymentWhiteMap = paymentWhiteListDtoList.stream().collect(Collectors.groupingBy(PaymentWhiteListDTO::getPaymentChannelId));

            // 重新封裝新的dto供前端使用
            responseDtoList = BeanUtil.copyToList(paymentChannels, ResponsePaymentChannelSearchDTO.class);
            responseDtoList.forEach(responsePaymentChannelSearchDTO -> {
                Long paymentChannelId = responsePaymentChannelSearchDTO.getId();
                responsePaymentChannelSearchDTO.setPaymentChannelBanks(paymentChannelBankMap.get(paymentChannelId));
                responsePaymentChannelSearchDTO.setPaymentDynamicColumns(paymentDynamicColumnMap.get(paymentChannelId));
                responsePaymentChannelSearchDTO.setPaymentWithdrawList(paymentWhiteMap.get(responsePaymentChannelSearchDTO.getId()));
            });
        }

        // 重新建立新的page(沿用basePage資料數量等資訊,因為資料量一樣)
        Page<ResponsePaymentChannelSearchDTO> page = new Page<>(paymentChannelPage.getPages(), paymentChannelPage.getSize());
        page.setRecords(responseDtoList);
        page.setCurrent(paymentChannelPage.getCurrent());
        page.setPages(paymentChannelPage.getPages());
        page.setTotal(paymentChannelPage.getTotal());
        return page;
    }

    private static LambdaQueryWrapper<PaymentChannel> buildSearchWrapper(SearchPaymentChannelDTO searchPaymentChannelDto) {
        LambdaQueryWrapper<PaymentChannel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentChannel::getCurrencyEnum, searchPaymentChannelDto.getCurrencyEnum());
        if (StringUtils.isNotBlank(searchPaymentChannelDto.getName())) {
            queryWrapper.like(PaymentChannel::getName, searchPaymentChannelDto.getName());
        }
        if (StringUtils.isNotBlank(searchPaymentChannelDto.getDepositDomain())) {
            queryWrapper.like(PaymentChannel::getDepositDomain, searchPaymentChannelDto.getDepositDomain());
        }
        if (StringUtils.isNotBlank(searchPaymentChannelDto.getWithdrawDomain())) {
            queryWrapper.like(PaymentChannel::getWithdrawDomain, searchPaymentChannelDto.getWithdrawDomain());
        }
        if (StringUtils.isNotBlank(searchPaymentChannelDto.getBalanceDomain())) {
            queryWrapper.like(PaymentChannel::getBalanceDomain, searchPaymentChannelDto.getBalanceDomain());
        }
        if (searchPaymentChannelDto.getEnableEnum() != null) {
            queryWrapper.eq(PaymentChannel::getEnableEnum, searchPaymentChannelDto.getEnableEnum());
        }
        if (searchPaymentChannelDto.getPlatformEnum() != null) {
            queryWrapper.eq(PaymentChannel::getPlatformEnum, searchPaymentChannelDto.getPlatformEnum());
        }
        if (searchPaymentChannelDto.getDeposit() != null) {
            queryWrapper.eq(PaymentChannel::getDepositEnableEnum, searchPaymentChannelDto.getDeposit());
        }
        if (searchPaymentChannelDto.getWithdraw() != null) {
            queryWrapper.eq(PaymentChannel::getWithdrawEnableEnum, searchPaymentChannelDto.getWithdraw());
        }
        queryWrapper.orderByDesc(CreateEntity::getCreateTime);
        return queryWrapper;
    }

    /**
     * 新增三方
     *
     * @param savePaymentChannelDto dto
     * @return uid
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.PAYMENT_CHANNEL, subType = LogSubTypeConstants.CREATE,
            success = "三方金流, 编号:{{#savePaymentChannelDto.id}}, 名称:{{#savePaymentChannelDto.name}}, " +
                    "币种:{{#savePaymentChannelDto.currencyEnum}}, 代收:{{#savePaymentChannelDto.depositEnableEnum}}, " +
                    "代付:{{#savePaymentChannelDto.withdrawEnableEnum}}, 启用:{{#savePaymentChannelDto.enableEnum}}")
    public Long saveData(SavePaymentChannelDTO savePaymentChannelDto) {
        Long paymentChannelId = IdWorker.getId();

        PaymentChannel paymentChannel = new PaymentChannel();
        BeanUtils.copyProperties(savePaymentChannelDto, paymentChannel);
        paymentChannel.setId(paymentChannelId);
        paymentChannel.setUpdateBy(savePaymentChannelDto.getAdminName());
        paymentChannel.setUpdateTime(savePaymentChannelDto.getUpdateTime());
        paymentChannel.setCreateBy(savePaymentChannelDto.getAdminName());
        paymentChannel.setCreateTime(savePaymentChannelDto.getUpdateTime());

        List<PaymentDynamicColumn> paymentDynamicColumnList = BeanUtil.copyToList(savePaymentChannelDto.getPaymentDynamicColumns(), PaymentDynamicColumn.class);
        List<PaymentChannelBank> paymentChannelBankList = BeanUtil.copyToList(savePaymentChannelDto.getPaymentChannelBanks(), PaymentChannelBank.class);

        try {

            boolean isSave = super.save(paymentChannel);
            if (!isSave) {
                log.error("[三方管理] 新增三方異常, paymentHandlerId:{}, platform:{}, name:{}", savePaymentChannelDto.getPaymentHandlerId(), savePaymentChannelDto.getPlatformEnum(), savePaymentChannelDto.getName());
                throw new ApiException(CommonCode.FAILED);
            }

            // payment_channel有新增完才新增動態欄位跟銀行編碼
            if (!CollectionUtils.isEmpty(paymentDynamicColumnList)) {
                paymentDynamicColumnList.forEach(s -> {
                    s.setId(IdWorker.getId());
                    s.setPaymentChannelId(paymentChannelId);
                    s.setUpdateBy(savePaymentChannelDto.getAdminName());
                    s.setUpdateTime(savePaymentChannelDto.getUpdateTime());
                    s.setCreateBy(savePaymentChannelDto.getAdminName());
                    s.setCreateTime(savePaymentChannelDto.getUpdateTime());
                });
                paymentDynamicColumnService.saveOrUpdateBatch(paymentDynamicColumnList);
            }

            if (!CollectionUtils.isEmpty(paymentChannelBankList)) {
                paymentChannelBankList.forEach(s -> {
                    s.setId(IdWorker.getId());
                    s.setPaymentChannelId(paymentChannelId);
                    s.setCreateBy(savePaymentChannelDto.getAdminName());
                    s.setCreateTime(savePaymentChannelDto.getUpdateTime());
                    s.setUpdateBy(savePaymentChannelDto.getAdminName());
                    s.setUpdateTime(savePaymentChannelDto.getUpdateTime());
                });
                paymentChannelBankService.saveOrUpdateBatch(paymentChannelBankList);
            }

        } catch (Exception e) {
            log.error("[三方管理] 新增三方異常", e);
            throw new ApiException(CommonCode.FAILED);
        }

        return paymentChannelId;
    }

    /**
     * 編輯三方
     *
     * @param editorPaymentChannelDto dto
     * @return uid
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.PAYMENT_CHANNEL, subType = LogSubTypeConstants.UPDATE,
            success = "三方金流, 编号:{{#savePaymentChannelDto.id}}, 名称:{{#savePaymentChannelDto.name}}, " +
                    "币种:{{#savePaymentChannelDto.currencyEnum}}, 代收:{{#savePaymentChannelDto.depositEnableEnum}}, " +
                    "代付:{{#savePaymentChannelDto.withdrawEnableEnum}}, 启用:{{#savePaymentChannelDto.enableEnum}}")
    public Long updateData(EditorPaymentChannelDTO editorPaymentChannelDto) {
        Long id = editorPaymentChannelDto.getId();
        PaymentChannel paymentChannel = super.getById(id);
        if (paymentChannel == null) {
            throw new ApiException(CommonCode.NO_DATA);
        }

        try {
            BeanUtils.copyProperties(editorPaymentChannelDto, paymentChannel);
            paymentChannel.setUpdateTime(editorPaymentChannelDto.getUpdateTime());
            paymentChannel.setUpdateBy(editorPaymentChannelDto.getAdminName());

            boolean isUpdate = super.updateById(paymentChannel);
            if (!isUpdate) {
                log.error("[三方管理] 更新三方異常, id:{}", id);
                throw new ApiException(CommonCode.FAILED);
            }

            if (CollectionUtil.isNotEmpty(editorPaymentChannelDto.getPaymentDynamicColumns())) {
                List<PaymentDynamicColumn> paymentDynamicColumnList = BeanUtil.copyToList(editorPaymentChannelDto.getPaymentDynamicColumns(), PaymentDynamicColumn.class);
                // delete此次更新沒有選擇的payment_dynamic_column (這邊要相反過來先做delete在做save/update, 因mybatis不像jpa做save可以直接取得當前uid)
                List<Long> paymentDynamicColumnIds = paymentDynamicColumnList.stream().map(PaymentDynamicColumn::getId)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(paymentDynamicColumnIds)) {
                    paymentDynamicColumnService.deleteByNotInIdsAndPaymentChannelId(paymentDynamicColumnIds, paymentChannel.getId());
                }

                // save or update payment_dynamic_column
                paymentDynamicColumnList.forEach(s -> {
                    if (s.getId() == null) {
                        s.setId(IdWorker.getId());
                        s.setCreateTime(editorPaymentChannelDto.getUpdateTime());
                        s.setCreateBy(editorPaymentChannelDto.getAdminName());
                    }
                    s.setPaymentChannelId(paymentChannel.getId());
                    s.setUpdateTime(editorPaymentChannelDto.getUpdateTime());
                    s.setUpdateBy(editorPaymentChannelDto.getAdminName());
                });
                paymentDynamicColumnService.saveOrUpdateBatch(paymentDynamicColumnList);
            }

            if (CollectionUtil.isNotEmpty(editorPaymentChannelDto.getPaymentChannelBanks())) {
                List<PaymentChannelBank> paymentChannelBankList = BeanUtil.copyToList(editorPaymentChannelDto.getPaymentChannelBanks(), PaymentChannelBank.class);
                // delete此次更新沒有選擇的payment_channel_bank (這邊要相反過來先做delete在做save/update, 因mybatis不像jpa做save可以直接取得當前uid)
                List<Long> paymentChannelBankIds = paymentChannelBankList.stream().map(PaymentChannelBank::getId)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(paymentChannelBankIds)) {
                    paymentChannelBankService.deleteByNotInIdAndPaymentChannelId(paymentChannelBankIds, paymentChannel.getId());
                }

                // save or update payment_channel_bank
                paymentChannelBankList.forEach(s -> {
                    if (s.getId() == null) {
                        s.setId(IdWorker.getId());
                        s.setCreateTime(editorPaymentChannelDto.getUpdateTime());
                        s.setCreateBy(editorPaymentChannelDto.getAdminName());
                    }
                    s.setPaymentChannelId(paymentChannel.getId());
                    s.setUpdateTime(editorPaymentChannelDto.getUpdateTime());
                    s.setUpdateBy(editorPaymentChannelDto.getAdminName());
                });
                paymentChannelBankService.saveOrUpdateBatch(paymentChannelBankList);
            }
        } catch (Exception e) {
            log.error("[三方管理] 更新三方異常", e);
            throw new ApiException(CommonCode.FAILED);
        }

        return id;
    }

    /**
     * 刪除三方
     *
     * @param id paymentChannel id
     * @return uid
     */
    @Override
    @Transactional
    @LogRecord(bizNo = "", type = LogTypeConstants.PAYMENT_CHANNEL, subType = LogSubTypeConstants.DELETE,
            success = "三方金流, 编号:{{#id}}, 名称:{{#channelName}}, 币种:{{#currencyEnum}}")
    public Long deleteData(Long id) {
        PaymentChannel paymentChannel = super.getById(id);
        if (paymentChannel == null) {
            throw new ApiException(CommonCode.NO_DATA);
        }

        try {
            boolean isDelete = super.removeById(id);
            if (!isDelete) {
                log.error("[三方管理] 刪除三方異常, id:{}", id);
                throw new ApiException(CommonCode.FAILED);
            }

            paymentChannelBankService.deleteByPaymentChannelId(id);
            paymentDynamicColumnService.deleteByPaymentChannelId(id);
        } catch (Exception e) {
            log.error("[三方管理] 刪除三方異常", e);
            throw new ApiException(CommonCode.FAILED);
        }

        LogRecordContext.putVariable("channelName", paymentChannel.getName());
        LogRecordContext.putVariable("currencyEnum", paymentChannel.getCurrencyEnum());
        return id;
    }

}
