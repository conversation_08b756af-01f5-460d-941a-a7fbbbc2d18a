package com.wd.lottery.module.payment.handler;

import com.wd.lottery.module.payment.dto.payment.*;
import com.wd.lottery.module.payment.dto.payment.*;

import java.util.Optional;

public interface PaymentWithdrawHandler extends PaymentBaseHandler {

    /**
     * 代收回調訂單參數
     *
     * @return map key
     */
    String withdrawNotifyOrderKey();

    /**
     * 代付下單參數組成, call api, 解析response
     *
     * @return dto
     */
    WithdrawResultDTO generateWithdrawRequest(DoWithdrawDTO doWithdrawDto, WithdrawResultDTO resultDto) throws Exception;

    /**
     * 處理代付回調API回傳結果
     *
     * @return WithdrawResponse dto
     */
    WithdrawPayDTO handleWithdrawNotify(DoPaymentNotifyDTO doWithdrawNotifyDto) throws Exception;

    /**
     * 輪詢出款訂單狀態
     * 呼叫三方查詢API，查詢出款訂單的狀態
     * @return 輪詢結果
     */
    OnlineOrderPollingResultDTO pollingWithdrawOrder(DoPaymentPollingDTO doPaymentPollingDTO) throws Exception;

    default String parseWithdrawOrderNo(DoPaymentNotifyDTO notifyDTO) {
        return Optional.ofNullable(notifyDTO)
                .map(DoPaymentNotifyDTO::getRequestMap)
                .map(requestMap -> requestMap.get(this.withdrawNotifyOrderKey()))
                .orElse("");
    }

}
