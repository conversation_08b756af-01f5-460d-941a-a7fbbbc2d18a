package com.wd.lottery.module.payment.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.exception.ApiException;
import com.wd.lottery.module.cash.constatns.PlatformEnum;
import com.wd.lottery.module.cash.constatns.PlatformTypeEnum;
import com.wd.lottery.module.common.constants.CommonConstants;
import com.wd.lottery.module.merchant.dto.MerchantConfigDepositPlatformDTO;
import com.wd.lottery.module.merchant.dto.MerchantConfigDepositPlatformPaymentMerchantDTO;
import com.wd.lottery.module.merchant.dto.MerchantConfigDepositPlatformPaymentMerchantOfflineDTO;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.payment.dto.payment.DepositChannelDTO;
import com.wd.lottery.module.payment.dto.payment.OfflineDepositChannelDTO;
import com.wd.lottery.module.payment.dto.payment.OnlineDepositChannelDTO;
import com.wd.lottery.module.payment.dto.paymentmerchant.WithdrawPaymentMerchantDTO;
import com.wd.lottery.module.payment.entity.PaymentChannel;
import com.wd.lottery.module.payment.entity.PaymentChannelBank;
import com.wd.lottery.module.payment.entity.PaymentMerchant;
import com.wd.lottery.module.payment.entity.PaymentMerchantOffline;
import com.wd.lottery.module.payment.service.*;
import com.wd.lottery.module.payment.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 三方管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DepositWithdrawChannelServiceImpl implements DepositWithdrawChannelService {

    private final PaymentChannelService paymentChannelService;

    private final PaymentChannelBankService paymentChannelBankService;

    private final PaymentMerchantService paymentMerchantService;

    private final MerchantConfigService merchantConfigService;

    private final PaymentMerchantOfflineService paymentMerchantOfflineService;

    /**
     * c端查詢當前用戶可使用的充值渠道
     *
     * @param merchantId   merchant uid
     * @param currencyEnum currency enum
     * @return list dto
     */
    @Override
    public List<DepositChannelDTO> setDepositChannel(Long merchantId, CurrencyEnum currencyEnum) {
        List<DepositChannelDTO> depositChannelDtoList = new ArrayList<>();
        MerchantConfig merchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonConstants.COMMON_DICT_KEY_DEPOSIT_PLATFORM, currencyEnum);
        if (merchantConfig == null) {
            log.error("[充值渠道] 當前商戶無配置充值渠道, 商戶uid:{}, currencyEnum:{}", merchantId, currencyEnum.name());
            return depositChannelDtoList;
        }

        List<MerchantConfigDepositPlatformDTO> merchantConfigDepositPlatformDtoList = merchantConfig.merchantConfigToList(MerchantConfigDepositPlatformDTO.class);
        if (CollectionUtil.isEmpty(merchantConfigDepositPlatformDtoList)) {
            log.error("[充值渠道] 當前商戶配置充值渠道資料數為0, 商戶uid:{}", merchantId);
            return depositChannelDtoList;
        }

        try {
            List<PaymentChannel> paymentChannelList = paymentChannelService.findByEnableEnum(EnableEnum.TRUE);
            Map<Long, PaymentChannel> paymentChannelMap = paymentChannelList.stream()
                    .filter(paymentChannel -> paymentChannel.getDepositEnableEnum() == EnableEnum.TRUE)
                    .collect(Collectors.toMap(PaymentChannel::getId, Function.identity()));

            // 解析線上配置的匯率及排序
            Map<String, Map<Long, MerchantConfigDepositPlatformPaymentMerchantDTO>> onlineMap = merchantConfigDepositPlatformDtoList.stream()
                    .filter(dto -> dto.getPlatformTypeEnum() == PlatformTypeEnum.ONLINE)
                    .collect(Collectors.toMap(
                            MerchantConfigDepositPlatformDTO::getPayName,
                            dto -> dto.getPaymentMerchant().stream().collect(Collectors.toMap(
                                    MerchantConfigDepositPlatformPaymentMerchantDTO::getPaymentMerchantId,
                                    paymentMerchant -> paymentMerchant
                            ))
                    ));

            // 解析線上所有配置的payment_merchant uid
            List<PaymentMerchant> paymentMerchantList = new ArrayList<>();
            List<Long> paymentMerchantIds = merchantConfigDepositPlatformDtoList.stream()
                    .flatMap(platformDto -> platformDto.getPaymentMerchant().stream())
                    .map(MerchantConfigDepositPlatformPaymentMerchantDTO::getPaymentMerchantId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(paymentMerchantIds)) {
                paymentMerchantList = paymentMerchantService.findListByIdsAndMerchantId(paymentMerchantIds, merchantId, Collections.singletonList(EnableEnum.TRUE));
                paymentMerchantList = paymentMerchantList.stream()
                        .filter(paymentMerchant ->
                                paymentMerchant.getDailyDepositMoney() < paymentMerchant.getDailyDepositDeactivateMoney() &&
                                paymentMerchant.getDailyDepositCount() < paymentMerchant.getDailyDepositDeactivateCount() &&
                                paymentMerchant.isDepositEnabled()
                        )
                        .collect(Collectors.toList());
            }

            // 解析線下配置的匯率及排序
            Map<String, Map<Long, MerchantConfigDepositPlatformPaymentMerchantOfflineDTO>> offlineMap = merchantConfigDepositPlatformDtoList.stream()
                    .filter(dto -> dto.getPlatformTypeEnum() == PlatformTypeEnum.OFFLINE)
                    .collect(Collectors.toMap(
                            MerchantConfigDepositPlatformDTO::getPayName,
                            dto -> dto.getPaymentMerchantOffline().stream().collect(Collectors.toMap(
                                    MerchantConfigDepositPlatformPaymentMerchantOfflineDTO::getPaymentMerchantOfflineId,
                                    paymentMerchantOffline -> paymentMerchantOffline
                            ))
                    ));

            // 解析線下所有配置的payment_merchant_offline uid
            List<PaymentMerchantOffline> paymentMerchantOfflineList = new ArrayList<>();
            List<Long> paymentMerchantOfflineIds = merchantConfigDepositPlatformDtoList.stream()
                    .flatMap(platformDto -> platformDto.getPaymentMerchantOffline().stream())
                    .map(MerchantConfigDepositPlatformPaymentMerchantOfflineDTO::getPaymentMerchantOfflineId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(paymentMerchantOfflineIds)) {
                paymentMerchantOfflineList = paymentMerchantOfflineService.findByIdsAndMerchantIdAndEnable(paymentMerchantOfflineIds, merchantId, EnableEnum.TRUE, currencyEnum);
                paymentMerchantOfflineList = paymentMerchantOfflineList.stream().filter(paymentMerchantOffline ->
                        paymentMerchantOffline.getDailyDepositMoney() < paymentMerchantOffline.getDailyDepositDeactivateMoney() &&
                                paymentMerchantOffline.getDailyDepositCount() < paymentMerchantOffline.getDailyDepositDeactivateCount()).collect(Collectors.toList());
                ;
            }

            if (CollectionUtil.isEmpty(paymentMerchantList) && CollectionUtil.isEmpty(paymentMerchantOfflineList)) {
                log.error("[充值渠道] 商戶當前無啟用中的三方及線下充值渠道, 商戶uid:{}", merchantId);
                return depositChannelDtoList;
            }

            // 解析線上資料(payment_merchant)
            Map<Long, PaymentMerchant> paymentMerchantMap;
            if (CollectionUtil.isNotEmpty(paymentMerchantList)) {
                paymentMerchantMap = paymentMerchantList.stream().collect(Collectors.toMap(PaymentMerchant::getId, Function.identity()));
            } else {
                paymentMerchantMap = new HashMap<>();
            }

            // 解析線下資料(payment_merchant_offline)
            Map<Long, PaymentMerchantOffline> paymentMerchantOfflineMap;
            if (CollectionUtil.isNotEmpty(paymentMerchantOfflineList)) {
                paymentMerchantOfflineMap = paymentMerchantOfflineList.stream().collect(Collectors.toMap(PaymentMerchantOffline::getId, Function.identity()));
            } else {
                paymentMerchantOfflineMap = new HashMap<>();
            }
            merchantConfigDepositPlatformDtoList.forEach(merchantConfigDepositPlatformDto -> {
                DepositChannelDTO depositChannelDto = new DepositChannelDTO();

                String channelName = merchantConfigDepositPlatformDto.getPayName();
                depositChannelDto.setPayName(channelName);

                PlatformTypeEnum platformTypeEnum = merchantConfigDepositPlatformDto.getPlatformTypeEnum();
                depositChannelDto.setPlayTypeEnum(platformTypeEnum);

                PlatformEnum platformEnum = merchantConfigDepositPlatformDto.getPlatformEnum();
                depositChannelDto.setPlatformEnum(platformEnum);

                Integer sort = merchantConfigDepositPlatformDto.getSort();
                depositChannelDto.setSort(sort);

                String image = merchantConfigDepositPlatformDto.getImg();
                depositChannelDto.setImage(image);

                // 封裝線上充值渠道
                if (depositChannelDto.getPlayTypeEnum() == PlatformTypeEnum.ONLINE && CollectionUtil.isNotEmpty(paymentMerchantMap)) {
                    String payName = depositChannelDto.getPayName();
                    Map<Long, MerchantConfigDepositPlatformPaymentMerchantDTO> channelMapping = onlineMap.get(payName);

                    List<OnlineDepositChannelDTO> onlineDepositChannelDtoList = merchantConfigDepositPlatformDto.getPaymentMerchant()
                            .stream()
                            .map(dto -> paymentMerchantMap.get(dto.getPaymentMerchantId()))
                            .filter(Objects::nonNull)
                            .filter(merchant -> paymentChannelMap.containsKey(merchant.getPaymentChannelId()))
                            .map(merchant -> {
                                OnlineDepositChannelDTO channelDto = BeanUtil.copyProperties(merchant, OnlineDepositChannelDTO.class);

                                Optional.ofNullable(channelMapping)
                                        .map(mapping -> mapping.get(channelDto.getId()))
                                        .ifPresent(mappedChannel -> {
                                            channelDto.setRecommendEnum(
                                                    Objects.nonNull(mappedChannel.getRecommendEnum())
                                                            ? mappedChannel.getRecommendEnum()
                                                            : BooleanEnum.FALSE
                                            );
                                            channelDto.setSort(mappedChannel.getSort());
                                            channelDto.setExchangeRate(mappedChannel.getExchangeRate());
                                        });

                                return channelDto;
                            })
                            .collect(Collectors.toList());

                    depositChannelDto.setOnlineDepositChannelList(onlineDepositChannelDtoList);
                }

                // 封裝線下充值渠道
                if (depositChannelDto.getPlayTypeEnum() == PlatformTypeEnum.OFFLINE && CollectionUtil.isNotEmpty(paymentMerchantOfflineMap)) {
                    List<OfflineDepositChannelDTO> offlineDepositChannelDtoList = new ArrayList<>();

                    List<MerchantConfigDepositPlatformPaymentMerchantOfflineDTO> merchantConfigDepositPlatformPaymentMerchantOfflineDtoList = merchantConfigDepositPlatformDto.getPaymentMerchantOffline();
                    merchantConfigDepositPlatformPaymentMerchantOfflineDtoList.forEach(merchantConfigDepositPlatformPaymentMerchantOfflineDto -> {
                        PaymentMerchantOffline paymentMerchantOffline = paymentMerchantOfflineMap.get(merchantConfigDepositPlatformPaymentMerchantOfflineDto.getPaymentMerchantOfflineId());
                        if (paymentMerchantOffline != null) {
                            OfflineDepositChannelDTO offlineDepositChannelDto = BeanUtil.copyProperties(paymentMerchantOffline, OfflineDepositChannelDTO.class);
                            if (offlineMap.get(depositChannelDto.getPayName()) != null && offlineMap.get(depositChannelDto.getPayName()).get(offlineDepositChannelDto.getId()) != null) {
                                offlineDepositChannelDto.setSort(offlineMap.get(depositChannelDto.getPayName()).get(offlineDepositChannelDto.getId()).getSort());
                                offlineDepositChannelDto.setExchangeRate(offlineMap.get(depositChannelDto.getPayName()).get(offlineDepositChannelDto.getId()).getExchangeRate());
                                offlineDepositChannelDto.setRecommendEnum(Objects.nonNull(offlineMap.get(depositChannelDto.getPayName()).get(offlineDepositChannelDto.getId()).getRecommendEnum())
                                        ? offlineMap.get(depositChannelDto.getPayName()).get(offlineDepositChannelDto.getId()).getRecommendEnum() : BooleanEnum.FALSE);
                            }
                            offlineDepositChannelDtoList.add(offlineDepositChannelDto);
                        }
                    });

                    depositChannelDto.setOfflineDepositChannelList(offlineDepositChannelDtoList);
                }

                depositChannelDtoList.add(depositChannelDto);
            });

            return depositChannelDtoList;
        } catch (Exception e) {
            log.error("[充值渠道] 解析配置異常, 商戶uid:{}", merchantId, e);
            throw new ApiException(CommonCode.FAILED);
        }
    }

    @Override
    public List<WithdrawPaymentMerchantDTO> setWithdrawChannel(Long merchantId, CurrencyEnum currencyEnum) {
        List<WithdrawPaymentMerchantDTO> withdrawPaymentMerchantDtoList = new ArrayList<>();
        List<PaymentChannel> paymentChannelList = paymentChannelService.findByEnableEnumAndCurrencyEnum(Collections.singletonList(EnableEnum.TRUE), currencyEnum);

        List<PaymentMerchant> paymentMerchantList;
        if (CollectionUtil.isNotEmpty(paymentChannelList)) {
            List<Long> paymentChannelIdList = paymentChannelList.stream()
                    .filter(s -> s.getWithdrawEnableEnum() == EnableEnum.TRUE)
                    .map(PaymentChannel::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(paymentChannelIdList)) {
                return withdrawPaymentMerchantDtoList;
            }

            paymentMerchantList = paymentMerchantService.findListInChannelIdAndMerchantIdInEnableEnum(paymentChannelIdList, merchantId, Collections.singletonList(EnableEnum.TRUE));
            paymentMerchantList.stream().filter(PaymentMerchant::isWithdrawalEnabled)
                    .forEach(paymentMerchant -> {
                WithdrawPaymentMerchantDTO withdrawPaymentMerchantDto = new WithdrawPaymentMerchantDTO();
                withdrawPaymentMerchantDto.setPaymentChannelId(paymentMerchant.getPaymentChannelId());
                withdrawPaymentMerchantDto.setPaymentMerchantId(paymentMerchant.getId());
                withdrawPaymentMerchantDto.setPayName(paymentMerchant.getChannelName());
                withdrawPaymentMerchantDto.setBalance(paymentMerchant.getBalance());
                withdrawPaymentMerchantDtoList.add(withdrawPaymentMerchantDto);
            });
        }
        return withdrawPaymentMerchantDtoList;
    }

    @Override
    public List<Long> getSupportedBankIdList(Long paymentChannelId) {
        return paymentChannelBankService.findAllByPaymentChannelId(paymentChannelId)
                .stream().map(PaymentChannelBank::getPaymentBankId)
                .collect(Collectors.toList());
    }

}
