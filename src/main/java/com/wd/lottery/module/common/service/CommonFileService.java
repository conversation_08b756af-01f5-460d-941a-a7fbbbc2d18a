package com.wd.lottery.module.common.service;

import java.io.InputStream;

public interface CommonFileService {

    /**
     * 文件上传
     * @param merchantId 商户 ID
     * @param inputStream 文件流
     * @param module 模块
     * @param isTemporary 是否临时文件
     * @return 文件路径
     */
    String upload(Long merchantId, InputStream inputStream, String module, String filename, boolean isTemporary);

    /**
     * 文件下载
     * @param merchantId 商户 ID
     * @param module 模块
     * @param filename 文件名
     * @return 文件流
     */
    InputStream download(Long merchantId, String module, String filename);

}
