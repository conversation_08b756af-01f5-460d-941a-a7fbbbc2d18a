package com.wd.lottery.module.common.constants;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Schema(description = "游戏类别", example = "SLOT")
public enum GameCategoryEnum {
    /**
     * 游戏大类
     */
    THREE(0, "3D"),
    LOTTERY(1, "Lottery"),
    SLOT(2, "Slot"),
    FISH(3, "Fish"),
    CASINO(4, "Casino"),
    P<PERSON>ER(5, "Poker"),
    MINI(6, "Mini game"),
    SPORT(7, "Sport"),
    E_SPORT(8, "E-Sport"),
    COCK(9, "Cock")
    ;

    @EnumValue
    private final Integer code;
    private final String desc;

    GameCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<String> getNameList(){
        return Arrays.stream(GameCategoryEnum.values()).map(Enum::name).collect(Collectors.toList());
    }


    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
