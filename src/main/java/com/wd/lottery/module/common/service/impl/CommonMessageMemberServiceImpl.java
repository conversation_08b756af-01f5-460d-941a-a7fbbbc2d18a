package com.wd.lottery.module.common.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wd.lottery.GrapeApplication;
import com.wd.lottery.common.annotation.MasterOnly;
import com.wd.lottery.common.api.BaseParam;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.module.common.constants.*;
import com.wd.lottery.module.common.constants.CommonRedisConstants;
import com.wd.lottery.module.common.constants.MessageGroupTypeEnum;
import com.wd.lottery.module.common.constants.MessageTypeEnum;
import com.wd.lottery.module.common.entity.CommonMessage;
import com.wd.lottery.module.common.entity.CommonMessageMember;
import com.wd.lottery.module.common.mapper.CommonMessageMemberMapper;
import com.wd.lottery.module.common.param.CommonMemberMessageSendParam;
import com.wd.lottery.module.common.service.CommonMessageMemberService;
import com.wd.lottery.module.common.service.CommonMessageService;
import com.wd.lottery.module.member.dto.MemberTokenInfoDTO;
import com.wd.lottery.module.member.entity.Member;
import com.wd.lottery.module.member.service.MemberService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommonMessageMemberServiceImpl extends ServiceImpl<CommonMessageMemberMapper, CommonMessageMember> implements CommonMessageMemberService {
    @Resource
    private CommonMessageService commonMessageService;

    @Resource
    private RedisService redisService;

    @Resource
    private MemberService memberService;

    @Override
    public List<CommonMessage> cGetList(MemberTokenInfoDTO memberTokenInfoDTO, BaseParam baseParam) {
        Assert.notNull(memberTokenInfoDTO);
        Assert.notNull(baseParam);
        List<CommonMessage> commonMessages = new ArrayList<>();
        Page<CommonMessageMember> page = super.lambdaQuery()
                .select(CommonMessageMember::getMessageId, CommonMessageMember::getId,CommonMessageMember::getReadEnum)
                .eq(CommonMessageMember::getMerchantId, memberTokenInfoDTO.getMerchantId())
                .eq(CommonMessageMember::getMemberId, memberTokenInfoDTO.getId())
                .eq(CommonMessageMember::getDelEnum, BooleanEnum.FALSE)
                .orderByDesc(CommonMessageMember::getCreateTime)
                .page(Page.of(baseParam.getCurrent(), baseParam.getSize(), false));
        List<CommonMessageMember> commonMessageMemberList = page.getRecords();
        if (CollUtil.isEmpty(commonMessageMemberList)) {
            return commonMessages;
        }
        Map<Long, Long> messageIdAndMemberMessageIdMap = commonMessageMemberList.stream().collect(Collectors.toMap(CommonMessageMember::getMessageId, CommonMessageMember::getId, (k1, k2) -> k1));
        List<CommonMessage> commonMessageList = commonMessageService.lambdaQuery()
                .select(CommonMessage::getId, CommonMessage::getTitle, CommonMessage::getContent, CommonMessage::getCreateTime)
                .in(CommonMessage::getId, messageIdAndMemberMessageIdMap.keySet())
                .list();
        commonMessageList.forEach(commonMessage -> commonMessage.setId(messageIdAndMemberMessageIdMap.get(commonMessage.getId())));
        Map<Long, CommonMessage> commonMessageMap = commonMessageList.stream().collect(Collectors.toMap(CommonMessage::getId, Function.identity()));
        for (CommonMessageMember commonMessageMember : commonMessageMemberList) {
            if (Objects.nonNull(commonMessageMap.get(commonMessageMember.getId()))) {
                commonMessageMap.get(commonMessageMember.getId()).setReadEnum(commonMessageMember.getReadEnum());
                commonMessages.add(commonMessageMap.get(commonMessageMember.getId()));
            }
        }

        return commonMessages;
    }

    @Override
    @MasterOnly
    public boolean existUnread(MemberTokenInfoDTO memberTokenInfoDTO) {
        String cacheKey = String.format(CommonRedisConstants.MESSAGE_MEMBER_EXIST_UNREAD, memberTokenInfoDTO.getId());
        Object exist = redisService.get(cacheKey);
        if (exist != null) {
            return (boolean) exist;
        }
        initMemberLazyMessage(memberTokenInfoDTO);
        boolean exists = super.lambdaQuery()
                .eq(CommonMessageMember::getMerchantId, memberTokenInfoDTO.getMerchantId())
                .eq(CommonMessageMember::getMemberId, memberTokenInfoDTO.getId())
                .eq(CommonMessageMember::getReadEnum, BooleanEnum.FALSE)
                .eq(CommonMessageMember::getDelEnum, BooleanEnum.FALSE)
                .last(Constants.SQL_LIMIT_1)
                .exists();
        redisService.set(cacheKey, exists, CommonRedisConstants.MESSAGE_MEMBER_EXIST_UNREAD_EXPIRE_SECONDS, TimeUnit.SECONDS);
        return exists;
    }

    @Override
    public void setAllRead(MemberTokenInfoDTO memberTokenInfoDTO) {
        super.lambdaUpdate()
                .set(CommonMessageMember::getReadEnum, BooleanEnum.TRUE)
                .eq(CommonMessageMember::getMerchantId, memberTokenInfoDTO.getMerchantId())
                .eq(CommonMessageMember::getMemberId, memberTokenInfoDTO.getId())
                .eq(CommonMessageMember::getReadEnum, BooleanEnum.FALSE)
                .update();
        String cacheKey = String.format(CommonRedisConstants.MESSAGE_MEMBER_EXIST_UNREAD, memberTokenInfoDTO.getId());
        redisService.set(cacheKey, false, CommonRedisConstants.MESSAGE_MEMBER_EXIST_UNREAD_EXPIRE_SECONDS, TimeUnit.SECONDS);
    }

    @Override
    public void setRead(MemberTokenInfoDTO memberTokenInfo, Long id) {
        super.lambdaUpdate()
                .set(CommonMessageMember::getReadEnum, BooleanEnum.TRUE)
                .eq(CommonMessageMember::getMerchantId, memberTokenInfo.getMerchantId())
                .eq(CommonMessageMember::getMemberId, memberTokenInfo.getId())
                .eq(CommonMessageMember::getId, id)
                .eq(CommonMessageMember::getReadEnum, BooleanEnum.FALSE)
                .update();
    }

    @Override
    public void deleteByReadEnum(MemberTokenInfoDTO memberTokenInfo, BooleanEnum readEnum) {
        super.lambdaUpdate()
                .set(CommonMessageMember::getDelEnum, BooleanEnum.TRUE)
                .eq(CommonMessageMember::getMerchantId, memberTokenInfo.getMerchantId())
                .eq(CommonMessageMember::getMemberId, memberTokenInfo.getId())
                .eq(Objects.nonNull(readEnum),CommonMessageMember::getReadEnum,readEnum)
                .update()
        ;
    }

    @Override
    public void deleteById(MemberTokenInfoDTO memberTokenInfoDTO, Long id) {
        super.lambdaUpdate()
                .set(CommonMessageMember::getDelEnum, BooleanEnum.TRUE)
                .eq(CommonMessageMember::getMerchantId, memberTokenInfoDTO.getMerchantId())
                .eq(CommonMessageMember::getMemberId, memberTokenInfoDTO.getId())
                .eq(CommonMessageMember::getId, id)
                .update()
        ;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void send(CommonMemberMessageSendParam commonMemberMessageSendParam) {
        checkParam(commonMemberMessageSendParam);
        CommonMessage commonMessage = BeanUtil.copyProperties(commonMemberMessageSendParam, CommonMessage.class);
        commonMessage.setCreateTime(LocalDateTime.now());
        commonMessage.setMessageTypeEnum(MessageTypeEnum.MEMBER_MESSAGE);
        commonMessageService.save(commonMessage);
        trySaveWhenSingleType(commonMemberMessageSendParam, commonMessage);
    }

    @Async
    @Override
    public void sendRegisterMessageByMember(Member member) {
        Assert.notNull(member);
        Assert.notNull(member.getId(), "member.getId() is null");
        Assert.notNull(member.getMerchantId(), "member.getMerchantId() is null");
        Assert.notNull(member.getCurrencyEnum(), "member.getMerchantId() is null");
        CommonMessage registerTemplateMessage = commonMessageService.getRegisterTemplateMessage(member.getMerchantId(), member.getCurrencyEnum());
        if (registerTemplateMessage == null) {
            return;
        }
        CommonMessageMemberService commonMessageMemberService = GrapeApplication.getBean(CommonMessageMemberService.class);

        CommonMemberMessageSendParam commonMemberMessageSendParam = BeanUtil.copyProperties(registerTemplateMessage, CommonMemberMessageSendParam.class);
        commonMemberMessageSendParam.setMessageGroupTypeEnum(MessageGroupTypeEnum.SINGLE);
        commonMemberMessageSendParam.setMemberIdList(Collections.singletonList(member.getId()));
        commonMemberMessageSendParam.setNotCheckMemberId(true);
        commonMessageMemberService.send(commonMemberMessageSendParam);
    }

    @Async
    @Override
    public void sendMessageByPaymentOrderCancel(Map<Long, Long> orderNoAndMemberIdMap, String createBy, Long merchantId, CurrencyEnum currencyEnum, String remark, String title) {
        CommonMessageMemberService commonMessageMemberService = GrapeApplication.getBean(CommonMessageMemberService.class);

        orderNoAndMemberIdMap.keySet().forEach(aLong -> {
            CommonMemberMessageSendParam commonMemberMessageSendParam = new CommonMemberMessageSendParam();
            commonMemberMessageSendParam.setCreateBy(createBy);
            commonMemberMessageSendParam.setMerchantId(merchantId);
            commonMemberMessageSendParam.setCurrencyEnum(currencyEnum);
            commonMemberMessageSendParam.setMessageGroupTypeEnum(MessageGroupTypeEnum.SINGLE);
            commonMemberMessageSendParam.setMemberIdList(Collections.singletonList(orderNoAndMemberIdMap.get(aLong)));
            commonMemberMessageSendParam.setNotCheckMemberId(true);
            commonMemberMessageSendParam.setContent(aLong + "<br />" + remark);
            commonMemberMessageSendParam.setTitle(title);
            commonMessageMemberService.send(commonMemberMessageSendParam);
        });
    }

    private void trySaveWhenSingleType(CommonMemberMessageSendParam commonMemberMessageSendParam, CommonMessage commonMessage) {
        if (!MessageGroupTypeEnum.SINGLE.equals(commonMemberMessageSendParam.getMessageGroupTypeEnum())) {
            return;
        }

        List<CommonMessageMember> commonMessageMemberList = new ArrayList<>();
        for (Long memberId : commonMemberMessageSendParam.getMemberIdList()) {
            CommonMessageMember commonMessageMember = assembleSaveMessageMember(memberId, commonMessage);
            commonMessageMemberList.add(commonMessageMember);
        }
        super.saveBatch(commonMessageMemberList);
    }

    private void checkParam(CommonMemberMessageSendParam commonMemberMessageSendParam) {
        Assert.notNull(commonMemberMessageSendParam.getMessageGroupTypeEnum());
        switch (commonMemberMessageSendParam.getMessageGroupTypeEnum()) {
            case SINGLE:
                Assert.notEmpty(commonMemberMessageSendParam.getMemberIdList());
                if (commonMemberMessageSendParam.isNotCheckMemberId()) {
                    break;
                }
                memberService.checkMemberIdList(commonMemberMessageSendParam.getMemberIdList(), commonMemberMessageSendParam.getMerchantId());
                break;
            case GROUP:
                Assert.notBlank(commonMemberMessageSendParam.getMessageGroupDetail());
                Assert.isTrue(NumberUtil.isLong(commonMemberMessageSendParam.getMessageGroupDetail()), "messageGroupTypeEnum GROUP type messageGroupTypeDetail not Long");
                break;
            case VIP:
                Assert.notBlank(commonMemberMessageSendParam.getMessageGroupDetail());
                Assert.isTrue(NumberUtil.isInteger(commonMemberMessageSendParam.getMessageGroupDetail()), "messageGroupTypeEnum GROUP type messageGroupTypeDetail not Integer");
            case ALL:
                break;
        }
    }

    private void initMemberLazyMessage(MemberTokenInfoDTO memberTokenInfoDTO) {
        List<CommonMessage> lazyMessageList = commonMessageService.getLazyMessageList(memberTokenInfoDTO.getMerchantId(), memberTokenInfoDTO.getCurrencyEnum());
        if (CollUtil.isEmpty(lazyMessageList)) {
            return;
        }

        List<CommonMessage> memberLazyMessageList = getLazyMemberMessageList(memberTokenInfoDTO, lazyMessageList);
        if (CollUtil.isEmpty(memberLazyMessageList)) {
            return;
        }

        filterAlreadyReceiveMessage(memberTokenInfoDTO, memberLazyMessageList);
        if (CollUtil.isEmpty(memberLazyMessageList)) {
            return;
        }
        saveMemberLazyMessageList(memberTokenInfoDTO, memberLazyMessageList);
    }

    @NotNull
    private static List<CommonMessage> getLazyMemberMessageList(MemberTokenInfoDTO memberTokenInfoDTO, List<CommonMessage> lazyMessageList) {
        List<CommonMessage> memberLazyMessageList = new ArrayList<>();
        for (CommonMessage commonMessage : lazyMessageList) {
            getOneLazyMemberMessage(memberTokenInfoDTO, commonMessage, memberLazyMessageList);
        }
        return memberLazyMessageList;
    }

    private void filterAlreadyReceiveMessage(MemberTokenInfoDTO memberTokenInfoDTO, List<CommonMessage> memberLazyMessageList) {
        List<Long> memberLazyMessageId = memberLazyMessageList.stream().map(CommonMessage::getId).collect(Collectors.toList());
        List<CommonMessageMember> alreadReceiveMessageMemberIdList = super.lambdaQuery()
                .select(CommonMessageMember::getMessageId)
                .eq(CommonMessageMember::getMerchantId, memberTokenInfoDTO.getMerchantId())
                .eq(CommonMessageMember::getMemberId, memberTokenInfoDTO.getId())
                .in(CommonMessageMember::getMessageId, memberLazyMessageId)
                .list();
        if (CollUtil.isEmpty(alreadReceiveMessageMemberIdList)) {
            return;
        }
        Set<Long> commonMessageMemberIdSet = alreadReceiveMessageMemberIdList.stream().map(CommonMessageMember::getMessageId).collect(Collectors.toSet());
        memberLazyMessageList.removeIf(o -> commonMessageMemberIdSet.contains(o.getId()));
    }

    private void saveMemberLazyMessageList(MemberTokenInfoDTO memberTokenInfoDTO, List<CommonMessage> memberLazyMessageList) {
        List<CommonMessageMember> commonMessageMemberSaveList = assembleSaveMessageMemberList(memberTokenInfoDTO, memberLazyMessageList);
        super.baseMapper.insertIgnoreBatch(commonMessageMemberSaveList);
    }

    @NotNull
    private List<CommonMessageMember> assembleSaveMessageMemberList(MemberTokenInfoDTO memberTokenInfoDTO, List<CommonMessage> memberLazyMessageList) {
        List<CommonMessageMember> commonMessageMemberSaveList = new ArrayList<>();
        for (CommonMessage commonMessage : memberLazyMessageList) {
            CommonMessageMember commonMessageMember = assembleSaveMessageMember(memberTokenInfoDTO.getId(), commonMessage);
            commonMessageMemberSaveList.add(commonMessageMember);
        }
        return commonMessageMemberSaveList;
    }

    private CommonMessageMember assembleSaveMessageMember(Long memberId, CommonMessage commonMessage) {
        CommonMessageMember commonMessageMember = new CommonMessageMember();
        commonMessageMember.setMessageId(commonMessage.getId());
        commonMessageMember.setCurrencyEnum(commonMessage.getCurrencyEnum());
        commonMessageMember.setMerchantId(commonMessage.getMerchantId());
        commonMessageMember.setReadEnum(BooleanEnum.FALSE);
        commonMessageMember.setDelEnum(BooleanEnum.FALSE);
        commonMessageMember.setCreateTime(commonMessage.getCreateTime());
        commonMessageMember.setMemberId(memberId);
        return commonMessageMember;
    }

    private static void getOneLazyMemberMessage(MemberTokenInfoDTO memberTokenInfoDTO, CommonMessage commonMessage, List<CommonMessage> memberLazyMessageList) {
        try {
            Assert.notNull(commonMessage.getMessageGroupTypeEnum());
            Assert.notNull(commonMessage.getCreateTime());
            Assert.notNull(memberTokenInfoDTO.getCreateTime());
            // 发送的消息在注册时间之前的不用发送
            if (commonMessage.getCreateTime().isBefore(memberTokenInfoDTO.getCreateTime())) {
                return;
            }
            switch (commonMessage.getMessageGroupTypeEnum()) {
                case ALL:
                    break;
                case VIP:
                    Assert.notNull(commonMessage.getMessageGroupDetail());
                    if (!Integer.valueOf(commonMessage.getMessageGroupDetail()).equals(memberTokenInfoDTO.getVipLevel())) {
                        return;
                    }
                    break;
                case GROUP:
                    Assert.notNull(commonMessage.getMessageGroupDetail());
                    if (!Long.valueOf(commonMessage.getMessageGroupDetail()).equals(memberTokenInfoDTO.getGroupId())) {
                        return;
                    }
                    break;
                default:
                    throw new IllegalArgumentException("Invalid messageGroupTypeEnum");
            }
            memberLazyMessageList.add(commonMessage);
        } catch (Exception e) {
            log.error("getOneMemberLazyMessage error, commonMessage:{}", commonMessage, e);
        }
    }
}
