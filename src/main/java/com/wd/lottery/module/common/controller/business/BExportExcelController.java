package com.wd.lottery.module.common.controller.business;//package com.wd.lottery.module.common.controlle.business;

import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.module.common.param.ExportExcelQueryParam;
import com.wd.lottery.module.common.service.ExportExcelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "导出记录")
@Slf4j
@RestController
@RequestMapping(value = "${business-path}/${module-path.log}/exportExcel", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class BExportExcelController {

    private final ExportExcelService exportExcelService;

    @Operation(summary = "查詢")
    @GetMapping("/page")
    public ApiResult<?> page(@ParameterObject ExportExcelQueryParam param) {
        param.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        if (!AdminTokenInfoUtil.isAdmin()) {
            param.setCreateBy(AdminTokenInfoUtil.getAdminName());
        }
        return ApiResult.success(exportExcelService.queryUploadFilePage(param));
    }

}