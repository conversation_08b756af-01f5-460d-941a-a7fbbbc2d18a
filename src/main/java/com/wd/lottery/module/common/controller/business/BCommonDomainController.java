package com.wd.lottery.module.common.controller.business;

import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.module.common.dto.*;
import com.wd.lottery.module.common.dto.*;
import com.wd.lottery.module.common.service.CommonDomainService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "域名管理")
@Slf4j
@RestController
@RequestMapping(value = "${business-path}/${module-path.common}/commonDomain", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class BCommonDomainController {

    private final CommonDomainService commonDomainService;

    @Operation(summary = "查詢")
    @GetMapping("/search")
    public ApiResult<?> search(@ParameterObject CommonDomainSearchDTO commonDomainSearchDto) {
        if (commonDomainSearchDto.getMerchantId() == null) {
            commonDomainSearchDto.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        }
        return ApiResult.success(commonDomainService.search(commonDomainSearchDto));
    }

    @Operation(summary = "新增")
    @PostMapping("/insert")
    public ApiResult<?> insert(@RequestBody @Valid CommonDomainInsertDTO commonDomainInsertDto) {
        commonDomainInsertDto.setUpdateTime(LocalDateTime.now());
        commonDomainInsertDto.setAdminName(AdminTokenInfoUtil.getAdminName());
        return ApiResult.success(commonDomainService.insert(commonDomainInsertDto));
    }

    @Operation(summary = "批次新增")
    @PostMapping("/batchInsert")
    public ApiResult<?> batchInsert(@RequestBody @Valid List<CommonDomainInsertDTO> commonDomainInsertDtoList) {
        return ApiResult.success(commonDomainService.batchInsert(commonDomainInsertDtoList, AdminTokenInfoUtil.getAdminName(), LocalDateTime.now()));
    }

    @Operation(summary = "更新")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody @Valid CommonDomainUpdateDTO commonDomainUpdateDto) {
        commonDomainUpdateDto.setUpdateTime(LocalDateTime.now());
        commonDomainUpdateDto.setAdminName(AdminTokenInfoUtil.getAdminName());
        return ApiResult.success(commonDomainService.update(commonDomainUpdateDto));
    }

    @Operation(summary = "刪除")
    @PostMapping("/delete")
    public ApiResult<?> update(@RequestBody @Valid CommonDomainDeleteDTO commonDomainDeleteDto) {
        return ApiResult.success(commonDomainService.delete(commonDomainDeleteDto.getIds()));
    }

    @Operation(summary = "啟/停用")
    @PostMapping("/enable")
    public ApiResult<?> enable(@RequestBody @Valid CommonDomainEnableDTO commonDomainEnableDto) {
        commonDomainEnableDto.setUpdateTime(LocalDateTime.now());
        commonDomainEnableDto.setAdminName(AdminTokenInfoUtil.getAdminName());
        return ApiResult.success(commonDomainService.enable(commonDomainEnableDto));
    }

}