package com.wd.lottery.module.common.config;

import com.wd.lottery.common.api.CommonCode;
import com.wd.lottery.common.exception.ApiException;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@RefreshScope
@Data
public class ExportExcelConfig {

    // 导出excel文件的最大行数
    @Value("${export-excel.splitFileRowSize:500000}")
    private Long splitFileRowSize;
    // 单次导出excel文件的最大文件数
    @Value("${export-excel.onceExportMaxFileMaxNumber:10}")
    private Long onceExportMaxFileSize;
    // 导出excel文件的最大线程数
    @Value("${export-excel.exportFileThreadSize:10}")
    private Long exportFileThreadSize;

    @Value("${export-excel.limit:10000}")
    private Integer limit;

    /**
     * 导出excel文件的线程池
     * @return
     */
    @Bean("exportExcelTaskExecutor")
    public ThreadPoolTaskExecutor exportExcelTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);//线程池维护线程的最小数量
        executor.setQueueCapacity(0);//缓冲队列
        executor.setThreadNamePrefix("export-excel-thread");
        executor.setVirtualThreads(true);
        // 线程池对拒绝任务的处理策略
        executor.setRejectedExecutionHandler(new RejectedExecutionHandler() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                throw new ApiException(CommonCode.EXCEL_UPLOAD_FAIL, "导出excel文件的线程池已满，请稍后再试");
            }
        });
        executor.initialize();
        return executor;
    }
}
