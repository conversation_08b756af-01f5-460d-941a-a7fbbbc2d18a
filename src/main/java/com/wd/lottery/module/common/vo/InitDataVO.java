package com.wd.lottery.module.common.vo;


import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.payment.dto.payment_bank.PaymentBankSearchDTO;
import com.wd.lottery.module.third.dto.ThirdSiteGameDTO;
import com.wd.lottery.module.third.dto.ThirdSitePlatformItemDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "首頁初始化數據")
public class InitDataVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "时区")
    private String timeZone;
    private String tronBlockUrl;
    @Schema(description = "图片读取服务器地址")
    private String imgReadServerUrl;
    List<NoticeInitDataVO> merchantNoticeList;
    List<ActivityInitDataVO> activityList;
    private List<MerchantConfig> merchantConfigList;
    private List<String> gameCategories;
    private List<ThirdSitePlatformItemDTO> thirdSitePlatformItemList;
    private  List<ThirdSiteGameDTO> hotThirdSiteGameList;
    private List<PaymentBankSearchDTO> paymentBankList;
    private String md5;
    private String layout;

    private String needRotateScreenPortraitPlatformCodeList;
    private String needRotateScreenLandscapePlatformCodeList;
    private String needRotateScreenPortraitGameIdList;
    private String needRotateScreenLandscapeGameIdList;

}
