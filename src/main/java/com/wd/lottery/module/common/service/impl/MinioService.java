package com.wd.lottery.module.common.service.impl;

import com.wd.lottery.module.common.config.MinioConfig;
import com.wd.lottery.module.common.service.CommonFileService;
import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.errors.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.InputStream;



@Slf4j
@RequiredArgsConstructor
@Component
@RefreshScope
public class MinioService implements CommonFileService {

    private final MinioConfig minioConfig;

    private MinioClient minioClient;
    

    @PostConstruct
    public void init() {
        minioClient = MinioClient.builder()
                .endpoint(minioConfig.getEndpoint())
                .credentials(minioConfig.getAccessKey(), minioConfig.getSecretKey())
                .build();
    }

    @Override
    public String upload(Long merchantId, InputStream inputStream, String module, String extension, boolean isTemporary) {
        log.debug("upload file, merchantId:{}, module:{}, extension:{}, isTemporary:{}", merchantId, module, extension, isTemporary);
        try {
            //1. create bucket if not exist
            createBucketIfNotExist(module);
            //2. create tmp file


        } catch (Exception e) {
            throw new IllegalStateException("upload file failed", e);
        }


        return "";
    }

    @Override
    public InputStream download(Long merchantId, String module, String extension) {
        return null;
    }

    private void createBucketIfNotExist(String bucketName) throws Exception {
        try {
            boolean b = minioClient.bucketExists(
                    BucketExistsArgs.builder()
                            .bucket(bucketName)
                            .region(minioConfig.getRegion())
                            .build());
            if (!b) {
                minioClient.makeBucket(MakeBucketArgs.builder()
                        .bucket(bucketName)
                        .region(minioConfig.getRegion())
                        .build());
            }
        }catch (Exception e) {
            log.error("create bucket failed", e);
            throw new IllegalStateException("create bucket failed");
        }
    }
}
