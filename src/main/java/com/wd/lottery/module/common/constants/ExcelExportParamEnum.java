package com.wd.lottery.module.common.constants;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "导出参数枚举")
public enum ExcelExportParamEnum {

    CASH_DEPOSIT("com.wd.lottery.module.cash.mapper.CashDepositOrderMapper.exportCashDepositOrder", "id"),
    CASH_WITHDRAW("com.wd.lottery.module.cash.mapper.CashWithdrawOrderMapper.exportCashWithdrawOrder", "id"),
    CURRENCY_REPORT("com.wd.lottery.module.report.mapper.ThirdCurrencyReportMapper.exportCurrencyReport", "id"),
    MEMBER_REPORT("com.wd.lottery.module.report.mapper.ReportMemberDateMapper.exportMemberReport", "id"),
    SUBORDINATE_MEMBER_REPORT("com.wd.lottery.module.report.mapper.ReportMemberDateMapper.exportSubordinateMemberReport", "id"),
    GAME_REPORT("com.wd.lottery.module.report.mapper.ReportMemberGameDateMapper.exportGameReport", "id"),
    FIRST_WITHDRAW_RECORD_REPORT("com.wd.lottery.module.report.mapper.ReportFirstWithdrawHisMapper.exportFirstWithdrawRecordReport", "id"),
    FIRST_DEPOSIT_RECORD_REPORT("com.wd.lottery.module.report.mapper.ReportFirstDepositHisMapper.exportFirstDepositRecordReport", "id"),
    MEMBER_LIST("com.wd.lottery.module.member.mapper.MemberMapper.exportMember", "id"),
    SUBORDINATE_MEMBER_LIST("com.wd.lottery.module.member.mapper.MemberMapper.exportSubordinateMember", "id"),
    ACTIVITY_REWARD_RECORD("com.wd.lottery.module.activity.mapper.ActivityRecordMapper.exportActivityRewardRecord", "id"),
    COMMISSION_REBATE_RECORD("com.wd.lottery.module.activity.mapper.ActivityTeamProxyBonusDailySummaryMapper.exportCommissionRebateRecord", "id"),
    AA_COMMISSION_REBATE_RECORD("com.wd.lottery.module.activity.mapper.ActivityAATeamProxyBonusDailySummaryMapper.exportAACommissionRebateRecord", "id"),
    ACTIVITY_RANKING_RECORD("com.wd.lottery.module.activity.mapper.ActivityRankingRecordMapper.exportRankingRecord", "id"),
    ACTIVITY_SAFE_RECORD("com.wd.lottery.module.activity.mapper.ActivitySafeAmountRecordMapper.exportActivitySafeAmountRecord", "id"),
    ACTIVITY_INVITE_ROULETTE_RECORD("com.wd.lottery.module.activity.mapper.ActivityInviteRouletteRecordMapper.exportLotteryRecord", "id"),
    THIRD_BET_ORDER_RECORD("com.wd.lottery.module.third.mapper.ThirdBetOrderMapper.exportThirdBetOrderRecord", "id")
    ;
    private final String mapperMethod;
    private final String nextSearchField;

    ExcelExportParamEnum(String mapperMethod, String nextSearchField){
        this.mapperMethod = mapperMethod;
        this.nextSearchField = nextSearchField;
    }

    public String getMapperMethod() {
        return mapperMethod;
    }

    public String getNextSearchField() {
        return nextSearchField;
    }
}
