package com.wd.lottery.module.common.controller.business;//package com.wd.lottery.module.common.controlle.business;

import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.module.common.param.OperationLogQueryParam;
import com.wd.lottery.module.common.service.CommonOperationLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@Tag(name = "操作日誌")
@Slf4j
@RestController
@RequestMapping(value = "${business-path}/${module-path.log}/operation", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class BCommonOperationLogController {

    private final CommonOperationLogService commonOperationLogService;

    @Operation(summary = "查詢操作日志列表，不返回总计，如果返回的数据数量与current一致则表明可能存在下一页")
    @GetMapping("getList")
    public ApiResult<?> getList(@ParameterObject @Valid OperationLogQueryParam operationLogQueryParam) {
        operationLogQueryParam.setMerchantId(AdminTokenInfoUtil.getRequestMerchantIdNotNull());
        operationLogQueryParam.setCurrencyEnum(AdminTokenInfoUtil.getRequestCurrencyEnumNotNull());
        operationLogQueryParam.setAdmin(AdminTokenInfoUtil.isAdmin());
        return ApiResult.success(commonOperationLogService.getList(operationLogQueryParam));
    }

}