package com.wd.lottery.module.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONUtil;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.common.constans.EnableEnum;
import com.wd.lottery.common.service.RedisService;
import com.wd.lottery.common.util.RequestUtil;
import com.wd.lottery.config.ThirdGamePlatformNeedRotateScreenConfig;
import com.wd.lottery.module.activity.service.ActivityService;
import com.wd.lottery.module.common.constants.CommonConstants;
import com.wd.lottery.module.common.constants.CommonRedisConstants;
import com.wd.lottery.module.common.dto.IndexDataDTO;
import com.wd.lottery.module.common.service.AwsS3Service;
import com.wd.lottery.module.common.service.IndexService;
import com.wd.lottery.module.common.vo.IconsVO;
import com.wd.lottery.module.common.vo.InitDataVO;
import com.wd.lottery.module.common.vo.ManifestVO;
import com.wd.lottery.module.common.vo.SafariSpecificVO;
import com.wd.lottery.module.common.constants.GameCategoryEnum;
import com.wd.lottery.module.member.entity.MemberRefer;
import com.wd.lottery.module.member.service.MemberReferService;
import com.wd.lottery.module.member.util.DomainUtil;
import com.wd.lottery.module.merchant.dto.MerchantConfigCopywritingAndroidDownloadLinkDTO;
import com.wd.lottery.module.merchant.dto.MerchantConfigCopywritingIosDownloadLinkDTO;
import com.wd.lottery.module.merchant.entity.Merchant;
import com.wd.lottery.module.merchant.entity.MerchantConfig;
import com.wd.lottery.module.merchant.service.MerchantConfigService;
import com.wd.lottery.module.merchant.service.MerchantNoticeService;
import com.wd.lottery.module.merchant.service.MerchantService;
import com.wd.lottery.module.payment.service.PaymentBankService;
import com.wd.lottery.module.third.service.CThirdPlatformService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IndexServiceImpl implements IndexService {

    @Resource
    private MerchantNoticeService merchantNoticeService;
    @Resource
    private MerchantService merchantService;
    @Resource
    RedisService redisService;
    @Resource(name = Constants.DISCARD_THREAD_POOL)
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    ActivityService activityService;

    @Resource
    private MerchantConfigService merchantConfigService;

    @Resource
    private PaymentBankService paymentBankService;

    @Resource
    private AwsS3Service awsS3Service;
    @Resource
    private CThirdPlatformService cThirdPlatformService;
    @Autowired
    private MemberReferService memberReferService;

    @Resource
    private ThirdGamePlatformNeedRotateScreenConfig thirdGamePlatformNeedRotateScreenConfig;

    @Override
    public InitDataVO getInitData(IndexDataDTO indexDataDTO) {
        if (paramMd5EqualRedisMd5(indexDataDTO)) {
            tryRefreshInitDataAsync(indexDataDTO);
            return new InitDataVO();
        }
        InitDataVO initDataFromRedis = getInitDataFromRedis(indexDataDTO);
        if (initDataFromRedis != null) {
            tryRefreshInitDataAsync(indexDataDTO);
            return initDataFromRedis;
        }
        return getInitDataFromDb(indexDataDTO);
    }

    @Override
    public ManifestVO getManifest(CurrencyEnum currencyEnum, Long merchantId) {
        ManifestVO manifestVo = ManifestVO.builder()
                .manifest_version(2)
                .version("1.2")
                .display("standalone")
                .background_color("#560100")
                .description("Online game")
                .start_url(RequestUtil.getEndpoint()+"/#/index?runtimeEnv=pwa")
                .safari_specific(SafariSpecificVO.builder().safari_extension_version(2).permissions(Arrays.asList("cross-origin-content", "web-page")).build())
                .build();

        Merchant merchant = merchantService.getByIdCache(merchantId);
        if (merchant == null || StringUtils.isBlank(merchant.getCode())) {
            return manifestVo;
        }
        //KG-2130 前端反馈此接口数据直接进行转换的，需要把此处 code 改为商户名
        manifestVo.setName(merchant.getMname());
        manifestVo.setMerchantName(merchant.getMname());
        manifestVo.setReferCode(this.getReferCodeByDomain());
        MerchantConfig androidMerchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonConstants.COPYWRITING_ANDROID_DOWNLOAD_LINK, currencyEnum);
        if (androidMerchantConfig != null) {
            MerchantConfigCopywritingAndroidDownloadLinkDTO merchantConfigCopywritingAndroidDownloadLinkDto = androidMerchantConfig.merchantConfigListGetFirst(MerchantConfigCopywritingAndroidDownloadLinkDTO.class);
            if (ObjectUtil.isNotNull(merchantConfigCopywritingAndroidDownloadLinkDto)) {
                List<IconsVO> iconsList = new ArrayList<>();

                // imgUrl 对应 192x192
                if (StringUtils.isNotBlank(merchantConfigCopywritingAndroidDownloadLinkDto.getImgUrl())) {
                    iconsList.add(IconsVO.builder().src(awsS3Service.getReadDomain() + merchantConfigCopywritingAndroidDownloadLinkDto.getImgUrl()).sizes("192x192").type("image/png").build());
                }

                // imgUrl256 对应 256x256
                if (StringUtils.isNotBlank(merchantConfigCopywritingAndroidDownloadLinkDto.getImgUrl256())) {
                    iconsList.add(IconsVO.builder().src(awsS3Service.getReadDomain() + merchantConfigCopywritingAndroidDownloadLinkDto.getImgUrl256()).sizes("256x256").type("image/png").build());
                }

                // imgUrl384 对应 384x384
                if (StringUtils.isNotBlank(merchantConfigCopywritingAndroidDownloadLinkDto.getImgUrl384())) {
                    iconsList.add(IconsVO.builder().src(awsS3Service.getReadDomain() + merchantConfigCopywritingAndroidDownloadLinkDto.getImgUrl384()).sizes("384x384").type("image/png").build());
                }

                // imgUrl512 对应 512x512
                if (StringUtils.isNotBlank(merchantConfigCopywritingAndroidDownloadLinkDto.getImgUrl512())) {
                    iconsList.add(IconsVO.builder().src(awsS3Service.getReadDomain() + merchantConfigCopywritingAndroidDownloadLinkDto.getImgUrl512()).sizes("512x512").type("image/png").build());
                }

                if (!iconsList.isEmpty()) {
                    manifestVo.setIcons(iconsList);
                }

            }
        }

        MerchantConfig iosMerchantConfig = merchantConfigService.getByMerchantIdAndDictKey(merchantId, CommonConstants.COPYWRITING_IOS_DOWNLOAD_LINK, currencyEnum);
        if (iosMerchantConfig != null) {
            MerchantConfigCopywritingIosDownloadLinkDTO merchantConfigCopywritingIosDownloadLinkDTO = iosMerchantConfig.merchantConfigListGetFirst(MerchantConfigCopywritingIosDownloadLinkDTO.class);
            if (ObjectUtil.isNotNull(merchantConfigCopywritingIosDownloadLinkDTO)) {
                List<IconsVO> iosIconsList = new ArrayList<>();

                // imgUrl 对应 192x192
                if (StringUtils.isNotBlank(merchantConfigCopywritingIosDownloadLinkDTO.getImgUrl())) {
                    iosIconsList.add(IconsVO.builder().src(awsS3Service.getReadDomain() + merchantConfigCopywritingIosDownloadLinkDTO.getImgUrl()).sizes("192x192").type("image/png").build());
                }

                // imgUrl256 对应 256x256
                if (StringUtils.isNotBlank(merchantConfigCopywritingIosDownloadLinkDTO.getImgUrl256())) {
                    iosIconsList.add(IconsVO.builder().src(awsS3Service.getReadDomain() + merchantConfigCopywritingIosDownloadLinkDTO.getImgUrl256()).sizes("256x256").type("image/png").build());
                }

                // imgUrl384 对应 384x384
                if (StringUtils.isNotBlank(merchantConfigCopywritingIosDownloadLinkDTO.getImgUrl384())) {
                    iosIconsList.add(IconsVO.builder().src(awsS3Service.getReadDomain() + merchantConfigCopywritingIosDownloadLinkDTO.getImgUrl384()).sizes("384x384").type("image/png").build());
                }

                // imgUrl512 对应 512x512
                if (StringUtils.isNotBlank(merchantConfigCopywritingIosDownloadLinkDTO.getImgUrl512())) {
                    iosIconsList.add(IconsVO.builder().src(awsS3Service.getReadDomain() + merchantConfigCopywritingIosDownloadLinkDTO.getImgUrl512()).sizes("512x512").type("image/png").build());
                }

                if (!iosIconsList.isEmpty()) {
                    manifestVo.setIosIcons(iosIconsList);
                }

            }
        }

        return manifestVo;
    }

    private InitDataVO getInitDataFromRedis(IndexDataDTO indexDataDTO) {
        return (InitDataVO) redisService.get(getDataKey(indexDataDTO));
    }

    /**
     * 如果请求带md5并且与redis相等则表明数据没变更直接返回
     */
    private boolean paramMd5EqualRedisMd5(IndexDataDTO indexDataDTO) {
        if (StrUtil.isNotBlank(indexDataDTO.getMd5())) {
            String md5Key = getMd5Key(indexDataDTO);
            String md5Value = (String) redisService.get(md5Key);
            return indexDataDTO.getMd5().equals(md5Value);
        }
        return false;
    }

    private InitDataVO getInitDataFromDb(IndexDataDTO indexDataDTO) {
        InitDataVO initDataVO = getInitDataVO(indexDataDTO);
        String dataKey = getDataKey(indexDataDTO);
        String md5SetKey = getMd5Key(indexDataDTO);
        redisService.set(dataKey, initDataVO, CommonRedisConstants.INIT_DATA_DATA_CACHE_TIME, TimeUnit.DAYS);
        redisService.set(md5SetKey, initDataVO.getMd5(), CommonRedisConstants.INIT_DATA_DATA_CACHE_TIME, TimeUnit.DAYS);
        return initDataVO;
    }

    private InitDataVO getInitDataVO(IndexDataDTO indexDataDTO) {
        InitDataVO initDataVO = new InitDataVO();

        Merchant merchant = merchantService.getById(indexDataDTO.getMerchantId());
        if (EnableEnum.FALSE.equals(merchant.getEnableEnum())) {
            return initDataVO;
        }

        initDataVO.setLayout(merchant.getLayout());

        merchantNoticeService.cGetEnableNoticeList(indexDataDTO, initDataVO);

        activityService.getEnableAndStartActivityList(indexDataDTO, initDataVO);

        merchantConfigService.getAllByMerchantIdForCIndex(indexDataDTO, initDataVO);

        initDataVO.setGameCategories(GameCategoryEnum.getNameList());

        cThirdPlatformService.getThirdSitePlatformList(indexDataDTO, initDataVO);

        cThirdPlatformService.getHotThirdSiteGameList(indexDataDTO, initDataVO);

        initDataVO.setPaymentBankList(paymentBankService.findAllByCurrencyEnum(indexDataDTO.getCurrencyEnum()));

        initDataVO.setImgReadServerUrl(awsS3Service.getReadDomain());

        initDataVO.setNeedRotateScreenPortraitPlatformCodeList(thirdGamePlatformNeedRotateScreenConfig.getNeedRotateScreenPortraitPlatformCodeList());
        initDataVO.setNeedRotateScreenLandscapePlatformCodeList(thirdGamePlatformNeedRotateScreenConfig.getNeedRotateScreenLandscapePlatformCodeList());
        initDataVO.setNeedRotateScreenPortraitGameIdList(thirdGamePlatformNeedRotateScreenConfig.getNeedRotateScreenPortraitGameIdList());
        initDataVO.setNeedRotateScreenLandscapeGameIdList(thirdGamePlatformNeedRotateScreenConfig.getNeedRotateScreenLandscapeGameIdList());

        String md5 = MD5.create().digestHex(JSONUtil.toJsonStr(initDataVO));
        initDataVO.setMd5(md5);
        return initDataVO;
    }

    /**
     * 间隔一段时间刷新缓存内的数据
     */
    private void tryRefreshInitDataAsync(IndexDataDTO indexDataDTO) {
        threadPoolTaskExecutor.execute(() -> {
            String lockKey = getLockKey(indexDataDTO);
            if (!redisService.setIfAbsent(lockKey, LocalDateTime.now().toString(), CommonRedisConstants.INIT_DATA_LOCK_TIME)) {
                return;
            }
            InitDataVO initDataVO = getInitDataVO(indexDataDTO);
            String dataKey = getDataKey(indexDataDTO);
            String md5Key = getMd5Key(indexDataDTO);
            String md5Value = (String) redisService.get(md5Key);
            if (initDataVO.getMd5().equals(md5Value)) {
                redisService.expire(dataKey, CommonRedisConstants.INIT_DATA_DATA_CACHE_TIME, TimeUnit.DAYS);
                redisService.expire(md5Key, CommonRedisConstants.INIT_DATA_DATA_CACHE_TIME, TimeUnit.DAYS);
                return;
            }
            redisService.set(dataKey, initDataVO, CommonRedisConstants.INIT_DATA_DATA_CACHE_TIME, TimeUnit.DAYS);
            redisService.set(md5Key, initDataVO.getMd5(), CommonRedisConstants.INIT_DATA_DATA_CACHE_TIME, TimeUnit.DAYS);
        });
    }

    private static String getLockKey(IndexDataDTO indexDataDTO) {
        return String.format(CommonRedisConstants.INIT_DATA_LOCK, indexDataDTO.getMerchantId(), indexDataDTO.getCurrencyEnum());
    }

    private static String getMd5Key(IndexDataDTO indexDataDTO) {
        return String.format(CommonRedisConstants.INIT_DATA_MD5, indexDataDTO.getMerchantId(), indexDataDTO.getCurrencyEnum());
    }

    private static String getDataKey(IndexDataDTO indexDataDTO) {
        return String.format(CommonRedisConstants.INIT_DATA_DATA, indexDataDTO.getMerchantId(), indexDataDTO.getCurrencyEnum());
    }

    /**
     * 根据域名获取推广码
     * <p>PS:库里的推广域名可能是http://、https://开头，也可能不存在协议</p>
     *
     * @return {@link String} 推广码
     */
    private String getReferCodeByDomain() {
        try {
            String endpoint = RequestUtil.getEndpoint();
            List<String> queryDomain = DomainUtil.initQueryDomain(endpoint);
            List<MemberRefer> refers = this.memberReferService.lambdaQuery().in(MemberRefer::getDomain, queryDomain).list();
            MemberRefer refer = CollUtil.findOne(refers, item -> StrUtil.equals(DomainUtil.getWebsiteAddress(item.getDomain()), DomainUtil.getWebsiteAddress(endpoint)));
            return Objects.isNull(refer) ? StrUtil.EMPTY : (Objects.isNull(refer.getReferCode()) ? StrUtil.EMPTY : Convert.toStr(refer.getReferCode()));
        } catch (Exception ex) {
            log.error("根据域名获取推广码失败", ex);
        }
        return StrUtil.EMPTY;
    }
}
