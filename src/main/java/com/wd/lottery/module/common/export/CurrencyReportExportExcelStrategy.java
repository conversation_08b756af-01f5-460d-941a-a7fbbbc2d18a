package com.wd.lottery.module.common.export;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wd.lottery.GrapeApplication;
import com.wd.lottery.common.constans.BooleanEnum;
import com.wd.lottery.common.constans.Constants;
import com.wd.lottery.common.constans.CurrencyEnum;
import com.wd.lottery.module.report.entity.ReportCurrencyBetOrder;
import com.wd.lottery.module.report.entity.ReportCurrencyBetOrderConfig;
import com.wd.lottery.module.report.excel.CurrencyReportExcel;
import com.wd.lottery.module.report.param.CurrencyReportExportParam;
import com.wd.lottery.module.report.service.ReportCurrencyBetOrderConfigService;
import com.wd.lottery.module.third.constants.CurrencyReportDataTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class CurrencyReportExportExcelStrategy extends MybatisMaxIdExportExcelStrategy<ReportCurrencyBetOrder, CurrencyReportExcel, CurrencyReportExportParam> {
    public CurrencyReportExportExcelStrategy(String mapperMethod, String nextSearchField, CurrencyReportExportParam initParam, Integer limit, String language) {
        super(mapperMethod, nextSearchField, initParam, limit, language);
    }

    @Override
    public List<CurrencyReportExcel> convertToExcelData(List<ReportCurrencyBetOrder> data) {
        List<String> platformCodes = data.stream().map(ReportCurrencyBetOrder::getPlatformCode).distinct().collect(Collectors.toList());
        ReportCurrencyBetOrderConfigService configService = GrapeApplication.getBean(ReportCurrencyBetOrderConfigService.class);
        List<ReportCurrencyBetOrderConfig> listByCodes = configService.getListByCodes(platformCodes);
        Map<String, ReportCurrencyBetOrderConfig> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(listByCodes)) {
            map = listByCodes.stream().collect(Collectors.toMap(ReportCurrencyBetOrderConfig::getPlatformCode, Function.identity()));
        }
        List<CurrencyReportExcel> currencyReportExcelList;
        CurrencyReportExportParam initParam = this.getInitParam();
        if (ObjectUtil.equal(BooleanEnum.TRUE, initParam.getGroupByDateEnum())) {
            currencyReportExcelList = buildReportByPlatformAndCurrencyAndDate(data, map);
        } else {
            currencyReportExcelList = groupByPlatformAndDate(data, initParam, map);
        }
        if (CollectionUtils.isEmpty(currencyReportExcelList)) {
            return Collections.emptyList();
        }
        // 查询timezone
        getTimeZoneFormConfig(currencyReportExcelList, map);
        return currencyReportExcelList;

    }


    private List<CurrencyReportExcel> groupByPlatformAndDate(List<ReportCurrencyBetOrder> list, CurrencyReportExportParam param, Map<String, ReportCurrencyBetOrderConfig> map) {
        Map<String, Map<CurrencyEnum, List<ReportCurrencyBetOrder>>> collect = list.stream().collect(
                Collectors.groupingBy(ReportCurrencyBetOrder::getPlatformCode,
                        Collectors.groupingBy(ReportCurrencyBetOrder::getCurrencyEnum)));
        return buildReportByCurrencyAndPlatform(collect, param, map);
    }

    private List<CurrencyReportExcel> buildReportByCurrencyAndPlatform(Map<String, Map<CurrencyEnum, List<ReportCurrencyBetOrder>>> collect, CurrencyReportExportParam param,
                                                                       Map<String, ReportCurrencyBetOrderConfig> map) {
        List<CurrencyReportExcel> result = Lists.newArrayListWithCapacity(50);
        for (String platformCode : collect.keySet()) {
            ReportCurrencyBetOrderConfig currencyReportConfig = map.get(platformCode);
            Map<CurrencyEnum, List<ReportCurrencyBetOrder>> platformMap = collect.get(platformCode);
            for (CurrencyEnum currencyType : platformMap.keySet()) {
                List<ReportCurrencyBetOrder> currencyReports = platformMap.get(currencyType);
                CurrencyReportExcel excel = mergeReportByGroup(currencyReports, param, currencyReportConfig);
                if (Objects.nonNull(excel)) {
                    result.add(excel);
                }
            }
        }
        return result;
    }

    private CurrencyReportExcel mergeReportByGroup(List<ReportCurrencyBetOrder> currencyReports, CurrencyReportExportParam param, ReportCurrencyBetOrderConfig currencyReportConfig) {
        if (CollectionUtils.isEmpty(currencyReports)) {
            return null;
        }
        ReportCurrencyBetOrder r1 = currencyReports.get(0);
        CurrencyReportExcel excel = new CurrencyReportExcel();
        excel.setDate(param.getStartDate().toString() + " ~ " + param.getEndDate().toString());
        excel.setPlatformCode(r1.getPlatformCode());
        excel.setPlatformName(r1.getPlatformName());
        excel.setCurrencyEnum(r1.getCurrencyEnum().name());

        Map<CurrencyReportDataTypeEnum, List<ReportCurrencyBetOrder>> reportTypeMap = currencyReports.stream()
                .collect(Collectors.groupingBy(ReportCurrencyBetOrder::getDataTypeEnum));
        List<ReportCurrencyBetOrder> reportRows = reportTypeMap.get(CurrencyReportDataTypeEnum.PLATFORM);
        List<ReportCurrencyBetOrder> orderRows = reportTypeMap.get(CurrencyReportDataTypeEnum.ORDER);
        Long totalReportBet = Constants.ZERO_LONG;
        Long totalOrderBet = Constants.ZERO_LONG;

        Long totalReportWin = Constants.ZERO_LONG;
        Long totalOrderWin = Constants.ZERO_LONG;
        Long totalReportBetCount = Constants.ZERO_LONG;
        Long totalOrderBetCount = Constants.ZERO_LONG;
        if (CollUtil.isNotEmpty(reportRows)) {
            for (ReportCurrencyBetOrder reportRow : reportRows) {
                totalReportBet += reportRow.getBetMoney();
                totalReportWin += reportRow.getWinMoney();
                totalReportBetCount += reportRow.getBetCount();
            }
        }
        if (CollUtil.isNotEmpty(orderRows)) {
            for (ReportCurrencyBetOrder orderRow : orderRows) {
                if (Objects.nonNull(currencyReportConfig) && ObjectUtil.equal(currencyReportConfig.getNeedValidBet(), BooleanEnum.FALSE)) {
                    totalOrderBet += orderRow.getBetMoney();
                } else {
                    totalOrderBet += orderRow.getValidBetMoney();
                }
                totalOrderWin += orderRow.getWinMoney();
                totalOrderBetCount += orderRow.getBetCount();
            }
        }
        excel.setReportBet(toDecimal(totalReportBet));
        excel.setReportWin(toDecimal(totalReportWin));
        excel.setOrderBet(toDecimal(totalOrderBet));
        excel.setOrderWin(toDecimal(totalOrderWin));
        excel.setBetDiff(excel.getOrderBet().subtract(excel.getReportBet()));
        excel.setWinDiff(excel.getOrderWin().subtract(excel.getReportWin()));
        excel.setReportBetCount(String.valueOf(totalReportBetCount));
        excel.setBetCount(String.valueOf(totalOrderBetCount));
        excel.setBetCountDiff(String.valueOf(totalOrderBetCount - totalReportBetCount));
        return excel;
    }

    private void getTimeZoneFormConfig(List<CurrencyReportExcel> currencyReportExcelList, Map<String, ReportCurrencyBetOrderConfig> map) {
        for (CurrencyReportExcel currencyReportExcel : currencyReportExcelList) {
            ReportCurrencyBetOrderConfig currencyReportConfig = map.get(currencyReportExcel.getPlatformCode());
            if (Objects.nonNull(currencyReportConfig) && ObjectUtil.equal(currencyReportConfig.getNeedConvert(), BooleanEnum.TRUE)) {
                currencyReportExcel.setTimeZone(currencyReportConfig.getTimeZone());
            } else {
                currencyReportExcel.setTimeZone("GMT-4");
            }
        }
    }

    private List<CurrencyReportExcel> buildReportByPlatformAndCurrencyAndDate(List<ReportCurrencyBetOrder> list, Map<String, ReportCurrencyBetOrderConfig> map) {
        // 日期， 平台， 币种 分组
        Map<CurrencyEnum, Map<String, Map<LocalDate, List<ReportCurrencyBetOrder>>>> collect = list.stream().collect(
                Collectors.groupingBy(ReportCurrencyBetOrder::getCurrencyEnum,
                        Collectors.groupingBy(ReportCurrencyBetOrder::getPlatformCode,
                                Collectors.groupingBy(ReportCurrencyBetOrder::getDate))));
        return buildPlatformCurrencyReportResult(collect, map);
    }

    private List<CurrencyReportExcel> buildPlatformCurrencyReportResult(Map<CurrencyEnum, Map<String, Map<LocalDate, List<ReportCurrencyBetOrder>>>> collect, Map<String, ReportCurrencyBetOrderConfig> map) {
        List<CurrencyReportExcel> result = new ArrayList<>(50);

        for (Map.Entry<CurrencyEnum, Map<String, Map<LocalDate, List<ReportCurrencyBetOrder>>>> dateEntry : collect.entrySet()) {
            for (Map.Entry<String, Map<LocalDate, List<ReportCurrencyBetOrder>>> platformEntry : dateEntry.getValue().entrySet()) {
                for (Map.Entry<LocalDate, List<ReportCurrencyBetOrder>> currencyEntry : platformEntry.getValue().entrySet()) {
                    CurrencyReportExcel excel = mergePlatformCurrencyReport(currencyEntry.getValue(), map);
                    if (Objects.nonNull(excel)) {
                        result.add(excel);
                    }
                }
            }
        }
        return result.stream()
                .sorted(Comparator.comparing(CurrencyReportExcel::getPlatformCode)
                        .thenComparing(CurrencyReportExcel::getCurrencyEnum))
                .collect(Collectors.toList());
    }

    private CurrencyReportExcel mergePlatformCurrencyReport(List<ReportCurrencyBetOrder> rows, Map<String, ReportCurrencyBetOrderConfig> map) {
        if (CollectionUtil.isEmpty(rows)) {
            return null;
        }
        ReportCurrencyBetOrder r1 = rows.get(0);
        if (rows.size() > 2) {
            log.error("币种报表统计数据来源大于 2, date: {}, platform: {}, currency: {}", r1.getDate().toString(), r1.getPlatformCode(), r1.getCurrencyEnum().name());
        }
        CurrencyReportExcel excel = new CurrencyReportExcel();
        excel.setDate(r1.getDate().toString());
        excel.setPlatformCode(r1.getPlatformCode());
        excel.setPlatformName(r1.getPlatformName());
        excel.setCurrencyEnum(r1.getCurrencyEnum().name());

        Map<CurrencyReportDataTypeEnum, List<ReportCurrencyBetOrder>> reportTypeMap = rows.stream()
                .collect(Collectors.groupingBy(ReportCurrencyBetOrder::getDataTypeEnum));

        List<ReportCurrencyBetOrder> reportRows = reportTypeMap.get(CurrencyReportDataTypeEnum.PLATFORM);
        List<ReportCurrencyBetOrder> orderRows = reportTypeMap.get(CurrencyReportDataTypeEnum.ORDER);

        ReportCurrencyBetOrder reportRow = null;
        ReportCurrencyBetOrder orderRow = null;
        if (CollectionUtil.isNotEmpty(reportRows)) {
            reportRow = reportRows.get(0);

            excel.setReportBet(toDecimal(reportRow.getBetMoney()));
            excel.setReportWin(toDecimal(reportRow.getWinMoney()));
            excel.setReportBetCount(String.valueOf(reportRow.getBetCount()));
        }
        if (CollectionUtil.isNotEmpty(orderRows)) {
            orderRow = orderRows.get(0);
            ReportCurrencyBetOrderConfig config = map.get(orderRow.getPlatformCode());
            if (Objects.nonNull(config)) {
                if (ObjectUtil.equal(config.getNeedValidBet(), Constants.ZERO_INTEGER)) {
                    excel.setOrderBet(toDecimal(orderRow.getBetMoney()));
                } else {
                    excel.setOrderBet(toDecimal(orderRow.getValidBetMoney()));
                }
            }
            excel.setOrderBet(toDecimal(orderRow.getBetMoney()));
            excel.setOrderWin(toDecimal(orderRow.getWinMoney()));
            excel.setBetCount(String.valueOf(orderRow.getBetCount()));
        }
        if (Objects.nonNull(reportRow) && Objects.nonNull(orderRow)) {
            excel.setBetDiff(excel.getOrderBet().subtract(excel.getReportBet()));
            excel.setWinDiff(excel.getOrderWin().subtract(excel.getReportWin()));
            excel.setBetCountDiff(String.valueOf(Long.parseLong(excel.getBetCount()) - reportRow.getBetCount()));
        }
        return excel;
    }

    private BigDecimal toDecimal(Long l) {
        if (Objects.isNull(l)) {
            return new BigDecimal(0);
        }
        return BigDecimal.valueOf(l)
                .divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);
    }


}
