package com.wd.lottery.module.common.controller.business;//package com.wd.lottery.module.common.controlle.business;

import com.wd.lottery.common.api.ApiResult;
import com.wd.lottery.common.util.AdminTokenInfoUtil;
import com.wd.lottery.module.common.dto.CommonLoginLogDTO;
import com.wd.lottery.module.common.service.CommonLoginLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "登錄日誌")
@Slf4j
@RestController
@RequestMapping(value = "${business-path}/${module-path.common}/login/log", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class BCommonLoginLogController {

    private final CommonLoginLogService commonLoginLogService;

    @Operation(summary = "查詢")
    @GetMapping("/page")
    public ApiResult<?> page(@ParameterObject CommonLoginLogDTO commonLoginLogDto) {
        return ApiResult.success(commonLoginLogService.search(commonLoginLogDto, AdminTokenInfoUtil.getRequestMerchantIdNotNull(), AdminTokenInfoUtil.getRequestCurrencyEnumNotNull()));
    }

}