package com.wd.lottery.module.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "minio")
public class MinioConfig {
    private String endpoint;
    private String accessKey;
    private String secretKey;
    private String region;

    /**
     * get a default region if not set
     */
    public String getRegion() {
        if (region == null || region.trim().isEmpty()) {
            region =  "us-east-1";
        }
        return region;
    }
}
