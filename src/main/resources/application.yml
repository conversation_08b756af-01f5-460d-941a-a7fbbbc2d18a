
server:
  port: 8080
  servlet:
    context-path: /lottery
spring:
  threads:
    virtual:
      enabled: true
  application:
    name: lottery
  cloud:
    consul:
      host: ***********
      port: 8500
      config:
        prefixes: configuration
        default-context: lottery
        data-key: data
        profile-separator: /
        format: YAML
        enabled: true
  config:
    import: "optional:consul:"

  profiles:
    active: dev

  jackson:
    time-zone: UTC

# mybatis-plus
mybatis-plus:
  mapperLocations: classpath:/mapper/**/*.xml
  configuration:
    cache-enabled: false
    local-cache-scope: STATEMENT
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# module path config
business-path: business
consumer-path: consumer
external-path: external
callback-path: callback
gateway-path: gateway
module-path:
  member: member
  cash: cash
  game: game
  common: common
  activity: activity
  #  商户模块
  merchant: merchant
  report: report
  #  金流
  payment: payment
  third: third
  #  日誌
  log: log
payment:
  numThreads: 5
  notify:
    whitelistRecord: false

retrofit:
  # 全局日志打印配置
  global-log:
    # 启用日志打印
    enable: true
    # 全局日志打印级别
    log-level: info
    # 全局日志打印策略
    log-strategy: basic
    # 是否聚合打印请求日志
    aggregate: true
  # 熔断降级配置
  degrade:
    # 熔断降级类型。
    degrade-type: sentinel
    # 全局sentinel降级配置
    global-sentinel-degrade:
      # 是否开启
      enable: true
      # 各降级策略对应的阈值。平均响应时间(ms)，异常比例(0-1)，异常数量(1-N)
      count: 10
      # 熔断时长，单位为 s
      time-window: 30
      # 降级策略（0：平均响应时间；1：异常比例；2：异常数量）
      grade: 2
http:
  # timeout 秒
  timeout: 5

springdoc:
  group-configs:
    - group: 'business'
      pathsToMatch: /${business-path}/**
    - group: 'consumer'
      pathsToMatch: /${consumer-path}/**
    - group: 'external'
      pathsToMatch: /${external-path}/**
    - group: 'callback'
      pathsToMatch: /${callback-path}/**

#  default-flat-param-object: true
knife4j:
  enable: true
  setting:
    swagger-model-name: 实体类列表
  documents:
    - name: 附录
      group: 'business'
      locations: classpath:markdown/*
    - name: 附录
      group: 'consumer'
      locations: classpath:markdown/*
    - name: 附录
      group: 'external'
      locations: classpath:markdown/*
management:
  security:
    enabled: false
  endpoints:
    web:
      exposure:
        include: "health"

logging:
  level:
    root: info
#    com.wd.lottery: debug



