props:
  sql-show: true
  check-table-metadata-enabled: false
  max-connections-size-per-query: 3
dataSources:
  master_0:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************
    username: testuser
    password: dell9527
    minPoolSize: 2
    maxPoolSize: 5
  slave_0:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************
    username: testuser
    password: dell9527
    minPoolSize: 2
    maxPoolSize: 5
  master_his_0:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************
    username: testuser
    password: dell9527
    minPoolSize: 2
    maxPoolSize: 5
  slave_his_0:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************
    username: testuser
    password: dell9527
    minPoolSize: 2
    maxPoolSize: 5
rules:
  - !SINGLE
    tables:
      - "*.*"
  - !READWRITE_SPLITTING
    dataSources:
      master_slave_0:
        writeDataSourceName: master_0
        readDataSourceNames:
          - slave_0
        transactionalReadQueryStrategy: PRIMARY
        loadBalancerName: random
      master_slave_his_0:
        writeDataSourceName: master_his_0
        readDataSourceNames:
          - slave_his_0
        transactionalReadQueryStrategy: PRIMARY
        loadBalancerName: random
    loadBalancers:
      random:
        type: RANDOM
  - !SHARDING
    tables:
      member:
        actualDataNodes: master_slave_${0}.member
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      member_proxy:
        actualDataNodes: master_slave_${0}.member_proxy
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      member_refer:
        actualDataNodes: master_slave_${0}.member_refer
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      cash_member_wallet:
        actualDataNodes: master_slave_${0}.cash_member_wallet
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      third_bet_order:
        actualDataNodes: master_slave_his_${0}.third_bet_order
        tableStrategy:
          standard:
            shardingColumn: member_id
            shardingAlgorithmName: class_user_sharding_key
      report_member_date:
        actualDataNodes: master_slave_${0}.report_member_date
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      report_member_game_date:
        actualDataNodes: master_slave_${0}.report_member_game_date
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      cash_deposit_order:
        actualDataNodes: master_slave_${0}.cash_deposit_order
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      cash_withdraw_order:
        actualDataNodes: master_slave_${0}.cash_withdraw_order
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      member_withdraw_account:
        actualDataNodes: master_slave_${0}.member_withdraw_account
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      cash_audit:
        actualDataNodes: master_slave_${0}.cash_audit
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_record:
        actualDataNodes: master_slave_${0}.activity_record
        tableStrategy:
          complex:
            shardingColumns: merchant_id,activity_type_enum
            shardingAlgorithmName: class_activity_record
      activity_vip_record:
        actualDataNodes: master_slave_${0}.activity_vip_record
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_vip_member:
        actualDataNodes: master_slave_${0}.activity_vip_member
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_safe_amount_record:
        actualDataNodes: master_slave_${0}.activity_safe_amount_record
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_safe_member_wallet:
        actualDataNodes: master_slave_${0}.activity_safe_member_wallet
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_safe_status_log:
        actualDataNodes: master_slave_${0}.activity_safe_status_log
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_team_proxy_bonus:
        actualDataNodes: master_slave_${0}.activity_team_proxy_bonus
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_team_proxy_bonus_daily_summary:
        actualDataNodes: master_slave_${0}.activity_team_proxy_bonus_daily_summary
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_recharge_roulette_statistics:
        actualDataNodes: master_slave_${0}.activity_recharge_roulette_statistics
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_team_proxy_lower_level_member:
        actualDataNodes: master_slave_${0}.activity_team_proxy_lower_level_member
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_aa_team_proxy_bonus:
        actualDataNodes: master_slave_${0}.activity_aa_team_proxy_bonus
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_aa_team_proxy_bonus_daily_summary:
        actualDataNodes: master_slave_${0}.activity_aa_team_proxy_bonus_daily_summary
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_aa_team_proxy_lower_level_member:
        actualDataNodes: master_slave_${0}.activity_aa_team_proxy_lower_level_member
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_aa_team_proxy_member:
        actualDataNodes: master_slave_${0}.activity_aa_team_proxy_member
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_rebate_statistics:
        actualDataNodes: master_slave_${0}.activity_rebate_statistics
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      activity_single_invite_record:
        actualDataNodes: master_slave_${0}.activity_single_invite_record
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      common_operation_log:
        actualDataNodes: master_slave_his_${0}.common_operation_log
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      common_login_log:
        actualDataNodes: master_slave_his_${0}.common_login_log
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      cash_member_wallet_log_his:
        actualDataNodes: master_slave_his_${0}.cash_member_wallet_log_his
        tableStrategy:
          complex:
            shardingColumns: merchant_id,create_time
            shardingAlgorithmName: class_merchant_id_create_time_month
      cash_deposit_analysis:
        actualDataNodes: master_slave_${0}.cash_deposit_analysis
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      third_seamless_bet_order:
        actualDataNodes: master_slave_${0}.third_seamless_bet_order
        tableStrategy:
          standard:
            shardingColumn: platform_code
            shardingAlgorithmName: class_platform_code
      cash_member_wallet_log:
        actualDataNodes: master_slave_${0}.cash_member_wallet_log
        tableStrategy:
          standard:
            shardingColumn: create_time
            shardingAlgorithmName: class_local_date_time_week
      third_seamless_convert_order:
        actualDataNodes: master_slave_${0}.third_seamless_convert_order
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
      third_convert_order:
        actualDataNodes: master_slave_${0}.third_convert_order
        tableStrategy:
          standard:
            shardingColumn: merchant_id
            shardingAlgorithmName: class_merchant_id
    shardingAlgorithms:
      class_merchant_id:
        type: CLASS_MERCHANT_ID
      class_activity_record:
        type: CLASS_ACTIVITY_RECORD
      class_user_sharding_key:
        type: CLASS_USER_SHARDING_KEY
      class_platform_code:
        type: CLASS_PLATFORM_CODE
      class_local_date_time_week:
        type: CLASS_LOCAL_DATE_TIME_WEEK
      class_merchant_id_create_time_month:
        type: CLASS_MERCHANT_ID_CREATE_TIME_MONTH

